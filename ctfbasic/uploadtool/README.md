# CTF文件上传绕过工具集

这是一个专门为CTF比赛设计的文件上传绕过工具集，包含了所有常见的文件上传绕过技术，特别针对upload-labs靶场的所有题目进行了优化。

## 🚀 功能特性

- **全面覆盖**: 包含所有已知的文件上传绕过技术
- **多语言支持**: 专门针对PHP和Java环境优化
- **自动化测试**: 一键运行所有绕过测试
- **详细日志**: 清晰的测试结果和成功率统计
- **新手友好**: 简单易用，适合CTF新手直接使用

## 📁 文件结构

```
uploadtool/
├── upload_utils.py          # 通用工具函数
├── php_upload_bypass.py     # PHP文件上传绕过脚本
├── java_upload_bypass.py    # Java文件上传绕过脚本
└── README.md               # 使用说明文档
```

## 🛠️ 安装依赖

```bash
pip3 install requests
```

## 📖 使用方法

### PHP文件上传绕过

```bash
# 基本用法
python3 php_upload_bypass.py http://target.com/upload.php

# 指定上传字段名
python3 php_upload_bypass.py http://target.com/upload.php file

# upload-labs靶场示例
python3 php_upload_bypass.py http://localhost/upload-labs/upload/upload.php upload_file
```

### Java文件上传绕过

```bash
# 基本用法
python3 java_upload_bypass.py http://target.com/upload.jsp

# 指定上传字段名
python3 java_upload_bypass.py http://target.com/upload.do upload_file

# Tomcat环境示例
python3 java_upload_bypass.py http://localhost:8080/app/upload.jsp file
```

## 🎯 支持的绕过技术

### PHP绕过技术

#### 1. 基础扩展名绕过
- php, php3, php4, php5, php7, php8
- phtml, pht, phps, phar, inc, module

#### 2. MIME类型绕过
- image/jpeg, image/png, image/gif
- text/plain, text/html, application/octet-stream
- 等多种MIME类型

#### 3. 文件头绕过（图片马）
- GIF89a, PNG, JPEG, BMP等文件头
- 结合PHP代码创建图片马

#### 4. 双重扩展名绕过
- shell.php.jpg, shell.jpg.php
- shell.php.txt, shell.txt.php
- 等多种组合

#### 5. 大小写绕过
- shell.PHP, shell.Php, shell.pHp
- shell.PHTML, shell.PhTmL
- 等大小写变化

#### 6. 特殊字符绕过
- 空格、点号、%20编码
- %00截断、Unicode编码
- Windows特殊字符

#### 7. .htaccess绕过
- AddType配置
- FilesMatch规则
- 自定义扩展名解析

#### 8. 条件竞争绕过
- 多线程上传和访问
- 大文件延长处理时间

#### 9. upload-labs特定绕过
- 双写绕过 (shell.pphphp)
- GET参数绕过
- 图片二次渲染绕过

### Java绕过技术

#### 1. JSP扩展名绕过
- jsp, jspx, jspf, jspa, jsw, jsv, jtml
- jsp.old, jsp.bak, jsp.tmp

#### 2. WAR文件绕过
- 完整的WAR包结构
- web.xml配置文件
- Servlet类文件

#### 3. JSPX XML格式绕过
- 标准XML格式的JSP
- jsp:scriptlet, jsp:expression
- jsp:declaration标签

#### 4. Servlet绕过
- Java源码上传
- 编译后的class文件
- 反射调用技术

#### 5. 代码混淆绕过
- 反射调用Runtime
- 字符串拼接混淆
- 数组字符混淆

#### 6. EL表达式绕过
- ${} 和 #{} 表达式
- JSF EL表达式
- Spring EL表达式

#### 7. 编码绕过
- Unicode编码
- HTML实体编码
- 十六进制编码
- Base64编码

## 🎮 upload-labs靶场对应

本工具专门针对upload-labs靶场进行了优化，可以解决以下关卡：

| 关卡 | 绕过技术 | 对应测试方法 |
|------|----------|--------------|
| Pass-01 | 前端JS验证 | test_basic_extension_bypass |
| Pass-02 | MIME类型检测 | test_mime_type_bypass |
| Pass-03 | 文件头检测 | test_file_header_bypass |
| Pass-04 | .htaccess重写 | test_htaccess_bypass |
| Pass-05 | 大小写绕过 | test_case_bypass |
| Pass-06 | 空格绕过 | test_special_char_bypass |
| Pass-07 | 点号绕过 | test_special_char_bypass |
| Pass-08 | ::$DATA绕过 | test_special_char_bypass |
| Pass-09 | 双重扩展名 | test_double_extension_bypass |
| Pass-10 | %00截断 | test_null_byte_bypass |
| Pass-11 | 双写绕过 | test_upload_labs_specific |
| Pass-12 | GET参数 | test_upload_labs_specific |
| Pass-13 | 图片二次渲染 | test_upload_labs_specific |
| Pass-14-21 | 综合技术 | test_advanced_combinations |

## 🔧 高级用法

### 自定义Payload

可以修改 `upload_utils.py` 中的 `get_php_payloads()` 和 `get_java_payloads()` 方法来添加自定义的payload。

### 批量测试

```bash
# 创建目标列表文件
echo "http://target1.com/upload.php" > targets.txt
echo "http://target2.com/upload.jsp" >> targets.txt

# 批量测试脚本
for target in $(cat targets.txt); do
    echo "Testing $target"
    python3 php_upload_bypass.py $target
done
```

### 结果分析

脚本会输出详细的测试结果，包括：
- 每个测试方法的成功/失败状态
- HTTP状态码
- 响应内容分析
- 文件访问测试结果

## ⚠️ 注意事项

1. **合法使用**: 仅用于授权的渗透测试和CTF比赛
2. **请求频率**: 脚本会自动控制请求频率，避免对目标服务器造成压力
3. **文件清理**: 测试完成后建议清理上传的测试文件
4. **网络环境**: 确保网络连接稳定，避免误报

## 🐛 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 检查目标URL是否可访问
   curl -I http://target.com/upload.php
   ```

2. **上传字段名错误**
   ```bash
   # 查看页面源码确认上传字段名
   curl http://target.com/upload.html | grep -i "input.*file"
   ```

3. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x *.py
   ```

## 📚 学习资源

- [upload-labs靶场](https://github.com/c0ny1/upload-labs)
- [文件上传漏洞总结](https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/Upload%20Insecure%20Files)
- [OWASP文件上传指南](https://owasp.org/www-community/vulnerabilities/Unrestricted_File_Upload)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

本项目仅供学习和研究使用，请勿用于非法用途。
