#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTF文件上传绕过工具 - 测试脚本
用于验证工具的基本功能是否正常
"""

import sys
import os
from upload_utils import UploadUtils
from php_upload_bypass import PHPUploadBypass
from java_upload_bypass import JavaUploadBypass

def test_upload_utils():
    """测试基础工具函数"""
    print("=== 测试基础工具函数 ===")
    
    utils = UploadUtils("http://example.com/upload.php")
    
    # 测试边界生成
    boundary = utils.generate_boundary()
    print(f"✓ 边界生成: {boundary}")
    
    # 测试文件头获取
    headers = utils.get_file_headers()
    print(f"✓ 文件头数量: {len(headers)}")
    
    # 测试PHP payload获取
    php_payloads = utils.get_php_payloads()
    print(f"✓ PHP Payload数量: {len(php_payloads)}")
    
    # 测试Java payload获取
    java_payloads = utils.get_java_payloads()
    print(f"✓ Java Payload数量: {len(java_payloads)}")
    
    # 测试multipart数据创建
    data, headers = utils.create_multipart_data("test.php", "<?php echo 'test'; ?>")
    print(f"✓ Multipart数据创建成功，大小: {len(data)} bytes")
    
    return True

def test_php_bypass():
    """测试PHP绕过工具"""
    print("\n=== 测试PHP绕过工具 ===")
    
    # 使用httpbin.org作为测试目标（不会真正上传文件）
    bypass = PHPUploadBypass("https://httpbin.org/post")
    
    # 测试基本功能
    print("✓ PHP绕过工具初始化成功")
    print(f"✓ 目标URL: {bypass.target_url}")
    print(f"✓ 上传字段: {bypass.upload_field}")
    print(f"✓ PHP Payload数量: {len(bypass.php_payloads)}")
    print(f"✓ 文件头数量: {len(bypass.file_headers)}")
    
    # 测试单个上传（不会真正执行，只是验证方法存在）
    test_methods = [
        'test_basic_extension_bypass',
        'test_mime_type_bypass', 
        'test_file_header_bypass',
        'test_double_extension_bypass',
        'test_case_bypass',
        'test_special_char_bypass',
        'test_null_byte_bypass',
        'test_htaccess_bypass',
        'test_race_condition_bypass',
        'test_file_include_bypass',
        'test_advanced_combinations',
        'test_payload_variations',
        'test_upload_labs_specific'
    ]
    
    for method_name in test_methods:
        if hasattr(bypass, method_name):
            print(f"✓ 方法存在: {method_name}")
        else:
            print(f"✗ 方法缺失: {method_name}")
            return False
    
    return True

def test_java_bypass():
    """测试Java绕过工具"""
    print("\n=== 测试Java绕过工具 ===")
    
    # 使用httpbin.org作为测试目标
    bypass = JavaUploadBypass("https://httpbin.org/post")
    
    # 测试基本功能
    print("✓ Java绕过工具初始化成功")
    print(f"✓ 目标URL: {bypass.target_url}")
    print(f"✓ 上传字段: {bypass.upload_field}")
    print(f"✓ Java Payload数量: {len(bypass.java_payloads)}")
    print(f"✓ 文件头数量: {len(bypass.file_headers)}")
    
    # 测试方法存在性
    test_methods = [
        'test_jsp_extension_bypass',
        'test_jsp_mime_bypass',
        'test_jsp_header_bypass',
        'test_jsp_double_extension',
        'test_jsp_case_bypass',
        'test_war_file_bypass',
        'test_jsp_payload_variations',
        'test_jspx_bypass',
        'test_servlet_bypass',
        'test_java_special_chars',
        'test_java_advanced_combinations',
        'test_java_filter_bypass',
        'test_java_obfuscation',
        'test_java_expression_language'
    ]
    
    for method_name in test_methods:
        if hasattr(bypass, method_name):
            print(f"✓ 方法存在: {method_name}")
        else:
            print(f"✗ 方法缺失: {method_name}")
            return False
    
    return True

def test_payload_quality():
    """测试Payload质量"""
    print("\n=== 测试Payload质量 ===")
    
    utils = UploadUtils("http://example.com")
    
    # 测试PHP Payload
    php_payloads = utils.get_php_payloads()
    for name, payload in php_payloads.items():
        if not payload or len(payload) < 5:
            print(f"✗ PHP Payload质量问题: {name}")
            return False
        if '<?php' not in payload and '<?' not in payload and '<script' not in payload:
            print(f"✗ PHP Payload格式问题: {name}")
            return False
    
    print(f"✓ 所有PHP Payload质量检查通过 ({len(php_payloads)}个)")
    
    # 测试Java Payload
    java_payloads = utils.get_java_payloads()
    for name, payload in java_payloads.items():
        if not payload or len(payload) < 5:
            print(f"✗ Java Payload质量问题: {name}")
            return False
        if name.startswith('jsp') and '<%' not in payload and '<?xml' not in payload:
            print(f"✗ Java Payload格式问题: {name}")
            return False
    
    print(f"✓ 所有Java Payload质量检查通过 ({len(java_payloads)}个)")
    
    return True

def test_file_headers():
    """测试文件头"""
    print("\n=== 测试文件头 ===")
    
    utils = UploadUtils("http://example.com")
    headers = utils.get_file_headers()
    
    expected_headers = ['GIF89a', 'PNG', 'JPEG', 'BMP', 'PDF']
    
    for header_name in expected_headers:
        if header_name not in headers:
            print(f"✗ 缺失文件头: {header_name}")
            return False
        
        header_bytes = headers[header_name]
        if not isinstance(header_bytes, bytes) or len(header_bytes) < 2:
            print(f"✗ 文件头格式错误: {header_name}")
            return False
    
    print(f"✓ 所有文件头检查通过 ({len(headers)}个)")
    return True

def run_all_tests():
    """运行所有测试"""
    print("开始CTF文件上传绕过工具测试...")
    print("=" * 50)
    
    tests = [
        ("基础工具函数", test_upload_utils),
        ("PHP绕过工具", test_php_bypass),
        ("Java绕过工具", test_java_bypass),
        ("Payload质量", test_payload_quality),
        ("文件头测试", test_file_headers)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"\n✓ {test_name} - 通过")
                passed += 1
            else:
                print(f"\n✗ {test_name} - 失败")
        except Exception as e:
            print(f"\n✗ {test_name} - 错误: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具可以正常使用。")
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("CTF文件上传绕过工具测试脚本")
        print("用法: python3 test_tools.py")
        print("功能: 验证所有工具的基本功能是否正常")
        return
    
    success = run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
