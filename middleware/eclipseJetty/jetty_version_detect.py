#!/usr/bin/env python3
"""
Eclipse Jetty 版本识别和指纹识别工具
通过多种方式识别Jetty版本和配置信息
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JettyVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 版本检测路径
        self.version_paths = [
            '/',
            '/index.html',
            '/index.jsp',
            '/admin',
            '/manager',
            '/status',
            '/server-status',
            '/server-info',
            '/jetty',
            '/jetty-admin',
            '/404',
            '/nonexistent',
        ]
        
        # 错误页面触发路径
        self.error_paths = [
            '/nonexistent_page_12345',
            '/error_test_jetty',
            '/404_test',
            '/500_test',
            '/403_test',
        ]
        
        # 已知版本漏洞映射
        self.version_vulnerabilities = {
            '9.4': ['CVE-2021-28169', 'CVE-2021-34429', 'CVE-2022-2048'],
            '10.0': ['CVE-2021-28169', 'CVE-2021-34429', 'CVE-2022-2048'],
            '11.0': ['CVE-2021-28169', 'CVE-2021-34429', 'CVE-2022-2048', 'CVE-2023-26048'],
        }
        
        # 特征检测
        self.jetty_indicators = [
            'jetty',
            'eclipse jetty',
            'org.eclipse.jetty',
            'jetty server',
            'jetty-server',
            'jetty-servlet',
            'jetty-webapp',
            'jetty-http',
            'jetty-io',
            'jetty-util',
        ]
        
        # 版本检测模式
        self.version_patterns = [
            r'jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
            r'eclipse jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
            r'server:\s*jetty[/\s]*([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
            r'jetty-([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
            r'org\.eclipse\.jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
        ]
    
    def get_server_headers(self):
        """获取服务器响应头信息"""
        print(f"[*] 获取服务器头信息: {self.target_url}")
        
        headers_info = {}
        
        try:
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            # 提取关键头信息
            important_headers = [
                'Server', 'X-Powered-By', 'X-AspNet-Version', 
                'X-Generator', 'X-Version', 'Via', 'X-Jetty-Version'
            ]
            
            for header in important_headers:
                if header in response.headers:
                    headers_info[header] = response.headers[header]
                    print(f"[+] {header}: {response.headers[header]}")
            
            # 检查响应状态和内容
            headers_info['status_code'] = response.status_code
            headers_info['content_length'] = len(response.content)
            
            return headers_info, response
            
        except Exception as e:
            print(f"[-] 获取头信息失败: {e}")
            return {}, None
    
    def detect_version_from_headers(self, headers_info):
        """从响应头检测版本信息"""
        version_info = {}
        
        # 检查Server头
        if 'Server' in headers_info:
            server_header = headers_info['Server']
            
            # 提取Jetty版本
            for pattern in self.version_patterns:
                match = re.search(pattern, server_header, re.IGNORECASE)
                if match:
                    version_info['jetty_version'] = match.group(1)
                    print(f"[+] 从Server头检测到Jetty版本: {match.group(1)}")
                    break
            
            if 'jetty' in server_header.lower():
                version_info['jetty_detected'] = True
                print(f"[+] 检测到Jetty，但版本可能被隐藏")
        
        return version_info
    
    def detect_version_from_errors(self):
        """通过错误页面检测版本信息"""
        print(f"[*] 通过错误页面检测版本...")
        
        version_info = {}
        
        for error_path in self.error_paths:
            try:
                url = urljoin(self.target_url, error_path)
                response = self.session.get(url, timeout=self.timeout)
                
                # 检查错误页面内容
                if response.status_code in [404, 403, 500]:
                    content = response.text.lower()
                    
                    # 查找版本信息
                    for pattern in self.version_patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            version_info['jetty_version'] = match.group(1)
                            print(f"[+] 从错误页面检测到版本: {match.group(1)}")
                            return version_info
                    
                    if any(indicator in content for indicator in self.jetty_indicators):
                        version_info['jetty_detected'] = True
                        print(f"[+] 从错误页面检测到Jetty")
                        
            except Exception as e:
                continue
        
        return version_info
    
    def detect_version_from_admin(self):
        """通过管理界面检测版本"""
        print(f"[*] 通过管理界面检测版本...")
        
        version_info = {}
        
        admin_paths = ['/admin', '/manager', '/jetty-admin', '/status', '/server-status']
        
        for path in admin_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                # 即使返回401，也可能在响应中包含版本信息
                if response.status_code in [200, 401, 403]:
                    content = response.text.lower()
                    
                    # 查找版本信息
                    for pattern in self.version_patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            version_info['jetty_version'] = match.group(1)
                            print(f"[+] 从管理界面检测到版本: {match.group(1)}")
                            return version_info
                    
                    if any(indicator in content for indicator in self.jetty_indicators):
                        version_info['jetty_detected'] = True
                        print(f"[+] 从管理界面检测到Jetty")
                        
            except Exception as e:
                continue
        
        return version_info
    
    def detect_version_from_static_files(self):
        """通过静态文件检测版本"""
        print(f"[*] 通过静态文件检测版本...")
        
        version_info = {}
        
        static_paths = [
            '/favicon.ico',
            '/robots.txt',
            '/sitemap.xml',
            '/crossdomain.xml',
            '/clientaccesspolicy.xml',
            '/js/',
            '/css/',
            '/images/',
            '/static/',
        ]
        
        for path in static_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    # 检查响应头中的版本信息
                    server_header = response.headers.get('Server', '')
                    if server_header:
                        for pattern in self.version_patterns:
                            match = re.search(pattern, server_header, re.IGNORECASE)
                            if match:
                                version_info['jetty_version'] = match.group(1)
                                print(f"[+] 从静态文件响应头检测到版本: {match.group(1)}")
                                return version_info
                        
                        if 'jetty' in server_header.lower():
                            version_info['jetty_detected'] = True
                            print(f"[+] 从静态文件检测到Jetty")
                            
            except Exception as e:
                continue
        
        return version_info
    
    def fingerprint_jetty(self):
        """Jetty指纹识别"""
        print(f"[*] 进行Jetty指纹识别...")
        
        fingerprint_info = {}
        
        # 测试特定的Jetty行为
        fingerprint_tests = [
            # 测试HTTP方法支持
            ('http_methods', '/', 'HTTP Methods'),
            # 测试错误页面格式
            ('error_pages', '/nonexistent', 'Error Pages'),
            # 测试静态文件处理
            ('static_files', '/favicon.ico', 'Static Files'),
            # 测试管理接口
            ('admin_interface', '/admin', 'Admin Interface'),
        ]
        
        for test_name, test_path, description in fingerprint_tests:
            try:
                url = urljoin(self.target_url, test_path)
                
                # 测试不同HTTP方法
                methods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']
                method_results = {}
                
                for method in methods:
                    try:
                        response = self.session.request(method, url, timeout=self.timeout)
                        method_results[method] = {
                            'status_code': response.status_code,
                            'has_jetty_indicators': any(indicator in response.text.lower() 
                                                       for indicator in self.jetty_indicators)
                        }
                    except Exception as e:
                        method_results[method] = {'error': str(e)}
                
                fingerprint_info[test_name] = {
                    'description': description,
                    'methods': method_results
                }
                
            except Exception as e:
                fingerprint_info[test_name] = {'error': str(e)}
        
        return fingerprint_info
    
    def get_vulnerability_info(self, version_info):
        """根据版本信息获取相关漏洞"""
        vulnerabilities = []
        
        if 'jetty_version' in version_info:
            version = version_info['jetty_version']
            
            # 提取主版本号
            version_parts = version.split('.')
            if len(version_parts) >= 2:
                major_minor = f"{version_parts[0]}.{version_parts[1]}"
                
                # 检查已知漏洞
                for vuln_version, vulns in self.version_vulnerabilities.items():
                    if version.startswith(vuln_version) or major_minor == vuln_version:
                        vulnerabilities.extend(vulns)
        
        return list(set(vulnerabilities))  # 去重
    
    def generate_report(self):
        """生成完整的检测报告"""
        print("=" * 80)
        print("Eclipse Jetty 版本检测报告")
        print("=" * 80)
        
        # 获取基础信息
        headers_info, response = self.get_server_headers()
        
        # 版本检测
        version_info = self.detect_version_from_headers(headers_info)
        
        if not version_info.get('jetty_version'):
            error_version_info = self.detect_version_from_errors()
            version_info.update(error_version_info)
        
        if not version_info.get('jetty_version'):
            admin_version_info = self.detect_version_from_admin()
            version_info.update(admin_version_info)
        
        if not version_info.get('jetty_version'):
            static_version_info = self.detect_version_from_static_files()
            version_info.update(static_version_info)
        
        if version_info:
            print(f"\n[+] 版本信息:")
            for key, value in version_info.items():
                print(f"  {key}: {value}")
        else:
            print(f"\n[-] 未检测到明确的版本信息")
        
        # 指纹识别
        fingerprint_info = self.fingerprint_jetty()
        if fingerprint_info:
            print(f"\n[+] 指纹信息:")
            for test_name, test_result in fingerprint_info.items():
                if 'error' not in test_result:
                    print(f"  {test_name}: {test_result['description']}")
        
        # 漏洞信息
        vulnerabilities = self.get_vulnerability_info(version_info)
        if vulnerabilities:
            print(f"\n[!] 可能存在的漏洞:")
            for cve in vulnerabilities:
                print(f"  - {cve}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        if 'jetty_version' in version_info:
            print("  - 检查当前版本是否为最新版本")
        if 'Server' in headers_info:
            print("  - 考虑隐藏Server头中的版本信息")
        if vulnerabilities:
            print("  - 及时修复已知漏洞")
        if any('admin' in test for test in fingerprint_info.keys()):
            print("  - 限制管理接口的访问权限")
        
        return {
            'headers_info': headers_info,
            'version_info': version_info,
            'fingerprint_info': fingerprint_info,
            'vulnerabilities': vulnerabilities
        }

def main():
    parser = argparse.ArgumentParser(description='Eclipse Jetty 版本识别和指纹识别工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--headers-only', action='store_true', help='仅检测响应头')
    parser.add_argument('--fingerprint-only', action='store_true', help='仅进行指纹识别')
    
    args = parser.parse_args()
    
    detector = JettyVersionDetector(args.target, args.timeout)
    
    if args.headers_only:
        headers_info, _ = detector.get_server_headers()
        version_info = detector.detect_version_from_headers(headers_info)
        for key, value in version_info.items():
            print(f"{key}: {value}")
    elif args.fingerprint_only:
        fingerprint_info = detector.fingerprint_jetty()
        for test_name, test_result in fingerprint_info.items():
            print(f"{test_name}: {test_result}")
    else:
        report = detector.generate_report()
        
        # 输出到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Jetty检测报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n指纹信息:\n")
                for test_name, test_result in report['fingerprint_info'].items():
                    f.write(f"{test_name}: {test_result}\n")
                
                f.write("\n可能的漏洞:\n")
                for cve in report['vulnerabilities']:
                    f.write(f"{cve}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
