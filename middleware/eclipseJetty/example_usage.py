#!/usr/bin/env python3
"""
Eclipse Jetty 漏洞扫描工具使用示例
演示如何使用各种扫描脚本
"""

import subprocess
import sys
import time

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("命令执行超时")
    except Exception as e:
        print(f"执行错误: {e}")
    
    time.sleep(2)

def main():
    if len(sys.argv) != 2:
        print("用法: python3 example_usage.py <target_url>")
        print("示例: python3 example_usage.py http://*************:8080")
        sys.exit(1)
    
    target = sys.argv[1]
    
    print("Eclipse Jetty 漏洞扫描工具使用示例")
    print(f"目标: {target}")
    
    # 示例1: 综合扫描
    run_command([
        'python3', 'jetty_comprehensive_scan.py', target
    ], "综合漏洞扫描")
    
    # 示例2: 版本识别
    run_command([
        'python3', 'jetty_version_detect.py', target
    ], "Jetty版本识别")
    
    # 示例3: 信息收集
    run_command([
        'python3', 'jetty_info_scan.py', target
    ], "Jetty信息收集")
    
    # 示例4: CVE-2021-28169检测
    run_command([
        'python3', 'CVE-2021-28169.py', target, '--check-only'
    ], "CVE-2021-28169 信息泄露漏洞检测")
    
    # 示例5: CVE-2021-34429检测
    run_command([
        'python3', 'CVE-2021-34429.py', target, '--check-only'
    ], "CVE-2021-34429 WEB-INF敏感信息泄露检测")
    
    # 示例6: CVE-2022-2048检测
    run_command([
        'python3', 'CVE-2022-2048.py', target, '--check-only'
    ], "CVE-2022-2048 资源管理错误漏洞检测")
    
    # 示例7: CVE-2023-26048检测
    run_command([
        'python3', 'CVE-2023-26048.py', target, '--check-only'
    ], "CVE-2023-26048 资源管理错误漏洞检测")
    
    # 示例8: 目录遍历检测
    run_command([
        'python3', 'jetty_directory_traversal.py', target, '--check-only'
    ], "Jetty目录遍历漏洞检测")
    
    # 示例9: 文件上传检测
    run_command([
        'python3', 'jetty_file_upload.py', target, '--check-only'
    ], "Jetty文件上传漏洞检测")
    
    print(f"\n{'='*60}")
    print("示例演示完成!")
    print("更多用法请参考 README.md 文件")
    print(f"{'='*60}")

if __name__ == '__main__':
    main()
