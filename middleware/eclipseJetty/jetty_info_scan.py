#!/usr/bin/env python3
"""
Eclipse Jetty 信息收集和扫描工具
收集Jetty服务器的详细信息和配置
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JettyInfoScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 常见的Jetty路径
        self.jetty_paths = [
            '/',
            '/admin',
            '/manager',
            '/status',
            '/server-status',
            '/server-info',
            '/jetty',
            '/jetty-admin',
            '/jetty-manager',
            '/console',
            '/jmx-console',
            '/web-console',
            '/monitoring',
            '/metrics',
            '/health',
            '/info',
            '/actuator',
            '/actuator/health',
            '/actuator/info',
            '/actuator/metrics',
        ]
        
        # 默认应用和示例
        self.default_apps = [
            '/test/',
            '/demo/',
            '/examples/',
            '/sample/',
            '/docs/',
            '/javadoc/',
            '/api-docs/',
            '/swagger/',
            '/swagger-ui/',
            '/h2-console/',
            '/jolokia/',
        ]
        
        # 敏感文件
        self.sensitive_files = [
            '/WEB-INF/web.xml',
            '/META-INF/MANIFEST.MF',
            '/META-INF/context.xml',
            '/jetty.xml',
            '/jetty-web.xml',
            '/webdefault.xml',
            '/start.ini',
            '/start.d/',
            '/etc/jetty.xml',
            '/etc/webdefault.xml',
            '/etc/jetty-ssl.xml',
            '/etc/jetty-https.xml',
            '/etc/jetty-http.xml',
            '/logs/access.log',
            '/logs/error.log',
            '/logs/jetty.log',
            '/logs/request.log',
            '/logs/yyyy_mm_dd.request.log',
            '/tmp/',
            '/work/',
            '/webapps/',
        ]
        
        # 配置文件模式
        self.config_patterns = [
            r'jetty\.xml',
            r'jetty-web\.xml',
            r'webdefault\.xml',
            r'start\.ini',
            r'jetty\.properties',
            r'realm\.properties',
        ]
    
    def scan_jetty_info(self):
        """扫描Jetty基础信息"""
        print(f"[*] 扫描Jetty基础信息: {self.target_url}")
        
        info_results = {
            'accessible_paths': [],
            'version_info': {},
            'server_info': {},
            'applications': []
        }
        
        for path in self.jetty_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                print(f"[*] 测试 {path} -> {response.status_code}")
                
                if response.status_code in [200, 401, 403]:
                    path_info = {
                        'path': path,
                        'url': url,
                        'status_code': response.status_code,
                        'content_length': len(response.text),
                        'has_jetty_features': self.check_jetty_features(response.text)
                    }
                    
                    # 提取版本信息
                    version_info = self.extract_version_info(response)
                    if version_info:
                        path_info['version_info'] = version_info
                        info_results['version_info'].update(version_info)
                    
                    # 检查是否是Jetty相关页面
                    if self.check_jetty_features(response.text):
                        print(f"[+] 发现Jetty相关页面: {path}")
                        info_results['accessible_paths'].append(path_info)
                        
                        # 特殊处理管理界面
                        if any(admin_path in path for admin_path in ['/admin', '/manager', '/console']):
                            self.analyze_admin_interface(response, path_info)
                        
                        # 特殊处理状态页面
                        elif 'status' in path or 'info' in path:
                            self.analyze_status_page(response, path_info)
                    
                    elif response.status_code == 200:
                        print(f"[!] 发现可访问路径: {path}")
                        info_results['accessible_paths'].append(path_info)
                        
            except Exception as e:
                continue
        
        return info_results
    
    def check_jetty_features(self, content):
        """检查内容是否包含Jetty特征"""
        jetty_indicators = [
            'jetty',
            'eclipse jetty',
            'org.eclipse.jetty',
            'jetty server',
            'jetty-server',
            'jetty-servlet',
            'jetty-webapp',
            'jetty-http',
            'jetty-io',
            'jetty-util',
            'jetty-security',
            'jetty-jmx',
        ]
        
        content_lower = content.lower()
        
        for indicator in jetty_indicators:
            if indicator in content_lower:
                return True
        
        return False
    
    def extract_version_info(self, response):
        """提取版本信息"""
        version_info = {}
        
        # 从响应头提取
        server_header = response.headers.get('Server', '')
        if server_header:
            jetty_match = re.search(r'jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)', 
                                  server_header, re.IGNORECASE)
            if jetty_match:
                version_info['server_header_version'] = jetty_match.group(1)
        
        # 从响应内容提取
        content = response.text
        version_patterns = [
            r'jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
            r'eclipse jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
            r'jetty-([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
        ]
        
        for pattern in version_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                version_info['content_version'] = match.group(1)
                break
        
        return version_info
    
    def analyze_admin_interface(self, response, path_info):
        """分析管理界面"""
        print(f"[*] 分析管理界面: {path_info['path']}")
        
        if response.status_code == 401:
            print(f"[!] 管理界面需要认证")
            path_info['requires_auth'] = True
        elif response.status_code == 200:
            print(f"[+] 管理界面可直接访问")
            path_info['requires_auth'] = False
            
            # 检查是否包含管理功能
            content_lower = response.text.lower()
            if any(keyword in content_lower for keyword in ['deploy', 'undeploy', 'start', 'stop', 'restart']):
                print(f"[+] 管理界面包含部署功能")
                path_info['has_deploy_functions'] = True
                
            if any(keyword in content_lower for keyword in ['configuration', 'settings', 'properties']):
                print(f"[+] 管理界面包含配置功能")
                path_info['has_config_functions'] = True
                
        elif response.status_code == 403:
            print(f"[!] 管理界面被禁止访问")
            path_info['forbidden'] = True
    
    def analyze_status_page(self, response, path_info):
        """分析状态页面"""
        print(f"[*] 分析状态页面: {path_info['path']}")
        
        if response.status_code == 200:
            print(f"[+] 状态页面可访问")
            
            content_lower = response.text.lower()
            
            # 检查是否包含系统信息
            if any(keyword in content_lower for keyword in ['memory', 'cpu', 'threads', 'connections']):
                print(f"[+] 包含系统状态信息")
                path_info['has_system_info'] = True
            
            # 检查是否包含应用信息
            if any(keyword in content_lower for keyword in ['applications', 'webapps', 'contexts']):
                print(f"[+] 包含应用状态信息")
                path_info['has_app_info'] = True
            
            # 检查是否包含JVM信息
            if any(keyword in content_lower for keyword in ['jvm', 'java', 'heap', 'garbage']):
                print(f"[+] 包含JVM信息")
                path_info['has_jvm_info'] = True
    
    def check_default_apps(self):
        """检测默认应用"""
        print(f"[*] 检测默认应用...")
        
        default_app_results = []
        
        for app_path in self.default_apps:
            try:
                url = urljoin(self.target_url, app_path)
                response = self.session.get(url, timeout=self.timeout)
                
                print(f"[*] 检测 {app_path} -> {response.status_code}")
                
                if response.status_code == 200:
                    print(f"[+] 发现可访问的默认应用: {app_path}")
                    
                    app_info = {
                        'path': app_path,
                        'url': url,
                        'status_code': response.status_code,
                        'content_length': len(response.text),
                        'description': self.get_app_description(app_path, response.text)
                    }
                    
                    default_app_results.append(app_info)
                    
            except Exception as e:
                continue
        
        return default_app_results
    
    def get_app_description(self, app_path, content):
        """获取应用描述"""
        descriptions = {
            '/test/': 'Jetty测试应用',
            '/demo/': 'Jetty演示应用',
            '/examples/': 'Jetty示例应用',
            '/docs/': 'Jetty文档',
            '/javadoc/': 'Java API文档',
            '/swagger/': 'Swagger API文档',
            '/h2-console/': 'H2数据库控制台',
            '/jolokia/': 'Jolokia JMX代理',
        }
        
        for path, desc in descriptions.items():
            if app_path.startswith(path):
                return desc
        
        # 尝试从内容中提取描述
        content_lower = content.lower()
        if 'test' in content_lower:
            return '测试应用'
        elif 'demo' in content_lower:
            return '演示应用'
        elif 'example' in content_lower:
            return '示例应用'
        elif 'documentation' in content_lower or 'docs' in content_lower:
            return '文档应用'
        else:
            return '未知应用'
    
    def check_sensitive_files(self):
        """检测敏感文件"""
        print(f"[*] 检测敏感文件...")
        
        sensitive_file_results = []
        
        for file_path in self.sensitive_files:
            try:
                url = urljoin(self.target_url, file_path)
                response = self.session.get(url, timeout=self.timeout)
                
                print(f"[*] 检测 {file_path} -> {response.status_code}")
                
                if response.status_code == 200 and len(response.text) > 0:
                    # 检查是否是有效的文件内容
                    if self.is_valid_sensitive_file(file_path, response.text):
                        print(f"[+] 发现敏感文件: {file_path}")
                        
                        file_info = {
                            'path': file_path,
                            'url': url,
                            'status_code': response.status_code,
                            'content_length': len(response.text),
                            'content_preview': response.text[:500]
                        }
                        
                        sensitive_file_results.append(file_info)
                        
            except Exception as e:
                continue
        
        return sensitive_file_results
    
    def is_valid_sensitive_file(self, file_path, content):
        """检查是否是有效的敏感文件"""
        content_lower = content.lower()
        
        if 'web.xml' in file_path:
            return '<web-app' in content_lower or '<servlet' in content_lower
        elif 'jetty.xml' in file_path or 'jetty-web.xml' in file_path:
            return '<configure' in content_lower or '<new' in content_lower
        elif 'manifest.mf' in file_path:
            return 'manifest-version' in content_lower
        elif 'context.xml' in file_path:
            return '<context' in content_lower
        elif '.log' in file_path:
            return len(content) > 50 and ('info' in content_lower or 'error' in content_lower)
        elif 'start.ini' in file_path:
            return '--module' in content_lower or 'jetty' in content_lower
        elif file_path.endswith('/'):
            return ('index of' in content_lower or 
                   'directory listing' in content_lower or
                   'parent directory' in content_lower)
        else:
            return len(content.strip()) > 20
    
    def scan_configuration_files(self):
        """扫描配置文件"""
        print(f"[*] 扫描配置文件...")
        
        config_files = []
        
        # 扫描根目录下的配置文件
        for pattern in self.config_patterns:
            try:
                # 尝试直接访问
                test_url = urljoin(self.target_url, f'/{pattern}')
                response = self.session.get(test_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    config_files.append({
                        'file': pattern,
                        'url': test_url,
                        'content_length': len(response.text),
                        'content_preview': response.text[:500]
                    })
                    
            except Exception as e:
                continue
        
        return config_files
    
    def generate_report(self):
        """生成完整的信息收集报告"""
        print("=" * 80)
        print("Eclipse Jetty 信息收集报告")
        print("=" * 80)
        
        # 基础信息扫描
        info_results = self.scan_jetty_info()
        
        # 默认应用检测
        default_apps = self.check_default_apps()
        
        # 敏感文件检测
        sensitive_files = self.check_sensitive_files()
        
        # 配置文件扫描
        config_files = self.scan_configuration_files()
        
        # 输出报告
        if info_results['version_info']:
            print(f"\n[+] 版本信息:")
            for key, value in info_results['version_info'].items():
                print(f"  {key}: {value}")
        
        if info_results['accessible_paths']:
            print(f"\n[+] 可访问路径:")
            for path_info in info_results['accessible_paths']:
                print(f"  - {path_info['path']} (状态码: {path_info['status_code']})")
        
        if default_apps:
            print(f"\n[+] 默认应用:")
            for app_info in default_apps:
                print(f"  - {app_info['path']} - {app_info['description']}")
        
        if sensitive_files:
            print(f"\n[!] 敏感文件:")
            for file_info in sensitive_files:
                print(f"  - {file_info['path']}")
        
        if config_files:
            print(f"\n[!] 配置文件:")
            for config_info in config_files:
                print(f"  - {config_info['file']}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        if default_apps:
            print("  - 移除或限制访问默认应用")
        if sensitive_files:
            print("  - 保护敏感文件不被直接访问")
        if config_files:
            print("  - 保护配置文件不被泄露")
        if any(path['path'] in ['/admin', '/manager'] and path.get('requires_auth', True) == False 
               for path in info_results['accessible_paths']):
            print("  - 为管理界面配置认证")
        
        return {
            'info_results': info_results,
            'default_apps': default_apps,
            'sensitive_files': sensitive_files,
            'config_files': config_files
        }

def main():
    parser = argparse.ArgumentParser(description='Eclipse Jetty 信息收集和扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--info-only', action='store_true', help='仅收集基础信息')
    parser.add_argument('--apps-only', action='store_true', help='仅检测默认应用')
    parser.add_argument('--files-only', action='store_true', help='仅检测敏感文件')
    
    args = parser.parse_args()
    
    scanner = JettyInfoScanner(args.target, args.timeout)
    
    if args.info_only:
        info_results = scanner.scan_jetty_info()
        for path_info in info_results['accessible_paths']:
            print(f"{path_info['path']}: {path_info['status_code']}")
    elif args.apps_only:
        default_apps = scanner.check_default_apps()
        for app_info in default_apps:
            print(f"{app_info['path']}: {app_info['description']}")
    elif args.files_only:
        sensitive_files = scanner.check_sensitive_files()
        for file_info in sensitive_files:
            print(f"{file_info['path']}: {file_info['content_length']} bytes")
    else:
        report = scanner.generate_report()
        
        # 输出到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Jetty信息收集报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['info_results']['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n可访问路径:\n")
                for path_info in report['info_results']['accessible_paths']:
                    f.write(f"{path_info['path']}: {path_info['status_code']}\n")
                
                f.write("\n默认应用:\n")
                for app_info in report['default_apps']:
                    f.write(f"{app_info['path']}: {app_info['description']}\n")
                
                f.write("\n敏感文件:\n")
                for file_info in report['sensitive_files']:
                    f.write(f"{file_info['path']}\n")
                
                f.write("\n配置文件:\n")
                for config_info in report['config_files']:
                    f.write(f"{config_info['file']}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
