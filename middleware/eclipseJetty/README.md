# Eclipse Jetty 漏洞扫描工具集

这是一个专门针对Eclipse Jetty应用服务器的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。

## 功能特性

- 🔍 **漏洞检测**: 支持多个Eclipse Jetty CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、配置泄露、敏感文件扫描
- 🔐 **安全测试**: 文件上传、目录遍历、信息泄露等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2021-28169 | Eclipse Jetty 信息泄露漏洞 | Medium | 9.4.37-9.4.42, 10.0.1-10.0.5, 11.0.1-11.0.5 |
| CVE-2021-34429 | Eclipse Jetty WEB-INF敏感信息泄露 | High | 9.4.37-9.4.42, 10.0.1-10.0.5, 11.0.1-11.0.5 |
| CVE-2022-2048 | Eclipse Jetty 资源管理错误漏洞 | High | 9.4.46, 10.0.10, 11.0.10 |
| CVE-2023-26048 | Eclipse Jetty 资源管理错误漏洞 | High | <=9.4.50, <=10.0.13, <=11.0.13 |
| 目录遍历 | Jetty目录遍历漏洞 | High | 配置相关 |
| 文件上传 | Jetty文件上传漏洞 | High | 配置相关 |
| 版本识别 | Jetty版本和指纹识别 | Info | 所有版本 |
| 信息收集 | Jetty敏感信息泄露 | Medium | 配置相关 |

## 安装和使用

### 环境要求

- Python 3.6+
- pip (Python包管理器)

### 安装依赖

```bash
cd eclipseJetty
pip install -r requirements.txt
```

### 基本使用

#### 1. 综合扫描 (推荐)

```bash
# 扫描所有漏洞
python3 jetty_comprehensive_scan.py http://target:8080

# 扫描指定漏洞
python3 jetty_comprehensive_scan.py http://target:8080 -v CVE-2021-34429 jetty_directory_traversal

# 保存扫描报告
python3 jetty_comprehensive_scan.py http://target:8080 -o report.json
```

#### 2. 单个漏洞扫描

```bash
# CVE-2021-28169 信息泄露漏洞
python3 CVE-2021-28169.py http://target:8080 --check-only
python3 CVE-2021-28169.py http://target:8080 -i

# CVE-2021-34429 WEB-INF敏感信息泄露
python3 CVE-2021-34429.py http://target:8080 --check-only
python3 CVE-2021-34429.py http://target:8080 -p "/WEB-INF/" -f "web.xml"

# CVE-2022-2048 资源管理错误漏洞
python3 CVE-2022-2048.py http://target:8080 --check-only
python3 CVE-2022-2048.py http://target:8080 -a concurrent

# CVE-2023-26048 资源管理错误漏洞
python3 CVE-2023-26048.py http://target:8080 --check-only
python3 CVE-2023-26048.py http://target:8080 -a connection_flood

# 目录遍历漏洞
python3 jetty_directory_traversal.py http://target:8080 --check-only
python3 jetty_directory_traversal.py http://target:8080 -f "etc/passwd"

# 文件上传漏洞
python3 jetty_file_upload.py http://target:8080 --check-only
python3 jetty_file_upload.py http://target:8080 -s jsp
```

#### 3. 信息收集

```bash
# 版本识别
python3 jetty_version_detect.py http://target:8080

# 信息收集
python3 jetty_info_scan.py http://target:8080
python3 jetty_info_scan.py http://target:8080 --apps-only  # 仅检测默认应用
python3 jetty_info_scan.py http://target:8080 --files-only  # 仅检测敏感文件
```

## 脚本详细说明

### 核心漏洞利用脚本

1. **CVE-2021-28169.py** - Eclipse Jetty 信息泄露漏洞
   - 检测和利用Jetty信息泄露漏洞
   - 支持多种路径测试和信息提取
   - 包含交互式测试模式

2. **CVE-2021-34429.py** - Eclipse Jetty WEB-INF敏感信息泄露
   - 利用WEB-INF目录访问绕过
   - 支持多种编码和绕过技术
   - 包含敏感文件自动扫描

3. **CVE-2022-2048.py** - Eclipse Jetty 资源管理错误漏洞
   - 检测和利用资源管理错误导致的DoS
   - 支持并发请求、大数据包、慢速请求攻击
   - 包含交互式攻击模式

4. **CVE-2023-26048.py** - Eclipse Jetty 资源管理错误漏洞
   - 检测和利用资源耗尽漏洞
   - 支持连接洪水、HTTP头洪水、HTTP/2攻击
   - 包含交互式利用模式

5. **jetty_directory_traversal.py** - Jetty目录遍历漏洞
   - 检测Jetty目录遍历漏洞
   - 支持多种编码和绕过技术
   - 包含交互式文件读取

6. **jetty_file_upload.py** - Jetty文件上传漏洞
   - 检测和利用文件上传漏洞
   - 支持多种WebShell类型和绕过技术
   - 包含交互式WebShell

### 辅助工具脚本

1. **jetty_version_detect.py** - 版本识别
   - 多种方式识别Jetty版本
   - 指纹识别和漏洞映射
   - 安全建议生成

2. **jetty_info_scan.py** - 信息收集
   - 全面的Jetty信息收集
   - 默认应用和敏感文件检测
   - 详细的安全评估

3. **jetty_comprehensive_scan.py** - 综合扫描
   - 集成所有漏洞检测功能
   - 自动化扫描和报告生成
   - 风险评估和安全建议

## 高级用法

### 交互式模式

大部分脚本都支持交互式模式，方便手动测试：

```bash
# 交互式信息泄露测试
python3 CVE-2021-28169.py http://target:8080 -i

# 交互式WEB-INF利用
python3 CVE-2021-34429.py http://target:8080 -i

# 交互式目录遍历
python3 jetty_directory_traversal.py http://target:8080 -i

# 交互式文件上传
python3 jetty_file_upload.py http://target:8080 -i
```

### 批量扫描

```bash
# 创建目标列表
echo "http://target1:8080" > targets.txt
echo "http://target2:8080" >> targets.txt

# 批量扫描
for target in $(cat targets.txt); do
    python3 jetty_comprehensive_scan.py $target -o "report_$(echo $target | tr '/:' '_').json"
done
```

### 自定义配置

```bash
# 自定义WebShell类型
python3 jetty_file_upload.py http://target:8080 -s php

# 自定义文件读取
python3 jetty_directory_traversal.py http://target:8080 -f "/flag.txt"

# 自定义WEB-INF路径
python3 CVE-2021-34429.py http://target:8080 -p "/WEB-INF/" -f "classes/config.properties"
```

## 注意事项

1. **合法使用**: 仅用于授权的渗透测试和安全研究
2. **网络环境**: 某些功能需要目标网络可达
3. **权限要求**: 某些功能可能需要特定权限
4. **版本兼容**: 不同Jetty版本可能有不同的漏洞
5. **配置依赖**: 大部分漏洞与Jetty配置相关

## 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   python3 script.py http://target:8080 -t 30
   ```

2. **SSL证书错误**
   - 脚本已自动忽略SSL警告

3. **权限不足**
   ```bash
   # 某些功能可能需要管理员权限
   sudo python3 script.py http://target:8080
   ```

4. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --upgrade
   ```

5. **HTTP/2支持问题**
   ```bash
   # 如果需要完整的HTTP/2支持，可以安装额外库
   pip install httpx h2
   ```

## 贡献

欢迎提交Issue和Pull Request来改进这个工具集。

## 免责声明

本工具仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规，作者不承担任何滥用责任。
