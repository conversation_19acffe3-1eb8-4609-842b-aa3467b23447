#!/usr/bin/env python3
"""
Apache Kafka 版本检测和指纹识别工具
通过多种方式识别Kafka版本和配置信息
"""

import sys
import requests
import argparse
import json
import socket
import struct
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class KafkaVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 9092
        
        # 已知版本漏洞映射
        self.version_vulnerabilities = {
            '2.8': ['CVE-2023-25194'],
            '3.0': ['CVE-2023-25194'],
            '3.1': ['CVE-2023-25194'],
            '3.2': ['CVE-2023-25194'],
            '3.3': ['CVE-2023-25194'],
            '3.4': ['CVE-2023-25194'],
            '2.7': ['CVE-2023-25194', 'CVE-2022-34917'],
            '2.6': ['CVE-2023-25194', 'CVE-2022-34917'],
            '2.5': ['CVE-2023-25194', 'CVE-2022-34917'],
        }
        
        # Kafka指纹特征
        self.fingerprint_indicators = [
            'kafka',
            'apache kafka',
            'broker',
            'topic',
            'partition',
            'offset',
            'consumer',
            'producer',
            'connect',
            'stream',
            'confluent',
        ]
        
        # API版本
        self.API_VERSIONS = {
            'API_VERSIONS': 18,
            'METADATA': 3,
        }
    
    def detect_kafka_services(self):
        """检测Kafka服务"""
        print(f"[*] 检测Kafka服务: {self.target_host}")
        
        detection_results = {
            'kafka_broker': None,
            'kafka_connect': None,
            'schema_registry': None,
            'rest_proxy': None,
            'control_center': None,
        }
        
        # 检测Kafka Broker
        broker_info = self.detect_kafka_broker()
        if broker_info:
            detection_results['kafka_broker'] = broker_info
            print(f"[+] 发现Kafka Broker: {self.target_host}:{self.target_port}")
        
        # 检测Kafka Connect
        connect_url = self.detect_kafka_connect()
        if connect_url:
            detection_results['kafka_connect'] = connect_url
            print(f"[+] 发现Kafka Connect: {connect_url}")
        
        # 检测Schema Registry
        schema_url = self.detect_schema_registry()
        if schema_url:
            detection_results['schema_registry'] = schema_url
            print(f"[+] 发现Schema Registry: {schema_url}")
        
        # 检测REST Proxy
        rest_url = self.detect_rest_proxy()
        if rest_url:
            detection_results['rest_proxy'] = rest_url
            print(f"[+] 发现REST Proxy: {rest_url}")
        
        # 检测Control Center
        control_url = self.detect_control_center()
        if control_url:
            detection_results['control_center'] = control_url
            print(f"[+] 发现Control Center: {control_url}")
        
        return detection_results
    
    def detect_kafka_broker(self):
        """检测Kafka Broker"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送API_VERSIONS请求
            api_versions_request = self.create_api_versions_request()
            sock.send(api_versions_request)
            
            # 接收响应
            response = sock.recv(1024)
            sock.close()
            
            if len(response) > 0:
                api_versions = self.parse_api_versions_response(response)
                return {
                    'host': self.target_host,
                    'port': self.target_port,
                    'api_versions': api_versions
                }
            
            return None
            
        except Exception as e:
            return None
    
    def create_api_versions_request(self):
        """创建API_VERSIONS请求"""
        request = bytearray()
        
        # 请求头
        request.extend(b'\x00\x00\x00\x00')  # 长度占位符
        request.extend(struct.pack('>H', self.API_VERSIONS['API_VERSIONS']))  # API Key
        request.extend(struct.pack('>H', 0))  # API Version
        request.extend(struct.pack('>I', 1))  # Correlation ID
        
        # Client ID
        client_id = b'kafka-version-detect'
        request.extend(struct.pack('>H', len(client_id)))
        request.extend(client_id)
        
        # 更新长度字段
        struct.pack_into('>I', request, 0, len(request) - 4)
        
        return bytes(request)
    
    def parse_api_versions_response(self, response):
        """解析API版本响应"""
        try:
            if len(response) < 8:
                return []
            
            # 跳过响应头
            offset = 8
            
            # 读取API版本数组长度
            if offset + 4 > len(response):
                return []
            
            array_length = struct.unpack('>I', response[offset:offset+4])[0]
            offset += 4
            
            api_versions = []
            
            for i in range(min(array_length, 20)):  # 限制解析数量
                if offset + 6 > len(response):
                    break
                
                api_key = struct.unpack('>H', response[offset:offset+2])[0]
                min_version = struct.unpack('>H', response[offset+2:offset+4])[0]
                max_version = struct.unpack('>H', response[offset+4:offset+6])[0]
                
                api_versions.append({
                    'api_key': api_key,
                    'min_version': min_version,
                    'max_version': max_version
                })
                
                offset += 6
            
            return api_versions
            
        except Exception as e:
            return []
    
    def detect_kafka_connect(self):
        """检测Kafka Connect"""
        ports = [8083, 8084]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                response = self.session.get(test_url, timeout=self.timeout)
                
                if self.is_kafka_connect_response(response):
                    return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def is_kafka_connect_response(self, response):
        """检查是否是Kafka Connect响应"""
        if response.status_code not in [200, 404, 405]:
            return False
        
        content = response.text.lower()
        indicators = ['kafka', 'connect', 'connector', 'version', 'commit']
        
        return any(indicator in content for indicator in indicators)
    
    def detect_schema_registry(self):
        """检测Schema Registry"""
        ports = [8081, 8082]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                subjects_url = urljoin(test_url, '/subjects')
                response = self.session.get(subjects_url, timeout=self.timeout)
                
                if response.status_code in [200, 401, 403]:
                    return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def detect_rest_proxy(self):
        """检测Kafka REST Proxy"""
        ports = [8080, 8082]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                topics_url = urljoin(test_url, '/topics')
                response = self.session.get(topics_url, timeout=self.timeout)
                
                if response.status_code in [200, 401, 403]:
                    content = response.text.lower()
                    if 'topic' in content or 'kafka' in content:
                        return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def detect_control_center(self):
        """检测Confluent Control Center"""
        ports = [9021, 8080]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                response = self.session.get(test_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    if 'confluent' in content or 'control center' in content:
                        return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def get_version_from_connect(self, connect_url):
        """通过Kafka Connect获取版本信息"""
        print(f"[*] 通过Kafka Connect获取版本信息...")
        
        try:
            response = self.session.get(connect_url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                
                version_info = {
                    'kafka_version': data.get('kafka_version', 'Unknown'),
                    'kafka_commit_id': data.get('kafka_commit_id', 'Unknown'),
                    'version': data.get('version', 'Unknown'),
                    'commit': data.get('commit', 'Unknown'),
                }
                
                print(f"[+] 通过Kafka Connect获取版本信息成功")
                return version_info
                
        except Exception as e:
            pass
        
        print(f"[-] 无法通过Kafka Connect获取版本信息")
        return None
    
    def get_version_from_headers(self, service_url):
        """通过响应头获取版本信息"""
        print(f"[*] 通过响应头获取版本信息...")
        
        try:
            response = self.session.get(service_url, timeout=self.timeout)
            
            version_info = {}
            
            # 检查Server头
            server_header = response.headers.get('Server', '')
            if server_header:
                version_info['server_header'] = server_header
                
                # 尝试从Server头提取版本
                import re
                version_match = re.search(r'kafka[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)', 
                                        server_header, re.IGNORECASE)
                if version_match:
                    version_info['version_from_header'] = version_match.group(1)
            
            # 检查其他相关头
            for header_name in ['X-Kafka-Version', 'X-Version', 'X-Powered-By']:
                header_value = response.headers.get(header_name)
                if header_value:
                    version_info[header_name.lower().replace('-', '_')] = header_value
            
            if version_info:
                print(f"[+] 从响应头获取到版本信息")
                return version_info
            else:
                print(f"[-] 响应头中未发现版本信息")
                return None
                
        except Exception as e:
            print(f"[-] 获取响应头失败: {e}")
            return None
    
    def get_version_from_error_pages(self, service_url):
        """通过错误页面获取版本信息"""
        print(f"[*] 通过错误页面获取版本信息...")
        
        # 尝试访问不存在的页面触发错误
        error_paths = [
            '/nonexistent_page_12345',
            '/invalid_path',
            '/error_test'
        ]
        
        for path in error_paths:
            try:
                error_url = urljoin(service_url, path)
                response = self.session.get(error_url, timeout=self.timeout)
                
                if response.status_code in [404, 500]:
                    content = response.text
                    
                    # 检查错误页面中的版本信息
                    import re
                    version_patterns = [
                        r'kafka[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                        r'version[:\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                        r'confluent[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                    ]
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            print(f"[+] 从错误页面获取到版本信息")
                            return {
                                'version_from_error': match.group(1),
                                'error_page_content': content[:500]
                            }
                            
            except Exception as e:
                continue
        
        print(f"[-] 错误页面中未发现版本信息")
        return None
    
    def fingerprint_kafka(self, services):
        """Kafka指纹识别"""
        print(f"[*] 进行Kafka指纹识别...")
        
        fingerprint_info = {}
        
        for service_type, service_info in services.items():
            if not service_info:
                continue
            
            try:
                if service_type == 'kafka_broker':
                    # Broker指纹识别
                    api_versions = service_info.get('api_versions', [])
                    fingerprint_info['broker_api_versions'] = len(api_versions)
                    fingerprint_info['max_api_key'] = max([v['api_key'] for v in api_versions]) if api_versions else 0
                    
                else:
                    # HTTP服务指纹识别
                    service_url = service_info
                    
                    # 测试不同的HTTP方法
                    methods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']
                    method_results = {}
                    
                    for method in methods:
                        try:
                            response = self.session.request(method, service_url, timeout=self.timeout)
                            method_results[method] = {
                                'status_code': response.status_code,
                                'has_kafka_indicators': any(indicator in response.text.lower() 
                                                           for indicator in self.fingerprint_indicators)
                            }
                        except Exception as e:
                            method_results[method] = {'error': str(e)}
                    
                    fingerprint_info[f'{service_type}_methods'] = method_results
                    
            except Exception as e:
                continue
        
        print(f"[+] Kafka指纹识别完成")
        return fingerprint_info
    
    def get_vulnerability_info(self, version_info):
        """根据版本信息获取相关漏洞"""
        vulnerabilities = []
        
        if not version_info:
            return vulnerabilities
        
        # 从版本信息中提取版本号
        version = None
        for key, value in version_info.items():
            if 'version' in key and isinstance(value, str):
                # 提取版本号
                import re
                version_match = re.search(r'([0-9]+\.[0-9]+)', value)
                if version_match:
                    version = version_match.group(1)
                    break
        
        if version:
            # 检查已知漏洞
            for vuln_version, vulns in self.version_vulnerabilities.items():
                if version.startswith(vuln_version):
                    vulnerabilities.extend(vulns)
        
        return list(set(vulnerabilities))  # 去重
    
    def generate_security_recommendations(self, version_info, vulnerabilities):
        """生成安全建议"""
        recommendations = []
        
        if vulnerabilities:
            recommendations.append(f"修复已知漏洞: {', '.join(vulnerabilities)}")
        
        if version_info:
            recommendations.append("考虑升级到最新版本")
            recommendations.append("限制Kafka服务的网络访问")
            recommendations.append("配置适当的认证和授权")
            recommendations.append("启用SSL/TLS加密")
        
        return recommendations
    
    def generate_report(self):
        """生成完整的版本检测报告"""
        print("=" * 80)
        print("Apache Kafka 版本检测报告")
        print("=" * 80)
        
        # 服务检测
        services = self.detect_kafka_services()
        
        if not any(services.values()):
            print("[-] 未发现Kafka服务")
            return None
        
        # 版本信息收集
        all_version_info = {}
        
        if services['kafka_connect']:
            connect_version_info = self.get_version_from_connect(services['kafka_connect'])
            if connect_version_info:
                all_version_info.update(connect_version_info)
        
        # 从其他服务获取版本信息
        for service_type, service_url in services.items():
            if service_url and service_type != 'kafka_broker':
                header_version_info = self.get_version_from_headers(service_url)
                if header_version_info:
                    all_version_info.update(header_version_info)
                
                error_version_info = self.get_version_from_error_pages(service_url)
                if error_version_info:
                    all_version_info.update(error_version_info)
        
        # 指纹识别
        fingerprint_info = self.fingerprint_kafka(services)
        
        # 漏洞信息
        vulnerabilities = self.get_vulnerability_info(all_version_info)
        
        # 安全建议
        recommendations = self.generate_security_recommendations(all_version_info, vulnerabilities)
        
        # 输出报告
        print(f"\n[+] 服务检测结果:")
        for service_type, service_info in services.items():
            if service_info:
                if service_type == 'kafka_broker':
                    print(f"  - {service_type}: {service_info['host']}:{service_info['port']}")
                else:
                    print(f"  - {service_type}: {service_info}")
        
        if all_version_info:
            print(f"\n[+] 版本信息:")
            for key, value in all_version_info.items():
                if key != 'error_page_content':  # 跳过长内容
                    print(f"  - {key}: {value}")
        
        if vulnerabilities:
            print(f"\n[!] 可能存在的漏洞:")
            for cve in vulnerabilities:
                print(f"  - {cve}")
        
        if recommendations:
            print(f"\n[*] 安全建议:")
            for rec in recommendations:
                print(f"  - {rec}")
        
        return {
            'services': services,
            'version_info': all_version_info,
            'fingerprint_info': fingerprint_info,
            'vulnerabilities': vulnerabilities,
            'recommendations': recommendations
        }

def main():
    parser = argparse.ArgumentParser(description='Apache Kafka 版本检测和指纹识别工具')
    parser.add_argument('target', help='目标URL (例如: tcp://target:9092)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--connect-only', action='store_true', help='仅通过Connect检测版本')
    parser.add_argument('--fingerprint-only', action='store_true', help='仅进行指纹识别')
    
    args = parser.parse_args()
    
    detector = KafkaVersionDetector(args.target, args.timeout)
    
    if args.connect_only:
        connect_url = detector.detect_kafka_connect()
        if connect_url:
            version_info = detector.get_version_from_connect(connect_url)
            if version_info:
                for key, value in version_info.items():
                    print(f"{key}: {value}")
            else:
                print("无法获取版本信息")
        else:
            print("未发现Kafka Connect")
    elif args.fingerprint_only:
        services = detector.detect_kafka_services()
        if any(services.values()):
            fingerprint_info = detector.fingerprint_kafka(services)
            if fingerprint_info:
                print(json.dumps(fingerprint_info, indent=2))
        else:
            print("未发现Kafka服务")
    else:
        report = detector.generate_report()
        
        # 输出到文件
        if args.output and report:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Kafka版本检测报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n可能的漏洞:\n")
                for cve in report['vulnerabilities']:
                    f.write(f"{cve}\n")
                
                f.write("\n安全建议:\n")
                for rec in report['recommendations']:
                    f.write(f"{rec}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
