#!/usr/bin/env python3
"""
Apache Kafka 信息收集和扫描工具
收集Kafka集群的详细信息和配置
"""

import sys
import requests
import argparse
import json
import socket
import struct
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class KafkaInfoScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 9092
        
        # 常见的Kafka端口
        self.kafka_ports = {
            9092: 'Kafka Broker',
            9093: 'Kafka Broker SSL',
            8083: 'Kafka Connect',
            8084: 'Kafka Connect SSL',
            8080: 'Kafka REST Proxy',
            8081: 'Schema Registry',
            8082: 'Kafka REST Proxy',
            9021: 'Confluent Control Center',
            8088: 'KSQL Server',
            8089: 'KSQL Server SSL',
            2181: 'Zookeeper',
            2182: 'Zookeeper SSL',
        }
        
        # Kafka API版本
        self.API_VERSIONS = {
            'PRODUCE': 0,
            'FETCH': 1,
            'LIST_OFFSETS': 2,
            'METADATA': 3,
            'LEADER_AND_ISR': 4,
            'STOP_REPLICA': 5,
            'UPDATE_METADATA': 6,
            'CONTROLLED_SHUTDOWN': 7,
            'OFFSET_COMMIT': 8,
            'OFFSET_FETCH': 9,
            'FIND_COORDINATOR': 10,
            'JOIN_GROUP': 11,
            'HEARTBEAT': 12,
            'LEAVE_GROUP': 13,
            'SYNC_GROUP': 14,
            'DESCRIBE_GROUPS': 15,
            'LIST_GROUPS': 16,
            'SASL_HANDSHAKE': 17,
            'API_VERSIONS': 18,
            'CREATE_TOPICS': 19,
            'DELETE_TOPICS': 20,
        }
    
    def scan_ports(self):
        """扫描Kafka相关端口"""
        print(f"[*] 扫描Kafka相关端口: {self.target_host}")
        
        open_ports = {}
        
        for port, service in self.kafka_ports.items():
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(self.timeout)
                
                result = sock.connect_ex((self.target_host, port))
                
                if result == 0:
                    print(f"[+] 端口 {port} 开放 ({service})")
                    open_ports[port] = service
                
                sock.close()
                
            except Exception as e:
                continue
        
        return open_ports
    
    def detect_kafka_services(self):
        """检测Kafka服务"""
        print(f"[*] 检测Kafka服务...")
        
        services = {}
        
        # 检测Kafka Broker
        broker_info = self.detect_kafka_broker()
        if broker_info:
            services['kafka_broker'] = broker_info
            print(f"[+] 发现Kafka Broker: {self.target_host}:{self.target_port}")
        
        # 检测Kafka Connect
        connect_url = self.detect_kafka_connect()
        if connect_url:
            services['kafka_connect'] = connect_url
            print(f"[+] 发现Kafka Connect: {connect_url}")
        
        # 检测Schema Registry
        schema_url = self.detect_schema_registry()
        if schema_url:
            services['schema_registry'] = schema_url
            print(f"[+] 发现Schema Registry: {schema_url}")
        
        # 检测REST Proxy
        rest_url = self.detect_rest_proxy()
        if rest_url:
            services['rest_proxy'] = rest_url
            print(f"[+] 发现REST Proxy: {rest_url}")
        
        # 检测Control Center
        control_url = self.detect_control_center()
        if control_url:
            services['control_center'] = control_url
            print(f"[+] 发现Control Center: {control_url}")
        
        return services
    
    def detect_kafka_broker(self):
        """检测Kafka Broker"""
        try:
            # 测试Kafka协议连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送API_VERSIONS请求
            api_versions_request = self.create_api_versions_request()
            sock.send(api_versions_request)
            
            # 接收响应
            response = sock.recv(1024)
            sock.close()
            
            if len(response) > 0:
                # 解析API版本响应
                api_versions = self.parse_api_versions_response(response)
                return {
                    'host': self.target_host,
                    'port': self.target_port,
                    'api_versions': api_versions
                }
            
            return None
            
        except Exception as e:
            return None
    
    def create_api_versions_request(self):
        """创建API_VERSIONS请求"""
        request = bytearray()
        
        # 请求头
        request.extend(b'\x00\x00\x00\x00')  # 长度占位符
        request.extend(struct.pack('>H', self.API_VERSIONS['API_VERSIONS']))  # API Key
        request.extend(struct.pack('>H', 0))  # API Version
        request.extend(struct.pack('>I', 1))  # Correlation ID
        
        # Client ID
        client_id = b'kafka-scanner'
        request.extend(struct.pack('>H', len(client_id)))
        request.extend(client_id)
        
        # 更新长度字段
        struct.pack_into('>I', request, 0, len(request) - 4)
        
        return bytes(request)
    
    def parse_api_versions_response(self, response):
        """解析API版本响应"""
        try:
            if len(response) < 8:
                return []
            
            # 跳过响应头
            offset = 8
            
            # 读取API版本数组长度
            if offset + 4 > len(response):
                return []
            
            array_length = struct.unpack('>I', response[offset:offset+4])[0]
            offset += 4
            
            api_versions = []
            
            for i in range(min(array_length, 10)):  # 限制解析数量
                if offset + 6 > len(response):
                    break
                
                api_key = struct.unpack('>H', response[offset:offset+2])[0]
                min_version = struct.unpack('>H', response[offset+2:offset+4])[0]
                max_version = struct.unpack('>H', response[offset+4:offset+6])[0]
                
                api_versions.append({
                    'api_key': api_key,
                    'min_version': min_version,
                    'max_version': max_version
                })
                
                offset += 6
            
            return api_versions
            
        except Exception as e:
            return []
    
    def detect_kafka_connect(self):
        """检测Kafka Connect"""
        ports = [8083, 8084]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                response = self.session.get(test_url, timeout=self.timeout)
                
                if self.is_kafka_connect_response(response):
                    return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def is_kafka_connect_response(self, response):
        """检查是否是Kafka Connect响应"""
        if response.status_code not in [200, 404, 405]:
            return False
        
        content = response.text.lower()
        indicators = ['kafka', 'connect', 'connector', 'version', 'commit']
        
        return any(indicator in content for indicator in indicators)
    
    def detect_schema_registry(self):
        """检测Schema Registry"""
        ports = [8081, 8082]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                subjects_url = urljoin(test_url, '/subjects')
                response = self.session.get(subjects_url, timeout=self.timeout)
                
                if response.status_code in [200, 401, 403]:
                    return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def detect_rest_proxy(self):
        """检测Kafka REST Proxy"""
        ports = [8080, 8082]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                topics_url = urljoin(test_url, '/topics')
                response = self.session.get(topics_url, timeout=self.timeout)
                
                if response.status_code in [200, 401, 403]:
                    content = response.text.lower()
                    if 'topic' in content or 'kafka' in content:
                        return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def detect_control_center(self):
        """检测Confluent Control Center"""
        ports = [9021, 8080]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                response = self.session.get(test_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    if 'confluent' in content or 'control center' in content:
                        return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def collect_broker_metadata(self):
        """收集Broker元数据"""
        print(f"[*] 收集Broker元数据...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送METADATA请求
            metadata_request = self.create_metadata_request()
            sock.send(metadata_request)
            
            # 接收响应
            response = sock.recv(4096)
            sock.close()
            
            if len(response) > 0:
                metadata = self.parse_metadata_response(response)
                return metadata
            
            return None
            
        except Exception as e:
            print(f"[-] 收集元数据失败: {e}")
            return None
    
    def create_metadata_request(self):
        """创建METADATA请求"""
        request = bytearray()
        
        # 请求头
        request.extend(b'\x00\x00\x00\x00')  # 长度占位符
        request.extend(struct.pack('>H', self.API_VERSIONS['METADATA']))  # API Key
        request.extend(struct.pack('>H', 0))  # API Version
        request.extend(struct.pack('>I', 2))  # Correlation ID
        
        # Client ID
        client_id = b'kafka-scanner'
        request.extend(struct.pack('>H', len(client_id)))
        request.extend(client_id)
        
        # Topics (null表示所有topics)
        request.extend(struct.pack('>i', -1))
        
        # 更新长度字段
        struct.pack_into('>I', request, 0, len(request) - 4)
        
        return bytes(request)
    
    def parse_metadata_response(self, response):
        """解析元数据响应"""
        try:
            if len(response) < 12:
                return None
            
            metadata = {
                'brokers': [],
                'topics': []
            }
            
            # 跳过响应头
            offset = 8
            
            # 读取broker数组
            if offset + 4 > len(response):
                return metadata
            
            broker_count = struct.unpack('>I', response[offset:offset+4])[0]
            offset += 4
            
            for i in range(min(broker_count, 10)):  # 限制解析数量
                if offset + 10 > len(response):
                    break
                
                broker_id = struct.unpack('>I', response[offset:offset+4])[0]
                offset += 4
                
                # 读取host字符串
                host_len = struct.unpack('>H', response[offset:offset+2])[0]
                offset += 2
                
                if offset + host_len + 4 > len(response):
                    break
                
                host = response[offset:offset+host_len].decode('utf-8', errors='ignore')
                offset += host_len
                
                port = struct.unpack('>I', response[offset:offset+4])[0]
                offset += 4
                
                metadata['brokers'].append({
                    'id': broker_id,
                    'host': host,
                    'port': port
                })
            
            return metadata
            
        except Exception as e:
            return None
    
    def collect_connect_info(self, connect_url):
        """收集Kafka Connect信息"""
        print(f"[*] 收集Kafka Connect信息...")
        
        try:
            connect_info = {}
            
            # 获取连接器列表
            connectors_url = urljoin(connect_url, '/connectors')
            response = self.session.get(connectors_url, timeout=self.timeout)
            
            if response.status_code == 200:
                connectors = response.json()
                connect_info['connectors'] = connectors
                print(f"[+] 发现 {len(connectors)} 个连接器")
            
            # 获取连接器插件
            plugins_url = urljoin(connect_url, '/connector-plugins')
            response = self.session.get(plugins_url, timeout=self.timeout)
            
            if response.status_code == 200:
                plugins = response.json()
                connect_info['plugins'] = plugins
                print(f"[+] 发现 {len(plugins)} 个连接器插件")
            
            return connect_info
            
        except Exception as e:
            print(f"[-] 收集Connect信息失败: {e}")
            return None
    
    def collect_schema_registry_info(self, schema_url):
        """收集Schema Registry信息"""
        print(f"[*] 收集Schema Registry信息...")
        
        try:
            schema_info = {}
            
            # 获取subjects
            subjects_url = urljoin(schema_url, '/subjects')
            response = self.session.get(subjects_url, timeout=self.timeout)
            
            if response.status_code == 200:
                subjects = response.json()
                schema_info['subjects'] = subjects
                print(f"[+] 发现 {len(subjects)} 个schema subjects")
            
            # 获取配置
            config_url = urljoin(schema_url, '/config')
            response = self.session.get(config_url, timeout=self.timeout)
            
            if response.status_code == 200:
                config = response.json()
                schema_info['config'] = config
            
            return schema_info
            
        except Exception as e:
            print(f"[-] 收集Schema Registry信息失败: {e}")
            return None
    
    def collect_rest_proxy_info(self, rest_url):
        """收集REST Proxy信息"""
        print(f"[*] 收集REST Proxy信息...")
        
        try:
            rest_info = {}
            
            # 获取topics
            topics_url = urljoin(rest_url, '/topics')
            response = self.session.get(topics_url, timeout=self.timeout)
            
            if response.status_code == 200:
                topics = response.json()
                rest_info['topics'] = topics
                print(f"[+] 发现 {len(topics)} 个topics")
            
            # 获取brokers
            brokers_url = urljoin(rest_url, '/brokers')
            response = self.session.get(brokers_url, timeout=self.timeout)
            
            if response.status_code == 200:
                brokers = response.json()
                rest_info['brokers'] = brokers
            
            return rest_info
            
        except Exception as e:
            print(f"[-] 收集REST Proxy信息失败: {e}")
            return None
    
    def generate_report(self):
        """生成完整的信息收集报告"""
        print("=" * 80)
        print("Apache Kafka 信息收集报告")
        print("=" * 80)
        
        # 端口扫描
        open_ports = self.scan_ports()
        
        # 服务检测
        services = self.detect_kafka_services()
        
        # 收集各种信息
        broker_metadata = None
        connect_info = None
        schema_info = None
        rest_info = None
        
        if 'kafka_broker' in services:
            broker_metadata = self.collect_broker_metadata()
        
        if 'kafka_connect' in services:
            connect_info = self.collect_connect_info(services['kafka_connect'])
        
        if 'schema_registry' in services:
            schema_info = self.collect_schema_registry_info(services['schema_registry'])
        
        if 'rest_proxy' in services:
            rest_info = self.collect_rest_proxy_info(services['rest_proxy'])
        
        # 输出报告
        if open_ports:
            print(f"\n[+] 开放端口:")
            for port, service in open_ports.items():
                print(f"  - {port}: {service}")
        
        if services:
            print(f"\n[+] 发现的服务:")
            for service_type, service_info in services.items():
                if isinstance(service_info, dict):
                    print(f"  - {service_type}: {service_info.get('host', '')}:{service_info.get('port', '')}")
                else:
                    print(f"  - {service_type}: {service_info}")
        
        if broker_metadata:
            print(f"\n[+] Broker信息:")
            for broker in broker_metadata.get('brokers', []):
                print(f"  - Broker {broker['id']}: {broker['host']}:{broker['port']}")
        
        if connect_info:
            print(f"\n[+] Kafka Connect信息:")
            connectors = connect_info.get('connectors', [])
            plugins = connect_info.get('plugins', [])
            print(f"  - 连接器数量: {len(connectors)}")
            print(f"  - 插件数量: {len(plugins)}")
            
            if connectors:
                print(f"  - 连接器列表:")
                for connector in connectors[:5]:  # 只显示前5个
                    print(f"    * {connector}")
        
        if schema_info:
            print(f"\n[+] Schema Registry信息:")
            subjects = schema_info.get('subjects', [])
            print(f"  - Schema subjects数量: {len(subjects)}")
            
            if subjects:
                print(f"  - Subjects列表:")
                for subject in subjects[:5]:  # 只显示前5个
                    print(f"    * {subject}")
        
        if rest_info:
            print(f"\n[+] REST Proxy信息:")
            topics = rest_info.get('topics', [])
            print(f"  - Topics数量: {len(topics)}")
            
            if topics:
                print(f"  - Topics列表:")
                for topic in topics[:5]:  # 只显示前5个
                    print(f"    * {topic}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        if open_ports.get(9092):
            print("  - 限制Kafka Broker端口(9092)的网络访问")
        if open_ports.get(8083):
            print("  - 保护Kafka Connect API端点")
        if open_ports.get(8081):
            print("  - 保护Schema Registry API端点")
        if open_ports.get(2181):
            print("  - 保护Zookeeper端口(2181)的网络访问")
        
        return {
            'open_ports': open_ports,
            'services': services,
            'broker_metadata': broker_metadata,
            'connect_info': connect_info,
            'schema_info': schema_info,
            'rest_info': rest_info
        }

def main():
    parser = argparse.ArgumentParser(description='Apache Kafka 信息收集和扫描工具')
    parser.add_argument('target', help='目标URL (例如: tcp://target:9092)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--ports-only', action='store_true', help='仅扫描端口')
    parser.add_argument('--broker-only', action='store_true', help='仅收集Broker信息')
    
    args = parser.parse_args()
    
    scanner = KafkaInfoScanner(args.target, args.timeout)
    
    if args.ports_only:
        open_ports = scanner.scan_ports()
        for port, service in open_ports.items():
            print(f"{port}: {service}")
    elif args.broker_only:
        broker_metadata = scanner.collect_broker_metadata()
        if broker_metadata:
            for broker in broker_metadata.get('brokers', []):
                print(f"Broker {broker['id']}: {broker['host']}:{broker['port']}")
    else:
        report = scanner.generate_report()
        
        # 输出到文件
        if args.output and report:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Kafka信息收集报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("开放端口:\n")
                for port, service in report['open_ports'].items():
                    f.write(f"{port}: {service}\n")
                
                f.write("\n发现的服务:\n")
                for service_type, service_info in report['services'].items():
                    f.write(f"{service_type}: {service_info}\n")
                
                if report['broker_metadata']:
                    f.write("\nBroker信息:\n")
                    for broker in report['broker_metadata'].get('brokers', []):
                        f.write(f"Broker {broker['id']}: {broker['host']}:{broker['port']}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
