#!/usr/bin/env python3
"""
Apache Kafka 漏洞扫描工具使用示例
演示如何使用各种扫描脚本
"""

import subprocess
import sys
import time

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("命令执行超时")
    except Exception as e:
        print(f"执行错误: {e}")
    
    time.sleep(2)

def main():
    if len(sys.argv) != 2:
        print("用法: python3 example_usage.py <target_url>")
        print("示例: python3 example_usage.py tcp://*************:9092")
        sys.exit(1)
    
    target = sys.argv[1]
    
    print("Apache Kafka 漏洞扫描工具使用示例")
    print(f"目标: {target}")
    
    # 示例1: 综合扫描
    run_command([
        'python3', 'kafka_comprehensive_scan.py', target
    ], "综合漏洞扫描")
    
    # 示例2: 版本识别
    run_command([
        'python3', 'kafka_version_detect.py', target
    ], "Kafka版本识别")
    
    # 示例3: 信息收集
    run_command([
        'python3', 'kafka_info_scan.py', target
    ], "Kafka信息收集")
    
    # 示例4: CVE-2023-25194检测
    connect_target = target.replace('tcp://', 'http://').replace('9092', '8083')
    run_command([
        'python3', 'CVE-2023-25194.py', connect_target, '--check-only'
    ], "CVE-2023-25194 Kafka Connect JNDI注入漏洞检测")
    
    # 示例5: 反序列化漏洞检测
    run_command([
        'python3', 'kafka_deserialization.py', target, '--check-only'
    ], "Kafka反序列化漏洞检测")
    
    # 示例6: 暴力破解检测
    run_command([
        'python3', 'kafka_bruteforce.py', target, '--check-only'
    ], "Kafka服务暴力破解检测")
    
    print(f"\n{'='*60}")
    print("示例演示完成!")
    print("更多用法请参考 README.md 文件")
    print(f"{'='*60}")

if __name__ == '__main__':
    main()
