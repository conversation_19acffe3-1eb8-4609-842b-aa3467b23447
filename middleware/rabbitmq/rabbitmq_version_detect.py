#!/usr/bin/env python3
"""
RabbitMQ 版本检测和指纹识别工具
通过多种方式识别RabbitMQ版本和配置信息
"""

import sys
import requests
import argparse
import json
import base64
import socket
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class RabbitMQVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 15672
        
        # 默认凭据
        self.default_credentials = [
            ('guest', 'guest'),
            ('admin', 'admin'),
            ('rabbitmq', 'rabbitmq'),
            ('user', 'user'),
            ('test', 'test'),
        ]
        
        # 已知版本漏洞映射
        self.version_vulnerabilities = {
            '3.8': ['CVE-2021-32718', 'CVE-2021-22116'],
            '3.9': ['CVE-2021-32718', 'CVE-2023-46118'],
            '3.10': ['CVE-2023-46118'],
            '3.11': ['CVE-2023-46118'],
            '3.12': ['CVE-2023-46118'],
        }
        
        # RabbitMQ指纹特征
        self.fingerprint_indicators = [
            'rabbitmq',
            'rabbit mq',
            'management',
            'overview',
            'exchanges',
            'queues',
            'connections',
            'channels',
            'users',
            'virtual hosts',
            'policies',
            'amqp',
            'erlang',
        ]
    
    def detect_rabbitmq_service(self):
        """检测RabbitMQ服务"""
        print(f"[*] 检测RabbitMQ服务: {self.target_host}")
        
        detection_results = {
            'management_interface': None,
            'amqp_port': None,
            'other_ports': []
        }
        
        # 检测管理界面
        management_url = self.detect_management_interface()
        if management_url:
            detection_results['management_interface'] = management_url
            print(f"[+] 发现管理界面: {management_url}")
        
        # 检测AMQP端口
        if self.test_port(5672):
            detection_results['amqp_port'] = 5672
            print(f"[+] AMQP端口开放: 5672")
        elif self.test_port(5671):
            detection_results['amqp_port'] = 5671
            print(f"[+] AMQP TLS端口开放: 5671")
        
        # 检测其他相关端口
        other_ports = [25672, 4369, 35672]
        for port in other_ports:
            if self.test_port(port):
                detection_results['other_ports'].append(port)
                print(f"[+] 其他端口开放: {port}")
        
        return detection_results
    
    def detect_management_interface(self):
        """检测管理界面"""
        # 常见端口和协议
        test_configs = [
            ('http', 15672),
            ('https', 15671),
            ('http', 8080),
            ('https', 8443),
        ]
        
        for protocol, port in test_configs:
            try:
                base_url = f"{protocol}://{self.target_host}:{port}"
                
                # 测试管理界面路径
                test_paths = ['/api/overview', '/#/', '/']
                
                for path in test_paths:
                    test_url = urljoin(base_url, path)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    if self.is_rabbitmq_management(response):
                        return base_url
                        
            except Exception as e:
                continue
        
        return None
    
    def test_port(self, port):
        """测试端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            result = sock.connect_ex((self.target_host, port))
            sock.close()
            
            return result == 0
            
        except Exception as e:
            return False
    
    def is_rabbitmq_management(self, response):
        """检查响应是否来自RabbitMQ管理界面"""
        if response.status_code not in [200, 401, 403]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'rabbitmq' in server_header:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        
        return any(indicator in content for indicator in self.fingerprint_indicators)
    
    def get_version_from_api(self, management_url):
        """通过API获取版本信息"""
        print(f"[*] 通过API获取版本信息...")
        
        for username, password in self.default_credentials:
            try:
                auth_string = f"{username}:{password}"
                auth_bytes = auth_string.encode('ascii')
                auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
                
                headers = {
                    'Authorization': f'Basic {auth_b64}',
                    'Content-Type': 'application/json'
                }
                
                api_url = urljoin(management_url, '/api/overview')
                response = self.session.get(api_url, headers=headers, timeout=self.timeout)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    version_info = {
                        'rabbitmq_version': data.get('rabbitmq_version', 'Unknown'),
                        'erlang_version': data.get('erlang_version', 'Unknown'),
                        'management_version': data.get('management_version', 'Unknown'),
                        'node': data.get('node', 'Unknown'),
                        'cluster_name': data.get('cluster_name', 'Unknown'),
                        'credentials_used': f"{username}:{password}"
                    }
                    
                    print(f"[+] 通过API获取版本信息成功")
                    return version_info
                    
            except Exception as e:
                continue
        
        print(f"[-] 无法通过API获取版本信息")
        return None
    
    def get_version_from_headers(self, management_url):
        """通过响应头获取版本信息"""
        print(f"[*] 通过响应头获取版本信息...")
        
        try:
            response = self.session.get(management_url, timeout=self.timeout)
            
            version_info = {}
            
            # 检查Server头
            server_header = response.headers.get('Server', '')
            if server_header:
                version_info['server_header'] = server_header
                
                # 尝试从Server头提取版本
                import re
                version_match = re.search(r'rabbitmq[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)', 
                                        server_header, re.IGNORECASE)
                if version_match:
                    version_info['version_from_header'] = version_match.group(1)
            
            # 检查其他相关头
            for header_name in ['X-RabbitMQ-Version', 'X-Version', 'X-Powered-By']:
                header_value = response.headers.get(header_name)
                if header_value:
                    version_info[header_name.lower().replace('-', '_')] = header_value
            
            if version_info:
                print(f"[+] 从响应头获取到版本信息")
                return version_info
            else:
                print(f"[-] 响应头中未发现版本信息")
                return None
                
        except Exception as e:
            print(f"[-] 获取响应头失败: {e}")
            return None
    
    def get_version_from_error_pages(self, management_url):
        """通过错误页面获取版本信息"""
        print(f"[*] 通过错误页面获取版本信息...")
        
        # 尝试访问不存在的页面触发错误
        error_paths = [
            '/nonexistent_page_12345',
            '/api/nonexistent',
            '/invalid_path',
            '/error_test'
        ]
        
        for path in error_paths:
            try:
                error_url = urljoin(management_url, path)
                response = self.session.get(error_url, timeout=self.timeout)
                
                if response.status_code in [404, 500]:
                    content = response.text
                    
                    # 检查错误页面中的版本信息
                    import re
                    version_patterns = [
                        r'rabbitmq[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                        r'version[:\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                        r'erlang[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                    ]
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            print(f"[+] 从错误页面获取到版本信息")
                            return {
                                'version_from_error': match.group(1),
                                'error_page_content': content[:500]
                            }
                            
            except Exception as e:
                continue
        
        print(f"[-] 错误页面中未发现版本信息")
        return None
    
    def fingerprint_rabbitmq(self, management_url):
        """RabbitMQ指纹识别"""
        print(f"[*] 进行RabbitMQ指纹识别...")
        
        fingerprint_info = {}
        
        try:
            # 测试不同的HTTP方法
            methods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']
            method_results = {}
            
            for method in methods:
                try:
                    response = self.session.request(method, management_url, timeout=self.timeout)
                    method_results[method] = {
                        'status_code': response.status_code,
                        'headers': dict(response.headers),
                        'has_rabbitmq_indicators': any(indicator in response.text.lower() 
                                                     for indicator in self.fingerprint_indicators)
                    }
                except Exception as e:
                    method_results[method] = {'error': str(e)}
            
            fingerprint_info['http_methods'] = method_results
            
            # 测试特定的RabbitMQ路径
            rabbitmq_paths = [
                '/api',
                '/api/overview',
                '/api/users',
                '/api/vhosts',
                '/api/exchanges',
                '/api/queues',
                '/#/',
                '/#/users',
                '/#/exchanges',
                '/#/queues'
            ]
            
            path_results = {}
            for path in rabbitmq_paths:
                try:
                    test_url = urljoin(management_url, path)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    path_results[path] = {
                        'status_code': response.status_code,
                        'content_length': len(response.text),
                        'has_rabbitmq_content': any(indicator in response.text.lower() 
                                                  for indicator in self.fingerprint_indicators)
                    }
                except Exception as e:
                    path_results[path] = {'error': str(e)}
            
            fingerprint_info['path_responses'] = path_results
            
            print(f"[+] RabbitMQ指纹识别完成")
            return fingerprint_info
            
        except Exception as e:
            print(f"[-] 指纹识别失败: {e}")
            return None
    
    def get_vulnerability_info(self, version_info):
        """根据版本信息获取相关漏洞"""
        vulnerabilities = []
        
        if not version_info:
            return vulnerabilities
        
        # 从版本信息中提取版本号
        version = None
        for key, value in version_info.items():
            if 'version' in key and isinstance(value, str):
                # 提取版本号
                import re
                version_match = re.search(r'([0-9]+\.[0-9]+)', value)
                if version_match:
                    version = version_match.group(1)
                    break
        
        if version:
            # 检查已知漏洞
            for vuln_version, vulns in self.version_vulnerabilities.items():
                if version.startswith(vuln_version):
                    vulnerabilities.extend(vulns)
        
        return list(set(vulnerabilities))  # 去重
    
    def generate_security_recommendations(self, version_info, vulnerabilities):
        """生成安全建议"""
        recommendations = []
        
        if version_info and version_info.get('credentials_used'):
            creds = version_info['credentials_used']
            if 'guest:guest' in creds:
                recommendations.append("禁用或更改默认guest用户密码")
        
        if vulnerabilities:
            recommendations.append(f"修复已知漏洞: {', '.join(vulnerabilities)}")
        
        if version_info:
            recommendations.append("考虑升级到最新版本")
            recommendations.append("限制管理界面的网络访问")
            recommendations.append("配置适当的用户权限")
        
        return recommendations
    
    def generate_report(self):
        """生成完整的版本检测报告"""
        print("=" * 80)
        print("RabbitMQ 版本检测报告")
        print("=" * 80)
        
        # 服务检测
        detection_results = self.detect_rabbitmq_service()
        
        if not detection_results['management_interface']:
            print("[-] 未发现RabbitMQ管理界面")
            return None
        
        management_url = detection_results['management_interface']
        
        # 版本信息收集
        api_version_info = self.get_version_from_api(management_url)
        header_version_info = self.get_version_from_headers(management_url)
        error_version_info = self.get_version_from_error_pages(management_url)
        
        # 指纹识别
        fingerprint_info = self.fingerprint_rabbitmq(management_url)
        
        # 合并版本信息
        all_version_info = {}
        if api_version_info:
            all_version_info.update(api_version_info)
        if header_version_info:
            all_version_info.update(header_version_info)
        if error_version_info:
            all_version_info.update(error_version_info)
        
        # 漏洞信息
        vulnerabilities = self.get_vulnerability_info(all_version_info)
        
        # 安全建议
        recommendations = self.generate_security_recommendations(all_version_info, vulnerabilities)
        
        # 输出报告
        print(f"\n[+] 服务检测结果:")
        print(f"  - 管理界面: {detection_results['management_interface']}")
        if detection_results['amqp_port']:
            print(f"  - AMQP端口: {detection_results['amqp_port']}")
        if detection_results['other_ports']:
            print(f"  - 其他端口: {', '.join(map(str, detection_results['other_ports']))}")
        
        if all_version_info:
            print(f"\n[+] 版本信息:")
            for key, value in all_version_info.items():
                if key != 'error_page_content':  # 跳过长内容
                    print(f"  - {key}: {value}")
        
        if vulnerabilities:
            print(f"\n[!] 可能存在的漏洞:")
            for cve in vulnerabilities:
                print(f"  - {cve}")
        
        if recommendations:
            print(f"\n[*] 安全建议:")
            for rec in recommendations:
                print(f"  - {rec}")
        
        return {
            'detection_results': detection_results,
            'version_info': all_version_info,
            'fingerprint_info': fingerprint_info,
            'vulnerabilities': vulnerabilities,
            'recommendations': recommendations
        }

def main():
    parser = argparse.ArgumentParser(description='RabbitMQ 版本检测和指纹识别工具')
    parser.add_argument('target', help='目标URL (例如: http://target:15672)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--api-only', action='store_true', help='仅通过API检测版本')
    parser.add_argument('--fingerprint-only', action='store_true', help='仅进行指纹识别')
    
    args = parser.parse_args()
    
    detector = RabbitMQVersionDetector(args.target, args.timeout)
    
    if args.api_only:
        management_url = detector.detect_management_interface()
        if management_url:
            version_info = detector.get_version_from_api(management_url)
            if version_info:
                for key, value in version_info.items():
                    print(f"{key}: {value}")
            else:
                print("无法获取版本信息")
        else:
            print("未发现管理界面")
    elif args.fingerprint_only:
        management_url = detector.detect_management_interface()
        if management_url:
            fingerprint_info = detector.fingerprint_rabbitmq(management_url)
            if fingerprint_info:
                print(json.dumps(fingerprint_info, indent=2))
        else:
            print("未发现管理界面")
    else:
        report = detector.generate_report()
        
        # 输出到文件
        if args.output and report:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"RabbitMQ版本检测报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n可能的漏洞:\n")
                for cve in report['vulnerabilities']:
                    f.write(f"{cve}\n")
                
                f.write("\n安全建议:\n")
                for rec in report['recommendations']:
                    f.write(f"{rec}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
