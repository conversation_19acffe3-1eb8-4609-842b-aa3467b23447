# RabbitMQ 漏洞扫描工具集

这是一个专门针对RabbitMQ消息队列服务器的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。

## 功能特性

- 🔍 **漏洞检测**: 支持多个RabbitMQ CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、用户枚举、队列扫描
- 🔐 **安全测试**: 暴力破解、消息注入、权限绕过等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2021-32718 | RabbitMQ 管理界面用户添加漏洞 | High | < 3.8.17 |
| CVE-2023-46118 | RabbitMQ HTTP API请求体限制绕过漏洞 | Medium | 多个版本 |
| CVE-2021-22116 | RabbitMQ 拒绝服务漏洞 | Medium | < 3.8.16 |
| 暴力破解 | RabbitMQ 管理界面弱口令 | High | 配置相关 |
| 队列操作 | RabbitMQ 队列操作和消息注入 | Medium | 权限相关 |
| 版本识别 | RabbitMQ版本和指纹识别 | Info | 所有版本 |
| 信息收集 | RabbitMQ敏感信息泄露 | Medium | 配置相关 |

## 安装和使用

### 环境要求

- Python 3.6+
- pip (Python包管理器)

### 安装依赖

```bash
cd rabbitmq
pip install -r requirements.txt
```

### 基本使用

#### 1. 综合扫描 (推荐)

```bash
# 扫描所有漏洞
python3 rabbitmq_comprehensive_scan.py http://target:15672

# 扫描指定漏洞
python3 rabbitmq_comprehensive_scan.py http://target:15672 -v CVE-2021-32718 rabbitmq_bruteforce

# 保存扫描报告
python3 rabbitmq_comprehensive_scan.py http://target:15672 -o report.json
```

#### 2. 单个漏洞扫描

```bash
# CVE-2021-32718 管理界面用户添加漏洞
python3 CVE-2021-32718.py http://target:15672 --check-only
python3 CVE-2021-32718.py http://target:15672 -u admin -p admin123

# CVE-2023-46118 HTTP API请求体限制绕过漏洞
python3 CVE-2023-46118.py http://target:15672 --check-only
python3 CVE-2023-46118.py http://target:15672 -a large_body

# CVE-2021-22116 拒绝服务漏洞
python3 CVE-2021-22116.py http://target:15672 --check-only
python3 CVE-2021-22116.py http://target:15672 -a amqp_flood

# 暴力破解
python3 rabbitmq_bruteforce.py http://target:15672
python3 rabbitmq_bruteforce.py http://target:15672 -e --threads 20

# 队列操作和消息注入
python3 rabbitmq_queue_exploit.py http://target:15672 --check-only
python3 rabbitmq_queue_exploit.py http://target:15672 --inject
```

#### 3. 信息收集

```bash
# 版本识别
python3 rabbitmq_version_detect.py http://target:15672

# 信息收集
python3 rabbitmq_info_scan.py http://target:15672
python3 rabbitmq_info_scan.py http://target:15672 --ports-only  # 仅扫描端口
python3 rabbitmq_info_scan.py http://target:15672 --system-only  # 仅收集系统信息
```

## 脚本详细说明

### 核心漏洞利用脚本

1. **CVE-2021-32718.py** - RabbitMQ 管理界面用户添加漏洞
   - 检测和利用管理界面用户添加权限绕过
   - 支持创建管理员用户
   - 包含交互式利用模式

2. **CVE-2023-46118.py** - RabbitMQ HTTP API请求体限制绕过漏洞
   - 利用HTTP API请求体限制绕过进行DoS攻击
   - 支持大请求体攻击和并发攻击
   - 包含交互式攻击模式

3. **CVE-2021-22116.py** - RabbitMQ 拒绝服务漏洞
   - 检测和利用AMQP协议处理漏洞
   - 支持AMQP洪水、畸形帧、连接耗尽攻击
   - 包含交互式利用模式

4. **rabbitmq_bruteforce.py** - RabbitMQ 管理界面暴力破解
   - 检测RabbitMQ管理界面弱口令
   - 支持默认凭据和扩展字典暴力破解
   - 包含多线程暴力破解

5. **rabbitmq_queue_exploit.py** - RabbitMQ 队列操作和消息注入
   - 检测和利用队列操作权限
   - 支持恶意消息注入和敏感消息搜索
   - 包含交互式队列操作

### 辅助工具脚本

1. **rabbitmq_version_detect.py** - 版本识别
   - 多种方式识别RabbitMQ版本
   - 指纹识别和漏洞映射
   - 安全建议生成

2. **rabbitmq_info_scan.py** - 信息收集
   - 全面的RabbitMQ信息收集
   - 端口扫描、用户枚举、队列扫描
   - 详细的安全评估

3. **rabbitmq_comprehensive_scan.py** - 综合扫描
   - 集成所有漏洞检测功能
   - 自动化扫描和报告生成
   - 风险评估和安全建议

## 高级用法

### 交互式模式

大部分脚本都支持交互式模式，方便手动测试：

```bash
# 交互式用户添加利用
python3 CVE-2021-32718.py http://target:15672 -i

# 交互式DoS攻击
python3 CVE-2023-46118.py http://target:15672 -i

# 交互式暴力破解
python3 rabbitmq_bruteforce.py http://target:15672 -i

# 交互式队列操作
python3 rabbitmq_queue_exploit.py http://target:15672 -i
```

### 批量扫描

```bash
# 创建目标列表
echo "http://target1:15672" > targets.txt
echo "http://target2:15672" >> targets.txt

# 批量扫描
for target in $(cat targets.txt); do
    python3 rabbitmq_comprehensive_scan.py $target -o "report_$(echo $target | tr '/:' '_').json"
done
```

### 自定义配置

```bash
# 自定义用户创建
python3 CVE-2021-32718.py http://target:15672 -u myuser -p mypass123

# 自定义暴力破解线程数
python3 rabbitmq_bruteforce.py http://target:15672 -e --threads 50

# 自定义队列消息注入
python3 rabbitmq_queue_exploit.py http://target:15672 -q sensitive_queue -m "malicious_payload"

# 自定义DoS攻击类型
python3 CVE-2023-46118.py http://target:15672 -a concurrent
```

## 常见RabbitMQ端口

| 端口 | 服务 | 描述 |
|------|------|------|
| 5672 | AMQP | 默认AMQP协议端口 |
| 5671 | AMQP over TLS | AMQP over TLS/SSL |
| 15672 | HTTP Management | HTTP管理界面 |
| 15671 | HTTPS Management | HTTPS管理界面 |
| 25672 | Inter-node | 节点间通信 |
| 4369 | EPMD | Erlang端口映射守护进程 |
| 35672 | CLI tools | CLI工具通信 |

## 注意事项

1. **合法使用**: 仅用于授权的渗透测试和安全研究
2. **网络环境**: 某些功能需要目标网络可达
3. **权限要求**: 某些功能可能需要特定权限
4. **版本兼容**: 不同RabbitMQ版本可能有不同的漏洞
5. **配置依赖**: 大部分漏洞与RabbitMQ配置相关

## 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   python3 script.py http://target:15672 -t 30
   ```

2. **SSL证书错误**
   - 脚本已自动忽略SSL警告

3. **权限不足**
   ```bash
   # 某些功能可能需要管理员权限
   sudo python3 script.py http://target:15672
   ```

4. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --upgrade
   ```

5. **AMQP协议支持问题**
   ```bash
   # 如果需要完整的AMQP协议支持，可以安装pika库
   pip install pika
   ```

## CTF使用技巧

1. **信息收集优先**: 先使用版本识别和信息收集脚本了解目标
2. **弱口令检测**: RabbitMQ经常使用默认凭据guest:guest
3. **队列消息**: 检查队列中是否有敏感信息或flag
4. **用户权限**: 利用用户添加漏洞获取管理员权限
5. **消息注入**: 通过消息注入可能触发其他应用的漏洞
6. **DoS攻击**: 在某些CTF场景中可能需要DoS攻击

## 贡献

欢迎提交Issue和Pull Request来改进这个工具集。

## 免责声明

本工具仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规，作者不承担任何滥用责任。
