#!/usr/bin/env python3
"""
RabbitMQ 信息收集和扫描工具
收集RabbitMQ服务器的详细信息和配置
"""

import sys
import requests
import argparse
import json
import base64
import socket
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class RabbitMQInfoScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 15672
        
        # 常见的RabbitMQ端口
        self.rabbitmq_ports = {
            5672: 'AMQP',
            5671: 'AMQP over TLS',
            15672: 'HTTP Management',
            15671: 'HTTPS Management',
            25672: 'Inter-node communication',
            4369: 'Erlang Port Mapper Daemon',
            35672: 'CLI tools communication'
        }
        
        # 默认凭据
        self.default_credentials = [
            ('guest', 'guest'),
            ('admin', 'admin'),
            ('rabbitmq', 'rabbitmq'),
            ('user', 'user'),
            ('test', 'test'),
        ]
        
        # API端点
        self.api_endpoints = [
            '/api/overview',
            '/api/users',
            '/api/vhosts',
            '/api/exchanges',
            '/api/queues',
            '/api/bindings',
            '/api/connections',
            '/api/channels',
            '/api/consumers',
            '/api/nodes',
            '/api/definitions',
            '/api/parameters',
            '/api/policies',
            '/api/cluster-name',
            '/api/extensions',
            '/api/health/checks/alarms',
            '/api/health/checks/local-alarms',
            '/api/health/checks/certificate-expiration',
            '/api/health/checks/port-listener',
            '/api/health/checks/protocol-listener',
            '/api/health/checks/virtual-hosts',
        ]
    
    def scan_ports(self):
        """扫描RabbitMQ相关端口"""
        print(f"[*] 扫描RabbitMQ相关端口: {self.target_host}")
        
        open_ports = {}
        
        for port, service in self.rabbitmq_ports.items():
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(self.timeout)
                
                result = sock.connect_ex((self.target_host, port))
                
                if result == 0:
                    print(f"[+] 端口 {port} 开放 ({service})")
                    open_ports[port] = service
                
                sock.close()
                
            except Exception as e:
                continue
        
        return open_ports
    
    def detect_management_interface(self):
        """检测管理界面"""
        print(f"[*] 检测RabbitMQ管理界面...")
        
        # 常见端口和协议
        test_configs = [
            ('http', 15672),
            ('https', 15671),
            ('http', 8080),
            ('https', 8443),
        ]
        
        for protocol, port in test_configs:
            try:
                base_url = f"{protocol}://{self.target_host}:{port}"
                
                # 测试管理界面路径
                test_paths = ['/api/overview', '/#/', '/']
                
                for path in test_paths:
                    test_url = urljoin(base_url, path)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    if self.is_rabbitmq_management(response):
                        print(f"[+] 发现RabbitMQ管理界面: {base_url}")
                        return base_url
                        
            except Exception as e:
                continue
        
        return None
    
    def is_rabbitmq_management(self, response):
        """检查响应是否来自RabbitMQ管理界面"""
        if response.status_code not in [200, 401, 403]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'rabbitmq' in server_header:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        rabbitmq_indicators = [
            'rabbitmq',
            'rabbit mq',
            'management',
            'overview',
            'exchanges',
            'queues',
            'connections',
            'channels',
            'users',
            'virtual hosts',
            'policies',
        ]
        
        return any(indicator in content for indicator in rabbitmq_indicators)
    
    def get_valid_credentials(self, management_url):
        """获取有效凭据"""
        print(f"[*] 尝试获取有效凭据...")
        
        for username, password in self.default_credentials:
            print(f"[*] 尝试: {username}:{password}")
            
            if self.test_credentials(management_url, username, password):
                print(f"[+] 有效凭据: {username}:{password}")
                return (username, password)
        
        return None
    
    def test_credentials(self, management_url, username, password):
        """测试凭据是否有效"""
        try:
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            api_url = urljoin(management_url, '/api/overview')
            response = self.session.get(api_url, headers=headers, timeout=self.timeout)
            
            return response.status_code == 200
            
        except Exception as e:
            return False
    
    def collect_system_info(self, management_url, credentials):
        """收集系统信息"""
        print(f"[*] 收集系统信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取概览信息
            overview_url = urljoin(management_url, '/api/overview')
            response = self.session.get(overview_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                overview = response.json()
                
                system_info = {
                    'rabbitmq_version': overview.get('rabbitmq_version', 'Unknown'),
                    'erlang_version': overview.get('erlang_version', 'Unknown'),
                    'management_version': overview.get('management_version', 'Unknown'),
                    'node': overview.get('node', 'Unknown'),
                    'cluster_name': overview.get('cluster_name', 'Unknown'),
                    'message_stats': overview.get('message_stats', {}),
                    'queue_totals': overview.get('queue_totals', {}),
                    'object_totals': overview.get('object_totals', {}),
                    'statistics_db_event_queue': overview.get('statistics_db_event_queue', 0),
                    'contexts': overview.get('contexts', []),
                    'listeners': overview.get('listeners', [])
                }
                
                return system_info
            
        except Exception as e:
            print(f"[-] 收集系统信息失败: {e}")
        
        return None
    
    def collect_users_info(self, management_url, credentials):
        """收集用户信息"""
        print(f"[*] 收集用户信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取用户列表
            users_url = urljoin(management_url, '/api/users')
            response = self.session.get(users_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                users = response.json()
                
                users_info = []
                for user in users:
                    user_info = {
                        'name': user.get('name', 'Unknown'),
                        'tags': user.get('tags', 'None'),
                        'password_hash': user.get('password_hash', 'N/A'),
                        'hashing_algorithm': user.get('hashing_algorithm', 'N/A')
                    }
                    users_info.append(user_info)
                
                return users_info
            
        except Exception as e:
            print(f"[-] 收集用户信息失败: {e}")
        
        return []
    
    def collect_vhosts_info(self, management_url, credentials):
        """收集虚拟主机信息"""
        print(f"[*] 收集虚拟主机信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取虚拟主机列表
            vhosts_url = urljoin(management_url, '/api/vhosts')
            response = self.session.get(vhosts_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                vhosts = response.json()
                
                vhosts_info = []
                for vhost in vhosts:
                    vhost_info = {
                        'name': vhost.get('name', 'Unknown'),
                        'tracing': vhost.get('tracing', False),
                        'cluster_state': vhost.get('cluster_state', {}),
                        'message_stats': vhost.get('message_stats', {}),
                        'messages': vhost.get('messages', 0),
                        'messages_ready': vhost.get('messages_ready', 0),
                        'messages_unacknowledged': vhost.get('messages_unacknowledged', 0)
                    }
                    vhosts_info.append(vhost_info)
                
                return vhosts_info
            
        except Exception as e:
            print(f"[-] 收集虚拟主机信息失败: {e}")
        
        return []
    
    def collect_queues_info(self, management_url, credentials):
        """收集队列信息"""
        print(f"[*] 收集队列信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取队列列表
            queues_url = urljoin(management_url, '/api/queues')
            response = self.session.get(queues_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                queues = response.json()
                
                queues_info = []
                for queue in queues[:10]:  # 只显示前10个队列
                    queue_info = {
                        'name': queue.get('name', 'Unknown'),
                        'vhost': queue.get('vhost', 'Unknown'),
                        'durable': queue.get('durable', False),
                        'auto_delete': queue.get('auto_delete', False),
                        'exclusive': queue.get('exclusive', False),
                        'messages': queue.get('messages', 0),
                        'messages_ready': queue.get('messages_ready', 0),
                        'messages_unacknowledged': queue.get('messages_unacknowledged', 0),
                        'consumers': queue.get('consumers', 0),
                        'memory': queue.get('memory', 0),
                        'state': queue.get('state', 'Unknown')
                    }
                    queues_info.append(queue_info)
                
                return queues_info, len(queues)
            
        except Exception as e:
            print(f"[-] 收集队列信息失败: {e}")
        
        return [], 0
    
    def collect_connections_info(self, management_url, credentials):
        """收集连接信息"""
        print(f"[*] 收集连接信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取连接列表
            connections_url = urljoin(management_url, '/api/connections')
            response = self.session.get(connections_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                connections = response.json()
                
                connections_info = []
                for conn in connections[:10]:  # 只显示前10个连接
                    conn_info = {
                        'name': conn.get('name', 'Unknown'),
                        'user': conn.get('user', 'Unknown'),
                        'vhost': conn.get('vhost', 'Unknown'),
                        'host': conn.get('host', 'Unknown'),
                        'port': conn.get('port', 0),
                        'peer_host': conn.get('peer_host', 'Unknown'),
                        'peer_port': conn.get('peer_port', 0),
                        'protocol': conn.get('protocol', 'Unknown'),
                        'state': conn.get('state', 'Unknown'),
                        'channels': conn.get('channels', 0),
                        'connected_at': conn.get('connected_at', 'Unknown')
                    }
                    connections_info.append(conn_info)
                
                return connections_info, len(connections)
            
        except Exception as e:
            print(f"[-] 收集连接信息失败: {e}")
        
        return [], 0
    
    def scan_api_endpoints(self, management_url, credentials):
        """扫描API端点"""
        print(f"[*] 扫描API端点...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            accessible_endpoints = []
            
            for endpoint in self.api_endpoints:
                try:
                    api_url = urljoin(management_url, endpoint)
                    response = self.session.get(api_url, headers=headers, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        accessible_endpoints.append({
                            'endpoint': endpoint,
                            'status_code': response.status_code,
                            'content_length': len(response.text)
                        })
                        print(f"[+] 可访问端点: {endpoint}")
                    elif response.status_code in [401, 403]:
                        print(f"[-] 受限端点: {endpoint} ({response.status_code})")
                    
                except Exception as e:
                    continue
            
            return accessible_endpoints
            
        except Exception as e:
            print(f"[-] 扫描API端点失败: {e}")
        
        return []
    
    def generate_report(self):
        """生成完整的信息收集报告"""
        print("=" * 80)
        print("RabbitMQ 信息收集报告")
        print("=" * 80)
        
        # 端口扫描
        open_ports = self.scan_ports()
        
        # 检测管理界面
        management_url = self.detect_management_interface()
        if not management_url:
            print("[-] 未发现RabbitMQ管理界面")
            return None
        
        # 获取有效凭据
        credentials = self.get_valid_credentials(management_url)
        if not credentials:
            print("[-] 无法获取有效凭据")
            return None
        
        # 收集各种信息
        system_info = self.collect_system_info(management_url, credentials)
        users_info = self.collect_users_info(management_url, credentials)
        vhosts_info = self.collect_vhosts_info(management_url, credentials)
        queues_info, total_queues = self.collect_queues_info(management_url, credentials)
        connections_info, total_connections = self.collect_connections_info(management_url, credentials)
        api_endpoints = self.scan_api_endpoints(management_url, credentials)
        
        # 输出报告
        if open_ports:
            print(f"\n[+] 开放端口:")
            for port, service in open_ports.items():
                print(f"  - {port}: {service}")
        
        if system_info:
            print(f"\n[+] 系统信息:")
            print(f"  - RabbitMQ版本: {system_info['rabbitmq_version']}")
            print(f"  - Erlang版本: {system_info['erlang_version']}")
            print(f"  - 管理版本: {system_info['management_version']}")
            print(f"  - 节点名称: {system_info['node']}")
            print(f"  - 集群名称: {system_info['cluster_name']}")
        
        if users_info:
            print(f"\n[+] 用户信息:")
            for user in users_info:
                print(f"  - {user['name']} (标签: {user['tags']})")
        
        if vhosts_info:
            print(f"\n[+] 虚拟主机:")
            for vhost in vhosts_info:
                print(f"  - {vhost['name']} (消息: {vhost['messages']})")
        
        if queues_info:
            print(f"\n[+] 队列信息 (显示前10个，总共{total_queues}个):")
            for queue in queues_info:
                print(f"  - {queue['name']} (vhost: {queue['vhost']}, 消息: {queue['messages']})")
        
        if connections_info:
            print(f"\n[+] 连接信息 (显示前10个，总共{total_connections}个):")
            for conn in connections_info:
                print(f"  - {conn['user']}@{conn['peer_host']}:{conn['peer_port']} ({conn['protocol']})")
        
        if api_endpoints:
            print(f"\n[+] 可访问API端点:")
            for endpoint in api_endpoints:
                print(f"  - {endpoint['endpoint']}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        if credentials and credentials[0] == 'guest':
            print("  - 禁用或更改默认guest用户密码")
        if system_info and 'guest' in [user['name'] for user in users_info]:
            print("  - 删除或限制guest用户权限")
        if open_ports.get(5672):
            print("  - 限制AMQP端口(5672)的网络访问")
        if open_ports.get(15672):
            print("  - 限制管理界面端口(15672)的网络访问")
        
        return {
            'open_ports': open_ports,
            'management_url': management_url,
            'credentials': credentials,
            'system_info': system_info,
            'users_info': users_info,
            'vhosts_info': vhosts_info,
            'queues_info': queues_info,
            'connections_info': connections_info,
            'api_endpoints': api_endpoints
        }

def main():
    parser = argparse.ArgumentParser(description='RabbitMQ 信息收集和扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target:15672)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--ports-only', action='store_true', help='仅扫描端口')
    parser.add_argument('--system-only', action='store_true', help='仅收集系统信息')
    
    args = parser.parse_args()
    
    scanner = RabbitMQInfoScanner(args.target, args.timeout)
    
    if args.ports_only:
        open_ports = scanner.scan_ports()
        for port, service in open_ports.items():
            print(f"{port}: {service}")
    elif args.system_only:
        management_url = scanner.detect_management_interface()
        if management_url:
            credentials = scanner.get_valid_credentials(management_url)
            if credentials:
                system_info = scanner.collect_system_info(management_url, credentials)
                if system_info:
                    for key, value in system_info.items():
                        print(f"{key}: {value}")
    else:
        report = scanner.generate_report()
        
        # 输出到文件
        if args.output and report:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"RabbitMQ信息收集报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("开放端口:\n")
                for port, service in report['open_ports'].items():
                    f.write(f"{port}: {service}\n")
                
                f.write("\n系统信息:\n")
                if report['system_info']:
                    for key, value in report['system_info'].items():
                        f.write(f"{key}: {value}\n")
                
                f.write("\n用户信息:\n")
                for user in report['users_info']:
                    f.write(f"{user['name']}: {user['tags']}\n")
                
                f.write("\n虚拟主机:\n")
                for vhost in report['vhosts_info']:
                    f.write(f"{vhost['name']}: {vhost['messages']} messages\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
