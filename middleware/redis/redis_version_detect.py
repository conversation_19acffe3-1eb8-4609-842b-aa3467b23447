#!/usr/bin/env python3
"""
Redis 版本检测和指纹识别工具
通过多种方式识别Redis版本和相关漏洞
"""

import sys
import socket
import argparse
import json
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379  # Redis默认端口
        
        # 已知版本漏洞映射
        self.version_vulnerabilities = {
            '7.0': [],  # 最新版本，暂无已知漏洞
            '6.2': ['CVE-2022-0543'],  # Debian特定的Lua沙箱逃逸
            '6.0': ['CVE-2022-0543', 'CVE-2021-32675', 'CVE-2021-32672'],
            '5.0': ['CVE-2022-0543', 'CVE-2021-32675', 'CVE-2021-32672', 'CVE-2019-10192'],
            '4.0': ['CVE-2022-0543', 'CVE-2021-32675', 'CVE-2021-32672', 'CVE-2019-10192', 'CVE-2018-11218'],
            '3.2': ['CVE-2022-0543', 'CVE-2021-32675', 'CVE-2021-32672', 'CVE-2019-10192', 'CVE-2018-11218'],
            '3.0': ['CVE-2022-0543', 'CVE-2021-32675', 'CVE-2021-32672', 'CVE-2019-10192', 'CVE-2018-11218'],
            '2.8': ['CVE-2022-0543', 'CVE-2021-32675', 'CVE-2021-32672', 'CVE-2019-10192', 'CVE-2018-11218'],
        }
        
        # CVE详细信息
        self.cve_details = {
            'CVE-2022-0543': {
                'description': 'Debian特定的Redis Lua沙箱逃逸RCE漏洞',
                'severity': 'Critical',
                'affected': 'Debian/Ubuntu Redis包'
            },
            'CVE-2021-32675': {
                'description': 'Redis Lua脚本中的拒绝服务漏洞',
                'severity': 'Medium',
                'affected': '6.2.x < 6.2.6, 6.0.x < 6.0.16, 5.0.x < 5.0.14'
            },
            'CVE-2021-32672': {
                'description': 'Redis Lua脚本中的随机堆读取漏洞',
                'severity': 'Medium',
                'affected': '6.2.x < 6.2.6, 6.0.x < 6.0.16, 5.0.x < 5.0.14'
            },
            'CVE-2019-10192': {
                'description': 'Redis HyperLogLog中的堆缓冲区溢出',
                'severity': 'High',
                'affected': '< 5.0.5'
            },
            'CVE-2018-11218': {
                'description': 'Redis内存损坏漏洞',
                'severity': 'High',
                'affected': '< 4.0.10, < 3.2.12'
            }
        }
        
        # Redis指纹特征
        self.fingerprint_indicators = [
            'redis_version',
            'redis_git_sha1',
            'redis_git_dirty',
            'redis_build_id',
            'redis_mode',
            'os',
            'arch_bits',
            'multiplexing_api',
            'gcc_version',
        ]
    
    def connect_redis(self):
        """连接Redis服务器"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            # 构造Redis协议命令
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            
            # 接收响应
            response = sock.recv(8192).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def detect_redis_service(self):
        """检测Redis服务"""
        print(f"[*] 检测Redis服务: {self.target_host}:{self.target_port}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        # 测试PING命令
        response = self.send_redis_command(sock, "PING")
        
        if response and ("PONG" in response or "NOAUTH" in response):
            print(f"[+] 发现Redis服务")
            sock.close()
            return True
        else:
            print(f"[-] 不是Redis服务或服务异常")
            sock.close()
            return False
    
    def get_version_from_info(self, password=None):
        """通过INFO命令获取版本信息"""
        print(f"[*] 通过INFO命令获取版本信息...")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return None
        
        # 如果提供了密码，尝试认证
        if password:
            auth_response = self.send_redis_command(sock, ["AUTH", password])
            if not auth_response or "+OK" not in auth_response:
                print(f"[-] 密码认证失败")
                sock.close()
                return None
        
        # 获取INFO信息
        response = self.send_redis_command(sock, "INFO")
        
        if response and "redis_version" in response:
            version_info = self.parse_info_response(response)
            print(f"[+] 通过INFO命令获取版本信息成功")
            sock.close()
            return version_info
        elif response and "NOAUTH" in response:
            print(f"[-] 需要密码认证")
            sock.close()
            return None
        else:
            print(f"[-] 无法获取版本信息")
            sock.close()
            return None
    
    def parse_info_response(self, response):
        """解析INFO命令响应"""
        try:
            version_info = {}
            
            # 跳过Redis协议头
            lines = response.split('\r\n')
            content_started = False
            
            for line in lines:
                if line.startswith('$') and not content_started:
                    continue
                elif not content_started and ':' in line:
                    content_started = True
                
                if content_started and ':' in line and not line.startswith('#'):
                    key, value = line.split(':', 1)
                    if key in self.fingerprint_indicators:
                        version_info[key] = value
            
            return version_info
            
        except Exception as e:
            return None
    
    def get_version_from_banner(self):
        """通过连接banner获取版本信息"""
        print(f"[*] 通过连接banner获取版本信息...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            
            # 某些Redis配置可能会发送banner
            banner = sock.recv(1024).decode('utf-8', errors='ignore')
            sock.close()
            
            if banner and 'redis' in banner.lower():
                print(f"[+] 从banner获取到信息: {banner}")
                return {'banner': banner}
            else:
                print(f"[-] banner中未发现版本信息")
                return None
                
        except Exception as e:
            print(f"[-] 获取banner失败: {e}")
            return None
    
    def fingerprint_redis(self, password=None):
        """Redis指纹识别"""
        print(f"[*] 进行Redis指纹识别...")
        
        sock = self.connect_redis()
        if not sock:
            return None
        
        # 如果提供了密码，尝试认证
        if password:
            auth_response = self.send_redis_command(sock, ["AUTH", password])
            if not auth_response or "+OK" not in auth_response:
                sock.close()
                return None
        
        fingerprint_info = {}
        
        try:
            # 测试不同的命令来识别特性
            commands_to_test = [
                'INFO server',
                'INFO memory',
                'INFO replication',
                'CONFIG GET *',
                'COMMAND COUNT',
                'MODULE LIST',
                'MEMORY USAGE',
                'CLIENT LIST',
            ]
            
            command_results = {}
            for cmd in commands_to_test:
                response = self.send_redis_command(sock, cmd)
                if response:
                    if response.startswith('+') or response.startswith('$') or response.startswith('*'):
                        command_results[cmd] = 'Supported'
                    elif response.startswith('-ERR unknown command'):
                        command_results[cmd] = 'Unknown command'
                    elif response.startswith('-NOAUTH'):
                        command_results[cmd] = 'Auth required'
                    else:
                        command_results[cmd] = 'Other response'
                else:
                    command_results[cmd] = 'No response'
            
            fingerprint_info['command_support'] = command_results
            
            # 测试Lua脚本支持
            lua_test = 'return "test"'
            lua_response = self.send_redis_command(sock, ['EVAL', lua_test, '0'])
            if lua_response and 'test' in lua_response:
                fingerprint_info['lua_support'] = True
            else:
                fingerprint_info['lua_support'] = False
            
            # 测试模块支持
            module_response = self.send_redis_command(sock, 'MODULE LIST')
            if module_response and not module_response.startswith('-ERR'):
                fingerprint_info['module_support'] = True
            else:
                fingerprint_info['module_support'] = False
            
            sock.close()
            print(f"[+] Redis指纹识别完成")
            return fingerprint_info
            
        except Exception as e:
            sock.close()
            print(f"[-] 指纹识别失败: {e}")
            return None
    
    def get_vulnerability_info(self, version_info):
        """根据版本信息获取相关漏洞"""
        vulnerabilities = []
        
        if not version_info:
            return vulnerabilities
        
        # 从版本信息中提取版本号
        redis_version = version_info.get('redis_version', '')
        
        if redis_version:
            # 提取主版本号
            import re
            version_match = re.search(r'(\d+\.\d+)', redis_version)
            if version_match:
                major_version = version_match.group(1)
                
                # 检查已知漏洞
                for vuln_version, vulns in self.version_vulnerabilities.items():
                    if major_version == vuln_version:
                        vulnerabilities.extend(vulns)
                        break
                
                # 检查是否是更老的版本
                try:
                    version_float = float(major_version)
                    for vuln_version, vulns in self.version_vulnerabilities.items():
                        vuln_float = float(vuln_version)
                        if version_float <= vuln_float:
                            vulnerabilities.extend(vulns)
                except:
                    pass
        
        return list(set(vulnerabilities))  # 去重
    
    def check_debian_lua_vulnerability(self, password=None):
        """检查Debian特定的Lua沙箱逃逸漏洞"""
        print(f"[*] 检查CVE-2022-0543 Debian Lua沙箱逃逸漏洞...")
        
        sock = self.connect_redis()
        if not sock:
            return False
        
        # 如果提供了密码，尝试认证
        if password:
            auth_response = self.send_redis_command(sock, ["AUTH", password])
            if not auth_response or "+OK" not in auth_response:
                sock.close()
                return False
        
        try:
            # 测试Lua沙箱逃逸
            test_payload = 'return package.loadlib("/usr/lib/x86_64-linux-gnu/liblua5.1.so.0", "luaopen_package")()'
            response = self.send_redis_command(sock, ['EVAL', test_payload, '0'])
            
            if response and not response.startswith('-ERR'):
                print(f"[+] 可能存在CVE-2022-0543漏洞!")
                sock.close()
                return True
            else:
                print(f"[-] 未发现CVE-2022-0543漏洞")
                sock.close()
                return False
                
        except Exception as e:
            sock.close()
            return False
    
    def generate_security_recommendations(self, version_info, vulnerabilities):
        """生成安全建议"""
        recommendations = []
        
        if vulnerabilities:
            recommendations.append(f"修复已知漏洞: {', '.join(vulnerabilities)}")
        
        if version_info:
            redis_version = version_info.get('redis_version', '')
            if redis_version:
                try:
                    version_float = float(redis_version.split('.')[0] + '.' + redis_version.split('.')[1])
                    if version_float < 7.0:
                        recommendations.append("考虑升级到Redis 7.0最新版本")
                except:
                    recommendations.append("考虑升级到最新版本")
            
            recommendations.append("启用密码认证 (requirepass)")
            recommendations.append("绑定到特定接口而非0.0.0.0")
            recommendations.append("启用保护模式 (protected-mode yes)")
            recommendations.append("禁用或重命名危险命令")
            recommendations.append("使用TLS加密连接")
        
        return recommendations
    
    def generate_report(self, password=None):
        """生成完整的版本检测报告"""
        print("=" * 80)
        print("Redis 版本检测报告")
        print("=" * 80)
        
        # 服务检测
        if not self.detect_redis_service():
            print("[-] 未发现Redis服务")
            return None
        
        # 版本信息收集
        version_info = self.get_version_from_info(password)
        banner_info = self.get_version_from_banner()
        
        # 合并版本信息
        all_version_info = {}
        if version_info:
            all_version_info.update(version_info)
        if banner_info:
            all_version_info.update(banner_info)
        
        # 指纹识别
        fingerprint_info = self.fingerprint_redis(password)
        
        # 漏洞信息
        vulnerabilities = self.get_vulnerability_info(all_version_info)
        
        # 检查特定漏洞
        debian_lua_vuln = self.check_debian_lua_vulnerability(password)
        if debian_lua_vuln and 'CVE-2022-0543' not in vulnerabilities:
            vulnerabilities.append('CVE-2022-0543')
        
        # 安全建议
        recommendations = self.generate_security_recommendations(all_version_info, vulnerabilities)
        
        # 输出报告
        if all_version_info:
            print(f"\n[+] 版本信息:")
            for key, value in all_version_info.items():
                print(f"  - {key}: {value}")
        
        if fingerprint_info:
            print(f"\n[+] 指纹信息:")
            for key, value in fingerprint_info.items():
                if key == 'command_support':
                    print(f"  - 命令支持:")
                    for cmd, status in value.items():
                        if status == 'Supported':
                            print(f"    * {cmd}: {status}")
                else:
                    print(f"  - {key}: {value}")
        
        if vulnerabilities:
            print(f"\n[!] 可能存在的漏洞:")
            for cve in vulnerabilities:
                if cve in self.cve_details:
                    details = self.cve_details[cve]
                    print(f"  - {cve}: {details['description']} ({details['severity']})")
                else:
                    print(f"  - {cve}")
        
        if recommendations:
            print(f"\n[*] 安全建议:")
            for rec in recommendations:
                print(f"  - {rec}")
        
        return {
            'version_info': all_version_info,
            'fingerprint_info': fingerprint_info,
            'vulnerabilities': vulnerabilities,
            'recommendations': recommendations
        }

def main():
    parser = argparse.ArgumentParser(description='Redis 版本检测和指纹识别工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('-p', '--password', help='Redis密码')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--info-only', action='store_true', help='仅获取INFO信息')
    parser.add_argument('--fingerprint-only', action='store_true', help='仅进行指纹识别')
    parser.add_argument('--check-cve-2022-0543', action='store_true', help='仅检查CVE-2022-0543漏洞')
    
    args = parser.parse_args()
    
    detector = RedisVersionDetector(args.target, args.timeout)
    
    if args.info_only:
        version_info = detector.get_version_from_info(args.password)
        if version_info:
            for key, value in version_info.items():
                print(f"{key}: {value}")
        else:
            print("无法获取版本信息")
    elif args.fingerprint_only:
        fingerprint_info = detector.fingerprint_redis(args.password)
        if fingerprint_info:
            print(json.dumps(fingerprint_info, indent=2))
        else:
            print("指纹识别失败")
    elif args.check_cve_2022_0543:
        vulnerable = detector.check_debian_lua_vulnerability(args.password)
        if vulnerable:
            print("[+] 存在CVE-2022-0543漏洞")
        else:
            print("[-] 不存在CVE-2022-0543漏洞")
    else:
        report = detector.generate_report(args.password)
        
        # 输出到文件
        if args.output and report:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Redis版本检测报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n可能的漏洞:\n")
                for cve in report['vulnerabilities']:
                    f.write(f"{cve}\n")
                
                f.write("\n安全建议:\n")
                for rec in report['recommendations']:
                    f.write(f"{rec}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
