#!/usr/bin/env python3
"""
Redis 信息收集和扫描工具
收集Redis服务器的详细信息和配置
"""

import sys
import socket
import argparse
import json
import time
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisInfoScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379  # Redis默认端口
        
        # 常见的Redis端口
        self.redis_ports = [6379, 6380, 6381, 6382, 6383, 7000, 7001, 7002, 7003, 7004, 7005, 7006]
        
        # Redis命令分类
        self.redis_commands = {
            'info': ['INFO', 'INFO server', 'INFO memory', 'INFO replication', 'INFO persistence', 'INFO stats', 'INFO clients'],
            'config': ['CONFIG GET *', 'CONFIG GET save', 'CONFIG GET dir', 'CONFIG GET dbfilename'],
            'data': ['KEYS *', 'DBSIZE', 'LASTSAVE', 'TIME'],
            'client': ['CLIENT LIST', 'CLIENT INFO'],
            'cluster': ['CLUSTER INFO', 'CLUSTER NODES'],
            'module': ['MODULE LIST'],
            'memory': ['MEMORY USAGE', 'MEMORY STATS'],
            'slowlog': ['SLOWLOG GET', 'SLOWLOG LEN'],
        }
    
    def connect_redis(self, host=None, port=None):
        """连接Redis服务器"""
        try:
            target_host = host or self.target_host
            target_port = port or self.target_port
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((target_host, target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            # 构造Redis协议命令
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            
            # 接收响应
            response = sock.recv(8192).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def scan_redis_ports(self):
        """扫描Redis相关端口"""
        print(f"[*] 扫描Redis相关端口: {self.target_host}")
        
        open_ports = []
        
        for port in self.redis_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(self.timeout)
                
                result = sock.connect_ex((self.target_host, port))
                
                if result == 0:
                    # 尝试发送PING命令确认是Redis服务
                    try:
                        ping_response = self.send_redis_command(sock, "PING")
                        if ping_response and ("PONG" in ping_response or "NOAUTH" in ping_response):
                            print(f"[+] 端口 {port} 开放 (Redis服务)")
                            open_ports.append(port)
                        else:
                            print(f"[+] 端口 {port} 开放 (未知服务)")
                    except:
                        print(f"[+] 端口 {port} 开放 (无法确认服务类型)")
                
                sock.close()
                
            except Exception as e:
                continue
        
        return open_ports
    
    def check_redis_auth(self, sock):
        """检查Redis认证状态"""
        try:
            # 发送PING命令
            response = self.send_redis_command(sock, "PING")
            
            if response and "PONG" in response:
                return {'auth_required': False, 'auth_status': 'No authentication required'}
            elif response and "NOAUTH" in response:
                return {'auth_required': True, 'auth_status': 'Authentication required'}
            else:
                return {'auth_required': None, 'auth_status': 'Unknown'}
                
        except Exception as e:
            return {'auth_required': None, 'auth_status': f'Error: {e}'}
    
    def collect_basic_info(self, sock):
        """收集基本信息"""
        print(f"[*] 收集基本信息...")
        
        basic_info = {}
        
        try:
            # 获取INFO信息
            for info_cmd in self.redis_commands['info']:
                response = self.send_redis_command(sock, info_cmd)
                if response and not response.startswith('-'):
                    section = info_cmd.replace('INFO ', '').replace('INFO', 'general')
                    basic_info[f'info_{section}'] = self.parse_info_response(response)
            
            return basic_info
            
        except Exception as e:
            print(f"[-] 收集基本信息失败: {e}")
            return {}
    
    def parse_info_response(self, response):
        """解析INFO命令响应"""
        try:
            info_data = {}
            
            # 跳过Redis协议头
            lines = response.split('\r\n')
            content_started = False
            
            for line in lines:
                if line.startswith('$') and not content_started:
                    continue
                elif not content_started and ':' in line:
                    content_started = True
                
                if content_started and ':' in line and not line.startswith('#'):
                    key, value = line.split(':', 1)
                    info_data[key] = value
            
            return info_data
            
        except Exception as e:
            return {'raw_response': response[:500]}
    
    def collect_config_info(self, sock):
        """收集配置信息"""
        print(f"[*] 收集配置信息...")
        
        config_info = {}
        
        try:
            for config_cmd in self.redis_commands['config']:
                response = self.send_redis_command(sock, config_cmd)
                if response and not response.startswith('-'):
                    cmd_key = config_cmd.replace('CONFIG GET ', '').replace('*', 'all')
                    config_info[f'config_{cmd_key}'] = self.parse_config_response(response)
            
            return config_info
            
        except Exception as e:
            print(f"[-] 收集配置信息失败: {e}")
            return {}
    
    def parse_config_response(self, response):
        """解析CONFIG命令响应"""
        try:
            config_data = {}
            
            lines = response.split('\r\n')
            i = 0
            
            while i < len(lines):
                if lines[i].startswith('*'):
                    # 数组开始
                    array_size = int(lines[i][1:])
                    i += 1
                    
                    for j in range(0, array_size, 2):
                        if i + 1 < len(lines) and lines[i].startswith('$'):
                            key_len = int(lines[i][1:])
                            i += 1
                            if i < len(lines):
                                key = lines[i]
                                i += 1
                                
                                if i < len(lines) and lines[i].startswith('$'):
                                    value_len = int(lines[i][1:])
                                    i += 1
                                    if i < len(lines):
                                        value = lines[i]
                                        config_data[key] = value
                                        i += 1
                    break
                else:
                    i += 1
            
            return config_data
            
        except Exception as e:
            return {'raw_response': response[:500]}
    
    def collect_data_info(self, sock):
        """收集数据信息"""
        print(f"[*] 收集数据信息...")
        
        data_info = {}
        
        try:
            for data_cmd in self.redis_commands['data']:
                response = self.send_redis_command(sock, data_cmd)
                if response and not response.startswith('-'):
                    cmd_key = data_cmd.replace(' ', '_').lower()
                    
                    if data_cmd == 'KEYS *':
                        data_info[cmd_key] = self.parse_keys_response(response)
                    else:
                        data_info[cmd_key] = response.strip()
            
            return data_info
            
        except Exception as e:
            print(f"[-] 收集数据信息失败: {e}")
            return {}
    
    def parse_keys_response(self, response):
        """解析KEYS命令响应"""
        try:
            keys = []
            
            lines = response.split('\r\n')
            i = 0
            
            while i < len(lines):
                if lines[i].startswith('*'):
                    array_size = int(lines[i][1:])
                    i += 1
                    
                    for j in range(array_size):
                        if i < len(lines) and lines[i].startswith('$'):
                            key_len = int(lines[i][1:])
                            i += 1
                            if i < len(lines):
                                key = lines[i]
                                keys.append(key)
                                i += 1
                    break
                else:
                    i += 1
            
            return keys[:50]  # 限制显示前50个键
            
        except Exception as e:
            return []
    
    def collect_client_info(self, sock):
        """收集客户端信息"""
        print(f"[*] 收集客户端信息...")
        
        client_info = {}
        
        try:
            for client_cmd in self.redis_commands['client']:
                response = self.send_redis_command(sock, client_cmd)
                if response and not response.startswith('-'):
                    cmd_key = client_cmd.replace(' ', '_').lower()
                    client_info[cmd_key] = response.strip()
            
            return client_info
            
        except Exception as e:
            print(f"[-] 收集客户端信息失败: {e}")
            return {}
    
    def collect_cluster_info(self, sock):
        """收集集群信息"""
        print(f"[*] 收集集群信息...")
        
        cluster_info = {}
        
        try:
            for cluster_cmd in self.redis_commands['cluster']:
                response = self.send_redis_command(sock, cluster_cmd)
                if response and not response.startswith('-'):
                    cmd_key = cluster_cmd.replace(' ', '_').lower()
                    cluster_info[cmd_key] = response.strip()
            
            return cluster_info
            
        except Exception as e:
            print(f"[-] 收集集群信息失败: {e}")
            return {}
    
    def collect_security_info(self, sock):
        """收集安全相关信息"""
        print(f"[*] 收集安全信息...")
        
        security_info = {}
        
        try:
            # 检查认证状态
            auth_info = self.check_redis_auth(sock)
            security_info['authentication'] = auth_info
            
            # 检查危险命令
            dangerous_commands = ['FLUSHALL', 'FLUSHDB', 'CONFIG', 'EVAL', 'DEBUG', 'SHUTDOWN']
            command_status = {}
            
            for cmd in dangerous_commands:
                try:
                    # 发送COMMAND INFO命令检查命令是否可用
                    response = self.send_redis_command(sock, ['COMMAND', 'INFO', cmd])
                    if response and not response.startswith('-'):
                        command_status[cmd] = 'Available'
                    else:
                        command_status[cmd] = 'Disabled or Unknown'
                except:
                    command_status[cmd] = 'Unknown'
            
            security_info['dangerous_commands'] = command_status
            
            return security_info
            
        except Exception as e:
            print(f"[-] 收集安全信息失败: {e}")
            return {}
    
    def analyze_security_risks(self, all_info):
        """分析安全风险"""
        risks = []
        
        try:
            # 检查认证
            auth_info = all_info.get('security_info', {}).get('authentication', {})
            if not auth_info.get('auth_required', True):
                risks.append({
                    'level': 'High',
                    'issue': '未启用认证',
                    'description': 'Redis服务未启用密码认证，任何人都可以访问'
                })
            
            # 检查配置
            config_info = all_info.get('config_info', {})
            
            # 检查bind配置
            bind_config = config_info.get('config_all', {}).get('bind', '')
            if '0.0.0.0' in bind_config or not bind_config:
                risks.append({
                    'level': 'Medium',
                    'issue': '绑定到所有接口',
                    'description': 'Redis绑定到0.0.0.0，可能暴露给外部网络'
                })
            
            # 检查保护模式
            protected_mode = config_info.get('config_all', {}).get('protected-mode', 'yes')
            if protected_mode.lower() == 'no':
                risks.append({
                    'level': 'High',
                    'issue': '保护模式已禁用',
                    'description': '保护模式被禁用，增加了安全风险'
                })
            
            # 检查数据持久化
            save_config = config_info.get('config_save', {})
            if save_config:
                risks.append({
                    'level': 'Low',
                    'issue': '数据持久化已启用',
                    'description': '可能通过写入文件进行攻击'
                })
            
            # 检查危险命令
            dangerous_commands = all_info.get('security_info', {}).get('dangerous_commands', {})
            available_dangerous = [cmd for cmd, status in dangerous_commands.items() if status == 'Available']
            
            if available_dangerous:
                risks.append({
                    'level': 'Medium',
                    'issue': '危险命令可用',
                    'description': f'以下危险命令可用: {", ".join(available_dangerous)}'
                })
            
            return risks
            
        except Exception as e:
            return [{'level': 'Unknown', 'issue': '风险分析失败', 'description': str(e)}]
    
    def generate_report(self, password=None):
        """生成完整的信息收集报告"""
        print("=" * 80)
        print("Redis 信息收集报告")
        print("=" * 80)
        
        # 端口扫描
        open_ports = self.scan_redis_ports()
        
        if not open_ports:
            print("[-] 未发现Redis服务")
            return None
        
        # 连接Redis服务
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return None
        
        # 如果提供了密码，尝试认证
        if password:
            auth_response = self.send_redis_command(sock, ["AUTH", password])
            if not auth_response or "+OK" not in auth_response:
                print(f"[-] 密码认证失败")
                sock.close()
                return None
            print(f"[+] 密码认证成功")
        
        # 收集各种信息
        all_info = {}
        all_info['basic_info'] = self.collect_basic_info(sock)
        all_info['config_info'] = self.collect_config_info(sock)
        all_info['data_info'] = self.collect_data_info(sock)
        all_info['client_info'] = self.collect_client_info(sock)
        all_info['cluster_info'] = self.collect_cluster_info(sock)
        all_info['security_info'] = self.collect_security_info(sock)
        
        sock.close()
        
        # 分析安全风险
        security_risks = self.analyze_security_risks(all_info)
        
        # 输出报告
        print(f"\n[+] 开放端口: {', '.join(map(str, open_ports))}")
        
        # 基本信息
        basic_info = all_info.get('basic_info', {})
        if basic_info:
            print(f"\n[+] 基本信息:")
            for category, info in basic_info.items():
                if isinstance(info, dict):
                    print(f"  {category}:")
                    for key, value in list(info.items())[:10]:  # 限制显示数量
                        print(f"    {key}: {value}")
        
        # 配置信息
        config_info = all_info.get('config_info', {})
        if config_info:
            print(f"\n[+] 重要配置:")
            config_all = config_info.get('config_all', {})
            important_configs = ['bind', 'port', 'protected-mode', 'requirepass', 'dir', 'dbfilename']
            for config in important_configs:
                if config in config_all:
                    print(f"  {config}: {config_all[config]}")
        
        # 数据信息
        data_info = all_info.get('data_info', {})
        if data_info:
            print(f"\n[+] 数据信息:")
            for key, value in data_info.items():
                if key == 'keys_*' and isinstance(value, list):
                    print(f"  键数量: {len(value)}")
                    if value:
                        print(f"  示例键: {', '.join(value[:5])}")
                else:
                    print(f"  {key}: {value}")
        
        # 安全风险
        if security_risks:
            print(f"\n[!] 安全风险:")
            for risk in security_risks:
                print(f"  [{risk['level']}] {risk['issue']}: {risk['description']}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        print("  - 启用密码认证 (requirepass)")
        print("  - 绑定到特定接口而非0.0.0.0")
        print("  - 启用保护模式 (protected-mode yes)")
        print("  - 禁用或重命名危险命令")
        print("  - 使用防火墙限制访问")
        
        return all_info

def main():
    parser = argparse.ArgumentParser(description='Redis 信息收集和扫描工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('-p', '--password', help='Redis密码')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--ports-only', action='store_true', help='仅扫描端口')
    parser.add_argument('--basic-only', action='store_true', help='仅收集基本信息')
    
    args = parser.parse_args()
    
    scanner = RedisInfoScanner(args.target, args.timeout)
    
    if args.ports_only:
        open_ports = scanner.scan_redis_ports()
        for port in open_ports:
            print(f"{port}")
    elif args.basic_only:
        sock = scanner.connect_redis()
        if sock:
            if args.password:
                auth_response = scanner.send_redis_command(sock, ["AUTH", args.password])
            basic_info = scanner.collect_basic_info(sock)
            sock.close()
            
            for category, info in basic_info.items():
                if isinstance(info, dict):
                    for key, value in info.items():
                        print(f"{key}: {value}")
    else:
        report = scanner.generate_report(args.password)
        
        # 输出到文件
        if args.output and report:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Redis信息收集报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("基本信息:\n")
                basic_info = report.get('basic_info', {})
                for category, info in basic_info.items():
                    if isinstance(info, dict):
                        f.write(f"{category}:\n")
                        for key, value in info.items():
                            f.write(f"  {key}: {value}\n")
                
                f.write("\n配置信息:\n")
                config_info = report.get('config_info', {})
                for category, info in config_info.items():
                    if isinstance(info, dict):
                        f.write(f"{category}:\n")
                        for key, value in info.items():
                            f.write(f"  {key}: {value}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
