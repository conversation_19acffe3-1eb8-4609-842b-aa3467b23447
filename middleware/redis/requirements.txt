# Redis 漏洞扫描工具依赖包
# 安装命令: pip install -r requirements.txt

# 命令行参数解析 (Python内置，但列出以确保兼容性)
argparse

# 系统相关 (Python内置)
sys
os
subprocess
threading
time
socket

# 编码处理 (Python内置)
base64

# 日期时间处理 (Python内置)
datetime

# JSON处理 (Python内置)
json

# 警告处理 (Python内置)
warnings

# URL解析 (Python内置)
urllib.parse

# 正则表达式 (Python内置)
re

# 可选依赖 (用于增强功能)
# 如果需要更好的Redis客户端支持
# redis>=4.0.0

# 如果需要更好的异步支持
# aioredis>=2.0.0

# 如果需要更好的SSL/TLS支持
# cryptography>=3.4.0

# 如果需要更好的并发控制
# concurrent.futures (Python 3.2+内置)

# 注意事项:
# 1. 大部分依赖都是Python标准库，无需额外安装
# 2. 所有脚本都使用原生socket连接，无需额外Redis客户端库
# 3. 建议Python版本: 3.6+
# 4. 某些功能可能需要特定的网络权限
# 5. Lua沙箱逃逸功能需要目标系统存在特定的liblua库文件
# 6. SSRF功能需要目标Redis支持Lua脚本和socket库
