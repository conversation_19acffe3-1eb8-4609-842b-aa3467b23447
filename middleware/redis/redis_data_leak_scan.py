#!/usr/bin/env python3
"""
Redis 数据泄露扫描工具
扫描Redis中存储的敏感数据，专门用于CTF获取flag和敏感信息
"""

import sys
import socket
import argparse
import json
import re
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisDataLeakScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379
        
        # 敏感数据模式
        self.sensitive_patterns = {
            'flag': [
                r'flag\{[^}]+\}',
                r'FLAG\{[^}]+\}',
                r'ctf\{[^}]+\}',
                r'CTF\{[^}]+\}',
                r'key\{[^}]+\}',
                r'KEY\{[^}]+\}',
                r'flag[:\s]*[a-zA-Z0-9_\-]+',
                r'FLAG[:\s]*[a-zA-Z0-9_\-]+',
            ],
            'password': [
                r'password[:\s]*[^\s]+',
                r'passwd[:\s]*[^\s]+',
                r'pwd[:\s]*[^\s]+',
                r'pass[:\s]*[^\s]+',
            ],
            'token': [
                r'token[:\s]*[a-zA-Z0-9_\-\.]+',
                r'jwt[:\s]*[a-zA-Z0-9_\-\.]+',
                r'bearer[:\s]*[a-zA-Z0-9_\-\.]+',
                r'api[_\-]?key[:\s]*[a-zA-Z0-9_\-]+',
            ],
            'secret': [
                r'secret[:\s]*[a-zA-Z0-9_\-]+',
                r'private[_\-]?key[:\s]*[^\s]+',
                r'access[_\-]?key[:\s]*[a-zA-Z0-9_\-]+',
            ],
            'email': [
                r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            ],
            'ip': [
                r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b',
            ],
            'url': [
                r'https?://[^\s]+',
                r'ftp://[^\s]+',
            ],
            'hash': [
                r'\b[a-fA-F0-9]{32}\b',  # MD5
                r'\b[a-fA-F0-9]{40}\b',  # SHA1
                r'\b[a-fA-F0-9]{64}\b',  # SHA256
            ]
        }
        
        # 常见敏感键名
        self.sensitive_keys = [
            'flag', 'FLAG', 'Flag',
            'password', 'passwd', 'pwd', 'pass',
            'token', 'jwt', 'auth', 'authorization',
            'secret', 'key', 'private', 'api_key',
            'admin', 'root', 'user', 'login',
            'config', 'conf', 'settings',
            'session', 'cookie', 'csrf',
            'database', 'db', 'sql',
            'backup', 'dump', 'export',
        ]
    
    def connect_redis(self):
        """连接Redis服务器"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            response = sock.recv(8192).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def parse_redis_array_response(self, response):
        """解析Redis数组响应"""
        try:
            items = []
            lines = response.split('\r\n')
            i = 0
            
            while i < len(lines):
                if lines[i].startswith('*'):
                    array_size = int(lines[i][1:])
                    i += 1
                    
                    for j in range(array_size):
                        if i < len(lines) and lines[i].startswith('$'):
                            item_len = int(lines[i][1:])
                            i += 1
                            if i < len(lines):
                                item = lines[i]
                                items.append(item)
                                i += 1
                    break
                else:
                    i += 1
            
            return items
        except Exception as e:
            return []
    
    def get_all_keys(self):
        """获取所有键"""
        print(f"[*] 获取Redis所有键...")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return []
        
        try:
            response = self.send_redis_command(sock, "KEYS *")
            if response:
                keys = self.parse_redis_array_response(response)
                print(f"[+] 发现 {len(keys)} 个键")
                sock.close()
                return keys
            else:
                print("[-] 无法获取键列表")
                sock.close()
                return []
        except Exception as e:
            print(f"[-] 获取键列表失败: {e}")
            sock.close()
            return []
    
    def get_key_value(self, key):
        """获取键的值"""
        sock = self.connect_redis()
        if not sock:
            return None
        
        try:
            # 先获取键的类型
            type_response = self.send_redis_command(sock, ["TYPE", key])
            
            if not type_response:
                sock.close()
                return None
            
            # 根据类型获取值
            if "string" in type_response:
                value_response = self.send_redis_command(sock, ["GET", key])
            elif "list" in type_response:
                value_response = self.send_redis_command(sock, ["LRANGE", key, "0", "-1"])
            elif "set" in type_response:
                value_response = self.send_redis_command(sock, ["SMEMBERS", key])
            elif "zset" in type_response:
                value_response = self.send_redis_command(sock, ["ZRANGE", key, "0", "-1", "WITHSCORES"])
            elif "hash" in type_response:
                value_response = self.send_redis_command(sock, ["HGETALL", key])
            else:
                sock.close()
                return None
            
            if value_response and value_response.startswith('$'):
                lines = value_response.split('\r\n')
                if len(lines) > 1:
                    sock.close()
                    return lines[1]
            elif value_response and value_response.startswith('*'):
                values = self.parse_redis_array_response(value_response)
                sock.close()
                return values
            
            sock.close()
            return None
            
        except Exception as e:
            sock.close()
            return None
    
    def scan_sensitive_keys(self):
        """扫描敏感键名"""
        print(f"[*] 扫描敏感键名...")
        
        keys = self.get_all_keys()
        if not keys:
            return []
        
        sensitive_findings = []
        
        for key in keys:
            key_lower = key.lower()
            
            # 检查键名是否包含敏感词
            for sensitive_word in self.sensitive_keys:
                if sensitive_word.lower() in key_lower:
                    value = self.get_key_value(key)
                    if value:
                        sensitive_findings.append({
                            'type': 'sensitive_key',
                            'key': key,
                            'value': value,
                            'reason': f'键名包含敏感词: {sensitive_word}'
                        })
                        print(f"[+] 发现敏感键: {key} (包含: {sensitive_word})")
                    break
        
        return sensitive_findings
    
    def scan_sensitive_values(self):
        """扫描敏感值"""
        print(f"[*] 扫描敏感值...")
        
        keys = self.get_all_keys()
        if not keys:
            return []
        
        sensitive_findings = []
        
        for key in keys:
            value = self.get_key_value(key)
            if not value:
                continue
            
            # 将值转换为字符串进行模式匹配
            if isinstance(value, list):
                value_str = ' '.join(str(v) for v in value)
            else:
                value_str = str(value)
            
            # 检查敏感模式
            for pattern_type, patterns in self.sensitive_patterns.items():
                for pattern in patterns:
                    matches = re.findall(pattern, value_str, re.IGNORECASE)
                    if matches:
                        sensitive_findings.append({
                            'type': 'sensitive_value',
                            'key': key,
                            'value': value,
                            'pattern_type': pattern_type,
                            'matches': matches,
                            'reason': f'值匹配{pattern_type}模式'
                        })
                        print(f"[+] 发现敏感值: {key} (匹配: {pattern_type})")
                        print(f"    匹配内容: {matches}")
                        break
        
        return sensitive_findings
    
    def scan_flag_data(self):
        """专门扫描flag数据"""
        print(f"[*] 专门扫描flag数据...")
        
        keys = self.get_all_keys()
        if not keys:
            return []
        
        flag_findings = []
        
        for key in keys:
            # 检查键名是否包含flag
            if 'flag' in key.lower():
                value = self.get_key_value(key)
                if value:
                    flag_findings.append({
                        'type': 'flag_key',
                        'key': key,
                        'value': value,
                        'reason': '键名包含flag'
                    })
                    print(f"[+] 发现flag键: {key}")
                    print(f"    值: {value}")
            
            # 检查值是否包含flag格式
            else:
                value = self.get_key_value(key)
                if not value:
                    continue
                
                if isinstance(value, list):
                    value_str = ' '.join(str(v) for v in value)
                else:
                    value_str = str(value)
                
                # 检查flag模式
                flag_patterns = self.sensitive_patterns['flag']
                for pattern in flag_patterns:
                    matches = re.findall(pattern, value_str, re.IGNORECASE)
                    if matches:
                        flag_findings.append({
                            'type': 'flag_value',
                            'key': key,
                            'value': value,
                            'matches': matches,
                            'reason': '值包含flag格式'
                        })
                        print(f"[+] 发现flag值: {key}")
                        print(f"    flag: {matches}")
                        break
        
        return flag_findings
    
    def dump_all_data(self):
        """导出所有数据"""
        print(f"[*] 导出所有Redis数据...")
        
        keys = self.get_all_keys()
        if not keys:
            return {}
        
        all_data = {}
        
        for key in keys:
            value = self.get_key_value(key)
            if value is not None:
                all_data[key] = value
        
        print(f"[+] 导出了 {len(all_data)} 个键值对")
        return all_data
    
    def search_data(self, search_term):
        """搜索数据"""
        print(f"[*] 搜索包含 '{search_term}' 的数据...")
        
        keys = self.get_all_keys()
        if not keys:
            return []
        
        search_results = []
        search_term_lower = search_term.lower()
        
        for key in keys:
            # 搜索键名
            if search_term_lower in key.lower():
                value = self.get_key_value(key)
                search_results.append({
                    'type': 'key_match',
                    'key': key,
                    'value': value,
                    'reason': f'键名包含: {search_term}'
                })
                print(f"[+] 键名匹配: {key}")
            
            # 搜索值
            else:
                value = self.get_key_value(key)
                if not value:
                    continue
                
                if isinstance(value, list):
                    value_str = ' '.join(str(v) for v in value)
                else:
                    value_str = str(value)
                
                if search_term_lower in value_str.lower():
                    search_results.append({
                        'type': 'value_match',
                        'key': key,
                        'value': value,
                        'reason': f'值包含: {search_term}'
                    })
                    print(f"[+] 值匹配: {key}")
        
        return search_results
    
    def interactive_scan(self):
        """交互式数据扫描"""
        print(f"[+] 进入交互式Redis数据泄露扫描模式")
        
        while True:
            try:
                print("\n数据扫描选项:")
                print("1. 扫描敏感键名")
                print("2. 扫描敏感值")
                print("3. 专门扫描flag")
                print("4. 搜索特定内容")
                print("5. 导出所有数据")
                print("6. 查看指定键")
                print("7. 列出所有键")
                print("8. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    findings = self.scan_sensitive_keys()
                    print(f"\n发现 {len(findings)} 个敏感键")
                    
                elif choice == '2':
                    findings = self.scan_sensitive_values()
                    print(f"\n发现 {len(findings)} 个敏感值")
                    
                elif choice == '3':
                    flags = self.scan_flag_data()
                    if flags:
                        print(f"\n发现 {len(flags)} 个flag相关数据:")
                        for flag in flags:
                            print(f"键: {flag['key']}")
                            print(f"值: {flag['value']}")
                            if 'matches' in flag:
                                print(f"匹配: {flag['matches']}")
                            print("-" * 30)
                    else:
                        print("未发现flag数据")
                    
                elif choice == '4':
                    search_term = input("搜索内容: ").strip()
                    if search_term:
                        results = self.search_data(search_term)
                        print(f"搜索到 {len(results)} 个结果")
                    
                elif choice == '5':
                    all_data = self.dump_all_data()
                    output_file = input("输出文件名 (默认redis_dump.json): ").strip() or "redis_dump.json"
                    try:
                        with open(output_file, 'w', encoding='utf-8') as f:
                            json.dump(all_data, f, ensure_ascii=False, indent=2)
                        print(f"数据已导出到: {output_file}")
                    except Exception as e:
                        print(f"导出失败: {e}")
                    
                elif choice == '6':
                    key = input("键名: ").strip()
                    if key:
                        value = self.get_key_value(key)
                        if value is not None:
                            print(f"键: {key}")
                            print(f"值: {value}")
                        else:
                            print("键不存在或获取失败")
                    
                elif choice == '7':
                    keys = self.get_all_keys()
                    if keys:
                        print(f"\n所有键 ({len(keys)} 个):")
                        for i, key in enumerate(keys, 1):
                            print(f"{i:3d}. {key}")
                    
                elif choice == '8':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Redis 数据泄露扫描工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('--scan-keys', action='store_true', help='扫描敏感键名')
    parser.add_argument('--scan-values', action='store_true', help='扫描敏感值')
    parser.add_argument('--scan-flags', action='store_true', help='专门扫描flag数据')
    parser.add_argument('--search', help='搜索特定内容')
    parser.add_argument('--dump-all', action='store_true', help='导出所有数据')
    parser.add_argument('-o', '--output', help='输出文件')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式扫描模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    
    args = parser.parse_args()
    
    scanner = RedisDataLeakScanner(args.target, args.timeout)
    
    if args.interactive:
        scanner.interactive_scan()
    elif args.scan_flags:
        flags = scanner.scan_flag_data()
        if flags:
            print(f"\n[+] 发现 {len(flags)} 个flag相关数据:")
            for flag in flags:
                print(f"键: {flag['key']}")
                print(f"值: {flag['value']}")
                if 'matches' in flag:
                    print(f"匹配: {flag['matches']}")
                print("-" * 50)
        else:
            print("[-] 未发现flag数据")
    elif args.scan_keys:
        findings = scanner.scan_sensitive_keys()
        print(f"发现 {len(findings)} 个敏感键")
    elif args.scan_values:
        findings = scanner.scan_sensitive_values()
        print(f"发现 {len(findings)} 个敏感值")
    elif args.search:
        results = scanner.search_data(args.search)
        print(f"搜索到 {len(results)} 个结果")
    elif args.dump_all:
        all_data = scanner.dump_all_data()
        output_file = args.output or "redis_dump.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)
            print(f"数据已导出到: {output_file}")
        except Exception as e:
            print(f"导出失败: {e}")
    else:
        # 默认执行flag扫描
        flags = scanner.scan_flag_data()
        if flags:
            print(f"[+] 发现 {len(flags)} 个flag相关数据")
        else:
            print("[-] 未发现flag数据，可以使用 -i 进入交互模式进行详细扫描")

if __name__ == '__main__':
    main()
