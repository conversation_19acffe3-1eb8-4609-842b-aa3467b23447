#!/usr/bin/env python3
"""
Nginx 版本识别和指纹识别工具
通过多种方式识别nginx版本和配置信息
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class NginxVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 版本检测路径
        self.version_paths = [
            '/',
            '/index.html',
            '/index.php',
            '/robots.txt',
            '/favicon.ico',
            '/status',
            '/nginx_status',
            '/server-status',
            '/server-info',
            '/info',
            '/phpinfo.php',
            '/test.php',
            '/404',
            '/nonexistent',
        ]
        
        # 错误页面触发路径
        self.error_paths = [
            '/nonexistent_page_12345',
            '/error_test_nginx',
            '/404_test',
            '/500_test',
            '/403_test',
        ]
        
        # 已知版本漏洞映射
        self.version_vulnerabilities = {
            '0.5.6': ['CVE-2017-7529'],
            '0.6': ['CVE-2017-7529'],
            '0.7': ['CVE-2017-7529', 'CVE-2013-4547'],
            '0.8': ['CVE-2017-7529', 'CVE-2013-4547'],
            '1.0': ['CVE-2017-7529', 'CVE-2013-4547'],
            '1.1': ['CVE-2017-7529', 'CVE-2013-4547'],
            '1.2': ['CVE-2017-7529', 'CVE-2013-4547'],
            '1.3': ['CVE-2017-7529', 'CVE-2013-4547'],
            '1.4': ['CVE-2017-7529', 'CVE-2013-4547'],
            '1.5': ['CVE-2017-7529', 'CVE-2013-4547'],
            '1.6': ['CVE-2017-7529'],
            '1.7': ['CVE-2017-7529'],
            '1.8': ['CVE-2017-7529'],
            '1.9': ['CVE-2017-7529'],
            '1.10': ['CVE-2017-7529'],
            '1.11': ['CVE-2017-7529'],
            '1.12': ['CVE-2017-7529'],
            '1.13': ['CVE-2017-7529'],
            '1.14': [],
            '1.15': ['CVE-2019-20372'],
            '1.16': ['CVE-2019-20372'],
        }
        
        # 模块检测
        self.module_indicators = {
            'php': ['X-Powered-By: PHP', 'php', '.php'],
            'ssl': ['https://', 'SSL', 'TLS'],
            'gzip': ['Content-Encoding: gzip', 'gzip'],
            'realip': ['X-Real-IP', 'X-Forwarded-For'],
            'auth': ['WWW-Authenticate', '401 Unauthorized'],
            'limit_req': ['503 Service Temporarily Unavailable'],
            'stub_status': ['Active connections', 'server accepts handled requests'],
        }
    
    def get_server_headers(self):
        """获取服务器响应头信息"""
        print(f"[*] 获取服务器头信息: {self.target_url}")
        
        headers_info = {}
        
        try:
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            # 提取关键头信息
            important_headers = [
                'Server', 'X-Powered-By', 'X-AspNet-Version', 
                'X-Generator', 'X-Version', 'Via', 'X-Nginx-Version'
            ]
            
            for header in important_headers:
                if header in response.headers:
                    headers_info[header] = response.headers[header]
                    print(f"[+] {header}: {response.headers[header]}")
            
            # 检查响应状态和内容
            headers_info['status_code'] = response.status_code
            headers_info['content_length'] = len(response.content)
            
            return headers_info, response
            
        except Exception as e:
            print(f"[-] 获取头信息失败: {e}")
            return {}, None
    
    def detect_version_from_headers(self, headers_info):
        """从响应头检测版本信息"""
        version_info = {}
        
        # 检查Server头
        if 'Server' in headers_info:
            server_header = headers_info['Server']
            
            # 提取nginx版本
            nginx_match = re.search(r'nginx/([0-9]+\.[0-9]+(?:\.[0-9]+)?)', server_header, re.IGNORECASE)
            if nginx_match:
                version_info['nginx_version'] = nginx_match.group(1)
                print(f"[+] 从Server头检测到版本: {nginx_match.group(1)}")
            elif 'nginx' in server_header.lower():
                version_info['nginx_detected'] = True
                print(f"[+] 检测到nginx，但版本被隐藏")
        
        return version_info
    
    def detect_version_from_errors(self):
        """通过错误页面检测版本信息"""
        print(f"[*] 通过错误页面检测版本...")
        
        version_info = {}
        
        for error_path in self.error_paths:
            try:
                url = urljoin(self.target_url, error_path)
                response = self.session.get(url, timeout=self.timeout)
                
                # 检查错误页面内容
                if response.status_code in [404, 403, 500]:
                    content = response.text.lower()
                    
                    # 查找版本信息
                    nginx_match = re.search(r'nginx/([0-9]+\.[0-9]+(?:\.[0-9]+)?)', content, re.IGNORECASE)
                    if nginx_match:
                        version_info['nginx_version'] = nginx_match.group(1)
                        print(f"[+] 从错误页面检测到版本: {nginx_match.group(1)}")
                        break
                    elif 'nginx' in content:
                        version_info['nginx_detected'] = True
                        print(f"[+] 从错误页面检测到nginx")
                        
            except Exception as e:
                continue
        
        return version_info
    
    def detect_modules(self):
        """检测nginx模块"""
        print(f"[*] 检测nginx模块...")
        
        detected_modules = []
        
        # 测试不同路径以触发模块响应
        test_paths = self.version_paths + ['/status', '/nginx_status']
        
        for path in test_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                # 检查响应头和内容
                response_text = response.text.lower()
                response_headers = str(response.headers).lower()
                
                for module, indicators in self.module_indicators.items():
                    for indicator in indicators:
                        if (indicator.lower() in response_headers or 
                            indicator.lower() in response_text):
                            if module not in detected_modules:
                                detected_modules.append(module)
                                print(f"[+] 检测到模块: {module}")
                                break
                            
            except Exception as e:
                continue
        
        return detected_modules
    
    def fingerprint_nginx(self):
        """nginx指纹识别"""
        print(f"[*] 进行nginx指纹识别...")
        
        fingerprint_info = {}
        
        # 测试特定的nginx行为
        fingerprint_tests = [
            # 测试大小写敏感性
            ('case_sensitivity', '/', '/INDEX.HTML'),
            # 测试目录遍历
            ('directory_traversal', '/../', '/..'),
            # 测试特殊字符处理
            ('special_chars', '/%20', '/ '),
            # 测试HTTP方法
            ('http_methods', 'OPTIONS', 'TRACE'),
        ]
        
        for test_name, test_path1, test_path2 in fingerprint_tests:
            try:
                if test_name == 'http_methods':
                    # 测试HTTP方法
                    response1 = self.session.request(test_path1, self.target_url, timeout=self.timeout)
                    response2 = self.session.request(test_path2, self.target_url, timeout=self.timeout)
                else:
                    # 测试路径
                    url1 = urljoin(self.target_url, test_path1)
                    url2 = urljoin(self.target_url, test_path2)
                    response1 = self.session.get(url1, timeout=self.timeout)
                    response2 = self.session.get(url2, timeout=self.timeout)
                
                fingerprint_info[test_name] = {
                    'test1_status': response1.status_code,
                    'test2_status': response2.status_code,
                    'different_response': response1.status_code != response2.status_code
                }
                
            except Exception as e:
                continue
        
        return fingerprint_info
    
    def get_vulnerability_info(self, version_info):
        """根据版本信息获取相关漏洞"""
        vulnerabilities = []
        
        if 'nginx_version' in version_info:
            version = version_info['nginx_version']
            
            # 提取主版本号
            version_parts = version.split('.')
            if len(version_parts) >= 2:
                major_minor = f"{version_parts[0]}.{version_parts[1]}"
                
                # 检查已知漏洞
                for vuln_version, vulns in self.version_vulnerabilities.items():
                    if version.startswith(vuln_version) or major_minor == vuln_version:
                        vulnerabilities.extend(vulns)
        
        return list(set(vulnerabilities))  # 去重
    
    def generate_report(self):
        """生成完整的检测报告"""
        print("=" * 80)
        print("Nginx 版本检测报告")
        print("=" * 80)
        
        # 获取基础信息
        headers_info, response = self.get_server_headers()
        
        # 版本检测
        version_info = self.detect_version_from_headers(headers_info)
        if not version_info.get('nginx_version'):
            error_version_info = self.detect_version_from_errors()
            version_info.update(error_version_info)
        
        if version_info:
            print(f"\n[+] 版本信息:")
            for key, value in version_info.items():
                print(f"  {key}: {value}")
        else:
            print(f"\n[-] 未检测到明确的版本信息")
        
        # 模块检测
        detected_modules = self.detect_modules()
        if detected_modules:
            print(f"\n[+] 检测到的模块:")
            for module in detected_modules:
                print(f"  - {module}")
        
        # 指纹识别
        fingerprint_info = self.fingerprint_nginx()
        if fingerprint_info:
            print(f"\n[+] 指纹信息:")
            for test_name, test_result in fingerprint_info.items():
                print(f"  {test_name}: {test_result}")
        
        # 漏洞信息
        vulnerabilities = self.get_vulnerability_info(version_info)
        if vulnerabilities:
            print(f"\n[!] 可能存在的漏洞:")
            for cve in vulnerabilities:
                print(f"  - {cve}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        if 'nginx_version' in version_info:
            print("  - 检查当前版本是否为最新版本")
        if 'Server' in headers_info:
            print("  - 考虑隐藏Server头中的版本信息")
        if vulnerabilities:
            print("  - 及时修复已知漏洞")
        if 'stub_status' in detected_modules:
            print("  - 限制status页面的访问权限")
        
        return {
            'headers_info': headers_info,
            'version_info': version_info,
            'detected_modules': detected_modules,
            'fingerprint_info': fingerprint_info,
            'vulnerabilities': vulnerabilities
        }

def main():
    parser = argparse.ArgumentParser(description='Nginx 版本识别和指纹识别工具')
    parser.add_argument('target', help='目标URL (例如: http://target)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--headers-only', action='store_true', help='仅检测响应头')
    parser.add_argument('--modules-only', action='store_true', help='仅检测模块')
    parser.add_argument('--fingerprint-only', action='store_true', help='仅进行指纹识别')
    
    args = parser.parse_args()
    
    detector = NginxVersionDetector(args.target, args.timeout)
    
    if args.headers_only:
        headers_info, _ = detector.get_server_headers()
        version_info = detector.detect_version_from_headers(headers_info)
        for key, value in version_info.items():
            print(f"{key}: {value}")
    elif args.modules_only:
        detected_modules = detector.detect_modules()
        for module in detected_modules:
            print(f"模块: {module}")
    elif args.fingerprint_only:
        fingerprint_info = detector.fingerprint_nginx()
        for test_name, test_result in fingerprint_info.items():
            print(f"{test_name}: {test_result}")
    else:
        report = detector.generate_report()
        
        # 输出到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Nginx检测报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n检测到的模块:\n")
                for module in report['detected_modules']:
                    f.write(f"{module}\n")
                
                f.write("\n指纹信息:\n")
                for test_name, test_result in report['fingerprint_info'].items():
                    f.write(f"{test_name}: {test_result}\n")
                
                f.write("\n可能的漏洞:\n")
                for cve in report['vulnerabilities']:
                    f.write(f"{cve}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
