#!/usr/bin/env python3
"""
Nginx 漏洞扫描工具使用示例
演示如何使用各种扫描脚本
"""

import subprocess
import sys
import time

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("命令执行超时")
    except Exception as e:
        print(f"执行错误: {e}")
    
    time.sleep(2)

def main():
    if len(sys.argv) != 2:
        print("用法: python3 example_usage.py <target_url>")
        print("示例: python3 example_usage.py http://*************")
        sys.exit(1)
    
    target = sys.argv[1]
    
    print("Nginx 漏洞扫描工具使用示例")
    print(f"目标: {target}")
    
    # 示例1: 综合扫描
    run_command([
        'python3', 'nginx_comprehensive_scan.py', target
    ], "综合漏洞扫描")
    
    # 示例2: 版本识别
    run_command([
        'python3', 'nginx_version_detect.py', target
    ], "Nginx版本识别")
    
    # 示例3: 配置泄露检测
    run_command([
        'python3', 'nginx_config_leak.py', target
    ], "配置文件泄露检测")
    
    # 示例4: CVE-2013-4547检测
    run_command([
        'python3', 'CVE-2013-4547.py', target, '--check-only'
    ], "CVE-2013-4547 空格绕过漏洞检测")
    
    # 示例5: CVE-2017-7529检测
    run_command([
        'python3', 'CVE-2017-7529.py', target, '--check-only'
    ], "CVE-2017-7529 整数溢出漏洞检测")
    
    # 示例6: CVE-2019-20372检测
    run_command([
        'python3', 'CVE-2019-20372.py', target, '--check-only'
    ], "CVE-2019-20372 HTTP请求走私检测")
    
    # 示例7: 命令注入检测
    run_command([
        'python3', 'nginx_cmd_inject.py', target, '--check-only'
    ], "命令注入漏洞检测")
    
    # 示例8: 文件读取检测
    run_command([
        'python3', 'nginx_file_read.py', target, '--check-only'
    ], "任意文件读取漏洞检测")
    
    # 示例9: 目录遍历检测
    run_command([
        'python3', 'nginx_traversal.py', target, '--check-only'
    ], "目录遍历漏洞检测")
    
    # 示例10: SSRF检测
    run_command([
        'python3', 'nginx_ssrf_detect.py', target, '--check-only'
    ], "SSRF漏洞检测")
    
    # 示例11: 解析漏洞检测
    run_command([
        'python3', 'nginx_parsing_vuln.py', target, '--check-only'
    ], "解析漏洞检测")
    
    print(f"\n{'='*60}")
    print("示例演示完成!")
    print("更多用法请参考 README.md 文件")
    print(f"{'='*60}")

if __name__ == '__main__':
    main()
