# Nginx 漏洞扫描工具集

这是一个专门针对Nginx应用服务器的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。

## 功能特性

- 🔍 **漏洞检测**: 支持多个Nginx CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、配置泄露、敏感文件扫描
- 🔐 **安全测试**: 文件上传绕过、解析漏洞、SSRF等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2013-4547 | Nginx 空格绕过漏洞 | Medium | 0.8.41-1.4.3/1.5.7 |
| CVE-2017-7529 | Nginx 整数溢出信息泄露 | Medium | 0.5.6-1.13.2 |
| CVE-2019-20372 | Nginx HTTP请求走私 | High | 1.15.10-1.16.0 |
| 命令注入 | Nginx配置错误导致的命令注入 | Critical | 配置相关 |
| 任意文件读取 | Nginx配置错误导致的文件读取 | High | 配置相关 |
| 目录遍历 | Nginx alias配置错误和off-by-one | High | 配置相关 |
| 文件上传绕过 | Nginx文件上传限制绕过 | High | 配置相关 |
| SSRF漏洞 | Nginx配置错误导致的SSRF | High | 配置相关 |
| 解析漏洞 | Nginx文件解析配置错误 | High | 配置相关 |
| 配置泄露 | Nginx敏感信息泄露 | Medium | 配置相关 |

## 安装和使用

### 环境要求

- Python 3.6+
- pip (Python包管理器)

### 安装依赖

```bash
cd nginx
pip install -r requirements.txt
```

### 基本使用

#### 1. 综合扫描 (推荐)

```bash
# 扫描所有漏洞
python3 nginx_comprehensive_scan.py http://target

# 扫描指定漏洞
python3 nginx_comprehensive_scan.py http://target -v CVE-2013-4547 nginx_cmd_inject

# 保存扫描报告
python3 nginx_comprehensive_scan.py http://target -o report.json
```

#### 2. 单个漏洞扫描

```bash
# CVE-2013-4547 空格绕过
python3 CVE-2013-4547.py http://target --check-only
python3 CVE-2013-4547.py http://target -p "/admin/"

# CVE-2017-7529 整数溢出
python3 CVE-2017-7529.py http://target --check-only
python3 CVE-2017-7529.py http://target -f "/etc/passwd"

# CVE-2019-20372 HTTP请求走私
python3 CVE-2019-20372.py http://target --check-only
python3 CVE-2019-20372.py http://target -i

# 命令注入
python3 nginx_cmd_inject.py http://target --check-only
python3 nginx_cmd_inject.py http://target -c "whoami"

# 任意文件读取
python3 nginx_file_read.py http://target --check-only
python3 nginx_file_read.py http://target -f "/etc/passwd"

# 目录遍历
python3 nginx_traversal.py http://target --check-only
python3 nginx_traversal.py http://target -f "/etc/passwd"

# 文件上传绕过
python3 nginx_upload_bypass.py http://target/upload --all
python3 nginx_upload_bypass.py http://target/upload -f "shell.php"

# SSRF检测
python3 nginx_ssrf_detect.py http://target --check-only
python3 nginx_ssrf_detect.py http://target -s

# 解析漏洞
python3 nginx_parsing_vuln.py http://target --check-only
python3 nginx_parsing_vuln.py http://target -p "/uploads/test.jpg/shell.php"
```

#### 3. 信息收集

```bash
# 版本识别
python3 nginx_version_detect.py http://target

# 配置泄露检测
python3 nginx_config_leak.py http://target
python3 nginx_config_leak.py http://target -c  # 仅检测配置文件
python3 nginx_config_leak.py http://target -s  # 仅检测敏感文件
```

## 脚本详细说明

### 核心漏洞利用脚本

1. **CVE-2013-4547.py** - Nginx 空格绕过漏洞
   - 检测nginx空格字符绕过安全限制
   - 支持多种空白字符绕过
   - 可测试受保护路径和敏感文件访问

2. **CVE-2017-7529.py** - Nginx 整数溢出信息泄露
   - 利用Range请求整数溢出读取额外数据
   - 支持多种整数溢出payload
   - 可检测和利用信息泄露

3. **CVE-2019-20372.py** - Nginx HTTP请求走私
   - 检测HTTP请求走私漏洞
   - 支持CL.TE、TE.CL、TE.TE等多种走私方式
   - 包含去同步检测功能

4. **nginx_cmd_inject.py** - 命令注入漏洞
   - 检测nginx配置错误导致的命令注入
   - 支持多种命令分隔符和编码绕过
   - 包含盲注入检测和交互式shell

5. **nginx_file_read.py** - 任意文件读取漏洞
   - 检测直接路径访问和参数化文件读取
   - 支持多种目录遍历payload
   - 包含敏感文件自动扫描

6. **nginx_traversal.py** - 目录遍历漏洞
   - 检测nginx alias配置错误
   - 支持off-by-one漏洞检测
   - 包含多种编码绕过方式

7. **nginx_upload_bypass.py** - 文件上传绕过
   - 检测文件上传限制绕过
   - 支持多种扩展名和MIME类型绕过
   - 包含nginx解析漏洞利用

8. **nginx_ssrf_detect.py** - SSRF漏洞检测
   - 检测服务端请求伪造漏洞
   - 支持内网扫描和云服务元数据访问
   - 包含多种协议和绕过方式

9. **nginx_parsing_vuln.py** - 解析漏洞
   - 检测nginx文件解析配置错误
   - 支持路径信息、空字节、双扩展名绕过
   - 包含WebShell生成和测试

### 辅助工具脚本

1. **nginx_version_detect.py** - 版本识别
   - 多种方式识别nginx版本
   - 模块检测和指纹识别
   - 漏洞映射和安全建议

2. **nginx_config_leak.py** - 配置泄露检测
   - 检测配置文件和敏感信息泄露
   - 支持目录遍历访问
   - 敏感信息自动提取

3. **nginx_comprehensive_scan.py** - 综合扫描
   - 集成所有漏洞检测功能
   - 自动化扫描和报告生成
   - 风险评估和安全建议

## 高级用法

### 交互式模式

大部分脚本都支持交互式模式，方便手动测试：

```bash
# 交互式命令注入
python3 nginx_cmd_inject.py http://target -i

# 交互式文件读取
python3 nginx_file_read.py http://target -i

# 交互式SSRF测试
python3 nginx_ssrf_detect.py http://target -i

# 交互式HTTP请求走私
python3 CVE-2019-20372.py http://target -i
```

### 批量扫描

```bash
# 创建目标列表
echo "http://target1" > targets.txt
echo "http://target2" >> targets.txt

# 批量扫描
for target in $(cat targets.txt); do
    python3 nginx_comprehensive_scan.py $target -o "report_$(echo $target | tr '/:' '_').json"
done
```

### 自定义payload

```bash
# 自定义WebShell
python3 nginx_upload_bypass.py http://target/upload --custom -s php -p mypass

# 自定义命令注入payload
python3 nginx_cmd_inject.py http://target -p cmd -c "cat /flag"

# 自定义文件读取
python3 nginx_file_read.py http://target -f "/flag.txt"
```

## 注意事项

1. **合法使用**: 仅用于授权的渗透测试和安全研究
2. **网络环境**: 某些功能需要目标网络可达
3. **权限要求**: 某些功能可能需要特定权限
4. **版本兼容**: 不同nginx版本可能有不同的漏洞
5. **配置依赖**: 大部分漏洞与nginx配置相关

## 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   python3 script.py http://target -t 30
   ```

2. **SSL证书错误**
   - 脚本已自动忽略SSL警告

3. **权限不足**
   ```bash
   # 某些功能可能需要管理员权限
   sudo python3 script.py http://target
   ```

4. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --upgrade
   ```

## 贡献

欢迎提交Issue和Pull Request来改进这个工具集。

## 免责声明

本工具仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规，作者不承担任何滥用责任。