#!/usr/bin/env python3
"""
Jenkins构建历史信息提取工具
从Jenkins构建历史和日志中提取敏感信息
"""

import sys
import requests
import argparse
import json
import re
import warnings
from urllib.parse import urljoin
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsBuildHistory:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 敏感信息匹配模式
        self.sensitive_patterns = {
            'passwords': [
                r'password["\']?\s*[:=]\s*["\']([^"\']{3,})["\']',
                r'passwd["\']?\s*[:=]\s*["\']([^"\']{3,})["\']',
                r'pwd["\']?\s*[:=]\s*["\']([^"\']{3,})["\']',
                r'-p\s+([^\s]{3,})',
                r'--password[=\s]+([^\s]{3,})'
            ],
            'tokens': [
                r'token["\']?\s*[:=]\s*["\']([^"\']{10,})["\']',
                r'api[_-]?key["\']?\s*[:=]\s*["\']([^"\']{10,})["\']',
                r'access[_-]?token["\']?\s*[:=]\s*["\']([^"\']{10,})["\']',
                r'bearer\s+([a-zA-Z0-9._-]{20,})',
                r'authorization:\s*bearer\s+([a-zA-Z0-9._-]{20,})'
            ],
            'secrets': [
                r'secret["\']?\s*[:=]\s*["\']([^"\']{10,})["\']',
                r'private[_-]?key["\']?\s*[:=]\s*["\']([^"\']{10,})["\']',
                r'client[_-]?secret["\']?\s*[:=]\s*["\']([^"\']{10,})["\']'
            ],
            'database': [
                r'jdbc:[\w]+://([^/\s"\']+)',
                r'mysql://([^/\s"\']+)',
                r'postgresql://([^/\s"\']+)',
                r'mongodb://([^/\s"\']+)',
                r'redis://([^/\s"\']+)'
            ],
            'urls': [
                r'https?://[^\s<>"\']+',
                r'ftp://[^\s<>"\']+',
                r'ssh://[^\s<>"\']+',
                r'ldap://[^\s<>"\']+',
                r'smtp://[^\s<>"\']+',
            ],
            'emails': [
                r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
            ],
            'ip_addresses': [
                r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
            ],
            'file_paths': [
                r'["\']([/\\][^"\']*\.(key|pem|p12|jks|keystore|crt|cer))["\']',
                r'["\']([/\\][^"\']*/(id_rsa|id_dsa|id_ecdsa|authorized_keys))["\']'
            ]
        }
        
        self.scan_results = {
            'jobs': [],
            'builds': [],
            'sensitive_data': {},
            'statistics': {}
        }
    
    def get_jobs_list(self):
        """获取Jenkins任务列表"""
        print(f"[*] 获取Jenkins任务列表...")
        
        try:
            api_url = urljoin(self.target_url, '/api/json?tree=jobs[name,url,buildable,builds[number,url,result,timestamp]]')
            response = self.session.get(api_url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                jobs = data.get('jobs', [])
                
                print(f"[+] 发现 {len(jobs)} 个任务")
                
                for job in jobs:
                    job_info = {
                        'name': job.get('name', ''),
                        'url': job.get('url', ''),
                        'buildable': job.get('buildable', False),
                        'builds': job.get('builds', [])
                    }
                    self.scan_results['jobs'].append(job_info)
                
                return jobs
            else:
                print(f"[-] 无法获取任务列表，状态码: {response.status_code}")
                return []
        
        except Exception as e:
            print(f"[-] 获取任务列表失败: {e}")
            return []
    
    def analyze_build_logs(self, job_name, build_number, build_url):
        """分析单个构建日志"""
        try:
            # 获取构建日志
            log_url = build_url.rstrip('/') + '/consoleText'
            response = self.session.get(log_url, timeout=self.timeout)
            
            if response.status_code == 200:
                log_content = response.text
                
                # 搜索敏感信息
                found_sensitive = {}
                
                for category, patterns in self.sensitive_patterns.items():
                    found_items = []
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, log_content, re.IGNORECASE | re.MULTILINE)
                        
                        for match in matches:
                            # 过滤掉明显的假阳性
                            if self.is_valid_sensitive_data(match, category):
                                found_items.append({
                                    'value': match,
                                    'pattern': pattern,
                                    'context': self.get_context(log_content, match)
                                })
                    
                    if found_items:
                        found_sensitive[category] = found_items
                
                if found_sensitive:
                    print(f"[!] 在 {job_name}#{build_number} 中发现敏感信息")
                    
                    build_info = {
                        'job_name': job_name,
                        'build_number': build_number,
                        'build_url': build_url,
                        'log_url': log_url,
                        'sensitive_data': found_sensitive,
                        'log_size': len(log_content)
                    }
                    
                    self.scan_results['builds'].append(build_info)
                    
                    # 更新统计信息
                    for category, items in found_sensitive.items():
                        if category not in self.scan_results['sensitive_data']:
                            self.scan_results['sensitive_data'][category] = []
                        
                        for item in items:
                            sensitive_info = {
                                'job': job_name,
                                'build': build_number,
                                'category': category,
                                'value': item['value'],
                                'context': item['context']
                            }
                            self.scan_results['sensitive_data'][category].append(sensitive_info)
                
                return found_sensitive
            
            else:
                print(f"[-] 无法获取构建日志: {job_name}#{build_number}")
                return {}
        
        except Exception as e:
            print(f"[-] 分析构建日志失败 ({job_name}#{build_number}): {e}")
            return {}
    
    def is_valid_sensitive_data(self, data, category):
        """验证敏感数据的有效性，过滤假阳性"""
        if not data or len(data.strip()) < 3:
            return False
        
        # 常见的假阳性过滤
        false_positives = [
            'password', 'token', 'secret', 'key', 'example', 'test', 'demo',
            'placeholder', 'your_password', 'your_token', 'change_me',
            '123456', 'admin', 'root', 'user', 'guest', 'default',
            'localhost', '127.0.0.1', '0.0.0.0', 'example.com'
        ]
        
        data_lower = data.lower()
        
        for fp in false_positives:
            if fp in data_lower:
                return False
        
        # 特定类别的验证
        if category == 'passwords':
            # 密码长度至少3位，不能是纯数字或纯字母
            if len(data) < 3 or data.isdigit() or data.isalpha():
                return False
        
        elif category == 'tokens':
            # Token长度至少10位
            if len(data) < 10:
                return False
        
        elif category == 'emails':
            # 简单的邮箱格式验证
            if '@' not in data or '.' not in data.split('@')[-1]:
                return False
        
        elif category == 'ip_addresses':
            # IP地址格式验证
            parts = data.split('.')
            if len(parts) != 4:
                return False
            try:
                for part in parts:
                    if not (0 <= int(part) <= 255):
                        return False
            except ValueError:
                return False
        
        return True
    
    def get_context(self, content, target, context_size=50):
        """获取敏感信息的上下文"""
        try:
            index = content.find(target)
            if index == -1:
                return ""
            
            start = max(0, index - context_size)
            end = min(len(content), index + len(target) + context_size)
            
            context = content[start:end]
            # 清理上下文，移除多余的空白字符
            context = ' '.join(context.split())
            
            return context
        
        except Exception:
            return ""
    
    def scan_build_history(self, max_builds_per_job=5):
        """扫描构建历史"""
        print(f"[*] 开始扫描构建历史...")
        
        jobs = self.get_jobs_list()
        
        if not jobs:
            print(f"[-] 没有可扫描的任务")
            return
        
        total_builds_scanned = 0
        
        for job in jobs:
            job_name = job['name']
            builds = job.get('builds', [])
            
            if not builds:
                print(f"[-] 任务 {job_name} 没有构建历史")
                continue
            
            print(f"[*] 扫描任务 {job_name} 的构建历史...")
            
            # 限制每个任务扫描的构建数量
            builds_to_scan = builds[:max_builds_per_job]
            
            for build in builds_to_scan:
                build_number = build.get('number', 0)
                build_url = build.get('url', '')
                
                if build_url:
                    print(f"[*] 分析构建: {job_name}#{build_number}")
                    self.analyze_build_logs(job_name, build_number, build_url)
                    total_builds_scanned += 1
        
        print(f"[*] 扫描完成，共分析了 {total_builds_scanned} 个构建")
        
        # 生成统计信息
        self.generate_statistics()
    
    def generate_statistics(self):
        """生成统计信息"""
        stats = {
            'total_jobs': len(self.scan_results['jobs']),
            'total_builds': len(self.scan_results['builds']),
            'sensitive_categories': {},
            'top_jobs_with_issues': {}
        }
        
        # 按类别统计敏感信息
        for category, items in self.scan_results['sensitive_data'].items():
            stats['sensitive_categories'][category] = len(items)
        
        # 统计问题最多的任务
        job_issue_count = {}
        for build in self.scan_results['builds']:
            job_name = build['job_name']
            if job_name not in job_issue_count:
                job_issue_count[job_name] = 0
            
            for category, items in build['sensitive_data'].items():
                job_issue_count[job_name] += len(items)
        
        # 排序并取前5个
        sorted_jobs = sorted(job_issue_count.items(), key=lambda x: x[1], reverse=True)
        stats['top_jobs_with_issues'] = dict(sorted_jobs[:5])
        
        self.scan_results['statistics'] = stats
    
    def generate_report(self):
        """生成扫描报告"""
        print(f"\n" + "="*60)
        print(f"Jenkins构建历史安全扫描报告")
        print(f"="*60)
        print(f"目标: {self.target_url}")
        
        stats = self.scan_results.get('statistics', {})
        
        # 基本统计
        print(f"\n基本统计:")
        print(f"  扫描任务数: {stats.get('total_jobs', 0)}")
        print(f"  分析构建数: {stats.get('total_builds', 0)}")
        print(f"  发现问题构建: {len(self.scan_results['builds'])}")
        
        # 敏感信息统计
        sensitive_categories = stats.get('sensitive_categories', {})
        if sensitive_categories:
            print(f"\n敏感信息统计:")
            for category, count in sensitive_categories.items():
                print(f"  {category}: {count}个")
        
        # 问题最多的任务
        top_jobs = stats.get('top_jobs_with_issues', {})
        if top_jobs:
            print(f"\n问题最多的任务:")
            for job_name, issue_count in top_jobs.items():
                print(f"  {job_name}: {issue_count}个问题")
        
        # 详细的敏感信息示例
        if self.scan_results['sensitive_data']:
            print(f"\n发现的敏感信息示例:")
            
            for category, items in self.scan_results['sensitive_data'].items():
                if items:
                    print(f"\n  {category.upper()}:")
                    for item in items[:3]:  # 只显示前3个
                        value = item['value']
                        if len(value) > 50:
                            value = value[:47] + "..."
                        print(f"    - {item['job']}#{item['build']}: {value}")
        
        # 安全建议
        print(f"\n安全建议:")
        recommendations = [
            "清理构建日志中的敏感信息",
            "使用Jenkins凭据管理功能",
            "配置日志轮转和清理策略",
            "限制构建日志的访问权限",
            "定期审计构建脚本",
            "使用环境变量传递敏感信息"
        ]
        
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
        
        print(f"="*60)

def main():
    parser = argparse.ArgumentParser(description='Jenkins构建历史信息提取工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--max-builds', type=int, default=5, help='每个任务最大扫描构建数')
    parser.add_argument('--job', help='指定要扫描的任务名称')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--output', '-o', help='输出结果到文件')
    parser.add_argument('--json', action='store_true', help='JSON格式输出')
    
    args = parser.parse_args()
    
    # 创建构建历史扫描器实例
    scanner = JenkinsBuildHistory(args.target, args.timeout)
    
    try:
        if args.job:
            # 扫描指定任务
            print(f"[*] 扫描指定任务: {args.job}")
            # 这里可以添加单个任务扫描的逻辑
            scanner.scan_build_history(args.max_builds)
        else:
            # 扫描所有任务
            scanner.scan_build_history(args.max_builds)
        
        # 生成报告
        if args.json:
            result = json.dumps(scanner.scan_results, ensure_ascii=False, indent=2)
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(result)
                print(f"[*] 结果已保存到: {args.output}")
            else:
                print(result)
        else:
            scanner.generate_report()
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(scanner.scan_results, f, ensure_ascii=False, indent=2)
                print(f"\n[*] 详细结果已保存到: {args.output}")
        
    except KeyboardInterrupt:
        print(f"\n[!] 扫描被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 扫描过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
