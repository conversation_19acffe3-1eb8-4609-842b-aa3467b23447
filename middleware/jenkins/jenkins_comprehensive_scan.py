#!/usr/bin/env python3
"""
Jenkins综合漏洞扫描器
集成所有Jenkins漏洞检测功能的综合扫描工具
"""

import sys
import os
import subprocess
import argparse
import json
import time
from datetime import datetime
import warnings
warnings.filterwarnings("ignore")

class JenkinsComprehensiveScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.scan_results = {
            'target': target_url,
            'scan_time': datetime.now().isoformat(),
            'jenkins_version': 'Unknown',
            'vulnerabilities': {},
            'sensitive_info': {},
            'recommendations': []
        }
        
        # 可用的扫描模块
        self.scan_modules = {
            'CVE-2024-23897': {
                'script': 'CVE-2024-23897.py',
                'description': 'Jenkins CLI任意文件读取',
                'severity': 'Critical'
            },
            'CVE-2019-1003000': {
                'script': 'CVE-2019-1003000.py', 
                'description': 'Script Security沙箱绕过',
                'severity': 'Critical'
            },
            'CVE-2017-1000353': {
                'script': 'CVE-2017-1000353.py',
                'description': 'Java反序列化RCE',
                'severity': 'Critical'
            },
            'script_console': {
                'script': 'jenkins_script_console.py',
                'description': '脚本控制台未授权访问',
                'severity': 'Critical'
            },
            'info_disclosure': {
                'script': 'jenkins_info_disclosure.py',
                'description': '敏感信息泄露',
                'severity': 'High'
            },
            'plugin_scan': {
                'script': 'jenkins_plugin_scan.py',
                'description': '插件漏洞扫描',
                'severity': 'High'
            }
        }
    
    def run_script(self, script_name, args):
        """运行指定的扫描脚本"""
        script_path = os.path.join(os.path.dirname(__file__), script_name)
        
        if not os.path.exists(script_path):
            return None, f"脚本不存在: {script_path}"
        
        try:
            cmd = ['python3', script_path] + args
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=self.timeout * 3
            )
            
            return result.returncode, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            return -1, "", "脚本执行超时"
        except Exception as e:
            return -1, "", str(e)
    
    def detect_jenkins_version(self):
        """检测Jenkins版本"""
        print(f"[*] 检测Jenkins版本信息...")
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('jenkins_version_detect.py', args)
        
        if returncode == 0 and 'Jenkins' in stdout:
            # 提取版本信息
            lines = stdout.split('\n')
            for line in lines:
                if 'Jenkins版本' in line or 'Jenkins Version' in line:
                    version = line.split(':')[-1].strip()
                    self.scan_results['jenkins_version'] = version
                    print(f"[+] 检测到Jenkins版本: {version}")
                    break
        else:
            print(f"[-] 版本检测失败")
    
    def scan_vulnerability(self, vuln_id, module_info):
        """扫描单个漏洞"""
        print(f"\n[*] 扫描 {vuln_id}: {module_info['description']}")
        print("-" * 60)
        
        # 构造扫描参数
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        
        returncode, stdout, stderr = self.run_script(module_info['script'], args)
        
        vulnerability_result = {
            'vuln_id': vuln_id,
            'description': module_info['description'],
            'severity': module_info['severity'],
            'vulnerable': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 检查输出中是否包含漏洞存在的指示
            if ('存在' in stdout or 'vulnerable' in stdout.lower() or 
                '发现' in stdout or 'found' in stdout.lower() or
                '成功' in stdout or 'success' in stdout.lower() or
                '可访问' in stdout or 'accessible' in stdout.lower()):
                vulnerability_result['vulnerable'] = True
                print(f"[+] {vuln_id} 漏洞存在!")
            else:
                print(f"[-] {vuln_id} 漏洞不存在")
        else:
            print(f"[-] {vuln_id} 扫描失败: {stderr}")
        
        self.scan_results['vulnerabilities'][vuln_id] = vulnerability_result
        return vulnerability_result
    
    def scan_all_vulnerabilities(self, selected_vulns=None):
        """扫描所有漏洞或指定漏洞"""
        if selected_vulns:
            modules_to_scan = {k: v for k, v in self.scan_modules.items() 
                             if k in selected_vulns}
        else:
            modules_to_scan = self.scan_modules
        
        print(f"[*] 开始扫描 {len(modules_to_scan)} 个漏洞模块...")
        
        vulnerable_count = 0
        for vuln_id, module_info in modules_to_scan.items():
            result = self.scan_vulnerability(vuln_id, module_info)
            if result['vulnerable']:
                vulnerable_count += 1
        
        print(f"\n[*] 扫描完成，发现 {vulnerable_count} 个漏洞")
        return vulnerable_count
    
    def collect_sensitive_info(self):
        """收集敏感信息"""
        print(f"\n[*] 收集敏感信息...")
        
        args = [self.target_url, '--timeout', str(self.timeout), '--json']
        returncode, stdout, stderr = self.run_script('jenkins_info_disclosure.py', args)
        
        if returncode == 0:
            try:
                info_data = json.loads(stdout)
                self.scan_results['sensitive_info'] = info_data
                
                if info_data.get('credentials'):
                    print(f"[+] 发现 {len(info_data['credentials'])} 个凭据")
                if info_data.get('config_files'):
                    print(f"[+] 发现 {len(info_data['config_files'])} 个配置文件")
                if info_data.get('api_tokens'):
                    print(f"[+] 发现 {len(info_data['api_tokens'])} 个API令牌")
                    
            except json.JSONDecodeError:
                print(f"[-] 敏感信息解析失败")
        else:
            print(f"[-] 敏感信息收集失败: {stderr}")
    
    def generate_recommendations(self):
        """生成安全建议"""
        recommendations = []
        
        # 基于发现的漏洞生成建议
        for vuln_id, vuln_info in self.scan_results['vulnerabilities'].items():
            if vuln_info['vulnerable']:
                if vuln_id == 'CVE-2024-23897':
                    recommendations.append("立即升级Jenkins到2.442或更高版本")
                elif vuln_id == 'CVE-2019-1003000':
                    recommendations.append("升级Script Security插件到1.50或更高版本")
                elif vuln_id == 'CVE-2017-1000353':
                    recommendations.append("升级Jenkins到2.57或更高版本")
                elif vuln_id == 'script_console':
                    recommendations.append("配置脚本控制台访问权限，限制管理员访问")
                elif vuln_id == 'info_disclosure':
                    recommendations.append("配置适当的访问控制，防止敏感信息泄露")
        
        # 通用安全建议
        recommendations.extend([
            "启用CSRF保护",
            "配置强密码策略",
            "定期更新Jenkins和插件",
            "启用审计日志",
            "限制网络访问",
            "禁用不必要的插件"
        ])
        
        self.scan_results['recommendations'] = list(set(recommendations))
    
    def generate_report(self, output_file=None):
        """生成扫描报告"""
        self.generate_recommendations()
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
            print(f"\n[*] 扫描报告已保存到: {output_file}")
        
        # 打印摘要报告
        print(f"\n" + "="*80)
        print(f"Jenkins安全扫描报告")
        print(f"="*80)
        print(f"目标: {self.scan_results['target']}")
        print(f"扫描时间: {self.scan_results['scan_time']}")
        print(f"Jenkins版本: {self.scan_results['jenkins_version']}")
        
        # 漏洞统计
        total_vulns = len(self.scan_results['vulnerabilities'])
        vulnerable_count = sum(1 for v in self.scan_results['vulnerabilities'].values() 
                             if v['vulnerable'])
        
        print(f"\n漏洞统计:")
        print(f"  总扫描项: {total_vulns}")
        print(f"  发现漏洞: {vulnerable_count}")
        print(f"  安全状态: {'危险' if vulnerable_count > 0 else '相对安全'}")
        
        # 详细漏洞信息
        if vulnerable_count > 0:
            print(f"\n发现的漏洞:")
            for vuln_id, vuln_info in self.scan_results['vulnerabilities'].items():
                if vuln_info['vulnerable']:
                    print(f"  [!] {vuln_id}: {vuln_info['description']} ({vuln_info['severity']})")
        
        # 敏感信息统计
        sensitive_info = self.scan_results.get('sensitive_info', {})
        if sensitive_info:
            print(f"\n敏感信息:")
            for category, items in sensitive_info.items():
                if items:
                    print(f"  {category}: {len(items)}个")
        
        # 安全建议
        if self.scan_results['recommendations']:
            print(f"\n安全建议:")
            for i, rec in enumerate(self.scan_results['recommendations'][:5], 1):
                print(f"  {i}. {rec}")
        
        print(f"="*80)

def main():
    parser = argparse.ArgumentParser(description='Jenkins综合漏洞扫描器')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--vulns', help='指定要扫描的漏洞，用逗号分隔')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不利用')
    parser.add_argument('--output', '-o', help='输出报告文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 创建扫描器实例
    scanner = JenkinsComprehensiveScanner(args.target, args.timeout)
    
    try:
        # 检测Jenkins版本
        scanner.detect_jenkins_version()
        
        # 扫描漏洞
        selected_vulns = args.vulns.split(',') if args.vulns else None
        scanner.scan_all_vulnerabilities(selected_vulns)
        
        # 收集敏感信息
        scanner.collect_sensitive_info()
        
        # 生成报告
        scanner.generate_report(args.output)
        
    except KeyboardInterrupt:
        print(f"\n[!] 扫描被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 扫描过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()