#!/usr/bin/env python3
"""
Jenkins 信息收集工具
全面收集Jenkins服务器的配置、任务、用户等信息
"""

import sys
import requests
import argparse
import json
import re
import warnings
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsInfoScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.info_results = {
            'basic_info': {},
            'jobs': [],
            'users': [],
            'plugins': [],
            'nodes': [],
            'system_info': {},
            'security_info': {},
            'accessible_endpoints': []
        }
    
    def collect_basic_info(self):
        """收集基本信息"""
        print(f"[*] 收集基本信息...")
        
        try:
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            basic_info = {
                'status_code': response.status_code,
                'server_header': response.headers.get('Server', ''),
                'jenkins_version': response.headers.get('X-Jenkins', ''),
                'content_type': response.headers.get('Content-Type', ''),
                'title': '',
                'authentication_required': False
            }
            
            # 提取页面标题
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                title_tag = soup.find('title')
                if title_tag:
                    basic_info['title'] = title_tag.get_text().strip()
                
                # 检查是否需要认证
                if 'login' in response.text.lower() or 'sign in' in response.text.lower():
                    basic_info['authentication_required'] = True
            
            self.info_results['basic_info'] = basic_info
            print(f"[+] 基本信息收集完成")
            
        except Exception as e:
            print(f"[-] 基本信息收集失败: {e}")
    
    def collect_jobs_info(self):
        """收集任务信息"""
        print(f"[*] 收集任务信息...")
        
        try:
            # 尝试通过API获取任务列表
            api_url = urljoin(self.target_url, '/api/json?tree=jobs[name,url,buildable,color,description]')
            response = self.session.get(api_url, timeout=self.timeout)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    jobs = data.get('jobs', [])
                    
                    print(f"[+] 发现 {len(jobs)} 个任务")
                    
                    for job in jobs:
                        job_info = {
                            'name': job.get('name', ''),
                            'url': job.get('url', ''),
                            'buildable': job.get('buildable', False),
                            'color': job.get('color', ''),
                            'description': job.get('description', ''),
                            'builds': []
                        }
                        
                        # 获取最近的构建信息
                        job_name = job_info['name']
                        builds_url = urljoin(self.target_url, f'/job/{job_name}/api/json?tree=builds[number,url,result,timestamp,duration]')
                        
                        try:
                            builds_response = self.session.get(builds_url, timeout=5)
                            if builds_response.status_code == 200:
                                builds_data = builds_response.json()
                                builds = builds_data.get('builds', [])[:5]  # 只获取最近5次构建
                                job_info['builds'] = builds
                        except:
                            pass
                        
                        self.info_results['jobs'].append(job_info)
                
                except json.JSONDecodeError:
                    print(f"[-] 无法解析任务API响应")
            else:
                print(f"[-] 无法访问任务API (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"[-] 任务信息收集失败: {e}")
    
    def collect_users_info(self):
        """收集用户信息"""
        print(f"[*] 收集用户信息...")
        
        try:
            # 尝试访问用户列表
            users_urls = [
                '/asynchPeople/',
                '/people/',
                '/securityRealm/user/',
            ]
            
            for users_url in users_urls:
                try:
                    url = urljoin(self.target_url, users_url)
                    response = self.session.get(url, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        print(f"[+] 发现可访问的用户页面: {users_url}")
                        
                        # 解析用户信息
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # 查找用户链接
                        user_links = soup.find_all('a', href=re.compile(r'/user/'))
                        
                        for link in user_links:
                            user_name = link.get_text().strip()
                            user_url = link.get('href', '')
                            
                            if user_name and user_name not in [u['name'] for u in self.info_results['users']]:
                                self.info_results['users'].append({
                                    'name': user_name,
                                    'url': user_url,
                                    'source': users_url
                                })
                        
                        break
                
                except Exception as e:
                    continue
            
            print(f"[+] 发现 {len(self.info_results['users'])} 个用户")
                
        except Exception as e:
            print(f"[-] 用户信息收集失败: {e}")
    
    def collect_plugins_info(self):
        """收集插件信息"""
        print(f"[*] 收集插件信息...")
        
        try:
            # 尝试访问插件管理API
            plugins_url = urljoin(self.target_url, '/pluginManager/api/json?depth=1')
            response = self.session.get(plugins_url, timeout=self.timeout)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    plugins = data.get('plugins', [])
                    
                    print(f"[+] 发现 {len(plugins)} 个插件")
                    
                    for plugin in plugins:
                        plugin_info = {
                            'shortName': plugin.get('shortName', ''),
                            'longName': plugin.get('longName', ''),
                            'version': plugin.get('version', ''),
                            'enabled': plugin.get('enabled', False),
                            'active': plugin.get('active', False),
                            'hasUpdate': plugin.get('hasUpdate', False),
                            'url': plugin.get('url', '')
                        }
                        self.info_results['plugins'].append(plugin_info)
                
                except json.JSONDecodeError:
                    print(f"[-] 无法解析插件API响应")
            else:
                print(f"[-] 无法访问插件API (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"[-] 插件信息收集失败: {e}")
    
    def collect_nodes_info(self):
        """收集节点信息"""
        print(f"[*] 收集节点信息...")
        
        try:
            # 尝试访问计算机节点API
            nodes_url = urljoin(self.target_url, '/computer/api/json?depth=1')
            response = self.session.get(nodes_url, timeout=self.timeout)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    computers = data.get('computer', [])
                    
                    print(f"[+] 发现 {len(computers)} 个节点")
                    
                    for computer in computers:
                        node_info = {
                            'displayName': computer.get('displayName', ''),
                            'numExecutors': computer.get('numExecutors', 0),
                            'offline': computer.get('offline', False),
                            'temporarilyOffline': computer.get('temporarilyOffline', False),
                            'description': computer.get('description', ''),
                            'monitorData': computer.get('monitorData', {})
                        }
                        self.info_results['nodes'].append(node_info)
                
                except json.JSONDecodeError:
                    print(f"[-] 无法解析节点API响应")
            else:
                print(f"[-] 无法访问节点API (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"[-] 节点信息收集失败: {e}")
    
    def collect_system_info(self):
        """收集系统信息"""
        print(f"[*] 收集系统信息...")
        
        try:
            # 尝试访问系统信息页面
            system_urls = [
                '/systemInfo',
                '/manage/systemInfo',
                '/computer/(master)/systemInfo'
            ]
            
            for system_url in system_urls:
                try:
                    url = urljoin(self.target_url, system_url)
                    response = self.session.get(url, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        print(f"[+] 发现可访问的系统信息页面: {system_url}")
                        
                        # 解析系统信息
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # 查找系统属性表格
                        tables = soup.find_all('table')
                        system_properties = {}
                        
                        for table in tables:
                            rows = table.find_all('tr')
                            for row in rows:
                                cells = row.find_all(['td', 'th'])
                                if len(cells) >= 2:
                                    key = cells[0].get_text().strip()
                                    value = cells[1].get_text().strip()
                                    if key and value:
                                        system_properties[key] = value
                        
                        self.info_results['system_info'] = {
                            'source': system_url,
                            'properties': system_properties
                        }
                        break
                
                except Exception as e:
                    continue
                
        except Exception as e:
            print(f"[-] 系统信息收集失败: {e}")
    
    def collect_security_info(self):
        """收集安全配置信息"""
        print(f"[*] 收集安全配置信息...")
        
        try:
            security_info = {
                'authentication_enabled': False,
                'authorization_strategy': 'Unknown',
                'security_realm': 'Unknown',
                'csrf_protection': False,
                'accessible_management_pages': []
            }
            
            # 检查管理页面
            management_pages = [
                '/manage',
                '/configure',
                '/configureSecurity',
                '/pluginManager',
                '/systemInfo',
                '/log',
                '/load-statistics',
                '/about'
            ]
            
            for page in management_pages:
                try:
                    url = urljoin(self.target_url, page)
                    response = self.session.get(url, timeout=5)
                    
                    if response.status_code == 200:
                        security_info['accessible_management_pages'].append(page)
                        
                        # 检查CSRF保护
                        if 'crumb' in response.text.lower() or 'csrf' in response.text.lower():
                            security_info['csrf_protection'] = True
                
                except:
                    continue
            
            # 检查认证状态
            login_url = urljoin(self.target_url, '/login')
            try:
                login_response = self.session.get(login_url, timeout=5)
                if login_response.status_code == 200:
                    if 'login' in login_response.text.lower():
                        security_info['authentication_enabled'] = True
            except:
                pass
            
            self.info_results['security_info'] = security_info
            print(f"[+] 安全配置信息收集完成")
            
        except Exception as e:
            print(f"[-] 安全配置信息收集失败: {e}")
    
    def scan_accessible_endpoints(self):
        """扫描可访问的端点"""
        print(f"[*] 扫描可访问端点...")
        
        endpoints = [
            '/api/json',
            '/api/xml',
            '/cli',
            '/script',
            '/scriptText',
            '/manage',
            '/configure',
            '/systemInfo',
            '/log',
            '/queue/api/json',
            '/computer/api/json',
            '/pluginManager/api/json',
            '/view/all/api/json',
            '/crumbIssuer/api/json',
            '/whoAmI/api/json',
            '/people',
            '/asynchPeople',
            '/about'
        ]
        
        accessible_endpoints = []
        
        for endpoint in endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    accessible_endpoints.append({
                        'endpoint': endpoint,
                        'status_code': response.status_code,
                        'content_type': response.headers.get('Content-Type', ''),
                        'content_length': len(response.text)
                    })
                    print(f"[+] 可访问端点: {endpoint}")
            
            except:
                continue
        
        self.info_results['accessible_endpoints'] = accessible_endpoints
        print(f"[+] 发现 {len(accessible_endpoints)} 个可访问端点")
    
    def run_full_scan(self):
        """运行完整信息收集"""
        print("=" * 60)
        print("Jenkins信息收集扫描")
        print("=" * 60)
        print(f"目标: {self.target_url}")
        print("=" * 60)
        
        # 执行各种信息收集
        self.collect_basic_info()
        self.collect_jobs_info()
        self.collect_users_info()
        self.collect_plugins_info()
        self.collect_nodes_info()
        self.collect_system_info()
        self.collect_security_info()
        self.scan_accessible_endpoints()
        
        return self.info_results
    
    def print_summary(self):
        """打印收集结果摘要"""
        print("\n" + "=" * 60)
        print("信息收集结果摘要")
        print("=" * 60)
        
        # 基本信息
        basic = self.info_results['basic_info']
        print(f"\n[*] 基本信息:")
        print(f"  状态码: {basic.get('status_code', 'Unknown')}")
        print(f"  Jenkins版本: {basic.get('jenkins_version', 'Unknown')}")
        print(f"  页面标题: {basic.get('title', 'Unknown')}")
        print(f"  需要认证: {'是' if basic.get('authentication_required', False) else '否'}")
        
        # 任务信息
        jobs_count = len(self.info_results['jobs'])
        print(f"\n[*] 任务信息: 发现 {jobs_count} 个任务")
        if jobs_count > 0:
            for job in self.info_results['jobs'][:5]:  # 显示前5个
                print(f"  - {job['name']} ({job['color']})")
        
        # 用户信息
        users_count = len(self.info_results['users'])
        print(f"\n[*] 用户信息: 发现 {users_count} 个用户")
        if users_count > 0:
            for user in self.info_results['users'][:10]:  # 显示前10个
                print(f"  - {user['name']}")
        
        # 插件信息
        plugins_count = len(self.info_results['plugins'])
        print(f"\n[*] 插件信息: 发现 {plugins_count} 个插件")
        if plugins_count > 0:
            enabled_plugins = [p for p in self.info_results['plugins'] if p.get('enabled', False)]
            print(f"  启用的插件: {len(enabled_plugins)} 个")
        
        # 节点信息
        nodes_count = len(self.info_results['nodes'])
        print(f"\n[*] 节点信息: 发现 {nodes_count} 个节点")
        if nodes_count > 0:
            online_nodes = [n for n in self.info_results['nodes'] if not n.get('offline', True)]
            print(f"  在线节点: {len(online_nodes)} 个")
        
        # 安全信息
        security = self.info_results['security_info']
        print(f"\n[*] 安全配置:")
        print(f"  认证启用: {'是' if security.get('authentication_enabled', False) else '否'}")
        print(f"  CSRF保护: {'是' if security.get('csrf_protection', False) else '否'}")
        management_pages = len(security.get('accessible_management_pages', []))
        print(f"  可访问管理页面: {management_pages} 个")
        
        # 可访问端点
        endpoints_count = len(self.info_results['accessible_endpoints'])
        print(f"\n[*] 可访问端点: {endpoints_count} 个")

def main():
    parser = argparse.ArgumentParser(description='Jenkins信息收集工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('--jobs-only', action='store_true', help='仅收集任务信息')
    parser.add_argument('--api-only', action='store_true', help='仅收集API信息')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--json', action='store_true', help='以JSON格式输出结果')
    
    args = parser.parse_args()
    
    if not args.target.startswith(('http://', 'https://')):
        args.target = 'http://' + args.target
    
    scanner = JenkinsInfoScanner(args.target, args.timeout)
    
    print("=" * 60)
    print("Jenkins信息收集工具")
    print("=" * 60)
    print(f"目标: {args.target}")
    print(f"超时: {args.timeout}秒")
    print("=" * 60)
    
    # 执行扫描
    if args.jobs_only:
        scanner.collect_basic_info()
        scanner.collect_jobs_info()
    elif args.api_only:
        scanner.collect_basic_info()
        scanner.scan_accessible_endpoints()
    else:
        scanner.run_full_scan()
    
    # 输出结果
    if args.json:
        print(json.dumps(scanner.info_results, ensure_ascii=False, indent=2))
    else:
        scanner.print_summary()

if __name__ == '__main__':
    main()