# Jenkins 漏洞扫描工具依赖包
# 安装命令: pip install -r requirements.txt

# HTTP请求库
requests>=2.25.1

# URL解析和处理
urllib3>=1.26.0

# 命令行参数解析 (Python内置，但列出以确保兼容性)
argparse

# 正则表达式 (Python内置)
re

# 系统相关 (Python内置)
sys
os
subprocess
threading
time
socket
struct

# 编码处理 (Python内置)
base64
binascii

# 文件和路径处理 (Python内置)
tempfile

# 日期时间处理 (Python内置)
datetime

# JSON处理 (Python内置)
json

# 警告处理 (Python内置)
warnings

# XML解析 (Python内置)
xml.etree.ElementTree

# HTML解析
beautifulsoup4>=4.9.0

# YAML解析 (用于Jenkins配置文件)
PyYAML>=5.4.0

# 可选依赖 (用于增强功能)
# 如果需要更好的HTTP/2支持
# httpx>=0.24.0

# 如果需要异步支持
# aiohttp>=3.8.0

# 如果需要更好的SSL/TLS支持
# cryptography>=3.4.0

# 如果需要更好的并发控制
# concurrent.futures (Python 3.2+内置)

# 注意事项:
# 1. 大部分依赖都是Python标准库，无需额外安装
# 2. 主要外部依赖是requests、beautifulsoup4、PyYAML库
# 3. 建议Python版本: 3.6+
# 4. 某些功能可能需要特定的网络权限
# 5. 某些反序列化漏洞可能需要ysoserial.jar文件（可选）