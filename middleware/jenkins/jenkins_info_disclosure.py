#!/usr/bin/env python3
"""
Jenkins 敏感信息泄露扫描工具
检测Jenkins配置文件、凭据、构建历史等敏感信息泄露
"""

import sys
import requests
import argparse
import json
import re
import base64
import warnings
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsInfoDisclosure:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 敏感文件路径
        self.sensitive_paths = [
            # 配置文件
            '/config.xml',
            '/jenkins/config.xml',
            '/var/jenkins_home/config.xml',
            
            # 用户配置
            '/users/users.xml',
            '/jenkins/users/users.xml',
            '/var/jenkins_home/users/users.xml',
            
            # 密钥文件
            '/secrets/master.key',
            '/secrets/hudson.util.Secret',
            '/secrets/initialAdminPassword',
            '/jenkins/secrets/master.key',
            '/jenkins/secrets/hudson.util.Secret',
            '/jenkins/secrets/initialAdminPassword',
            '/var/jenkins_home/secrets/master.key',
            '/var/jenkins_home/secrets/hudson.util.Secret',
            '/var/jenkins_home/secrets/initialAdminPassword',
            
            # 插件配置
            '/plugins/',
            '/jenkins/plugins/',
            '/var/jenkins_home/plugins/',
            
            # 日志文件
            '/logs/',
            '/jenkins/logs/',
            '/var/jenkins_home/logs/',
            
            # 工作空间
            '/workspace/',
            '/jenkins/workspace/',
            '/var/jenkins_home/workspace/',
            
            # 构建历史
            '/jobs/',
            '/jenkins/jobs/',
            '/var/jenkins_home/jobs/',
        ]
        
        # API端点
        self.api_endpoints = [
            '/api/json',
            '/api/xml',
            '/computer/api/json',
            '/queue/api/json',
            '/view/all/api/json',
            '/pluginManager/api/json',
            '/systemInfo',
            '/manage',
            '/configure',
            '/credentials/',
            '/credential-store/',
        ]
        
        # 敏感信息模式
        self.sensitive_patterns = {
            'password': [
                r'password["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'<password>([^<]+)</password>',
                r'PASSWORD["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'api_key': [
                r'api[_-]?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'apikey["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'<apiKey>([^<]+)</apiKey>',
            ],
            'token': [
                r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'<token>([^<]+)</token>',
                r'access[_-]?token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'secret': [
                r'secret["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'<secret>([^<]+)</secret>',
                r'SECRET["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'database': [
                r'jdbc:[\w]+://([^/]+)/(\w+)',
                r'database["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'db[_-]?url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'email': [
                r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            ],
            'ip_address': [
                r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b',
            ],
            'private_key': [
                r'-----BEGIN [A-Z ]+PRIVATE KEY-----',
                r'-----BEGIN RSA PRIVATE KEY-----',
                r'-----BEGIN OPENSSH PRIVATE KEY-----',
            ]
        }
    
    def scan_sensitive_files(self):
        """扫描敏感文件"""
        print(f"[*] 扫描敏感文件...")
        found_files = []
        
        for path in self.sensitive_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 发现可访问文件: {path}")
                    
                    # 分析文件内容
                    sensitive_info = self.extract_sensitive_info(response.text)
                    
                    found_files.append({
                        'path': path,
                        'url': url,
                        'size': len(response.text),
                        'content_type': response.headers.get('Content-Type', ''),
                        'sensitive_info': sensitive_info
                    })
                    
                    # 显示部分内容
                    content_preview = response.text[:500] + ('...' if len(response.text) > 500 else '')
                    print(f"  内容预览:\n{content_preview}")
                    
                    if sensitive_info:
                        print(f"  发现敏感信息: {list(sensitive_info.keys())}")
                
            except Exception as e:
                continue
        
        return found_files
    
    def scan_api_endpoints(self):
        """扫描API端点"""
        print(f"[*] 扫描API端点...")
        accessible_apis = []
        
        for endpoint in self.api_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 发现可访问API: {endpoint}")
                    
                    # 尝试解析JSON响应
                    api_data = None
                    try:
                        if 'application/json' in response.headers.get('Content-Type', ''):
                            api_data = response.json()
                    except:
                        pass
                    
                    accessible_apis.append({
                        'endpoint': endpoint,
                        'url': url,
                        'status_code': response.status_code,
                        'content_type': response.headers.get('Content-Type', ''),
                        'data': api_data,
                        'content_length': len(response.text)
                    })
                    
                    # 显示API信息
                    if api_data:
                        if isinstance(api_data, dict):
                            keys = list(api_data.keys())[:10]  # 显示前10个键
                            print(f"  可用数据字段: {keys}")
                    
            except Exception as e:
                continue
        
        return accessible_apis
    
    def scan_jenkins_jobs(self):
        """扫描Jenkins任务信息"""
        print(f"[*] 扫描Jenkins任务...")
        jobs_info = []
        
        try:
            # 获取所有任务列表
            jobs_url = urljoin(self.target_url, '/api/json?tree=jobs[name,url,buildable,color]')
            response = self.session.get(jobs_url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                jobs = data.get('jobs', [])
                
                print(f"[+] 发现 {len(jobs)} 个任务")
                
                for job in jobs:
                    job_name = job.get('name', '')
                    job_url = job.get('url', '')
                    
                    print(f"  任务: {job_name}")
                    
                    # 获取任务详细信息
                    job_detail = self.get_job_details(job_name)
                    
                    jobs_info.append({
                        'name': job_name,
                        'url': job_url,
                        'buildable': job.get('buildable', False),
                        'color': job.get('color', ''),
                        'details': job_detail
                    })
        
        except Exception as e:
            print(f"[-] 扫描任务失败: {e}")
        
        return jobs_info
    
    def get_job_details(self, job_name):
        """获取任务详细信息"""
        try:
            # 获取任务配置
            config_url = urljoin(self.target_url, f'/job/{job_name}/config.xml')
            config_response = self.session.get(config_url, timeout=self.timeout)
            
            job_details = {
                'config_accessible': config_response.status_code == 200,
                'builds': [],
                'sensitive_info': {}
            }
            
            if config_response.status_code == 200:
                # 提取配置中的敏感信息
                job_details['sensitive_info'] = self.extract_sensitive_info(config_response.text)
            
            # 获取构建历史
            builds_url = urljoin(self.target_url, f'/job/{job_name}/api/json?tree=builds[number,url,result,timestamp]')
            builds_response = self.session.get(builds_url, timeout=self.timeout)
            
            if builds_response.status_code == 200:
                builds_data = builds_response.json()
                builds = builds_data.get('builds', [])[:5]  # 只获取最近5次构建
                
                for build in builds:
                    build_number = build.get('number')
                    
                    # 获取构建日志
                    log_url = urljoin(self.target_url, f'/job/{job_name}/{build_number}/consoleText')
                    log_response = self.session.get(log_url, timeout=self.timeout)
                    
                    build_info = {
                        'number': build_number,
                        'result': build.get('result'),
                        'timestamp': build.get('timestamp'),
                        'log_accessible': log_response.status_code == 200,
                        'log_sensitive_info': {}
                    }
                    
                    if log_response.status_code == 200:
                        # 提取日志中的敏感信息
                        build_info['log_sensitive_info'] = self.extract_sensitive_info(log_response.text)
                    
                    job_details['builds'].append(build_info)
            
            return job_details
            
        except Exception as e:
            return {'error': str(e)}
    
    def extract_sensitive_info(self, content):
        """提取敏感信息"""
        sensitive_info = {}
        
        for info_type, patterns in self.sensitive_patterns.items():
            matches = []
            
            for pattern in patterns:
                found = re.findall(pattern, content, re.IGNORECASE)
                if found:
                    matches.extend(found)
            
            if matches:
                # 去重并限制数量
                unique_matches = list(set(matches))[:10]
                sensitive_info[info_type] = unique_matches
        
        return sensitive_info
    
    def scan_credentials(self):
        """扫描凭据信息"""
        print(f"[*] 扫描凭据信息...")
        credentials_info = []
        
        try:
            # 尝试访问凭据管理页面
            creds_urls = [
                '/credentials/',
                '/credentials/store/system/',
                '/credential-store/domain/_/',
                '/manage/credentials/',
            ]
            
            for creds_url in creds_urls:
                try:
                    url = urljoin(self.target_url, creds_url)
                    response = self.session.get(url, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        print(f"[+] 发现可访问凭据页面: {creds_url}")
                        
                        # 解析HTML页面查找凭据信息
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # 查找凭据表格
                        tables = soup.find_all('table')
                        for table in tables:
                            rows = table.find_all('tr')
                            for row in rows:
                                cells = row.find_all(['td', 'th'])
                                if len(cells) >= 2:
                                    row_text = ' '.join([cell.get_text().strip() for cell in cells])
                                    if any(keyword in row_text.lower() for keyword in ['credential', 'username', 'password', 'key']):
                                        credentials_info.append({
                                            'source': creds_url,
                                            'content': row_text
                                        })
                
                except Exception as e:
                    continue
        
        except Exception as e:
            print(f"[-] 扫描凭据失败: {e}")
        
        return credentials_info
    
    def scan_build_artifacts(self):
        """扫描构建产物"""
        print(f"[*] 扫描构建产物...")
        artifacts_info = []
        
        try:
            # 获取任务列表
            jobs_url = urljoin(self.target_url, '/api/json?tree=jobs[name]')
            response = self.session.get(jobs_url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                jobs = data.get('jobs', [])
                
                for job in jobs[:5]:  # 限制检查前5个任务
                    job_name = job.get('name', '')
                    
                    # 检查最近的构建产物
                    artifacts_url = urljoin(self.target_url, f'/job/{job_name}/lastSuccessfulBuild/api/json?tree=artifacts[*]')
                    artifacts_response = self.session.get(artifacts_url, timeout=self.timeout)
                    
                    if artifacts_response.status_code == 200:
                        artifacts_data = artifacts_response.json()
                        artifacts = artifacts_data.get('artifacts', [])
                        
                        if artifacts:
                            print(f"[+] 任务 {job_name} 发现 {len(artifacts)} 个构建产物")
                            
                            for artifact in artifacts:
                                artifact_info = {
                                    'job': job_name,
                                    'filename': artifact.get('fileName', ''),
                                    'path': artifact.get('relativePath', ''),
                                    'size': artifact.get('size', 0),
                                    'download_url': urljoin(self.target_url, f'/job/{job_name}/lastSuccessfulBuild/artifact/{artifact.get("relativePath", "")}')
                                }
                                artifacts_info.append(artifact_info)
        
        except Exception as e:
            print(f"[-] 扫描构建产物失败: {e}")
        
        return artifacts_info
    
    def check_vulnerability(self):
        """检查信息泄露漏洞"""
        print(f"[*] 检查Jenkins信息泄露...")
        
        vulnerabilities = []
        
        # 检查是否可以访问敏感API
        sensitive_apis = ['/api/json', '/systemInfo', '/manage']
        
        for api in sensitive_apis:
            try:
                url = urljoin(self.target_url, api)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    vulnerabilities.append(f"可访问敏感API: {api}")
            except:
                continue
        
        # 检查是否可以访问配置文件
        config_files = ['/config.xml', '/users/users.xml']
        
        for config_file in config_files:
            try:
                url = urljoin(self.target_url, config_file)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    vulnerabilities.append(f"可访问配置文件: {config_file}")
            except:
                continue
        
        if vulnerabilities:
            return True, f"发现信息泄露漏洞: {', '.join(vulnerabilities)}"
        else:
            return False, "未发现明显的信息泄露漏洞"

def main():
    parser = argparse.ArgumentParser(description='Jenkins敏感信息泄露扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('--files', action='store_true', help='扫描敏感文件')
    parser.add_argument('--apis', action='store_true', help='扫描API端点')
    parser.add_argument('--jobs', action='store_true', help='扫描任务信息')
    parser.add_argument('--credentials', action='store_true', help='扫描凭据信息')
    parser.add_argument('--artifacts', action='store_true', help='扫描构建产物')
    parser.add_argument('--credentials-only', action='store_true', help='仅扫描凭据')
    parser.add_argument('--check-only', action='store_true', help='仅检查是否存在信息泄露')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not args.target.startswith(('http://', 'https://')):
        args.target = 'http://' + args.target
    
    scanner = JenkinsInfoDisclosure(args.target, args.timeout)
    
    print("=" * 60)
    print("Jenkins敏感信息泄露扫描工具")
    print("=" * 60)
    print(f"目标: {args.target}")
    print(f"超时: {args.timeout}秒")
    print("=" * 60)
    
    # 检查信息泄露
    vulnerable, msg = scanner.check_vulnerability()
    print(f"\n[*] 信息泄露检查结果: {msg}")
    
    if args.check_only:
        print("[+] 信息泄露检查完成")
        sys.exit(0)
    
    # 执行相应扫描
    if args.credentials_only:
        credentials = scanner.scan_credentials()
        if credentials:
            print(f"\n[+] 发现 {len(credentials)} 个凭据相关信息")
            for cred in credentials:
                print(f"  来源: {cred['source']}")
                print(f"  内容: {cred['content']}")
        else:
            print("\n[-] 未发现凭据信息")
    
    elif args.files or args.apis or args.jobs or args.credentials or args.artifacts:
        # 执行指定的扫描
        if args.files:
            files = scanner.scan_sensitive_files()
            print(f"\n[*] 敏感文件扫描完成，发现 {len(files)} 个文件")
        
        if args.apis:
            apis = scanner.scan_api_endpoints()
            print(f"\n[*] API端点扫描完成，发现 {len(apis)} 个可访问端点")
        
        if args.jobs:
            jobs = scanner.scan_jenkins_jobs()
            print(f"\n[*] 任务扫描完成，发现 {len(jobs)} 个任务")
        
        if args.credentials:
            credentials = scanner.scan_credentials()
            print(f"\n[*] 凭据扫描完成，发现 {len(credentials)} 个相关信息")
        
        if args.artifacts:
            artifacts = scanner.scan_build_artifacts()
            print(f"\n[*] 构建产物扫描完成，发现 {len(artifacts)} 个产物")
    
    else:
        # 默认执行全面扫描
        print(f"\n[*] 执行全面信息泄露扫描...")
        
        files = scanner.scan_sensitive_files()
        apis = scanner.scan_api_endpoints()
        jobs = scanner.scan_jenkins_jobs()
        credentials = scanner.scan_credentials()
        artifacts = scanner.scan_build_artifacts()
        
        print(f"\n" + "=" * 60)
        print("扫描总结")
        print("=" * 60)
        print(f"敏感文件: {len(files)} 个")
        print(f"API端点: {len(apis)} 个")
        print(f"任务信息: {len(jobs)} 个")
        print(f"凭据信息: {len(credentials)} 个")
        print(f"构建产物: {len(artifacts)} 个")

if __name__ == '__main__':
    main()