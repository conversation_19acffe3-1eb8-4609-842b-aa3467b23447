#!/usr/bin/env python3
"""
Jenkins版本识别工具
通过多种方式识别Jenkins版本和相关组件信息
"""

import sys
import requests
import argparse
import re
import json
import warnings
from urllib.parse import urljoin
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.version_info = {
            'jenkins_version': 'Unknown',
            'server_info': 'Unknown',
            'detection_methods': [],
            'plugins': [],
            'vulnerabilities': []
        }
    
    def detect_from_headers(self):
        """从HTTP头检测版本信息"""
        print(f"[*] 从HTTP头检测版本信息...")
        
        try:
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            # 检查Jenkins相关头
            jenkins_headers = {
                'X-Jenkins': 'Jenkins版本',
                'X-Jenkins-Version': 'Jenkins版本',
                'Server': '服务器信息'
            }
            
            for header, description in jenkins_headers.items():
                if header in response.headers:
                    value = response.headers[header]
                    print(f"[+] 发现{description}: {value}")
                    
                    if 'jenkins' in header.lower():
                        self.version_info['jenkins_version'] = value
                    elif header == 'Server':
                        self.version_info['server_info'] = value
                    
                    self.version_info['detection_methods'].append(f'HTTP头 ({header})')
            
            return len([h for h in jenkins_headers if h in response.headers]) > 0
            
        except Exception as e:
            print(f"[-] HTTP头检测失败: {e}")
            return False
    
    def detect_from_login_page(self):
        """从登录页面检测版本信息"""
        print(f"[*] 从登录页面检测版本信息...")
        
        try:
            login_urls = [
                '/login',
                '/loginEntry',
                '/securityRealm/commenceLogin'
            ]
            
            for login_path in login_urls:
                url = urljoin(self.target_url, login_path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 查找版本信息的正则表达式
                    version_patterns = [
                        r'Jenkins ver\. ([0-9.]+)',
                        r'Jenkins version ([0-9.]+)',
                        r'Jenkins ([0-9.]+)',
                        r'data-version="([0-9.]+)"',
                        r'version:\s*"([0-9.]+)"'
                    ]
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            version = match.group(1)
                            print(f"[+] 从登录页面检测到Jenkins版本: {version}")
                            self.version_info['jenkins_version'] = version
                            self.version_info['detection_methods'].append('登录页面')
                            return True
            
            return False
            
        except Exception as e:
            print(f"[-] 登录页面检测失败: {e}")
            return False
    
    def detect_from_api(self):
        """从API接口检测版本信息"""
        print(f"[*] 从API接口检测版本信息...")
        
        try:
            api_endpoints = [
                '/api/json',
                '/api/xml',
                '/computer/api/json'
            ]
            
            for endpoint in api_endpoints:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    if 'json' in endpoint:
                        try:
                            data = response.json()
                            
                            # 检查版本信息
                            if 'version' in data:
                                version = data['version']
                                print(f"[+] 从API检测到Jenkins版本: {version}")
                                self.version_info['jenkins_version'] = version
                                self.version_info['detection_methods'].append(f'API接口 ({endpoint})')
                                return True
                            
                            # 检查其他可能的版本字段
                            for key in ['jenkinsVersion', 'hudson', 'jenkins']:
                                if key in data and isinstance(data[key], str):
                                    version_match = re.search(r'([0-9.]+)', data[key])
                                    if version_match:
                                        version = version_match.group(1)
                                        print(f"[+] 从API字段{key}检测到版本: {version}")
                                        self.version_info['jenkins_version'] = version
                                        self.version_info['detection_methods'].append(f'API接口 ({endpoint})')
                                        return True
                        
                        except json.JSONDecodeError:
                            continue
                    
                    else:  # XML endpoint
                        content = response.text
                        version_match = re.search(r'<version>([0-9.]+)</version>', content)
                        if version_match:
                            version = version_match.group(1)
                            print(f"[+] 从XML API检测到Jenkins版本: {version}")
                            self.version_info['jenkins_version'] = version
                            self.version_info['detection_methods'].append(f'API接口 ({endpoint})')
                            return True
            
            return False
            
        except Exception as e:
            print(f"[-] API接口检测失败: {e}")
            return False
    
    def detect_from_static_files(self):
        """从静态文件检测版本信息"""
        print(f"[*] 从静态文件检测版本信息...")
        
        try:
            static_files = [
                '/static/jenkins-version.txt',
                '/static/version.txt',
                '/jenkins-version.txt',
                '/version.txt',
                '/static/js/jenkins.js',
                '/static/css/jenkins.css'
            ]
            
            for file_path in static_files:
                url = urljoin(self.target_url, file_path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 查找版本信息
                    version_patterns = [
                        r'([0-9]+\.[0-9]+\.[0-9]+)',
                        r'version["\']?\s*[:=]\s*["\']([0-9.]+)["\']',
                        r'Jenkins\s+([0-9.]+)'
                    ]
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content)
                        if match:
                            version = match.group(1)
                            print(f"[+] 从静态文件检测到版本: {version}")
                            self.version_info['jenkins_version'] = version
                            self.version_info['detection_methods'].append(f'静态文件 ({file_path})')
                            return True
            
            return False
            
        except Exception as e:
            print(f"[-] 静态文件检测失败: {e}")
            return False
    
    def detect_plugins(self):
        """检测已安装的插件"""
        print(f"[*] 检测已安装插件...")
        
        try:
            plugin_endpoints = [
                '/pluginManager/api/json?depth=1',
                '/pluginManager/installed'
            ]
            
            for endpoint in plugin_endpoints:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    if 'json' in endpoint:
                        try:
                            data = response.json()
                            
                            if 'plugins' in data:
                                plugins = data['plugins']
                                print(f"[+] 检测到 {len(plugins)} 个插件")
                                
                                for plugin in plugins[:10]:  # 只显示前10个
                                    plugin_info = {
                                        'name': plugin.get('shortName', 'Unknown'),
                                        'version': plugin.get('version', 'Unknown'),
                                        'enabled': plugin.get('enabled', False)
                                    }
                                    self.version_info['plugins'].append(plugin_info)
                                    print(f"  - {plugin_info['name']} v{plugin_info['version']}")
                                
                                return True
                        
                        except json.JSONDecodeError:
                            continue
                    
                    else:
                        # HTML页面解析
                        content = response.text
                        plugin_matches = re.findall(r'<td[^>]*>([^<]+)</td>\s*<td[^>]*>([0-9.]+)</td>', content)
                        
                        if plugin_matches:
                            print(f"[+] 从HTML页面检测到 {len(plugin_matches)} 个插件")
                            for name, version in plugin_matches[:10]:
                                plugin_info = {
                                    'name': name.strip(),
                                    'version': version.strip(),
                                    'enabled': True
                                }
                                self.version_info['plugins'].append(plugin_info)
                                print(f"  - {plugin_info['name']} v{plugin_info['version']}")
                            
                            return True
            
            return False
            
        except Exception as e:
            print(f"[-] 插件检测失败: {e}")
            return False
    
    def check_vulnerabilities(self):
        """根据版本检查已知漏洞"""
        print(f"[*] 检查已知漏洞...")
        
        if self.version_info['jenkins_version'] == 'Unknown':
            print(f"[-] 无法确定版本，跳过漏洞检查")
            return
        
        try:
            version = self.version_info['jenkins_version']
            version_parts = [int(x) for x in version.split('.')]
            
            # 已知漏洞数据库
            vulnerabilities = [
                {
                    'cve': 'CVE-2024-23897',
                    'description': 'Jenkins CLI任意文件读取',
                    'affected_versions': '≤2.441, LTS≤2.426.2',
                    'check': lambda v: (len(v) >= 3 and v[0] <= 2 and 
                                      (v[1] < 441 or (v[1] == 441 and v[2] == 0)))
                },
                {
                    'cve': 'CVE-2017-1000353',
                    'description': 'Java反序列化RCE',
                    'affected_versions': '≤2.56, LTS≤2.46.1',
                    'check': lambda v: (len(v) >= 2 and v[0] <= 2 and v[1] <= 56)
                },
                {
                    'cve': 'CVE-2016-0792',
                    'description': '远程代码执行',
                    'affected_versions': '≤1.650',
                    'check': lambda v: (len(v) >= 2 and 
                                      (v[0] < 1 or (v[0] == 1 and v[1] <= 650)))
                }
            ]
            
            for vuln in vulnerabilities:
                try:
                    if vuln['check'](version_parts):
                        print(f"[!] 可能存在漏洞: {vuln['cve']} - {vuln['description']}")
                        self.version_info['vulnerabilities'].append({
                            'cve': vuln['cve'],
                            'description': vuln['description'],
                            'affected_versions': vuln['affected_versions']
                        })
                except:
                    continue
            
            if not self.version_info['vulnerabilities']:
                print(f"[+] 未发现已知版本漏洞")
        
        except Exception as e:
            print(f"[-] 漏洞检查失败: {e}")
    
    def generate_report(self):
        """生成检测报告"""
        print(f"\n" + "="*60)
        print(f"Jenkins版本检测报告")
        print(f"="*60)
        print(f"目标: {self.target_url}")
        print(f"Jenkins版本: {self.version_info['jenkins_version']}")
        print(f"服务器信息: {self.version_info['server_info']}")
        
        if self.version_info['detection_methods']:
            print(f"\n检测方法:")
            for method in self.version_info['detection_methods']:
                print(f"  - {method}")
        
        if self.version_info['plugins']:
            print(f"\n已安装插件 (前10个):")
            for plugin in self.version_info['plugins'][:10]:
                status = "启用" if plugin['enabled'] else "禁用"
                print(f"  - {plugin['name']} v{plugin['version']} ({status})")
        
        if self.version_info['vulnerabilities']:
            print(f"\n可能存在的漏洞:")
            for vuln in self.version_info['vulnerabilities']:
                print(f"  [!] {vuln['cve']}: {vuln['description']}")
                print(f"      影响版本: {vuln['affected_versions']}")
        
        print(f"="*60)

def main():
    parser = argparse.ArgumentParser(description='Jenkins版本识别工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--json', action='store_true', help='JSON格式输出')
    parser.add_argument('--output', '-o', help='输出结果到文件')
    
    args = parser.parse_args()
    
    # 创建检测器实例
    detector = JenkinsVersionDetector(args.target, args.timeout)
    
    try:
        # 执行各种检测方法
        detector.detect_from_headers()
        detector.detect_from_login_page()
        detector.detect_from_api()
        detector.detect_from_static_files()
        detector.detect_plugins()
        detector.check_vulnerabilities()
        
        # 生成报告
        if args.json:
            result = json.dumps(detector.version_info, ensure_ascii=False, indent=2)
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(result)
                print(f"[*] 结果已保存到: {args.output}")
            else:
                print(result)
        else:
            detector.generate_report()
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(detector.version_info, f, ensure_ascii=False, indent=2)
                print(f"\n[*] 详细结果已保存到: {args.output}")
        
    except KeyboardInterrupt:
        print(f"\n[!] 检测被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 检测过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()