#!/usr/bin/env python3
"""
Jenkins插件漏洞扫描工具
扫描Jenkins已安装插件的安全漏洞
"""

import sys
import requests
import argparse
import json
import re
import warnings
from urllib.parse import urljoin
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsPluginScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 已知插件漏洞数据库
        self.plugin_vulnerabilities = {
            'script-security': {
                'versions': ['1.49', '1.48', '1.47'],
                'vulnerabilities': [
                    {
                        'cve': 'CVE-2019-1003000',
                        'description': 'Script Security插件沙箱绕过',
                        'severity': 'Critical',
                        'affected_versions': '≤1.49'
                    }
                ]
            },
            'build-timeout': {
                'versions': ['1.20', '1.19'],
                'vulnerabilities': [
                    {
                        'cve': 'CVE-2019-10320',
                        'description': 'Build Timeout插件XSS漏洞',
                        'severity': 'Medium',
                        'affected_versions': '≤1.20'
                    }
                ]
            },
            'git': {
                'versions': ['4.0.0', '3.12.1'],
                'vulnerabilities': [
                    {
                        'cve': 'CVE-2019-10392',
                        'description': 'Git插件凭据泄露',
                        'severity': 'High',
                        'affected_versions': '≤4.0.0'
                    }
                ]
            },
            'pipeline-stage-view': {
                'versions': ['2.13', '2.12'],
                'vulnerabilities': [
                    {
                        'cve': 'CVE-2019-10431',
                        'description': 'Pipeline Stage View插件XSS',
                        'severity': 'Medium',
                        'affected_versions': '≤2.13'
                    }
                ]
            }
        }
        
        self.scan_results = {
            'installed_plugins': [],
            'vulnerable_plugins': [],
            'recommendations': []
        }
    
    def get_installed_plugins(self):
        """获取已安装的插件列表"""
        print(f"[*] 获取已安装插件列表...")
        
        plugin_endpoints = [
            '/pluginManager/api/json?depth=1',
            '/pluginManager/installed',
            '/manage/pluginManager/installed'
        ]
        
        for endpoint in plugin_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    if 'json' in endpoint:
                        return self.parse_json_plugins(response.text)
                    else:
                        return self.parse_html_plugins(response.text)
                
                elif response.status_code == 403:
                    print(f"[!] 插件管理页面需要权限: {endpoint}")
                elif response.status_code == 401:
                    print(f"[!] 插件管理页面需要认证: {endpoint}")
                
            except Exception as e:
                continue
        
        print(f"[-] 无法获取插件列表")
        return []
    
    def parse_json_plugins(self, json_content):
        """解析JSON格式的插件信息"""
        try:
            data = json.loads(json_content)
            plugins = []
            
            if 'plugins' in data:
                for plugin in data['plugins']:
                    plugin_info = {
                        'name': plugin.get('shortName', 'Unknown'),
                        'display_name': plugin.get('displayName', 'Unknown'),
                        'version': plugin.get('version', 'Unknown'),
                        'enabled': plugin.get('enabled', False),
                        'active': plugin.get('active', False),
                        'has_update': plugin.get('hasUpdate', False),
                        'url': plugin.get('url', '')
                    }
                    plugins.append(plugin_info)
                
                print(f"[+] 从JSON API获取到 {len(plugins)} 个插件")
                return plugins
            
        except json.JSONDecodeError as e:
            print(f"[-] JSON解析失败: {e}")
        
        return []
    
    def parse_html_plugins(self, html_content):
        """解析HTML格式的插件信息"""
        try:
            plugins = []
            
            # 使用正则表达式提取插件信息
            plugin_patterns = [
                r'<tr[^>]*>\s*<td[^>]*>\s*<input[^>]*name="plugin\.([^"]+)"[^>]*>\s*</td>\s*<td[^>]*>([^<]+)</td>\s*<td[^>]*>([^<]+)</td>',
                r'plugin\.([^"]+).*?<td[^>]*>([^<]+)</td>.*?<td[^>]*>([0-9.]+)</td>'
            ]
            
            for pattern in plugin_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL)
                
                if matches:
                    for match in matches:
                        plugin_info = {
                            'name': match[0].strip(),
                            'display_name': match[1].strip(),
                            'version': match[2].strip(),
                            'enabled': True,
                            'active': True,
                            'has_update': False,
                            'url': ''
                        }
                        plugins.append(plugin_info)
                    
                    print(f"[+] 从HTML页面解析到 {len(plugins)} 个插件")
                    return plugins
            
            # 尝试其他解析方法
            lines = html_content.split('\n')
            for line in lines:
                if 'plugin.' in line and 'version' in line.lower():
                    # 简单的插件信息提取
                    plugin_match = re.search(r'plugin\.([^"\'>\s]+)', line)
                    version_match = re.search(r'([0-9]+\.[0-9]+(?:\.[0-9]+)?)', line)
                    
                    if plugin_match and version_match:
                        plugin_info = {
                            'name': plugin_match.group(1),
                            'display_name': plugin_match.group(1),
                            'version': version_match.group(1),
                            'enabled': True,
                            'active': True,
                            'has_update': False,
                            'url': ''
                        }
                        plugins.append(plugin_info)
            
            if plugins:
                print(f"[+] 从HTML内容提取到 {len(plugins)} 个插件")
                return plugins
            
        except Exception as e:
            print(f"[-] HTML解析失败: {e}")
        
        return []
    
    def check_plugin_vulnerabilities(self, plugins):
        """检查插件漏洞"""
        print(f"[*] 检查插件安全漏洞...")
        
        vulnerable_plugins = []
        
        for plugin in plugins:
            plugin_name = plugin['name']
            plugin_version = plugin['version']
            
            # 检查已知漏洞数据库
            if plugin_name in self.plugin_vulnerabilities:
                vuln_data = self.plugin_vulnerabilities[plugin_name]
                
                for vulnerability in vuln_data['vulnerabilities']:
                    if self.is_version_vulnerable(plugin_version, vulnerability['affected_versions']):
                        vulnerable_plugin = {
                            'plugin': plugin,
                            'vulnerability': vulnerability
                        }
                        vulnerable_plugins.append(vulnerable_plugin)
                        
                        print(f"[!] 发现漏洞插件: {plugin_name} v{plugin_version}")
                        print(f"    CVE: {vulnerability['cve']}")
                        print(f"    描述: {vulnerability['description']}")
                        print(f"    严重程度: {vulnerability['severity']}")
            
            # 检查插件是否有更新
            if plugin.get('has_update', False):
                print(f"[!] 插件有可用更新: {plugin_name} v{plugin_version}")
        
        return vulnerable_plugins
    
    def is_version_vulnerable(self, current_version, affected_versions):
        """检查版本是否受漏洞影响"""
        try:
            if '≤' in affected_versions:
                # 小于等于某个版本
                max_version = affected_versions.replace('≤', '').strip()
                return self.compare_versions(current_version, max_version) <= 0
            elif '≥' in affected_versions:
                # 大于等于某个版本
                min_version = affected_versions.replace('≥', '').strip()
                return self.compare_versions(current_version, min_version) >= 0
            elif '-' in affected_versions:
                # 版本范围
                min_ver, max_ver = affected_versions.split('-')
                return (self.compare_versions(current_version, min_ver.strip()) >= 0 and
                       self.compare_versions(current_version, max_ver.strip()) <= 0)
            else:
                # 精确版本匹配
                return current_version == affected_versions.strip()
        
        except Exception:
            return False
    
    def compare_versions(self, version1, version2):
        """比较版本号"""
        try:
            v1_parts = [int(x) for x in version1.split('.')]
            v2_parts = [int(x) for x in version2.split('.')]
            
            # 补齐版本号长度
            max_len = max(len(v1_parts), len(v2_parts))
            v1_parts.extend([0] * (max_len - len(v1_parts)))
            v2_parts.extend([0] * (max_len - len(v2_parts)))
            
            for i in range(max_len):
                if v1_parts[i] < v2_parts[i]:
                    return -1
                elif v1_parts[i] > v2_parts[i]:
                    return 1
            
            return 0
        
        except Exception:
            return 0
    
    def scan_plugin_configurations(self, plugins):
        """扫描插件配置安全问题"""
        print(f"[*] 扫描插件配置安全问题...")
        
        config_issues = []
        
        for plugin in plugins:
            plugin_name = plugin['name']
            
            # 检查危险插件
            dangerous_plugins = [
                'build-timeout',
                'script-security',
                'groovy',
                'pipeline-groovy',
                'workflow-cps'
            ]
            
            if plugin_name in dangerous_plugins and plugin['enabled']:
                config_issues.append({
                    'plugin': plugin_name,
                    'issue': '危险插件已启用',
                    'description': f'{plugin_name}插件可能存在安全风险',
                    'severity': 'Medium'
                })
            
            # 检查过时插件
            if plugin.get('has_update', False):
                config_issues.append({
                    'plugin': plugin_name,
                    'issue': '插件版本过时',
                    'description': f'{plugin_name}插件有可用更新',
                    'severity': 'Low'
                })
        
        if config_issues:
            print(f"[!] 发现 {len(config_issues)} 个配置问题")
            for issue in config_issues:
                print(f"  - {issue['plugin']}: {issue['issue']} ({issue['severity']})")
        
        return config_issues
    
    def generate_recommendations(self):
        """生成安全建议"""
        recommendations = []
        
        if self.scan_results['vulnerable_plugins']:
            recommendations.append("立即更新存在漏洞的插件到最新版本")
            recommendations.append("定期检查插件安全公告")
        
        recommendations.extend([
            "禁用不必要的插件",
            "定期更新所有插件",
            "监控插件安全漏洞",
            "限制插件安装权限",
            "审查插件配置",
            "使用插件安全扫描工具"
        ])
        
        self.scan_results['recommendations'] = recommendations
    
    def generate_report(self):
        """生成扫描报告"""
        print(f"\n" + "="*60)
        print(f"Jenkins插件安全扫描报告")
        print(f"="*60)
        print(f"目标: {self.target_url}")
        
        # 插件统计
        total_plugins = len(self.scan_results['installed_plugins'])
        vulnerable_count = len(self.scan_results['vulnerable_plugins'])
        
        print(f"\n插件统计:")
        print(f"  已安装插件: {total_plugins}")
        print(f"  存在漏洞: {vulnerable_count}")
        print(f"  安全状态: {'危险' if vulnerable_count > 0 else '相对安全'}")
        
        # 漏洞详情
        if self.scan_results['vulnerable_plugins']:
            print(f"\n发现的漏洞插件:")
            for vuln_plugin in self.scan_results['vulnerable_plugins']:
                plugin = vuln_plugin['plugin']
                vuln = vuln_plugin['vulnerability']
                print(f"  [!] {plugin['name']} v{plugin['version']}")
                print(f"      CVE: {vuln['cve']}")
                print(f"      描述: {vuln['description']}")
                print(f"      严重程度: {vuln['severity']}")
        
        # 安全建议
        if self.scan_results['recommendations']:
            print(f"\n安全建议:")
            for i, rec in enumerate(self.scan_results['recommendations'][:5], 1):
                print(f"  {i}. {rec}")
        
        print(f"="*60)

def main():
    parser = argparse.ArgumentParser(description='Jenkins插件漏洞扫描工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--output', '-o', help='输出结果到文件')
    parser.add_argument('--json', action='store_true', help='JSON格式输出')
    
    args = parser.parse_args()
    
    # 创建插件扫描器实例
    scanner = JenkinsPluginScanner(args.target, args.timeout)
    
    try:
        # 获取已安装插件
        plugins = scanner.get_installed_plugins()
        scanner.scan_results['installed_plugins'] = plugins
        
        if plugins:
            # 检查插件漏洞
            vulnerable_plugins = scanner.check_plugin_vulnerabilities(plugins)
            scanner.scan_results['vulnerable_plugins'] = vulnerable_plugins
            
            # 扫描配置问题
            config_issues = scanner.scan_plugin_configurations(plugins)
            
            # 生成建议
            scanner.generate_recommendations()
            
            # 生成报告
            if args.json:
                result = json.dumps(scanner.scan_results, ensure_ascii=False, indent=2)
                if args.output:
                    with open(args.output, 'w', encoding='utf-8') as f:
                        f.write(result)
                    print(f"[*] 结果已保存到: {args.output}")
                else:
                    print(result)
            else:
                scanner.generate_report()
                
                if args.output:
                    with open(args.output, 'w', encoding='utf-8') as f:
                        json.dump(scanner.scan_results, f, ensure_ascii=False, indent=2)
                    print(f"\n[*] 详细结果已保存到: {args.output}")
        else:
            print(f"[-] 未能获取插件信息")
            sys.exit(1)
        
    except KeyboardInterrupt:
        print(f"\n[!] 扫描被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 扫描过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()