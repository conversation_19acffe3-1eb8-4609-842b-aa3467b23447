#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Elasticsearch漏洞扫描工具集使用示例
"""

import subprocess
import sys

def run_command(cmd):
    """运行命令并打印输出"""
    print(f"Executing: {cmd}")
    print("-" * 40)
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        print(f"Return code: {result.returncode}")
    except Exception as e:
        print(f"Error executing command: {e}")
    print("=" * 50)

def main():
    target = "http://target:9200"  # 替换为实际目标
    
    print("Elasticsearch漏洞扫描工具集使用示例")
    print("=" * 50)
    
    # 1. 版本检测
    print("1. 版本检测")
    run_command(f"python3 elasticsearch_version_detect.py {target}")
    
    # 2. 信息收集
    print("2. 信息收集")
    run_command(f"python3 elasticsearch_info_scan.py {target}")
    
    # 3. 未授权访问检测
    print("3. 未授权访问检测")
    run_command(f"python3 elasticsearch_unauthorized_access.py {target} --check-only")
    
    # 4. 暴力破解
    print("4. 暴力破解")
    run_command(f"python3 elasticsearch_bruteforce.py {target}")
    
    # 5. CVE-2014-3120检测
    print("5. CVE-2014-3120检测")
    run_command(f"python3 CVE-2014-3120.py {target} --check-only")
    
    # 6. CVE-2015-1427检测
    print("6. CVE-2015-1427检测")
    run_command(f"python3 CVE-2015-1427.py {target} --check-only")
    
    # 7. CVE-2015-5531检测
    print("7. CVE-2015-5531检测")
    run_command(f"python3 CVE-2015-5531.py {target} --check-only")
    
    # 8. 增强版文件读取
    print("8. 文件读取检测")
    run_command(f"python3 elasticsearch_file_read_enhanced.py {target} --check-only")
    
    # 9. 插件漏洞扫描
    print("9. 插件漏洞扫描")
    run_command(f"python3 elasticsearch_plugin_scan.py {target}")
    
    # 10. 配置错误检测
    print("10. 配置错误检测")
    run_command(f"python3 elasticsearch_misconfig_scan.py {target}")
    
    # 11. Kibana漏洞扫描
    print("11. Kibana漏洞扫描")
    run_command(f"python3 kibana_scan.py {target}")
    
    # 12. CVE-2019-7609检测
    print("12. CVE-2019-7609检测")
    run_command(f"python3 CVE-2019-7609.py {target} --check-only")
    
    # 13. 数据导出
    print("13. 数据导出 (敏感索引)")
    run_command(f"python3 elasticsearch_data_exfiltration.py {target} --sensitive-only")
    
    # 14. 综合扫描
    print("14. 综合扫描")
    run_command(f"python3 elasticsearch_comprehensive_scan.py {target}")

if __name__ == "__main__":
    main()