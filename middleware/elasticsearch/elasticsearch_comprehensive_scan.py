#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import importlib
import sys
import time
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 定义要扫描的模块列表
SCAN_MODULES = [
    "elasticsearch_version_detect",
    "elasticsearch_info_scan", 
    "elasticsearch_unauthorized_access",
    "elasticsearch_bruteforce",
    "CVE-2014-3120",
    "CVE-2015-1427",
    "CVE-2015-5531",
    "elasticsearch_file_read_enhanced",
    "elasticsearch_plugin_scan",
    "elasticsearch_misconfig_scan",
    "kibana_scan",
    "CVE-2019-7609"
]

def load_module(module_name):
    """动态加载模块"""
    try:
        return importlib.import_module(module_name)
    except ImportError:
        try:
            return importlib.import_module(f".{module_name}", package=".")
        except ImportError:
            return None

def run_scan_module(module_name, url, check_only=True):
    """运行单个扫描模块"""
    print(f"[*] Running {module_name}...")
    
    module = load_module(module_name)
    if not module:
        return {"error": f"Failed to load module {module_name}"}
    
    try:
        # 这里我们模拟调用，实际应用中需要根据每个模块的接口进行调用
        if module_name == "elasticsearch_version_detect":
            return run_version_detect(url)
        elif module_name == "elasticsearch_info_scan":
            return run_info_scan(url)
        elif module_name == "elasticsearch_unauthorized_access":
            return run_unauthorized_access_check(url, check_only)
        elif module_name == "elasticsearch_bruteforce":
            return run_bruteforce_check(url)
        elif module_name == "CVE-2014-3120":
            return run_cve_2014_3120_check(url, check_only)
        elif module_name == "CVE-2015-1427":
            return run_cve_2015_1427_check(url, check_only)
        elif module_name == "CVE-2015-5531":
            return run_cve_2015_5531_check(url, check_only)
        elif module_name == "elasticsearch_file_read":
            return run_file_read_check(url, check_only)
        elif module_name == "elasticsearch_plugin_scan":
            return run_plugin_scan(url)
        elif module_name == "elasticsearch_misconfig_scan":
            return run_misconfig_scan(url)
        elif module_name == "kibana_scan":
            return run_kibana_scan(url, check_only)
        elif module_name == "CVE-2019-7609":
            return run_cve_2019_7609_check(url, check_only)
        else:
            return {"error": f"Unknown module {module_name}"}
    except Exception as e:
        return {"error": f"Exception in {module_name}: {str(e)}"}

def run_version_detect(url):
    """运行版本检测"""
    try:
        import requests
        response = requests.get(url, timeout=10, verify=False)
        if response.status_code == 200:
            data = response.json()
            if "version" in data:
                return {
                    "success": True,
                    "version": data["version"].get("number"),
                    "lucene_version": data["version"].get("lucene_version"),
                    "tagline": data.get("tagline")
                }
        return {"success": False, "error": "Failed to retrieve version info"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_info_scan(url):
    """运行信息扫描"""
    try:
        import requests
        results = {}
        
        # 获取集群信息
        try:
            response = requests.get(urljoin(url, "/"), timeout=10, verify=False)
            if response.status_code == 200:
                results["cluster_info"] = response.json()
        except:
            pass
            
        # 获取节点信息
        try:
            response = requests.get(urljoin(url, "/_nodes"), timeout=10, verify=False)
            if response.status_code == 200:
                results["nodes_info"] = response.json()
        except:
            pass
            
        # 获取索引信息
        try:
            response = requests.get(urljoin(url, "/_cat/indices?format=json"), timeout=10, verify=False)
            if response.status_code == 200:
                results["indices_info"] = response.json()
        except:
            pass
        
        return {"success": True, "data": results}
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_unauthorized_access_check(url, check_only):
    """运行未授权访问检查"""
    try:
        import requests
        sensitive_endpoints = ["/", "/_nodes", "/_cat/indices"]
        accessible = []
        
        for endpoint in sensitive_endpoints:
            try:
                response = requests.get(urljoin(url, endpoint), timeout=10, verify=False)
                if response.status_code == 200:
                    accessible.append(endpoint)
            except:
                pass
        
        response = requests.get(url, timeout=10, verify=False)
        auth_required = "WWW-Authenticate" in response.headers
        
        return {
            "success": True,
            "vulnerable": len(accessible) > 0 and not auth_required,
            "accessible_endpoints": accessible,
            "auth_required": auth_required
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_bruteforce_check(url):
    """运行暴力破解检查（只检查是否需要认证）"""
    try:
        import requests
        response = requests.get(url, timeout=10, verify=False)
        if response.status_code == 200 and "cluster_name" in response.text:
            return {"success": True, "auth_required": False}
        elif response.status_code == 401:
            return {"success": True, "auth_required": True}
        else:
            return {"success": False, "error": f"Unexpected status code: {response.status_code}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_cve_2014_3120_check(url, check_only):
    """检查CVE-2014-3120"""
    try:
        import requests
        import time
        
        # 创建测试索引
        test_index = f"test_{int(time.time())}"
        test_data = {"name": "test"}
        
        # 创建测试索引
        response = requests.post(
            urljoin(url, f"/{test_index}/test"),
            json=test_data,
            timeout=10,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            # 清理
            try:
                requests.delete(urljoin(url, f"/{test_index}"), timeout=10, verify=False)
            except:
                pass
            return {"success": True, "vulnerable": False, "reason": "Failed to create test index"}
        
        # 刷新索引
        requests.post(urljoin(url, f"/{test_index}/_refresh"), timeout=10, verify=False)
        
        # 尝试执行动态脚本
        payload = {
            "size": 1,
            "query": {
                "filtered": {
                    "query": {
                        "match_all": {}
                    }
                }
            },
            "script_fields": {
                "exploit": {
                    "script": "1+1"
                }
            }
        }
        
        response = requests.post(
            urljoin(url, f"/{test_index}/_search?pretty"),
            json=payload,
            timeout=10,
            verify=False
        )
        
        # 清理测试索引
        try:
            requests.delete(urljoin(url, f"/{test_index}"), timeout=10, verify=False)
        except:
            pass
        
        # 检查响应
        vulnerable = response.status_code == 200 and "exploit" in response.text and "2" in response.text
        
        return {"success": True, "vulnerable": vulnerable}
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_cve_2015_1427_check(url, check_only):
    """检查CVE-2015-1427"""
    try:
        import requests
        import time
        
        # 创建测试索引
        test_index = f"test_{int(time.time())}"
        test_data = {"name": "test"}
        
        # 创建测试索引
        response = requests.post(
            urljoin(url, f"/{test_index}/test"),
            json=test_data,
            timeout=10,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            # 清理
            try:
                requests.delete(urljoin(url, f"/{test_index}"), timeout=10, verify=False)
            except:
                pass
            return {"success": True, "vulnerable": False, "reason": "Failed to create test index"}
        
        # 刷新索引
        requests.post(urljoin(url, f"/{test_index}/_refresh"), timeout=10, verify=False)
        
        # 尝试执行Groovy脚本
        payload = {
            "size": 1,
            "script_fields": {
                "exploit": {
                    "script": "1",
                    "lang": "groovy"
                }
            }
        }
        
        response = requests.post(
            urljoin(url, f"/{test_index}/_search?pretty"),
            json=payload,
            timeout=10,
            verify=False
        )
        
        # 清理测试索引
        try:
            requests.delete(urljoin(url, f"/{test_index}"), timeout=10, verify=False)
        except:
            pass
        
        # 检查响应
        vulnerable = response.status_code == 200 and "exploit" in response.text
        
        return {"success": True, "vulnerable": vulnerable}
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_file_read_check(url, check_only):
    """检查文件读取漏洞"""
    # 简化检查，只验证是否需要认证
    try:
        import requests
        response = requests.get(url, timeout=10, verify=False)
        if response.status_code == 200 and "cluster_name" in response.text:
            return {"success": True, "auth_required": False}
        elif response.status_code == 401:
            return {"success": True, "auth_required": True}
        else:
            return {"success": False, "error": f"Unexpected status code: {response.status_code}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_cve_2015_5531_check(url, check_only):
    """检查CVE-2015-5531目录遍历漏洞"""
    try:
        import requests
        import time
        
        # 创建一个临时仓库
        repo_name = f"test_repo_{int(time.time())}"
        repo_path = "/tmp/" + repo_name
        
        # 创建仓库
        create_repo_payload = {
            "type": "fs",
            "settings": {
                "location": repo_path
            }
        }
        
        response = requests.put(
            urljoin(url, f"/_snapshot/{repo_name}"),
            json=create_repo_payload,
            timeout=10,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            return {"success": True, "vulnerable": False, "reason": "Failed to create repository"}
        
        # 尝试目录遍历
        traversal_path = f"/_snapshot/{repo_name}/%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd"
        
        response = requests.get(
            urljoin(url, traversal_path),
            timeout=10,
            verify=False
        )
        
        # 清理仓库
        try:
            requests.delete(urljoin(url, f"/_snapshot/{repo_name}"), timeout=10, verify=False)
        except:
            pass
        
        # 检查响应
        vulnerable = response.status_code == 200 and ("root:" in response.text or "failed to read" in response.text.lower())
        
        return {"success": True, "vulnerable": vulnerable}
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_plugin_scan(url):
    """检查Elasticsearch插件漏洞"""
    try:
        import requests
        
        # 获取插件信息
        response = requests.get(urljoin(url, "/_nodes/plugins"), timeout=10, verify=False)
        if response.status_code != 200:
            return {"success": False, "error": "Failed to retrieve plugin information"}
        
        data = response.json()
        plugins = []
        
        # 解析插件信息
        for node_id, node_info in data.get("nodes", {}).items():
            for plugin in node_info.get("plugins", []):
                plugin_name = plugin.get("name")
                if plugin_name and plugin_name not in [p.get("name") for p in plugins]:
                    plugins.append({
                        "name": plugin_name,
                        "description": plugin.get("description", ""),
                        "version": plugin.get("version", "")
                    })
        
        # 检查常见的有漏洞的插件
        vulnerable_plugins = []
        for plugin in plugins:
            plugin_name = plugin.get("name", "").lower()
            if plugin_name in ["site", "head", "kopf", "hq", "marvel", "bigdesk", "elasticsearch-sql"]:
                vulnerable_plugins.append(plugin)
        
        return {
            "success": True,
            "plugins": plugins,
            "vulnerable_plugins": vulnerable_plugins,
            "vulnerable": len(vulnerable_plugins) > 0
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_misconfig_scan(url):
    """检查Elasticsearch配置错误"""
    try:
        import requests
        
        misconfigurations = []
        
        # 检查未授权访问
        response = requests.get(url, timeout=10, verify=False)
        if response.status_code == 200 and "cluster_name" in response.text:
            misconfigurations.append({
                "name": "未授权访问",
                "description": "Elasticsearch 实例允许未经身份验证的访问",
                "severity": "高"
            })
        
        # 检查动态脚本执行
        response = requests.get(urljoin(url, "/_cluster/settings"), timeout=10, verify=False)
        if response.status_code == 200:
            data = response.json()
            if "script.disable_dynamic" not in str(data):
                misconfigurations.append({
                    "name": "动态脚本执行",
                    "description": "允许动态脚本执行，可能导致远程代码执行",
                    "severity": "高"
                })
        
        # 检查敏感索引
        response = requests.get(urljoin(url, "/_cat/indices?format=json"), timeout=10, verify=False)
        if response.status_code == 200:
            indices = response.json()
            sensitive_indices = []
            
            for index in indices:
                index_name = index.get("index", "").lower()
                if any(keyword in index_name for keyword in ["user", "password", "secret", "admin", "account"]):
                    sensitive_indices.append(index_name)
            
            if sensitive_indices:
                misconfigurations.append({
                    "name": "敏感索引暴露",
                    "description": "敏感索引可被访问",
                    "severity": "高",
                    "indices": sensitive_indices
                })
        
        return {
            "success": True,
            "misconfigurations": misconfigurations,
            "vulnerable": len(misconfigurations) > 0
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_kibana_scan(url, check_only):
    """检查Kibana相关漏洞"""
    try:
        import requests
        import re
        from urllib.parse import urlparse
        
        # 尝试检测Kibana URL
        parsed_url = urlparse(url)
        host = parsed_url.hostname
        
        kibana_url = None
        kibana_ports = [5601, 443, 80]
        
        for port in kibana_ports:
            try_url = f"{parsed_url.scheme}://{host}:{port}"
            try:
                response = requests.get(try_url, timeout=10, verify=False)
                if response.status_code == 200 and ("kibana" in response.text.lower() or "kbn-" in response.text):
                    kibana_url = try_url
                    break
            except:
                pass
        
        if not kibana_url:
            return {"success": True, "kibana_detected": False}
        
        # 获取Kibana版本
        version = None
        try:
            response = requests.get(kibana_url, timeout=10, verify=False)
            if response.status_code == 200:
                version_match = re.search(r'kbn-version"?\s*content="?([0-9\.]+)', response.text)
                if version_match:
                    version = version_match.group(1)
        except:
            pass
        
        # 检查常见漏洞
        vulnerabilities = []
        
        # 检查CVE-2018-17246
        if version:
            version_parts = [int(x) for x in version.split(".")]
            if (version_parts[0] == 5 and (version_parts[1] < 6 or (version_parts[1] == 6 and version_parts[2] < 13))) or \
               (version_parts[0] == 6 and ((version_parts[1] < 4) or (version_parts[1] == 4 and version_parts[2] < 3) or (version_parts[1] == 5 and version_parts[2] < 4))):
                vulnerabilities.append({
                    "cve": "CVE-2018-17246",
                    "description": "Kibana Local File Inclusion",
                    "severity": "Critical"
                })
        
        # 检查未授权访问
        try:
            response = requests.get(kibana_url, timeout=10, verify=False)
            if response.status_code == 200 and "kibana" in response.text.lower() and "login" not in response.text.lower():
                vulnerabilities.append({
                    "cve": "Unauthorized-Access",
                    "description": "Kibana Unauthorized Access",
                    "severity": "High"
                })
        except:
            pass
        
        return {
            "success": True,
            "kibana_detected": True,
            "kibana_url": kibana_url,
            "kibana_version": version,
            "vulnerabilities": vulnerabilities,
            "vulnerable": len(vulnerabilities) > 0
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def run_cve_2019_7609_check(url, check_only):
    """检查CVE-2019-7609 Kibana Canvas RCE漏洞"""
    try:
        import requests
        import re
        from urllib.parse import urlparse
        
        # 尝试检测Kibana URL
        parsed_url = urlparse(url)
        host = parsed_url.hostname
        
        kibana_url = None
        kibana_ports = [5601, 443, 80]
        
        for port in kibana_ports:
            try_url = f"{parsed_url.scheme}://{host}:{port}"
            try:
                response = requests.get(try_url, timeout=10, verify=False)
                if response.status_code == 200 and ("kibana" in response.text.lower() or "kbn-" in response.text):
                    kibana_url = try_url
                    break
            except:
                pass
        
        if not kibana_url:
            return {"success": True, "kibana_detected": False}
        
        # 获取Kibana版本
        version = None
        try:
            response = requests.get(kibana_url, timeout=10, verify=False)
            if response.status_code == 200:
                version_match = re.search(r'kbn-version"?\s*content="?([0-9\.]+)', response.text)
                if version_match:
                    version = version_match.group(1)
        except:
            pass
        
        # 检查是否存在漏洞
        vulnerable = False
        if version:
            version_parts = [int(x) for x in version.split(".")]
            
            # 受影响的版本: <5.6.15, <6.6.1
            if (version_parts[0] == 5 and (version_parts[1] < 6 or (version_parts[1] == 6 and version_parts[2] < 15))) or \
               (version_parts[0] == 6 and (version_parts[1] < 6 or (version_parts[1] == 6 and version_parts[2] < 1))):
                # 版本检查通过，尝试访问Canvas
                canvas_url = urljoin(kibana_url, "/app/canvas")
                try:
                    response = requests.get(canvas_url, timeout=10, verify=False)
                    if response.status_code == 200 and "canvas" in response.text.lower():
                        vulnerable = True
                except:
                    pass
        
        return {
            "success": True,
            "kibana_detected": True,
            "kibana_url": kibana_url,
            "kibana_version": version,
            "vulnerable": vulnerable
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch Comprehensive Vulnerability Scanner")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("-v", "--vulns", nargs="+", help="Specific vulnerabilities to scan (default: all)")
    parser.add_argument("--check-only", action="store_true", help="Only check for vulnerabilities, don't exploit")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    parser.add_argument("--no-color", action="store_true", help="Disable colored output")
    
    args = parser.parse_args()
    
    # 确定要扫描的模块
    if args.vulns:
        modules_to_scan = []
        for vuln in args.vulns:
            if vuln in SCAN_MODULES:
                modules_to_scan.append(vuln)
            else:
                # 尝试模糊匹配
                matched = False
                for module in SCAN_MODULES:
                    if vuln.lower() in module.lower():
                        modules_to_scan.append(module)
                        matched = True
                        break
                if not matched:
                    print(f"[-] Warning: Unknown vulnerability/module '{vuln}'")
    else:
        modules_to_scan = SCAN_MODULES
    
    print(f"[*] Starting comprehensive scan of {args.url}")
    print(f"[*] Modules to scan: {', '.join(modules_to_scan)}")
    
    # 存储结果
    scan_results = {
        "target": args.url,
        "start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "modules_scanned": modules_to_scan,
        "results": {}
    }
    
    # 运行每个模块的扫描
    for module_name in modules_to_scan:
        try:
            result = run_scan_module(module_name, args.url, args.check_only)
            scan_results["results"][module_name] = result
            
            # 输出结果
            if result.get("success"):
                if "vulnerable" in result and result["vulnerable"]:
                    print(f"[+] {module_name}: VULNERABLE")
                elif "vulnerable" in result and not result["vulnerable"]:
                    print(f"[-] {module_name}: NOT VULNERABLE")
                else:
                    print(f"[*] {module_name}: Scan completed")
            else:
                print(f"[!] {module_name}: ERROR - {result.get('error', 'Unknown error')}")
        except Exception as e:
            error_msg = str(e)
            scan_results["results"][module_name] = {"success": False, "error": error_msg}
            print(f"[!] {module_name}: EXCEPTION - {error_msg}")
    
    scan_results["end_time"] = time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 保存结果
    if args.output:
        try:
            with open(args.output, "w") as f:
                json.dump(scan_results, f, indent=2)
            print(f"[+] Results saved to {args.output}")
        except Exception as e:
            print(f"[-] Failed to save results to {args.output}: {e}")
    
    # 输出摘要
    print("\n" + "="*50)
    print("SCAN SUMMARY")
    print("="*50)
    vulnerable_count = 0
    for module, result in scan_results["results"].items():
        if result.get("success") and result.get("vulnerable"):
            vulnerable_count += 1
    
    print(f"Target: {args.url}")
    print(f"Modules scanned: {len(modules_to_scan)}")
    print(f"Vulnerabilities found: {vulnerable_count}")
    print(f"Scan completed at: {scan_results['end_time']}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())