#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 已知的有漏洞的插件及其对应的CVE
VULNERABLE_PLUGINS = {
    "kopf": {
        "description": "Elasticsearch Kopf Plugin - 可能存在未授权访问",
        "paths": ["/_plugin/kopf/", "/_plugin/kopf/#!/cluster"],
        "severity": "Medium"
    },
    "head": {
        "description": "Elasticsearch Head Plugin - 可能存在未授权访问",
        "paths": ["/_plugin/head/", "/_plugin/head/index.html"],
        "severity": "Medium"
    },
    "hq": {
        "description": "Elasticsearch HQ Plugin - 可能存在未授权访问",
        "paths": ["/_plugin/hq/", "/_plugin/hq/index.html"],
        "severity": "Medium"
    },
    "marvel": {
        "description": "Elasticsearch Marvel Plugin - 可能存在未授权访问",
        "paths": ["/_plugin/marvel/", "/_plugin/marvel/index.html"],
        "severity": "Medium"
    },
    "site": {
        "description": "Elasticsearch Site Plugin - 可能存在目录遍历漏洞",
        "paths": ["/_plugin/site/", "/_plugin/site/index.html"],
        "severity": "High",
        "cve": "CVE-2015-3337"
    },
    "bigdesk": {
        "description": "Elasticsearch Bigdesk Plugin - 可能存在未授权访问",
        "paths": ["/_plugin/bigdesk/", "/_plugin/bigdesk/index.html"],
        "severity": "Medium"
    },
    "elasticsearch-sql": {
        "description": "Elasticsearch SQL Plugin - 可能存在SQL注入",
        "paths": ["/_sql", "/_sql?sql=SELECT * FROM elasticsearch"],
        "severity": "High"
    }
}

def get_installed_plugins(base_url, timeout=10):
    """获取已安装的插件列表"""
    try:
        # 尝试通过API获取插件列表
        response = requests.get(urljoin(base_url, "/_nodes/plugins"), timeout=timeout, verify=False)
        if response.status_code == 200:
            data = response.json()
            plugins = []
            
            # 解析插件信息
            for node_id, node_info in data.get("nodes", {}).items():
                for plugin in node_info.get("plugins", []):
                    plugin_name = plugin.get("name")
                    if plugin_name and plugin_name not in [p["name"] for p in plugins]:
                        plugins.append({
                            "name": plugin_name,
                            "description": plugin.get("description", ""),
                            "version": plugin.get("version", "")
                        })
            
            return True, plugins
    except Exception as e:
        pass
    
    # 如果API方法失败，尝试通过探测常见插件路径
    return scan_common_plugins(base_url, timeout)

def scan_common_plugins(base_url, timeout=10):
    """扫描常见插件路径"""
    detected_plugins = []
    
    for plugin_name, plugin_info in VULNERABLE_PLUGINS.items():
        for path in plugin_info["paths"]:
            try:
                response = requests.get(urljoin(base_url, path), timeout=timeout, verify=False)
                if response.status_code == 200:
                    detected_plugins.append({
                        "name": plugin_name,
                        "description": plugin_info["description"],
                        "path": path,
                        "severity": plugin_info["severity"],
                        "cve": plugin_info.get("cve")
                    })
                    break  # 找到一个路径就确认插件存在
            except:
                pass
    
    return True, detected_plugins

def check_plugin_vulnerabilities(base_url, plugins, timeout=10):
    """检查插件漏洞"""
    vulnerabilities = []
    
    for plugin in plugins:
        plugin_name = plugin["name"].lower()
        
        # 检查是否是已知的有漏洞的插件
        if plugin_name in VULNERABLE_PLUGINS:
            vuln_info = VULNERABLE_PLUGINS[plugin_name]
            
            # 尝试访问插件路径
            for path in vuln_info["paths"]:
                try:
                    response = requests.get(urljoin(base_url, path), timeout=timeout, verify=False)
                    if response.status_code == 200:
                        vulnerabilities.append({
                            "plugin": plugin_name,
                            "description": vuln_info["description"],
                            "severity": vuln_info["severity"],
                            "path": path,
                            "cve": vuln_info.get("cve"),
                            "accessible": True
                        })
                        break
                except:
                    pass
    
    return vulnerabilities

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch Plugin Vulnerability Scanner")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    
    args = parser.parse_args()
    
    print(f"[*] Scanning Elasticsearch plugins at {args.url}")
    
    # 获取已安装的插件
    success, plugins = get_installed_plugins(args.url, args.timeout)
    
    if not success:
        print("[-] Failed to retrieve plugin information")
        return 1
    
    if not plugins:
        print("[-] No plugins detected")
        return 0
    
    print(f"[+] Detected {len(plugins)} plugins:")
    for plugin in plugins:
        if isinstance(plugin, dict):
            if "name" in plugin:
                print(f"  - {plugin['name']}")
                if "description" in plugin:
                    print(f"    Description: {plugin['description']}")
                if "version" in plugin:
                    print(f"    Version: {plugin['version']}")
                if "path" in plugin:
                    print(f"    Path: {plugin['path']}")
                if "severity" in plugin:
                    print(f"    Severity: {plugin['severity']}")
                if "cve" in plugin and plugin["cve"]:
                    print(f"    CVE: {plugin['cve']}")
    
    # 检查插件漏洞
    vulnerabilities = check_plugin_vulnerabilities(args.url, plugins, args.timeout)
    
    if vulnerabilities:
        print(f"\n[!] Found {len(vulnerabilities)} vulnerable plugins:")
        for vuln in vulnerabilities:
            print(f"  - {vuln['plugin']}: {vuln['description']}")
            print(f"    Severity: {vuln['severity']}")
            print(f"    Path: {vuln['path']}")
            if "cve" in vuln and vuln["cve"]:
                print(f"    CVE: {vuln['cve']}")
    else:
        print("\n[-] No plugin vulnerabilities detected")
    
    # 保存结果
    if args.output:
        results = {
            "target": args.url,
            "plugins": plugins,
            "vulnerabilities": vulnerabilities
        }
        
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        print(f"[+] Results saved to {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())