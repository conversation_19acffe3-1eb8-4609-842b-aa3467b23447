#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def detect_version(base_url, timeout=10):
    """检测Elasticsearch版本"""
    version_info = {
        "version": None,
        "number": None,
        "lucene_version": None,
        "tagline": None,
        "vulnerabilities": []
    }
    
    try:
        response = requests.get(base_url, timeout=timeout, verify=False)
        if response.status_code == 200:
            data = response.json()
            if "version" in data:
                version_info["version"] = data
                version_info["number"] = data.get("version", {}).get("number")
                version_info["lucene_version"] = data.get("version", {}).get("lucene_version")
                version_info["tagline"] = data.get("tagline")
                
                # 根据版本确定可能存在的漏洞
                version_number = version_info["number"]
                if version_number:
                    version_parts = [int(x) for x in version_number.split(".") if x.isdigit()]
                    if len(version_parts) >= 2:
                        major, minor = version_parts[0], version_parts[1]
                        
                        # CVE-2014-3120 (ElasticSearch < 1.2)
                        if major < 1 or (major == 1 and minor < 2):
                            version_info["vulnerabilities"].append({
                                "cve": "CVE-2014-3120",
                                "description": "Elasticsearch Dynamic Scripting RCE",
                                "severity": "Critical"
                            })
                        
                        # CVE-2015-1427 (ElasticSearch < 1.3.8, < 1.4.3)
                        if (major == 1 and ((minor == 3 and version_parts[2] < 8) or (minor == 4 and version_parts[2] < 3))) or (major == 1 and minor < 3):
                            version_info["vulnerabilities"].append({
                                "cve": "CVE-2015-1427",
                                "description": "Elasticsearch Groovy Sandbox Escape RCE",
                                "severity": "Critical"
                            })
                        
                        # CVE-2015-3337 (ElasticSearch < 1.4.5, < 1.5.2)
                        if (major == 1 and ((minor == 4 and version_parts[2] < 5) or (minor == 5 and version_parts[2] < 2))) or (major == 1 and minor < 4):
                            version_info["vulnerabilities"].append({
                                "cve": "CVE-2015-3337",
                                "description": "Elasticsearch Directory Traversal",
                                "severity": "High"
                            })
                        
                        # CVE-2015-5658 (ElasticSearch < 2.0.0)
                        if major < 2:
                            version_info["vulnerabilities"].append({
                                "cve": "CVE-2015-5658",
                                "description": "Elasticsearch File Read Vulnerability",
                                "severity": "High"
                            })
    except Exception as e:
        pass
    
    return version_info

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch Version Detection")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    
    args = parser.parse_args()
    
    print(f"[*] Detecting Elasticsearch version at {args.url}")
    
    version_info = detect_version(args.url, args.timeout)
    
    if version_info["number"]:
        print(f"[+] Elasticsearch version detected: {version_info['number']}")
        print(f"[+] Lucene version: {version_info['lucene_version']}")
        print(f"[+] Tagline: {version_info['tagline']}")
        
        if version_info["vulnerabilities"]:
            print("\n[!] Potential Vulnerabilities:")
            for vuln in version_info["vulnerabilities"]:
                print(f"  - {vuln['cve']}: {vuln['description']} (Severity: {vuln['severity']})")
        else:
            print("[-] No known vulnerabilities found for this version")
    else:
        print("[-] Unable to detect Elasticsearch version")
        return 1
    
    if args.output:
        with open(args.output, "w") as f:
            json.dump(version_info, f, indent=2)
        print(f"[+] Results saved to {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())