#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def check_unauthorized_access(base_url, timeout=10):
    """检查是否存在未授权访问"""
    checks = {
        "cluster_info": False,
        "nodes_info": False,
        "indices": False,
        "cat_indices": False,
        "access_control": "unknown"
    }
    
    sensitive_endpoints = [
        "/", 
        "/_nodes", 
        "/_cat/indices", 
        "/_aliases",
        "/_cluster/health",
        "/_cluster/state"
    ]
    
    accessible_endpoints = []
    
    try:
        for endpoint in sensitive_endpoints:
            response = requests.get(urljoin(base_url, endpoint), timeout=timeout, verify=False)
            if response.status_code == 200:
                accessible_endpoints.append(endpoint)
                if endpoint == "/":
                    checks["cluster_info"] = True
                elif endpoint == "/_nodes":
                    checks["nodes_info"] = True
                elif endpoint == "/_cat/indices":
                    checks["cat_indices"] = True
        
        # 检查是否需要认证
        response = requests.get(base_url, timeout=timeout, verify=False)
        if "WWW-Authenticate" in response.headers:
            checks["access_control"] = "basic_auth"
        elif response.status_code == 200 and response.headers.get("content-type", "").startswith("application/json"):
            checks["access_control"] = "none"
        else:
            checks["access_control"] = "other"
            
        checks["accessible_endpoints"] = accessible_endpoints
        
        return checks
    except Exception as e:
        return {"error": str(e)}

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch Unauthorized Access Checker")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("--check-only", action="store_true", help="Only check for vulnerability, don't dump data")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    
    args = parser.parse_args()
    
    print(f"[*] Checking for Elasticsearch unauthorized access at {args.url}")
    
    result = check_unauthorized_access(args.url, args.timeout)
    
    if "error" in result:
        print(f"[-] Error occurred: {result['error']}")
        return 1
    
    is_vulnerable = len(result.get("accessible_endpoints", [])) > 0 and result.get("access_control") == "none"
    
    if is_vulnerable:
        print("[!] Unauthorized access vulnerability detected!")
        print(f"[!] Access control: {result['access_control']}")
        print(f"[!] Accessible endpoints: {', '.join(result['accessible_endpoints'])}")
    else:
        print("[-] No unauthorized access detected")
        print(f"[-] Access control: {result.get('access_control', 'unknown')}")
        if result.get("accessible_endpoints"):
            print(f"[-] Accessible endpoints (may require auth): {', '.join(result['accessible_endpoints'])}")
    
    if not args.check_only and is_vulnerable:
        print("\n[*] Dumping basic information (use --check-only to skip):")
        try:
            response = requests.get(urljoin(args.url, "/"), timeout=args.timeout, verify=False)
            if response.status_code == 200:
                print(json.dumps(response.json(), indent=2))
        except Exception as e:
            print(f"[-] Failed to dump information: {e}")
    
    if args.output:
        with open(args.output, "w") as f:
            json.dump(result, f, indent=2)
        print(f"[+] Results saved to {args.output}")
    
    return 0 if not is_vulnerable else 1

if __name__ == "__main__":
    sys.exit(main())