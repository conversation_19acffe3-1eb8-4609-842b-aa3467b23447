#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 常见的配置错误和安全问题
COMMON_MISCONFIGURATIONS = [
    {
        "name": "未授权访问",
        "description": "Elasticsearch 实例允许未经身份验证的访问",
        "severity": "高",
        "check_path": "/",
        "check_method": "GET"
    },
    {
        "name": "动态脚本执行",
        "description": "允许动态脚本执行，可能导致远程代码执行",
        "severity": "高",
        "check_path": "/_cluster/settings",
        "check_method": "GET",
        "check_key": "script.disable_dynamic"
    },
    {
        "name": "索引自动创建",
        "description": "允许自动创建索引，可能被滥用",
        "severity": "中",
        "check_path": "/_cluster/settings",
        "check_method": "GET",
        "check_key": "action.auto_create_index"
    },
    {
        "name": "跨域资源共享 (CORS)",
        "description": "配置了过于宽松的CORS策略",
        "severity": "中",
        "check_path": "/_cluster/settings",
        "check_method": "GET",
        "check_key": "http.cors.allow-origin"
    },
    {
        "name": "敏感索引暴露",
        "description": "敏感索引可被访问",
        "severity": "高",
        "check_path": "/_cat/indices?format=json",
        "check_method": "GET",
        "sensitive_indices": ["user", "users", "password", "passwords", "secret", "admin", "account", "accounts"]
    },
    {
        "name": "集群状态暴露",
        "description": "集群状态信息可被未授权访问",
        "severity": "中",
        "check_path": "/_cluster/state",
        "check_method": "GET"
    },
    {
        "name": "节点统计信息暴露",
        "description": "节点统计信息可被未授权访问",
        "severity": "中",
        "check_path": "/_nodes/stats",
        "check_method": "GET"
    },
    {
        "name": "快照仓库暴露",
        "description": "快照仓库信息可被未授权访问",
        "severity": "中",
        "check_path": "/_snapshot/_all",
        "check_method": "GET"
    }
]

def check_auth_required(base_url, timeout=10):
    """检查是否需要认证"""
    try:
        response = requests.get(base_url, timeout=timeout, verify=False)
        if response.status_code == 401:
            return True
        elif response.status_code == 200 and "cluster_name" in response.text:
            return False
        else:
            return None  # 无法确定
    except Exception as e:
        return None

def check_misconfigurations(base_url, timeout=10):
    """检查常见配置错误"""
    results = []
    auth_required = check_auth_required(base_url, timeout)
    
    if auth_required is False:
        # 如果不需要认证，添加未授权访问问题
        results.append({
            "name": "未授权访问",
            "description": "Elasticsearch 实例允许未经身份验证的访问",
            "severity": "高",
            "details": "任何人都可以访问此 Elasticsearch 实例，建议配置基本认证或其他访问控制机制"
        })
    
    # 检查其他配置问题
    for config in COMMON_MISCONFIGURATIONS:
        if config["name"] == "未授权访问":
            continue  # 已经检查过了
            
        try:
            response = requests.request(
                config["check_method"],
                urljoin(base_url, config["check_path"]),
                timeout=timeout,
                verify=False
            )
            
            # 如果需要认证但我们没有提供，跳过此检查
            if response.status_code == 401:
                continue
                
            if response.status_code == 200:
                # 检查特定配置键
                if "check_key" in config:
                    data = response.json()
                    # 递归查找键
                    found = False
                    value = None
                    
                    def search_key(obj, key):
                        if isinstance(obj, dict):
                            for k, v in obj.items():
                                if k == key:
                                    return True, v
                                if isinstance(v, (dict, list)):
                                    found, val = search_key(v, key)
                                    if found:
                                        return True, val
                        elif isinstance(obj, list):
                            for item in obj:
                                found, val = search_key(item, key)
                                if found:
                                    return True, val
                        return False, None
                    
                    found, value = search_key(data, config["check_key"])
                    
                    if found:
                        results.append({
                            "name": config["name"],
                            "description": config["description"],
                            "severity": config["severity"],
                            "details": f"配置 {config['check_key']} 的值为 {value}"
                        })
                
                # 检查敏感索引
                elif "sensitive_indices" in config:
                    try:
                        indices = response.json()
                        sensitive_found = []
                        
                        for index in indices:
                            index_name = index.get("index", "").lower()
                            for sensitive in config["sensitive_indices"]:
                                if sensitive in index_name:
                                    sensitive_found.append(index_name)
                                    break
                        
                        if sensitive_found:
                            results.append({
                                "name": config["name"],
                                "description": config["description"],
                                "severity": config["severity"],
                                "details": f"发现敏感索引: {', '.join(sensitive_found)}"
                            })
                    except:
                        pass
                
                # 其他通用检查
                else:
                    results.append({
                        "name": config["name"],
                        "description": config["description"],
                        "severity": config["severity"],
                        "details": f"端点 {config['check_path']} 可被访问"
                    })
        except Exception as e:
            pass
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch 配置错误扫描器")
    parser.add_argument("url", help="目标 URL (例如: http://target:9200)")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="请求超时时间 (默认: 10s)")
    parser.add_argument("-o", "--output", help="保存结果的输出文件")
    
    args = parser.parse_args()
    
    print(f"[*] 扫描 Elasticsearch 配置错误: {args.url}")
    
    # 检查配置错误
    misconfigurations = check_misconfigurations(args.url, args.timeout)
    
    if misconfigurations:
        print(f"[!] 发现 {len(misconfigurations)} 个配置问题:")
        for issue in misconfigurations:
            print(f"  - {issue['name']} ({issue['severity']})")
            print(f"    描述: {issue['description']}")
            print(f"    详情: {issue['details']}")
            print()
    else:
        print("[-] 未发现明显的配置问题")
    
    # 保存结果
    if args.output:
        results = {
            "target": args.url,
            "misconfigurations": misconfigurations
        }
        
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        print(f"[+] 结果已保存到 {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())