#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_cluster_info(base_url, timeout=10):
    """获取集群信息"""
    try:
        response = requests.get(urljoin(base_url, "/"), timeout=timeout, verify=False)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        pass
    return None

def get_nodes_info(base_url, timeout=10):
    """获取节点信息"""
    try:
        response = requests.get(urljoin(base_url, "/_nodes"), timeout=timeout, verify=False)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        pass
    return None

def get_indices_info(base_url, timeout=10):
    """获取索引信息"""
    try:
        response = requests.get(urljoin(base_url, "/_cat/indices?format=json"), timeout=timeout, verify=False)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        pass
    return None

def get_aliases_info(base_url, timeout=10):
    """获取别名信息"""
    try:
        response = requests.get(urljoin(base_url, "/_aliases"), timeout=timeout, verify=False)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        pass
    return None

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch Information Scanner")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("--indices-only", action="store_true", help="Only retrieve indices information")
    parser.add_argument("--config-only", action="store_true", help="Only retrieve configuration information")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    
    args = parser.parse_args()
    
    print(f"[*] Scanning Elasticsearch at {args.url}")
    
    results = {
        "url": args.url,
        "cluster_info": None,
        "nodes_info": None,
        "indices_info": None,
        "aliases_info": None
    }
    
    if not args.indices_only and not args.config_only:
        # 获取集群信息
        print("[*] Retrieving cluster information...")
        results["cluster_info"] = get_cluster_info(args.url, args.timeout)
        
        # 获取节点信息
        print("[*] Retrieving nodes information...")
        results["nodes_info"] = get_nodes_info(args.url, args.timeout)
        
        # 获取别名信息
        print("[*] Retrieving aliases information...")
        results["aliases_info"] = get_aliases_info(args.url, args.timeout)
    
    if not args.config_only:
        # 获取索引信息
        print("[*] Retrieving indices information...")
        results["indices_info"] = get_indices_info(args.url, args.timeout)
    
    # 输出结果
    if args.output:
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        print(f"[+] Results saved to {args.output}")
    else:
        print("\n" + "="*50)
        print("SCAN RESULTS")
        print("="*50)
        
        if results["cluster_info"]:
            print("\n[Cluster Information]")
            print(json.dumps(results["cluster_info"], indent=2))
            
        if results["nodes_info"]:
            print("\n[Nodes Information]")
            print(json.dumps(results["nodes_info"], indent=2))
            
        if results["indices_info"]:
            print("\n[Indices Information]")
            print(json.dumps(results["indices_info"], indent=2))
            
        if results["aliases_info"]:
            print("\n[Aliases Information]")
            print(json.dumps(results["aliases_info"], indent=2))

if __name__ == "__main__":
    main()