# Elasticsearch 漏洞扫描工具集

这是一个专门针对 Elasticsearch 搜索引擎的 CTF 漏洞扫描和利用工具集，包含多个常见漏洞的检测和利用脚本。该工具集适用于安全研究人员、渗透测试人员和 CTF 比赛参与者。

## 🚀 功能特性

- 🔍 **漏洞检测**: 支持多个 Elasticsearch 漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、配置信息、索引信息收集
- 🔐 **安全测试**: 暴力破解、未授权访问检测等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议
- 📝 **详细报告**: 生成详细的扫描报告和结果分析
- 🔄 **数据导出**: 支持导出 Elasticsearch 索引数据
- 🧩 **插件检测**: 检测常见插件漏洞
- ⚙️ **配置检查**: 检测常见配置错误
- 🔎 **Kibana检测**: 支持 Kibana 相关漏洞检测

## 📋 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 | 脚本文件 |
|---------|------|----------|----------|----------|
| CVE-2014-3120 | Elasticsearch 远程代码执行漏洞 | Critical | < 1.2 | `CVE-2014-3120.py` |
| CVE-2015-1427 | Groovy 沙箱绕过 RCE 漏洞 | Critical | < 1.3.8, < 1.4.3 | `CVE-2015-1427.py` |
| CVE-2015-5531 | Elasticsearch 目录遍历漏洞 | High | < 1.6.1 | `CVE-2015-5531.py` |
| CVE-2019-7609 | Kibana Canvas 原型污染 RCE 漏洞 | Critical | < 5.6.15, < 6.6.1 | `CVE-2019-7609.py` |
| 未授权访问 | Elasticsearch 敏感信息泄露 | High | 配置相关 | `elasticsearch_unauthorized_access.py` |
| 暴力破解 | Elasticsearch 弱口令检测 | Medium | 配置相关 | `elasticsearch_bruteforce.py` |
| 版本识别 | Elasticsearch 版本和指纹识别 | Info | 所有版本 | `elasticsearch_version_detect.py` |
| 信息收集 | 集群信息、节点信息、索引信息 | Info | 所有版本 | `elasticsearch_info_scan.py` |
| 文件读取 | 通过_search API进行文件读取（支持多行） | High | 特定配置 | `elasticsearch_file_read_enhanced.py` |
| 插件漏洞 | 检测常见插件漏洞 | Medium-High | 插件相关 | `elasticsearch_plugin_scan.py` |
| 配置错误 | 检测常见配置错误 | Medium-High | 配置相关 | `elasticsearch_misconfig_scan.py` |
| Kibana漏洞 | 检测 Kibana 相关漏洞 | Medium-Critical | Kibana相关 | `kibana_scan.py` |
| 数据导出 | 导出 Elasticsearch 索引数据 | Info | 所有版本 | `elasticsearch_data_exfiltration.py` |

## 🛠️ 安装和使用

### 环境要求

- Python 3.6+
- pip (Python包管理器)
- 网络连接（用于访问目标 Elasticsearch 实例）

### 安装依赖

```bash
# 克隆或下载项目后，进入 elasticsearch 目录
cd elasticsearch

# 安装依赖包
pip install -r requirements.txt
```

### 依赖包说明

- `requests>=2.25.1`: HTTP 请求库
- `urllib3>=1.26.5`: HTTP 客户端库
- `colorama>=0.4.4`: 终端颜色输出
- `tqdm>=4.62.3`: 进度条显示
- `beautifulsoup4>=4.10.0`: HTML解析
- `lxml>=4.6.3`: XML处理库

## 📖 脚本详细说明

### 1. 综合扫描脚本

**文件**: `elasticsearch_comprehensive_scan.py`

**功能**: 一键扫描目标 Elasticsearch 实例的所有支持漏洞

**使用方法**:
```bash
python3 elasticsearch_comprehensive_scan.py <target_url>
```

**参数说明**:
- `target_url`: Elasticsearch 实例的 URL（如：http://target:9200）

**示例**:
```bash
python3 elasticsearch_comprehensive_scan.py http://*************:9200
```

### 2. 版本检测脚本

**文件**: `elasticsearch_version_detect.py`

**功能**: 检测 Elasticsearch 版本信息

**使用方法**:
```bash
python3 elasticsearch_version_detect.py <target_url>
```

**输出信息**:
- Elasticsearch 版本号
- Lucene 版本
- 集群标签信息

### 3. 信息收集脚本

**文件**: `elasticsearch_info_scan.py`

**功能**: 收集 Elasticsearch 集群详细信息

**使用方法**:
```bash
python3 elasticsearch_info_scan.py <target_url>
```

**收集信息**:
- 集群信息
- 节点信息
- 索引信息
- 分片信息
- 插件信息

### 4. 未授权访问检测

**文件**: `elasticsearch_unauthorized_access.py`

**功能**: 检测 Elasticsearch 是否存在未授权访问漏洞

**使用方法**:
```bash
# 仅检测模式
python3 elasticsearch_unauthorized_access.py <target_url> --check-only

# 检测并尝试利用
python3 elasticsearch_unauthorized_access.py <target_url>
```

### 5. 暴力破解脚本

**文件**: `elasticsearch_bruteforce.py`

**功能**: 对 Elasticsearch 进行用户名密码暴力破解

**使用方法**:
```bash
python3 elasticsearch_bruteforce.py <target_url>
```

**字典文件**:
- `dictionaries/users.txt`: 用户名字典
- `dictionaries/passwords.txt`: 密码字典

### 6. CVE-2014-3120 漏洞利用

**文件**: `CVE-2014-3120.py`

**功能**: 检测和利用 Elasticsearch 远程代码执行漏洞

**使用方法**:
```bash
# 仅检测模式
python3 CVE-2014-3120.py <target_url> --check-only

# 检测并执行命令
python3 CVE-2014-3120.py <target_url> --command "whoami"
```

**参数说明**:
- `--check-only`: 仅检测漏洞，不执行命令
- `--command`: 指定要执行的命令

### 7. CVE-2015-1427 漏洞利用

**文件**: `CVE-2015-1427.py`

**功能**: 检测和利用 Groovy 沙箱绕过 RCE 漏洞

**使用方法**:
```bash
# 仅检测模式
python3 CVE-2015-1427.py <target_url> --check-only

# 检测并执行命令
python3 CVE-2015-1427.py <target_url> --command "whoami"
```

### 8. CVE-2015-5531 漏洞利用

**文件**: `CVE-2015-5531.py`

**功能**: 检测和利用 Elasticsearch 目录遍历漏洞

**使用方法**:
```bash
# 仅检测模式
python3 CVE-2015-5531.py <target_url> --check-only

# 检测并读取文件
python3 CVE-2015-5531.py <target_url> --file "/etc/passwd"
```

### 9. 文件读取脚本

**文件**: `elasticsearch_file_read_enhanced.py`

**功能**: 通过 Elasticsearch 的 _search API 进行文件读取（支持多行）

**使用方法**:
```bash
# 仅检测模式
python3 elasticsearch_file_read_enhanced.py <target_url> --check-only

# 检测并读取文件
python3 elasticsearch_file_read_enhanced.py <target_url> --file "/etc/passwd"

# 读取常见敏感文件
python3 elasticsearch_file_read_enhanced.py <target_url> --common-files
```

### 10. 插件漏洞扫描

**文件**: `elasticsearch_plugin_scan.py`

**功能**: 检测 Elasticsearch 插件相关漏洞

**使用方法**:
```bash
python3 elasticsearch_plugin_scan.py <target_url>
```

**检测插件**:
- kopf
- head
- hq
- marvel
- site
- bigdesk
- elasticsearch-sql

### 11. 配置错误检测

**文件**: `elasticsearch_misconfig_scan.py`

**功能**: 检测 Elasticsearch 常见配置错误

**使用方法**:
```bash
python3 elasticsearch_misconfig_scan.py <target_url>
```

**检测项目**:
- 未授权访问
- 动态脚本执行
- 索引自动创建
- 跨域资源共享 (CORS)
- 敏感索引暴露
- 集群状态暴露
- 节点统计信息暴露
- 快照仓库暴露

### 12. Kibana 漏洞扫描

**文件**: `kibana_scan.py`

**功能**: 检测 Kibana 相关漏洞

**使用方法**:
```bash
# 使用 Elasticsearch URL 自动检测 Kibana
python3 kibana_scan.py <elasticsearch_url>

# 直接指定 Kibana URL
python3 kibana_scan.py --kibana-url <kibana_url>
```

**检测漏洞**:
- CVE-2018-17246 (Kibana 本地文件包含)
- CVE-2019-7609 (Kibana Canvas 原型污染 RCE)
- CVE-2019-7610 (Kibana 保存对象导入 RCE)
- 未授权访问

### 13. CVE-2019-7609 漏洞检测

**文件**: `CVE-2019-7609.py`

**功能**: 检测 Kibana Canvas 原型污染 RCE 漏洞

**注意**: 此脚本主要用于检测漏洞是否存在，在 CTF 环境中可能需要根据具体情况修改利用方式

**使用方法**:
```bash
# 仅检测模式
python3 CVE-2019-7609.py <kibana_url> --check-only

# 尝试检测并执行命令（需要根据目标环境调整）
python3 CVE-2019-7609.py <kibana_url> --command "whoami"
```

### 14. 数据导出工具

**文件**: `elasticsearch_data_exfiltration.py`

**功能**: 导出 Elasticsearch 索引数据

**使用方法**:
```bash
# 导出所有索引
python3 elasticsearch_data_exfiltration.py <target_url>

# 导出指定索引
python3 elasticsearch_data_exfiltration.py <target_url> -i index1 index2

# 仅导出可能包含敏感信息的索引
python3 elasticsearch_data_exfiltration.py <target_url> --sensitive-only
```

## 🎯 使用示例

### 快速开始

1. **基本扫描**:
```bash
# 对目标进行综合扫描
python3 elasticsearch_comprehensive_scan.py http://target:9200
```

2. **分步扫描**:
```bash
# 1. 版本检测
python3 elasticsearch_version_detect.py http://target:9200

# 2. 信息收集
python3 elasticsearch_info_scan.py http://target:9200

# 3. 未授权访问检测
python3 elasticsearch_unauthorized_access.py http://target:9200 --check-only

# 4. 暴力破解
python3 elasticsearch_bruteforce.py http://target:9200

# 5. 漏洞检测
python3 CVE-2014-3120.py http://target:9200 --check-only
python3 CVE-2015-1427.py http://target:9200 --check-only
python3 CVE-2015-5531.py http://target:9200 --check-only
```

3. **漏洞利用**:
```bash
# 利用 CVE-2014-3120 执行命令
python3 CVE-2014-3120.py http://target:9200 --command "id"

# 利用 CVE-2015-1427 执行命令
python3 CVE-2015-1427.py http://target:9200 --command "cat /etc/passwd"

# 利用 CVE-2015-5531 读取文件
python3 CVE-2015-5531.py http://target:9200 --file "/etc/passwd"
```

4. **Kibana 相关**:
```bash
# 扫描 Kibana 漏洞
python3 kibana_scan.py http://target:9200

# 检测 CVE-2019-7609 漏洞
python3 CVE-2019-7609.py http://target:5601 --check-only
```

5. **数据导出**:
```bash
# 导出敏感索引数据
python3 elasticsearch_data_exfiltration.py http://target:9200 --sensitive-only
```

### 使用示例脚本

项目提供了 `example_usage.py` 脚本，展示了所有工具的使用方法：

```bash
python3 example_usage.py
```

## 📁 文件结构

```
elasticsearch/
├── README.md                           # 项目说明文档
├── requirements.txt                    # 依赖包列表
├── example_usage.py                    # 使用示例脚本
├── elasticsearch_comprehensive_scan.py # 综合扫描脚本
├── elasticsearch_version_detect.py     # 版本检测脚本
├── elasticsearch_info_scan.py          # 信息收集脚本
├── elasticsearch_unauthorized_access.py # 未授权访问检测
├── elasticsearch_bruteforce.py         # 暴力破解脚本
├── elasticsearch_file_read_enhanced.py # 文件读取脚本
├── elasticsearch_plugin_scan.py        # 插件漏洞扫描
├── elasticsearch_misconfig_scan.py     # 配置错误检测
├── elasticsearch_data_exfiltration.py  # 数据导出工具
├── kibana_scan.py                      # Kibana 漏洞扫描
├── CVE-2014-3120.py                   # CVE-2014-3120 漏洞利用
├── CVE-2015-1427.py                   # CVE-2015-1427 漏洞利用
├── CVE-2015-5531.py                   # CVE-2015-5531 漏洞利用
├── CVE-2019-7609.py                   # CVE-2019-7609 漏洞检测
└── dictionaries/                       # 字典文件目录
    ├── users.txt                       # 用户名字典
    └── passwords.txt                   # 密码字典
```

## ⚠️ 安全声明

1. **合法使用**: 本工具仅用于授权的安全测试和 CTF 比赛
2. **责任声明**: 使用者需自行承担使用本工具的法律责任
3. **测试环境**: 建议在测试环境中使用，避免影响生产系统
4. **数据保护**: 使用过程中请注意保护敏感数据

## 🔧 故障排除

### 常见问题

1. **连接超时**:
   - 检查目标 Elasticsearch 服务是否正常运行
   - 确认网络连接和防火墙设置
   - 调整脚本中的超时参数

2. **SSL 证书错误**:
   - 脚本已禁用 SSL 验证警告
   - 如仍有问题，可修改脚本中的 `verify=False` 参数

3. **权限不足**:
   - 确保有足够的权限访问目标系统
   - 某些漏洞利用可能需要特定权限

### 调试模式

如需调试，可在脚本中添加 `--verbose` 参数（如果支持）或修改脚本添加调试输出。

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：

- 提交 Issue 到项目仓库
- 发送邮件到项目维护者

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

---

**注意**: 使用本工具进行任何形式的攻击行为都是违法的，使用者需自行承担相应的法律责任。