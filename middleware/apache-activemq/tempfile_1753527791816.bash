# Elasticsearch 漏洞扫描工具集

这是一个专门针对Elasticsearch搜索引擎的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。Elasticsearch作为CTF中的重点目标，本工具集提供了全面的攻击向量。

## 功能特性

- 🔍 **漏洞检测**: 支持多个Elasticsearch CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、配置扫描、数据枚举
- 🔐 **安全测试**: 未授权访问、暴力破解、文件读取等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2014-3120 | Elasticsearch 远程代码执行漏洞 | Critical | < 1.2 |
| CVE-2015-1427 | Elasticsearch Groovy沙箱绕过RCE | Critical | < 1.3.8, < 1.4.3 |
| CVE-2015-3337 | Elasticsearch 目录遍历漏洞 | High | < 1.5.2 |
| CVE-2015-5658 | Elasticsearch 任意文件读取漏洞 | High | < 2.0.0 |
| 未授权访问 | Elasticsearch 未授权访问漏洞 | High | 配置相关 |
| 文件读取 | Elasticsearch 文件读取漏洞 | High | 多个版本 |
| 暴力破解 | Elasticsearch 弱口令暴力破解 | High | 配置相关 |
| 版本识别 | Elasticsearch版本和指纹识别 | Info | 所有版本 |
| 信息收集 | Elasticsearch敏感信息泄露 | Medium | 配置相关 |
| 模板注入RCE | Elasticsearch 模板注入RCE | Critical | 特定版本 |

## 安装和使用

### 环境要求

- Python 3.6+
- 网络访问权限

### 安装依赖

