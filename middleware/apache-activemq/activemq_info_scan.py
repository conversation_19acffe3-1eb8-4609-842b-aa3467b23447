#!/usr/bin/env python3
"""
Apache ActiveMQ 信息收集和扫描工具
收集ActiveMQ服务器的详细信息和配置
"""

import sys
import requests
import argparse
import json
import base64
import socket
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class ActiveMQInfoScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 8161
        
        # 常见的ActiveMQ端口
        self.activemq_ports = {
            8161: 'HTTP Management Console',
            8162: 'HTTPS Management Console',
            61616: 'OpenWire Protocol',
            61617: 'SSL OpenWire Protocol',
            61613: 'STOMP Protocol',
            61614: 'SSL STOMP Protocol',
            1883: 'MQTT Protocol',
            8883: 'SSL MQTT Protocol',
            5672: 'AMQP Protocol',
            61619: 'WebSocket',
        }
        
        # 默认凭据
        self.default_credentials = [
            ('admin', 'admin'),
            ('user', 'user'),
            ('guest', 'guest'),
            ('activemq', 'activemq'),
            ('amq', 'amq'),
        ]
        
        # 信息收集路径
        self.info_paths = [
            '/admin',
            '/admin/',
            '/admin/xml/systemProperties.jsp',
            '/admin/xml/brokerQuery.jsp',
            '/admin/xml/queues.jsp',
            '/admin/xml/topics.jsp',
            '/admin/xml/subscribers.jsp',
            '/admin/xml/connections.jsp',
            '/hawtio',
            '/hawtio/jolokia',
            '/api/jolokia',
            '/jolokia',
            '/console',
            '/demo',
            '/fileserver',
        ]
    
    def scan_ports(self):
        """扫描ActiveMQ相关端口"""
        print(f"[*] 扫描ActiveMQ相关端口: {self.target_host}")
        
        open_ports = {}
        
        for port, service in self.activemq_ports.items():
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(self.timeout)
                
                result = sock.connect_ex((self.target_host, port))
                
                if result == 0:
                    print(f"[+] 端口 {port} 开放 ({service})")
                    open_ports[port] = service
                
                sock.close()
                
            except Exception as e:
                continue
        
        return open_ports
    
    def detect_management_interface(self):
        """检测管理界面"""
        print(f"[*] 检测ActiveMQ管理界面...")
        
        # 常见端口和协议
        test_configs = [
            ('http', 8161),
            ('https', 8162),
            ('http', 8080),
            ('https', 8443),
        ]
        
        for protocol, port in test_configs:
            try:
                base_url = f"{protocol}://{self.target_host}:{port}"
                
                # 测试管理界面路径
                test_paths = ['/admin', '/admin/', '/hawtio', '/console', '/']
                
                for path in test_paths:
                    test_url = urljoin(base_url, path)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    if self.is_activemq_management(response):
                        print(f"[+] 发现ActiveMQ管理界面: {base_url}")
                        return base_url
                        
            except Exception as e:
                continue
        
        return None
    
    def is_activemq_management(self, response):
        """检查响应是否来自ActiveMQ管理界面"""
        if response.status_code not in [200, 401, 403]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'jetty' in server_header:
            content = response.text.lower()
            if 'activemq' in content:
                return True
        
        # 检查响应内容
        content = response.text.lower()
        activemq_indicators = [
            'activemq',
            'apache activemq',
            'message broker',
            'queues',
            'topics',
            'connections',
            'subscribers',
            'producers',
            'consumers',
            'browse',
            'send message',
            'purge',
            'hawtio',
            'jolokia',
        ]
        
        return any(indicator in content for indicator in activemq_indicators)
    
    def get_valid_credentials(self, management_url):
        """获取有效凭据"""
        print(f"[*] 尝试获取有效凭据...")
        
        for username, password in self.default_credentials:
            print(f"[*] 尝试: {username}:{password}")
            
            if self.test_credentials(management_url, username, password):
                print(f"[+] 有效凭据: {username}:{password}")
                return (username, password)
        
        return None
    
    def test_credentials(self, management_url, username, password):
        """测试凭据是否有效"""
        try:
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            test_url = urljoin(management_url, '/admin')
            response = self.session.get(test_url, headers=headers, timeout=self.timeout)
            
            return response.status_code == 200 and 'activemq' in response.text.lower()
            
        except Exception as e:
            return False
    
    def collect_system_info(self, management_url, credentials):
        """收集系统信息"""
        print(f"[*] 收集系统信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            system_info = {}
            
            # 获取系统属性
            props_url = urljoin(management_url, '/admin/xml/systemProperties.jsp')
            response = self.session.get(props_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                system_info['system_properties'] = self.parse_xml_response(response.text)
            
            # 获取broker信息
            broker_url = urljoin(management_url, '/admin/xml/brokerQuery.jsp')
            response = self.session.get(broker_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                system_info['broker_info'] = self.parse_xml_response(response.text)
            
            return system_info
            
        except Exception as e:
            print(f"[-] 收集系统信息失败: {e}")
        
        return None
    
    def parse_xml_response(self, xml_content):
        """解析XML响应"""
        try:
            import re
            
            # 提取关键信息
            info = {}
            
            # 提取版本信息
            version_match = re.search(r'activemq[.\s]*version[^>]*>([^<]+)', xml_content, re.IGNORECASE)
            if version_match:
                info['activemq_version'] = version_match.group(1).strip()
            
            # 提取Java版本
            java_match = re.search(r'java[.\s]*version[^>]*>([^<]+)', xml_content, re.IGNORECASE)
            if java_match:
                info['java_version'] = java_match.group(1).strip()
            
            # 提取操作系统信息
            os_match = re.search(r'os[.\s]*name[^>]*>([^<]+)', xml_content, re.IGNORECASE)
            if os_match:
                info['os_name'] = os_match.group(1).strip()
            
            return info
            
        except Exception as e:
            return {'raw_content': xml_content[:500]}
    
    def collect_queues_info(self, management_url, credentials):
        """收集队列信息"""
        print(f"[*] 收集队列信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取队列列表
            queues_url = urljoin(management_url, '/admin/xml/queues.jsp')
            response = self.session.get(queues_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                return self.parse_queues_xml(response.text)
            
        except Exception as e:
            print(f"[-] 收集队列信息失败: {e}")
        
        return []
    
    def parse_queues_xml(self, xml_content):
        """解析队列XML"""
        try:
            import re
            
            queues = []
            
            # 简单的XML解析
            queue_matches = re.findall(r'<queue[^>]*name="([^"]*)"[^>]*>', xml_content, re.IGNORECASE)
            
            for queue_name in queue_matches:
                queues.append({
                    'name': queue_name,
                    'type': 'queue'
                })
            
            return queues
            
        except Exception as e:
            return []
    
    def collect_topics_info(self, management_url, credentials):
        """收集主题信息"""
        print(f"[*] 收集主题信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取主题列表
            topics_url = urljoin(management_url, '/admin/xml/topics.jsp')
            response = self.session.get(topics_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                return self.parse_topics_xml(response.text)
            
        except Exception as e:
            print(f"[-] 收集主题信息失败: {e}")
        
        return []
    
    def parse_topics_xml(self, xml_content):
        """解析主题XML"""
        try:
            import re
            
            topics = []
            
            # 简单的XML解析
            topic_matches = re.findall(r'<topic[^>]*name="([^"]*)"[^>]*>', xml_content, re.IGNORECASE)
            
            for topic_name in topic_matches:
                topics.append({
                    'name': topic_name,
                    'type': 'topic'
                })
            
            return topics
            
        except Exception as e:
            return []
    
    def collect_connections_info(self, management_url, credentials):
        """收集连接信息"""
        print(f"[*] 收集连接信息...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            # 获取连接列表
            connections_url = urljoin(management_url, '/admin/xml/connections.jsp')
            response = self.session.get(connections_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                return self.parse_connections_xml(response.text)
            
        except Exception as e:
            print(f"[-] 收集连接信息失败: {e}")
        
        return []
    
    def parse_connections_xml(self, xml_content):
        """解析连接XML"""
        try:
            import re
            
            connections = []
            
            # 简单的XML解析
            conn_matches = re.findall(r'<connection[^>]*clientId="([^"]*)"[^>]*remoteAddress="([^"]*)"[^>]*>', 
                                    xml_content, re.IGNORECASE)
            
            for client_id, remote_addr in conn_matches:
                connections.append({
                    'client_id': client_id,
                    'remote_address': remote_addr
                })
            
            return connections
            
        except Exception as e:
            return []
    
    def scan_info_paths(self, management_url, credentials):
        """扫描信息路径"""
        print(f"[*] 扫描信息路径...")
        
        try:
            username, password = credentials
            auth_string = f"{username}:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }
            
            accessible_paths = []
            
            for path in self.info_paths:
                try:
                    test_url = urljoin(management_url, path)
                    response = self.session.get(test_url, headers=headers, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        accessible_paths.append({
                            'path': path,
                            'status_code': response.status_code,
                            'content_length': len(response.text)
                        })
                        print(f"[+] 可访问路径: {path}")
                    elif response.status_code in [401, 403]:
                        print(f"[-] 受限路径: {path} ({response.status_code})")
                    
                except Exception as e:
                    continue
            
            return accessible_paths
            
        except Exception as e:
            print(f"[-] 扫描信息路径失败: {e}")
        
        return []
    
    def generate_report(self):
        """生成完整的信息收集报告"""
        print("=" * 80)
        print("Apache ActiveMQ 信息收集报告")
        print("=" * 80)
        
        # 端口扫描
        open_ports = self.scan_ports()
        
        # 检测管理界面
        management_url = self.detect_management_interface()
        if not management_url:
            print("[-] 未发现ActiveMQ管理界面")
            return None
        
        # 获取有效凭据
        credentials = self.get_valid_credentials(management_url)
        if not credentials:
            print("[-] 无法获取有效凭据")
            return None
        
        # 收集各种信息
        system_info = self.collect_system_info(management_url, credentials)
        queues_info = self.collect_queues_info(management_url, credentials)
        topics_info = self.collect_topics_info(management_url, credentials)
        connections_info = self.collect_connections_info(management_url, credentials)
        accessible_paths = self.scan_info_paths(management_url, credentials)
        
        # 输出报告
        if open_ports:
            print(f"\n[+] 开放端口:")
            for port, service in open_ports.items():
                print(f"  - {port}: {service}")
        
        if system_info:
            print(f"\n[+] 系统信息:")
            for category, info in system_info.items():
                if isinstance(info, dict):
                    for key, value in info.items():
                        print(f"  - {key}: {value}")
        
        if queues_info:
            print(f"\n[+] 队列信息:")
            for queue in queues_info:
                print(f"  - {queue['name']}")
        
        if topics_info:
            print(f"\n[+] 主题信息:")
            for topic in topics_info:
                print(f"  - {topic['name']}")
        
        if connections_info:
            print(f"\n[+] 连接信息:")
            for conn in connections_info:
                print(f"  - {conn['client_id']} ({conn['remote_address']})")
        
        if accessible_paths:
            print(f"\n[+] 可访问路径:")
            for path_info in accessible_paths:
                print(f"  - {path_info['path']}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        if credentials and credentials[0] == 'admin':
            print("  - 更改默认admin用户密码")
        if open_ports.get(61616):
            print("  - 限制OpenWire端口(61616)的网络访问")
        if open_ports.get(8161):
            print("  - 限制管理界面端口(8161)的网络访问")
        
        return {
            'open_ports': open_ports,
            'management_url': management_url,
            'credentials': credentials,
            'system_info': system_info,
            'queues_info': queues_info,
            'topics_info': topics_info,
            'connections_info': connections_info,
            'accessible_paths': accessible_paths
        }

def main():
    parser = argparse.ArgumentParser(description='Apache ActiveMQ 信息收集和扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8161)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--ports-only', action='store_true', help='仅扫描端口')
    parser.add_argument('--system-only', action='store_true', help='仅收集系统信息')
    
    args = parser.parse_args()
    
    scanner = ActiveMQInfoScanner(args.target, args.timeout)
    
    if args.ports_only:
        open_ports = scanner.scan_ports()
        for port, service in open_ports.items():
            print(f"{port}: {service}")
    elif args.system_only:
        management_url = scanner.detect_management_interface()
        if management_url:
            credentials = scanner.get_valid_credentials(management_url)
            if credentials:
                system_info = scanner.collect_system_info(management_url, credentials)
                if system_info:
                    for category, info in system_info.items():
                        if isinstance(info, dict):
                            for key, value in info.items():
                                print(f"{key}: {value}")
    else:
        report = scanner.generate_report()
        
        # 输出到文件
        if args.output and report:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"ActiveMQ信息收集报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("开放端口:\n")
                for port, service in report['open_ports'].items():
                    f.write(f"{port}: {service}\n")
                
                f.write("\n系统信息:\n")
                if report['system_info']:
                    for category, info in report['system_info'].items():
                        if isinstance(info, dict):
                            for key, value in info.items():
                                f.write(f"{key}: {value}\n")
                
                f.write("\n队列信息:\n")
                for queue in report['queues_info']:
                    f.write(f"{queue['name']}\n")
                
                f.write("\n主题信息:\n")
                for topic in report['topics_info']:
                    f.write(f"{topic['name']}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
