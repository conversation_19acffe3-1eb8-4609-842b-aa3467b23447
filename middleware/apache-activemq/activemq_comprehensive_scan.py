#!/usr/bin/env python3
"""
Apache ActiveMQ 综合漏洞扫描工具
集成多个ActiveMQ漏洞检测和利用功能的综合扫描器
"""

import sys
import os
import subprocess
import argparse
import json
from datetime import datetime
import warnings
warnings.filterwarnings("ignore")

class ActiveMQComprehensiveScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.scan_results = {
            'target': target_url,
            'scan_time': datetime.now().isoformat(),
            'vulnerabilities': {},
            'services': {},
            'version_info': {},
            'recommendations': []
        }
        
        # 可用的扫描模块
        self.scan_modules = {
            'CVE-2023-46604': {
                'script': 'CVE-2023-46604.py',
                'description': 'Apache ActiveMQ OpenWire协议反序列化RCE漏洞',
                'severity': 'Critical'
            },
            'CVE-2016-3088': {
                'script': 'CVE-2016-3088.py',
                'description': 'Apache ActiveMQ 文件上传漏洞',
                'severity': 'High'
            },
            'CVE-2015-5254': {
                'script': 'CVE-2015-5254.py',
                'description': 'Apache ActiveMQ JMS ObjectMessage反序列化漏洞',
                'severity': 'High'
            },
            'activemq_bruteforce': {
                'script': 'activemq_bruteforce.py',
                'description': 'Apache ActiveMQ 管理界面暴力破解',
                'severity': 'High'
            }
        }
        
        # 辅助扫描模块
        self.auxiliary_modules = {
            'version_detect': {
                'script': 'activemq_version_detect.py',
                'description': '版本识别'
            },
            'info_scan': {
                'script': 'activemq_info_scan.py',
                'description': '信息收集'
            }
        }
    
    def run_script(self, script_name, args):
        """运行指定的扫描脚本"""
        script_path = os.path.join(os.path.dirname(__file__), script_name)
        
        if not os.path.exists(script_path):
            return None, f"脚本不存在: {script_path}"
        
        try:
            cmd = ['python3', script_path] + args
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=self.timeout * 3
            )
            
            return result.returncode, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            return -1, "", "脚本执行超时"
        except Exception as e:
            return -1, "", str(e)
    
    def scan_vulnerability(self, vuln_id, module_info):
        """扫描单个漏洞"""
        print(f"\n[*] 扫描 {vuln_id}: {module_info['description']}")
        print("-" * 60)
        
        # 构造扫描参数
        args = [self.target_url, '--check-only', '--timeout', str(self.timeout)]
        
        returncode, stdout, stderr = self.run_script(module_info['script'], args)
        
        vulnerability_result = {
            'vuln_id': vuln_id,
            'description': module_info['description'],
            'severity': module_info['severity'],
            'vulnerable': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 检查输出中是否包含漏洞存在的指示
            if ('存在' in stdout or 'vulnerable' in stdout.lower() or 
                '发现' in stdout or 'found' in stdout.lower() or
                '成功' in stdout or 'success' in stdout.lower() or
                '有效凭据' in stdout or 'valid' in stdout.lower()):
                vulnerability_result['vulnerable'] = True
                print(f"[+] {vuln_id} 漏洞存在!")
            else:
                print(f"[-] {vuln_id} 漏洞不存在")
        else:
            print(f"[-] {vuln_id} 扫描失败: {stderr}")
        
        self.scan_results['vulnerabilities'][vuln_id] = vulnerability_result
        return vulnerability_result
    
    def scan_version_info(self):
        """扫描版本信息"""
        print(f"\n[*] 扫描版本信息")
        print("-" * 60)
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('activemq_version_detect.py', args)
        
        if returncode == 0:
            print(stdout)
            self.scan_results['version_info'] = {
                'detected': True,
                'output': stdout
            }
        else:
            print(f"[-] 版本检测失败: {stderr}")
            self.scan_results['version_info'] = {
                'detected': False,
                'error': stderr
            }
    
    def scan_info_collection(self):
        """扫描信息收集"""
        print(f"\n[*] 扫描信息收集")
        print("-" * 60)
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('activemq_info_scan.py', args)
        
        if returncode == 0:
            print(stdout)
            self.scan_results['info_collection'] = {
                'scanned': True,
                'output': stdout
            }
        else:
            print(f"[-] 信息收集失败: {stderr}")
            self.scan_results['info_collection'] = {
                'scanned': False,
                'error': stderr
            }
    
    def generate_recommendations(self):
        """生成安全建议"""
        recommendations = []
        
        # 基于漏洞扫描结果生成建议
        critical_vulns = []
        high_vulns = []
        medium_vulns = []
        
        for vuln_id, vuln_info in self.scan_results['vulnerabilities'].items():
            if vuln_info['vulnerable']:
                if vuln_info['severity'] == 'Critical':
                    critical_vulns.append(vuln_id)
                elif vuln_info['severity'] == 'High':
                    high_vulns.append(vuln_id)
                elif vuln_info['severity'] == 'Medium':
                    medium_vulns.append(vuln_id)
        
        if critical_vulns:
            recommendations.append({
                'priority': 'Critical',
                'issue': f"发现严重漏洞: {', '.join(critical_vulns)}",
                'recommendation': "立即修复这些严重漏洞，它们可能导致远程代码执行"
            })
        
        if high_vulns:
            recommendations.append({
                'priority': 'High',
                'issue': f"发现高危漏洞: {', '.join(high_vulns)}",
                'recommendation': "尽快修复这些高危漏洞"
            })
        
        if medium_vulns:
            recommendations.append({
                'priority': 'Medium',
                'issue': f"发现中危漏洞: {', '.join(medium_vulns)}",
                'recommendation': "建议修复这些中危漏洞"
            })
        
        # 基于版本信息生成建议
        if 'version_info' in self.scan_results and self.scan_results['version_info'].get('detected'):
            output = self.scan_results['version_info']['output']
            if 'activemq' in output.lower():
                recommendations.append({
                    'priority': 'Medium',
                    'issue': "检测到ActiveMQ版本信息",
                    'recommendation': "考虑隐藏ActiveMQ版本信息以减少信息泄露"
                })
        
        # 基于暴力破解检测生成建议
        if 'activemq_bruteforce' in self.scan_results['vulnerabilities']:
            bruteforce_result = self.scan_results['vulnerabilities']['activemq_bruteforce']
            if bruteforce_result['vulnerable']:
                recommendations.append({
                    'priority': 'High',
                    'issue': "发现弱口令",
                    'recommendation': "更改默认密码，使用强密码策略"
                })
        
        # 基于文件上传漏洞检测生成建议
        if 'CVE-2016-3088' in self.scan_results['vulnerabilities']:
            upload_result = self.scan_results['vulnerabilities']['CVE-2016-3088']
            if upload_result['vulnerable']:
                recommendations.append({
                    'priority': 'High',
                    'issue': "发现文件上传漏洞",
                    'recommendation': "升级ActiveMQ版本，限制文件上传功能"
                })
        
        # 基于反序列化漏洞检测生成建议
        if 'CVE-2023-46604' in self.scan_results['vulnerabilities']:
            rce_result = self.scan_results['vulnerabilities']['CVE-2023-46604']
            if rce_result['vulnerable']:
                recommendations.append({
                    'priority': 'Critical',
                    'issue': "发现OpenWire协议反序列化RCE漏洞",
                    'recommendation': "立即升级ActiveMQ版本，禁用不安全的反序列化"
                })
        
        self.scan_results['recommendations'] = recommendations
        return recommendations
    
    def run_comprehensive_scan(self, selected_vulns=None):
        """运行综合扫描"""
        print("=" * 80)
        print(f"Apache ActiveMQ 综合漏洞扫描")
        print(f"目标: {self.target_url}")
        print(f"扫描时间: {self.scan_results['scan_time']}")
        print("=" * 80)
        
        # 1. 版本识别
        self.scan_version_info()
        
        # 2. 信息收集
        self.scan_info_collection()
        
        # 3. 漏洞扫描
        vulns_to_scan = selected_vulns or list(self.scan_modules.keys())
        
        for vuln_id in vulns_to_scan:
            if vuln_id in self.scan_modules:
                self.scan_vulnerability(vuln_id, self.scan_modules[vuln_id])
        
        # 4. 生成建议
        recommendations = self.generate_recommendations()
        
        # 5. 输出总结
        self.print_summary()
        
        return self.scan_results
    
    def print_summary(self):
        """打印扫描总结"""
        print("\n" + "=" * 80)
        print("扫描总结")
        print("=" * 80)
        
        # 漏洞总结
        vulnerable_count = sum(1 for v in self.scan_results['vulnerabilities'].values() if v['vulnerable'])
        total_count = len(self.scan_results['vulnerabilities'])
        
        print(f"\n[*] 漏洞扫描结果: {vulnerable_count}/{total_count} 个漏洞存在")
        
        for vuln_id, vuln_info in self.scan_results['vulnerabilities'].items():
            status = "存在" if vuln_info['vulnerable'] else "不存在"
            severity = vuln_info['severity']
            print(f"  {vuln_id} ({severity}): {status}")
        
        # 安全建议
        if self.scan_results['recommendations']:
            print(f"\n[!] 安全建议:")
            for i, rec in enumerate(self.scan_results['recommendations'], 1):
                print(f"  {i}. [{rec['priority']}] {rec['issue']}")
                print(f"     建议: {rec['recommendation']}")
        
        # 风险评估
        critical_count = sum(1 for v in self.scan_results['vulnerabilities'].values() 
                           if v['vulnerable'] and v['severity'] == 'Critical')
        high_count = sum(1 for v in self.scan_results['vulnerabilities'].values() 
                        if v['vulnerable'] and v['severity'] == 'High')
        
        if critical_count > 0:
            print(f"\n[!] 风险评估: 严重 (发现 {critical_count} 个严重漏洞)")
        elif high_count > 0:
            print(f"\n[!] 风险评估: 高 (发现 {high_count} 个高危漏洞)")
        elif vulnerable_count > 0:
            print(f"\n[!] 风险评估: 中等 (发现 {vulnerable_count} 个漏洞)")
        else:
            print(f"\n[+] 风险评估: 低 (未发现已知漏洞)")
    
    def save_report(self, filename):
        """保存扫描报告"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
            print(f"\n[+] 扫描报告已保存到: {filename}")
        except Exception as e:
            print(f"[-] 保存报告失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='Apache ActiveMQ 综合漏洞扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8161)')
    parser.add_argument('-v', '--vulns', nargs='+', help='指定要扫描的漏洞')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告文件 (JSON格式)')
    parser.add_argument('--list-vulns', action='store_true', help='列出所有支持的漏洞')
    
    args = parser.parse_args()
    
    scanner = ActiveMQComprehensiveScanner(args.target, args.timeout)
    
    if args.list_vulns:
        print("支持的漏洞:")
        for vuln_id, info in scanner.scan_modules.items():
            print(f"  {vuln_id}: {info['description']} ({info['severity']})")
        return
    
    # 运行综合扫描
    results = scanner.run_comprehensive_scan(args.vulns)
    
    # 保存报告
    if args.output:
        scanner.save_report(args.output)

if __name__ == '__main__':
    main()
