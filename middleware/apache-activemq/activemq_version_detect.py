#!/usr/bin/env python3
"""
Apache ActiveMQ 版本检测和指纹识别工具
通过多种方式识别ActiveMQ版本和配置信息
"""

import sys
import requests
import argparse
import json
import base64
import socket
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class ActiveMQVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 解析目标信息
        from urllib.parse import urlparse
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 8161
        
        # 默认凭据
        self.default_credentials = [
            ('admin', 'admin'),
            ('user', 'user'),
            ('guest', 'guest'),
            ('activemq', 'activemq'),
            ('amq', 'amq'),
        ]
        
        # 已知版本漏洞映射
        self.version_vulnerabilities = {
            '5.15': ['CVE-2023-46604', 'CVE-2016-3088'],
            '5.16': ['CVE-2023-46604', 'CVE-2016-3088'],
            '5.17': ['CVE-2023-46604'],
            '5.18': ['CVE-2023-46604'],
            '5.14': ['CVE-2016-3088', 'CVE-2015-5254'],
            '5.13': ['CVE-2015-5254'],
            '5.12': ['CVE-2015-5254'],
        }
        
        # ActiveMQ指纹特征
        self.fingerprint_indicators = [
            'activemq',
            'apache activemq',
            'message broker',
            'queues',
            'topics',
            'connections',
            'subscribers',
            'producers',
            'consumers',
            'browse',
            'send message',
            'purge',
            'hawtio',
            'jolokia',
        ]
    
    def detect_activemq_service(self):
        """检测ActiveMQ服务"""
        print(f"[*] 检测ActiveMQ服务: {self.target_host}")
        
        detection_results = {
            'management_interface': None,
            'openwire_port': None,
            'other_ports': []
        }
        
        # 检测管理界面
        management_url = self.detect_management_interface()
        if management_url:
            detection_results['management_interface'] = management_url
            print(f"[+] 发现管理界面: {management_url}")
        
        # 检测OpenWire端口
        if self.test_port(61616):
            detection_results['openwire_port'] = 61616
            print(f"[+] OpenWire端口开放: 61616")
        elif self.test_port(61617):
            detection_results['openwire_port'] = 61617
            print(f"[+] SSL OpenWire端口开放: 61617")
        
        # 检测其他相关端口
        other_ports = [61613, 61614, 1883, 8883, 5672]
        for port in other_ports:
            if self.test_port(port):
                detection_results['other_ports'].append(port)
                print(f"[+] 其他端口开放: {port}")
        
        return detection_results
    
    def detect_management_interface(self):
        """检测管理界面"""
        # 常见端口和协议
        test_configs = [
            ('http', 8161),
            ('https', 8162),
            ('http', 8080),
            ('https', 8443),
        ]
        
        for protocol, port in test_configs:
            try:
                base_url = f"{protocol}://{self.target_host}:{port}"
                
                # 测试管理界面路径
                test_paths = ['/admin', '/admin/', '/hawtio', '/console', '/']
                
                for path in test_paths:
                    test_url = urljoin(base_url, path)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    if self.is_activemq_management(response):
                        return base_url
                        
            except Exception as e:
                continue
        
        return None
    
    def test_port(self, port):
        """测试端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            result = sock.connect_ex((self.target_host, port))
            sock.close()
            
            return result == 0
            
        except Exception as e:
            return False
    
    def is_activemq_management(self, response):
        """检查响应是否来自ActiveMQ管理界面"""
        if response.status_code not in [200, 401, 403]:
            return False
        
        # 检查响应头
        server_header = response.headers.get('Server', '').lower()
        if 'jetty' in server_header:
            content = response.text.lower()
            if 'activemq' in content:
                return True
        
        # 检查响应内容
        content = response.text.lower()
        
        return any(indicator in content for indicator in self.fingerprint_indicators)
    
    def get_version_from_management(self, management_url):
        """通过管理界面获取版本信息"""
        print(f"[*] 通过管理界面获取版本信息...")
        
        for username, password in self.default_credentials:
            try:
                auth_string = f"{username}:{password}"
                auth_bytes = auth_string.encode('ascii')
                auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
                
                headers = {
                    'Authorization': f'Basic {auth_b64}',
                    'Content-Type': 'application/json'
                }
                
                # 尝试获取系统属性
                props_url = urljoin(management_url, '/admin/xml/systemProperties.jsp')
                response = self.session.get(props_url, headers=headers, timeout=self.timeout)
                
                if response.status_code == 200:
                    version_info = self.parse_version_from_xml(response.text)
                    if version_info:
                        version_info['credentials_used'] = f"{username}:{password}"
                        print(f"[+] 通过管理界面获取版本信息成功")
                        return version_info
                        
            except Exception as e:
                continue
        
        print(f"[-] 无法通过管理界面获取版本信息")
        return None
    
    def parse_version_from_xml(self, xml_content):
        """从XML中解析版本信息"""
        try:
            import re
            
            version_info = {}
            
            # 提取ActiveMQ版本
            activemq_patterns = [
                r'activemq[.\s]*version[^>]*>([^<]+)',
                r'apache[.\s]*activemq[^>]*>([^<]+)',
                r'activemq[^>]*>([0-9]+\.[0-9]+\.[0-9]+)',
            ]
            
            for pattern in activemq_patterns:
                match = re.search(pattern, xml_content, re.IGNORECASE)
                if match:
                    version_info['activemq_version'] = match.group(1).strip()
                    break
            
            # 提取Java版本
            java_match = re.search(r'java[.\s]*version[^>]*>([^<]+)', xml_content, re.IGNORECASE)
            if java_match:
                version_info['java_version'] = java_match.group(1).strip()
            
            # 提取操作系统信息
            os_match = re.search(r'os[.\s]*name[^>]*>([^<]+)', xml_content, re.IGNORECASE)
            if os_match:
                version_info['os_name'] = os_match.group(1).strip()
            
            return version_info if version_info else None
            
        except Exception as e:
            return None
    
    def get_version_from_headers(self, management_url):
        """通过响应头获取版本信息"""
        print(f"[*] 通过响应头获取版本信息...")
        
        try:
            response = self.session.get(management_url, timeout=self.timeout)
            
            version_info = {}
            
            # 检查Server头
            server_header = response.headers.get('Server', '')
            if server_header:
                version_info['server_header'] = server_header
                
                # 尝试从Server头提取版本
                import re
                version_match = re.search(r'jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)', 
                                        server_header, re.IGNORECASE)
                if version_match:
                    version_info['jetty_version'] = version_match.group(1)
            
            # 检查其他相关头
            for header_name in ['X-ActiveMQ-Version', 'X-Version', 'X-Powered-By']:
                header_value = response.headers.get(header_name)
                if header_value:
                    version_info[header_name.lower().replace('-', '_')] = header_value
            
            if version_info:
                print(f"[+] 从响应头获取到版本信息")
                return version_info
            else:
                print(f"[-] 响应头中未发现版本信息")
                return None
                
        except Exception as e:
            print(f"[-] 获取响应头失败: {e}")
            return None
    
    def get_version_from_error_pages(self, management_url):
        """通过错误页面获取版本信息"""
        print(f"[*] 通过错误页面获取版本信息...")
        
        # 尝试访问不存在的页面触发错误
        error_paths = [
            '/nonexistent_page_12345',
            '/admin/nonexistent',
            '/invalid_path',
            '/error_test'
        ]
        
        for path in error_paths:
            try:
                error_url = urljoin(management_url, path)
                response = self.session.get(error_url, timeout=self.timeout)
                
                if response.status_code in [404, 500]:
                    content = response.text
                    
                    # 检查错误页面中的版本信息
                    import re
                    version_patterns = [
                        r'activemq[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                        r'jetty[/\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                        r'version[:\s]+([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
                    ]
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            print(f"[+] 从错误页面获取到版本信息")
                            return {
                                'version_from_error': match.group(1),
                                'error_page_content': content[:500]
                            }
                            
            except Exception as e:
                continue
        
        print(f"[-] 错误页面中未发现版本信息")
        return None
    
    def fingerprint_activemq(self, management_url):
        """ActiveMQ指纹识别"""
        print(f"[*] 进行ActiveMQ指纹识别...")
        
        fingerprint_info = {}
        
        try:
            # 测试不同的HTTP方法
            methods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']
            method_results = {}
            
            for method in methods:
                try:
                    response = self.session.request(method, management_url, timeout=self.timeout)
                    method_results[method] = {
                        'status_code': response.status_code,
                        'headers': dict(response.headers),
                        'has_activemq_indicators': any(indicator in response.text.lower() 
                                                     for indicator in self.fingerprint_indicators)
                    }
                except Exception as e:
                    method_results[method] = {'error': str(e)}
            
            fingerprint_info['http_methods'] = method_results
            
            # 测试特定的ActiveMQ路径
            activemq_paths = [
                '/admin',
                '/admin/',
                '/admin/xml/systemProperties.jsp',
                '/admin/xml/brokerQuery.jsp',
                '/hawtio',
                '/console',
                '/demo',
                '/fileserver'
            ]
            
            path_results = {}
            for path in activemq_paths:
                try:
                    test_url = urljoin(management_url, path)
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    path_results[path] = {
                        'status_code': response.status_code,
                        'content_length': len(response.text),
                        'has_activemq_content': any(indicator in response.text.lower() 
                                                  for indicator in self.fingerprint_indicators)
                    }
                except Exception as e:
                    path_results[path] = {'error': str(e)}
            
            fingerprint_info['path_responses'] = path_results
            
            print(f"[+] ActiveMQ指纹识别完成")
            return fingerprint_info
            
        except Exception as e:
            print(f"[-] 指纹识别失败: {e}")
            return None
    
    def get_vulnerability_info(self, version_info):
        """根据版本信息获取相关漏洞"""
        vulnerabilities = []
        
        if not version_info:
            return vulnerabilities
        
        # 从版本信息中提取版本号
        version = None
        for key, value in version_info.items():
            if 'version' in key and isinstance(value, str):
                # 提取版本号
                import re
                version_match = re.search(r'([0-9]+\.[0-9]+)', value)
                if version_match:
                    version = version_match.group(1)
                    break
        
        if version:
            # 检查已知漏洞
            for vuln_version, vulns in self.version_vulnerabilities.items():
                if version.startswith(vuln_version):
                    vulnerabilities.extend(vulns)
        
        return list(set(vulnerabilities))  # 去重
    
    def generate_security_recommendations(self, version_info, vulnerabilities):
        """生成安全建议"""
        recommendations = []
        
        if version_info and version_info.get('credentials_used'):
            creds = version_info['credentials_used']
            if 'admin:admin' in creds:
                recommendations.append("更改默认admin用户密码")
        
        if vulnerabilities:
            recommendations.append(f"修复已知漏洞: {', '.join(vulnerabilities)}")
        
        if version_info:
            recommendations.append("考虑升级到最新版本")
            recommendations.append("限制管理界面的网络访问")
            recommendations.append("配置适当的用户权限")
        
        return recommendations
    
    def generate_report(self):
        """生成完整的版本检测报告"""
        print("=" * 80)
        print("Apache ActiveMQ 版本检测报告")
        print("=" * 80)
        
        # 服务检测
        detection_results = self.detect_activemq_service()
        
        if not detection_results['management_interface']:
            print("[-] 未发现ActiveMQ管理界面")
            return None
        
        management_url = detection_results['management_interface']
        
        # 版本信息收集
        management_version_info = self.get_version_from_management(management_url)
        header_version_info = self.get_version_from_headers(management_url)
        error_version_info = self.get_version_from_error_pages(management_url)
        
        # 指纹识别
        fingerprint_info = self.fingerprint_activemq(management_url)
        
        # 合并版本信息
        all_version_info = {}
        if management_version_info:
            all_version_info.update(management_version_info)
        if header_version_info:
            all_version_info.update(header_version_info)
        if error_version_info:
            all_version_info.update(error_version_info)
        
        # 漏洞信息
        vulnerabilities = self.get_vulnerability_info(all_version_info)
        
        # 安全建议
        recommendations = self.generate_security_recommendations(all_version_info, vulnerabilities)
        
        # 输出报告
        print(f"\n[+] 服务检测结果:")
        print(f"  - 管理界面: {detection_results['management_interface']}")
        if detection_results['openwire_port']:
            print(f"  - OpenWire端口: {detection_results['openwire_port']}")
        if detection_results['other_ports']:
            print(f"  - 其他端口: {', '.join(map(str, detection_results['other_ports']))}")
        
        if all_version_info:
            print(f"\n[+] 版本信息:")
            for key, value in all_version_info.items():
                if key != 'error_page_content':  # 跳过长内容
                    print(f"  - {key}: {value}")
        
        if vulnerabilities:
            print(f"\n[!] 可能存在的漏洞:")
            for cve in vulnerabilities:
                print(f"  - {cve}")
        
        if recommendations:
            print(f"\n[*] 安全建议:")
            for rec in recommendations:
                print(f"  - {rec}")
        
        return {
            'detection_results': detection_results,
            'version_info': all_version_info,
            'fingerprint_info': fingerprint_info,
            'vulnerabilities': vulnerabilities,
            'recommendations': recommendations
        }

def main():
    parser = argparse.ArgumentParser(description='Apache ActiveMQ 版本检测和指纹识别工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8161)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--management-only', action='store_true', help='仅通过管理界面检测版本')
    parser.add_argument('--fingerprint-only', action='store_true', help='仅进行指纹识别')
    
    args = parser.parse_args()
    
    detector = ActiveMQVersionDetector(args.target, args.timeout)
    
    if args.management_only:
        management_url = detector.detect_management_interface()
        if management_url:
            version_info = detector.get_version_from_management(management_url)
            if version_info:
                for key, value in version_info.items():
                    print(f"{key}: {value}")
            else:
                print("无法获取版本信息")
        else:
            print("未发现管理界面")
    elif args.fingerprint_only:
        management_url = detector.detect_management_interface()
        if management_url:
            fingerprint_info = detector.fingerprint_activemq(management_url)
            if fingerprint_info:
                print(json.dumps(fingerprint_info, indent=2))
        else:
            print("未发现管理界面")
    else:
        report = detector.generate_report()
        
        # 输出到文件
        if args.output and report:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"ActiveMQ版本检测报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n可能的漏洞:\n")
                for cve in report['vulnerabilities']:
                    f.write(f"{cve}\n")
                
                f.write("\n安全建议:\n")
                for rec in report['recommendations']:
                    f.write(f"{rec}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
