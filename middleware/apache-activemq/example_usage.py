#!/usr/bin/env python3
"""
Apache ActiveMQ 漏洞扫描工具使用示例
演示如何使用各种扫描脚本
"""

import subprocess
import sys
import time

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("命令执行超时")
    except Exception as e:
        print(f"执行错误: {e}")
    
    time.sleep(2)

def main():
    if len(sys.argv) != 2:
        print("用法: python3 example_usage.py <target_url>")
        print("示例: python3 example_usage.py http://*************:8161")
        sys.exit(1)
    
    target = sys.argv[1]
    
    print("Apache ActiveMQ 漏洞扫描工具使用示例")
    print(f"目标: {target}")
    
    # 示例1: 综合扫描
    run_command([
        'python3', 'activemq_comprehensive_scan.py', target
    ], "综合漏洞扫描")
    
    # 示例2: 版本识别
    run_command([
        'python3', 'activemq_version_detect.py', target
    ], "ActiveMQ版本识别")
    
    # 示例3: 信息收集
    run_command([
        'python3', 'activemq_info_scan.py', target
    ], "ActiveMQ信息收集")
    
    # 示例4: CVE-2023-46604检测
    openwire_target = target.replace('http://', 'tcp://').replace('8161', '61616')
    run_command([
        'python3', 'CVE-2023-46604.py', openwire_target, '--check-only'
    ], "CVE-2023-46604 OpenWire协议反序列化RCE漏洞检测")
    
    # 示例5: CVE-2016-3088检测
    run_command([
        'python3', 'CVE-2016-3088.py', target, '--check-only'
    ], "CVE-2016-3088 文件上传漏洞检测")
    
    # 示例6: CVE-2015-5254检测
    run_command([
        'python3', 'CVE-2015-5254.py', openwire_target, '--check-only'
    ], "CVE-2015-5254 JMS ObjectMessage反序列化漏洞检测")
    
    # 示例7: 暴力破解检测
    run_command([
        'python3', 'activemq_bruteforce.py', target, '--check-only'
    ], "ActiveMQ管理界面暴力破解检测")
    
    print(f"\n{'='*60}")
    print("示例演示完成!")
    print("更多用法请参考 README.md 文件")
    print(f"{'='*60}")

if __name__ == '__main__':
    main()
