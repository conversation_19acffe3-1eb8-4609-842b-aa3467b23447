# Apache ActiveMQ 漏洞扫描工具依赖包
# 安装命令: pip install -r requirements.txt

# HTTP请求库
requests>=2.25.1

# URL解析和处理
urllib3>=1.26.0

# 命令行参数解析 (Python内置，但列出以确保兼容性)
argparse

# 正则表达式 (Python内置)
re

# 系统相关 (Python内置)
sys
os
subprocess
threading
time
socket
struct

# 编码处理 (Python内置)
base64
binascii

# 日期时间处理 (Python内置)
datetime

# JSON处理 (Python内置)
json

# 警告处理 (Python内置)
warnings

# 可选依赖 (用于增强功能)
# 如果需要更好的XML解析支持
# lxml>=4.6.0

# 如果需要更好的异步支持
# aiohttp>=3.8.0

# 如果需要更好的SSL/TLS支持
# cryptography>=3.4.0

# 如果需要更好的并发控制
# concurrent.futures (Python 3.2+内置)

# 注意事项:
# 1. 大部分依赖都是Python标准库，无需额外安装
# 2. 主要外部依赖是requests库
# 3. 建议Python版本: 3.6+
# 4. 某些功能可能需要特定的网络权限
# 5. OpenWire协议相关功能可能需要额外的序列化库支持
