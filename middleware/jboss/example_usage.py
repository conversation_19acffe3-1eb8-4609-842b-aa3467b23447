#!/usr/bin/env python3
"""
JBoss 漏洞扫描工具使用示例
演示如何使用各种扫描脚本
"""

import subprocess
import sys
import time

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("命令执行超时")
    except Exception as e:
        print(f"执行错误: {e}")
    
    time.sleep(2)

def main():
    if len(sys.argv) != 2:
        print("用法: python3 example_usage.py <target_url>")
        print("示例: python3 example_usage.py http://*************:8080")
        sys.exit(1)
    
    target = sys.argv[1]
    
    print("JBoss 漏洞扫描工具使用示例")
    print(f"目标: {target}")
    
    # 示例1: 综合扫描
    run_command([
        'python3', 'jboss_comprehensive_scan.py', target
    ], "综合漏洞扫描")
    
    # 示例2: 版本识别
    run_command([
        'python3', 'jboss_version_detect.py', target
    ], "JBoss版本识别")
    
    # 示例3: 信息泄露扫描
    run_command([
        'python3', 'jboss_info_leak_scan.py', target
    ], "信息泄露扫描")
    
    # 示例4: CVE-2015-7501检测
    run_command([
        'python3', 'CVE-2015-7501.py', target, '--check-only'
    ], "CVE-2015-7501 Java反序列化漏洞检测")
    
    # 示例5: CVE-2017-12149检测
    run_command([
        'python3', 'CVE-2017-12149.py', target, '--check-only'
    ], "CVE-2017-12149 反序列化漏洞检测")
    
    # 示例6: CVE-2010-0738检测
    run_command([
        'python3', 'CVE-2010-0738.py', target, '--check-only'
    ], "CVE-2010-0738 JMX Console未授权访问检测")
    
    # 示例7: 默认凭据测试
    run_command([
        'python3', 'jboss_admin_bruteforce.py', target, '--quick'
    ], "快速默认凭据测试")
    
    # 示例8: 目录遍历检测
    run_command([
        'python3', 'CVE-2006-5750.py', target, '--check-only'
    ], "CVE-2006-5750 目录遍历漏洞检测")
    
    print(f"\n{'='*60}")
    print("示例演示完成!")
    print("更多用法请参考 README.md 文件")
    print(f"{'='*60}")

if __name__ == '__main__':
    main()
