#!/usr/bin/env python3
"""
JBoss 综合漏洞扫描工具
集成多个CVE检测和利用功能的综合扫描器
"""

import sys
import os
import subprocess
import argparse
import json
from datetime import datetime
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JBossComprehensiveScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.scan_results = {
            'target': target_url,
            'scan_time': datetime.now().isoformat(),
            'vulnerabilities': {},
            'services': {},
            'version_info': {},
            'recommendations': []
        }
        
        # 可用的扫描模块
        self.scan_modules = {
            'CVE-2010-0738': {
                'script': 'CVE-2010-0738.py',
                'description': 'JMX Console 未授权访问',
                'severity': 'High'
            },
            'CVE-2015-7501': {
                'script': 'CVE-2015-7501.py',
                'description': 'Java反序列化漏洞',
                'severity': 'Critical'
            },
            'CVE-2017-12149': {
                'script': 'CVE-2017-12149.py',
                'description': 'ReadOnlyAccessFilter 反序列化',
                'severity': 'Critical'
            },
            'CVE-2006-5750': {
                'script': 'CVE-2006-5750.py',
                'description': '目录遍历漏洞',
                'severity': 'High'
            },
            'CVE-2017-7504': {
                'script': 'CVE-2017-7504.py',
                'description': 'JMS HTTP反序列化',
                'severity': 'Critical'
            },
            'CVE-2017-2595': {
                'script': 'CVE-2017-2595.py',
                'description': '日志查看器路径遍历',
                'severity': 'Medium'
            },
            'CVE-2014-3530': {
                'script': 'CVE-2014-3530.py',
                'description': 'PicketLink XXE漏洞',
                'severity': 'High'
            }
        }
        
        # 辅助扫描模块
        self.auxiliary_modules = {
            'version_detect': {
                'script': 'jboss_version_detect.py',
                'description': '版本识别'
            },
            'info_leak_scan': {
                'script': 'jboss_info_leak_scan.py',
                'description': '信息泄露扫描'
            },
            'admin_bruteforce': {
                'script': 'jboss_admin_bruteforce.py',
                'description': '管理控制台暴力破解'
            }
        }
    
    def run_script(self, script_name, args):
        """运行指定的扫描脚本"""
        script_path = os.path.join(os.path.dirname(__file__), script_name)
        
        if not os.path.exists(script_path):
            return None, f"脚本不存在: {script_path}"
        
        try:
            cmd = ['python3', script_path] + args
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=self.timeout * 3
            )
            
            return result.returncode, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            return -1, "", "脚本执行超时"
        except Exception as e:
            return -1, "", str(e)
    
    def scan_vulnerability(self, cve_id, module_info):
        """扫描单个漏洞"""
        print(f"\n[*] 扫描 {cve_id}: {module_info['description']}")
        print("-" * 60)
        
        # 构造扫描参数
        args = [self.target_url, '--check-only', '--timeout', str(self.timeout)]
        
        returncode, stdout, stderr = self.run_script(module_info['script'], args)
        
        vulnerability_result = {
            'cve_id': cve_id,
            'description': module_info['description'],
            'severity': module_info['severity'],
            'vulnerable': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 检查输出中是否包含漏洞存在的指示
            if ('存在' in stdout or 'vulnerable' in stdout.lower() or 
                '发现' in stdout or 'found' in stdout.lower()):
                vulnerability_result['vulnerable'] = True
                print(f"[+] {cve_id} 漏洞存在!")
            else:
                print(f"[-] {cve_id} 漏洞不存在")
        else:
            print(f"[-] {cve_id} 扫描失败: {stderr}")
        
        self.scan_results['vulnerabilities'][cve_id] = vulnerability_result
        return vulnerability_result
    
    def scan_version_info(self):
        """扫描版本信息"""
        print(f"\n[*] 扫描版本信息")
        print("-" * 60)
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('jboss_version_detect.py', args)
        
        if returncode == 0:
            print(stdout)
            self.scan_results['version_info'] = {
                'detected': True,
                'output': stdout
            }
        else:
            print(f"[-] 版本检测失败: {stderr}")
            self.scan_results['version_info'] = {
                'detected': False,
                'error': stderr
            }
    
    def scan_info_leaks(self):
        """扫描信息泄露"""
        print(f"\n[*] 扫描信息泄露")
        print("-" * 60)
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('jboss_info_leak_scan.py', args)
        
        if returncode == 0:
            print(stdout)
            self.scan_results['services'] = {
                'scanned': True,
                'output': stdout
            }
        else:
            print(f"[-] 信息泄露扫描失败: {stderr}")
            self.scan_results['services'] = {
                'scanned': False,
                'error': stderr
            }
    
    def test_default_credentials(self):
        """测试默认凭据"""
        print(f"\n[*] 测试默认凭据")
        print("-" * 60)
        
        args = [self.target_url, '--quick', '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('jboss_admin_bruteforce.py', args)
        
        credential_result = {
            'tested': True,
            'found_credentials': False,
            'output': stdout
        }
        
        if returncode == 0:
            if '发现有效凭据' in stdout or 'valid credentials' in stdout.lower():
                credential_result['found_credentials'] = True
                print("[+] 发现有效的默认凭据!")
            else:
                print("[-] 未发现有效的默认凭据")
        else:
            print(f"[-] 凭据测试失败: {stderr}")
            credential_result['tested'] = False
            credential_result['error'] = stderr
        
        self.scan_results['credentials'] = credential_result
    
    def generate_recommendations(self):
        """生成安全建议"""
        recommendations = []
        
        # 基于漏洞扫描结果生成建议
        critical_vulns = []
        high_vulns = []
        
        for cve_id, vuln_info in self.scan_results['vulnerabilities'].items():
            if vuln_info['vulnerable']:
                if vuln_info['severity'] == 'Critical':
                    critical_vulns.append(cve_id)
                elif vuln_info['severity'] == 'High':
                    high_vulns.append(cve_id)
        
        if critical_vulns:
            recommendations.append({
                'priority': 'Critical',
                'issue': f"发现严重漏洞: {', '.join(critical_vulns)}",
                'recommendation': "立即修复这些严重漏洞，它们可能导致远程代码执行"
            })
        
        if high_vulns:
            recommendations.append({
                'priority': 'High',
                'issue': f"发现高危漏洞: {', '.join(high_vulns)}",
                'recommendation': "尽快修复这些高危漏洞"
            })
        
        # 基于服务扫描结果生成建议
        if 'services' in self.scan_results and self.scan_results['services'].get('scanned'):
            output = self.scan_results['services']['output']
            if 'JMX Console' in output and '可访问' in output:
                recommendations.append({
                    'priority': 'High',
                    'issue': "JMX Console可直接访问",
                    'recommendation': "禁用或限制JMX Console的访问"
                })
        
        # 基于凭据测试结果生成建议
        if (self.scan_results.get('credentials', {}).get('found_credentials')):
            recommendations.append({
                'priority': 'High',
                'issue': "发现默认或弱凭据",
                'recommendation': "更改所有默认密码，使用强密码策略"
            })
        
        self.scan_results['recommendations'] = recommendations
        return recommendations
    
    def run_comprehensive_scan(self, selected_cves=None):
        """运行综合扫描"""
        print("=" * 80)
        print(f"JBoss 综合漏洞扫描")
        print(f"目标: {self.target_url}")
        print(f"扫描时间: {self.scan_results['scan_time']}")
        print("=" * 80)
        
        # 1. 版本识别
        self.scan_version_info()
        
        # 2. 信息泄露扫描
        self.scan_info_leaks()
        
        # 3. 默认凭据测试
        self.test_default_credentials()
        
        # 4. 漏洞扫描
        cves_to_scan = selected_cves or list(self.scan_modules.keys())
        
        for cve_id in cves_to_scan:
            if cve_id in self.scan_modules:
                self.scan_vulnerability(cve_id, self.scan_modules[cve_id])
        
        # 5. 生成建议
        recommendations = self.generate_recommendations()
        
        # 6. 输出总结
        self.print_summary()
        
        return self.scan_results
    
    def print_summary(self):
        """打印扫描总结"""
        print("\n" + "=" * 80)
        print("扫描总结")
        print("=" * 80)
        
        # 漏洞总结
        vulnerable_count = sum(1 for v in self.scan_results['vulnerabilities'].values() if v['vulnerable'])
        total_count = len(self.scan_results['vulnerabilities'])
        
        print(f"\n[*] 漏洞扫描结果: {vulnerable_count}/{total_count} 个漏洞存在")
        
        for cve_id, vuln_info in self.scan_results['vulnerabilities'].items():
            status = "存在" if vuln_info['vulnerable'] else "不存在"
            severity = vuln_info['severity']
            print(f"  {cve_id} ({severity}): {status}")
        
        # 安全建议
        if self.scan_results['recommendations']:
            print(f"\n[!] 安全建议:")
            for i, rec in enumerate(self.scan_results['recommendations'], 1):
                print(f"  {i}. [{rec['priority']}] {rec['issue']}")
                print(f"     建议: {rec['recommendation']}")
        
        # 风险评估
        critical_count = sum(1 for v in self.scan_results['vulnerabilities'].values() 
                           if v['vulnerable'] and v['severity'] == 'Critical')
        
        if critical_count > 0:
            print(f"\n[!] 风险评估: 严重 (发现 {critical_count} 个严重漏洞)")
        elif vulnerable_count > 0:
            print(f"\n[!] 风险评估: 中等 (发现 {vulnerable_count} 个漏洞)")
        else:
            print(f"\n[+] 风险评估: 低 (未发现已知漏洞)")
    
    def save_report(self, filename):
        """保存扫描报告"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
            print(f"\n[+] 扫描报告已保存到: {filename}")
        except Exception as e:
            print(f"[-] 保存报告失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='JBoss 综合漏洞扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-c', '--cves', nargs='+', help='指定要扫描的CVE (例如: CVE-2015-7501 CVE-2017-12149)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告文件 (JSON格式)')
    parser.add_argument('--list-cves', action='store_true', help='列出所有支持的CVE')
    
    args = parser.parse_args()
    
    scanner = JBossComprehensiveScanner(args.target, args.timeout)
    
    if args.list_cves:
        print("支持的CVE漏洞:")
        for cve_id, info in scanner.scan_modules.items():
            print(f"  {cve_id}: {info['description']} ({info['severity']})")
        return
    
    # 运行综合扫描
    results = scanner.run_comprehensive_scan(args.cves)
    
    # 保存报告
    if args.output:
        scanner.save_report(args.output)

if __name__ == '__main__':
    main()
