#!/usr/bin/env python3
"""
JBoss 信息泄露和敏感路径扫描工具
扫描JBoss常见的管理界面、控制台和敏感文件
"""

import sys
import requests
import argparse
from urllib.parse import urljoin
import re
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JBossInfoLeakScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 常见的JBoss敏感路径
        self.paths = [
            # 管理控制台
            '/jmx-console/',
            '/jmx-console/HtmlAdaptor',
            '/web-console/',
            '/admin-console/',
            '/console/',
            '/console/App.html',
            '/console/portal/DefaultPortal',
            '/management/',
            '/management/console',

            # Invoker服务
            '/invoker/JMXInvokerServlet',
            '/invoker/EJBInvokerServlet',
            '/invoker/readonly',
            '/invoker/HTTPInvoker',
            '/invoker/JNDIFactory',

            # 状态和监控
            '/status',
            '/status?full=true',
            '/jbossws/',
            '/jbossws/services',

            # 配置文件和日志
            '/server-info',
            '/server-status',
            '/logs/',
            '/tmp/',
            '/work/',

            # 应用部署
            '/deploy/',
            '/undeploy',
            '/redeploy',

            # 其他敏感路径
            '/HtmlAdaptor',
            '/jndi-view',
            '/system-properties',
            '/thread-dump',
            '/class-loading',
        ]

        # 敏感信息模式
        self.sensitive_patterns = [
            r'password["\s]*[:=]["\s]*([^"\s,}]+)',
            r'username["\s]*[:=]["\s]*([^"\s,}]+)',
            r'jdbc:[\w:/@.-]+',
            r'java\.version',
            r'os\.name',
            r'user\.dir',
            r'java\.home',
            r'JBoss.*version',
            r'Server.*version',
        ]

    def scan_path(self, path):
        """扫描单个路径"""
        url = urljoin(self.target_url, path)
        result = {
            'path': path,
            'url': url,
            'status_code': None,
            'accessible': False,
            'content_length': 0,
            'title': '',
            'sensitive_info': [],
            'error': None
        }

        try:
            # 尝试不同的HTTP方法
            for method in ['GET', 'POST', 'HEAD']:
                response = self.session.request(method, url, timeout=self.timeout)

                if response.status_code == 200:
                    result['status_code'] = response.status_code
                    result['accessible'] = True
                    result['content_length'] = len(response.content)
                    result['method'] = method

                    # 提取页面标题
                    title_match = re.search(r'<title>(.*?)</title>', response.text, re.IGNORECASE)
                    if title_match:
                        result['title'] = title_match.group(1).strip()

                    # 检查敏感信息
                    for pattern in self.sensitive_patterns:
                        matches = re.findall(pattern, response.text, re.IGNORECASE)
                        if matches:
                            result['sensitive_info'].extend(matches)

                    break
                elif response.status_code in [401, 403]:
                    result['status_code'] = response.status_code
                    result['method'] = method
                    break

        except Exception as e:
            result['error'] = str(e)

        return result

    def scan_all(self):
        """扫描所有路径"""
        print(f"[*] 开始扫描目标: {self.target_url}")
        print(f"[*] 扫描路径数量: {len(self.paths)}")
        print("-" * 80)

        accessible_paths = []
        protected_paths = []

        for i, path in enumerate(self.paths, 1):
            print(f"[{i:2d}/{len(self.paths)}] 扫描: {path}", end=" ")

            result = self.scan_path(path)

            if result['accessible']:
                print(f"✓ [{result['status_code']}] {result.get('method', 'GET')}")
                accessible_paths.append(result)

                if result['title']:
                    print(f"    标题: {result['title']}")
                if result['sensitive_info']:
                    print(f"    敏感信息: {', '.join(set(result['sensitive_info'][:3]))}")

            elif result['status_code'] in [401, 403]:
                print(f"⚠ [{result['status_code']}] 需要认证")
                protected_paths.append(result)

            elif result['error']:
                print(f"✗ 错误: {result['error']}")

            else:
                print(f"✗ [{result.get('status_code', 'N/A')}]")

        return accessible_paths, protected_paths

    def generate_report(self, accessible_paths, protected_paths):
        """生成扫描报告"""
        print("\n" + "=" * 80)
        print("扫描报告")
        print("=" * 80)

        if accessible_paths:
            print(f"\n[+] 发现 {len(accessible_paths)} 个可访问路径:")
            for result in accessible_paths:
                print(f"  • {result['path']} ({result['status_code']}) - {result.get('title', 'No title')}")
                if result['sensitive_info']:
                    print(f"    敏感信息: {', '.join(set(result['sensitive_info']))}")

        if protected_paths:
            print(f"\n[!] 发现 {len(protected_paths)} 个受保护路径:")
            for result in protected_paths:
                print(f"  • {result['path']} ({result['status_code']})")

        # 安全建议
        print(f"\n[*] 安全建议:")
        if accessible_paths:
            print("  - 发现可访问的管理界面，建议启用访问控制")
            print("  - 检查是否泄露敏感信息")
            print("  - 考虑禁用不必要的管理接口")

        if any('jmx-console' in r['path'] for r in accessible_paths):
            print("  - JMX Console可访问，存在高风险！")

        if any('invoker' in r['path'] for r in accessible_paths):
            print("  - Invoker服务可访问，可能存在反序列化漏洞！")

def main():
    parser = argparse.ArgumentParser(description='JBoss 信息泄露和敏感路径扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')

    args = parser.parse_args()

    scanner = JBossInfoLeakScanner(args.target, args.timeout)
    accessible_paths, protected_paths = scanner.scan_all()
    scanner.generate_report(accessible_paths, protected_paths)

    # 输出到文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(f"JBoss扫描报告 - {args.target}\n")
            f.write("=" * 50 + "\n\n")

            f.write("可访问路径:\n")
            for result in accessible_paths:
                f.write(f"{result['path']} - {result['status_code']} - {result.get('title', '')}\n")
                if result['sensitive_info']:
                    f.write(f"  敏感信息: {', '.join(set(result['sensitive_info']))}\n")

            f.write("\n受保护路径:\n")
            for result in protected_paths:
                f.write(f"{result['path']} - {result['status_code']}\n")

        print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()