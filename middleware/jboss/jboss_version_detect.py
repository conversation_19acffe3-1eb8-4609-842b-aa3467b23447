#!/usr/bin/env python3
"""
JBoss 版本识别和指纹识别工具
通过多种方式识别JBoss版本和配置信息
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class JBossVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 版本信息检测路径
        self.version_paths = [
            '/status',
            '/status?full=true',
            '/server-info',
            '/server-status',
            '/jmx-console/',
            '/web-console/',
            '/admin-console/',
            '/console/',
            '/management/',
            '/invoker/JMXInvokerServlet',
            '/invoker/EJBInvokerServlet',
            '/invoker/readonly',
        ]
        
        # 版本特征模式
        self.version_patterns = [
            (r'JBoss[^0-9]*([0-9]+\.[0-9]+\.[0-9]+[^<\s]*)', 'JBoss Version'),
            (r'WildFly[^0-9]*([0-9]+\.[0-9]+\.[0-9]+[^<\s]*)', 'WildFly Version'),
            (r'JBoss.*Application.*Server[^0-9]*([0-9]+\.[0-9]+[^<\s]*)', 'JBoss AS'),
            (r'JBoss.*Enterprise.*Application.*Platform[^0-9]*([0-9]+\.[0-9]+[^<\s]*)', 'JBoss EAP'),
            (r'Server:\s*JBoss[^0-9]*([0-9]+\.[0-9]+[^<\s]*)', 'Server Header'),
            (r'X-Powered-By:\s*JBoss[^0-9]*([0-9]+\.[0-9]+[^<\s]*)', 'X-Powered-By Header'),
            (r'java\.version["\s]*[:=]["\s]*([0-9]+\.[0-9]+[^<\s"]*)', 'Java Version'),
            (r'os\.name["\s]*[:=]["\s]*([^<\s"]+)', 'Operating System'),
            (r'user\.dir["\s]*[:=]["\s]*([^<\s"]+)', 'User Directory'),
        ]
        
        # 已知版本漏洞映射
        self.version_vulnerabilities = {
            '4.0': ['CVE-2010-0738', 'CVE-2007-1036', 'CVE-2006-5750'],
            '4.2': ['CVE-2010-0738', 'CVE-2010-1428', 'CVE-2010-1429'],
            '4.3': ['CVE-2010-0738', 'CVE-2010-1428', 'CVE-2010-1429'],
            '5.0': ['CVE-2015-7501', 'CVE-2017-12149'],
            '5.1': ['CVE-2015-7501', 'CVE-2017-12149'],
            '5.2': ['CVE-2015-7501', 'CVE-2017-12149'],
            '6.0': ['CVE-2015-7501', 'CVE-2017-2595'],
            '6.1': ['CVE-2015-7501', 'CVE-2017-2595'],
            '6.2': ['CVE-2015-7501', 'CVE-2017-2595'],
            '6.3': ['CVE-2015-7501', 'CVE-2017-2595'],
            '6.4': ['CVE-2015-7501', 'CVE-2017-2595'],
            '7.0': ['CVE-2017-2595'],
            '7.1': ['CVE-2017-2595'],
        }
    
    def get_server_headers(self):
        """获取服务器响应头信息"""
        print(f"[*] 获取服务器头信息: {self.target_url}")
        
        headers_info = {}
        
        try:
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            # 提取关键头信息
            important_headers = [
                'Server', 'X-Powered-By', 'X-AspNet-Version', 
                'X-Generator', 'X-Version', 'Via'
            ]
            
            for header in important_headers:
                if header in response.headers:
                    headers_info[header] = response.headers[header]
                    print(f"[+] {header}: {response.headers[header]}")
            
            # 检查响应状态和内容
            headers_info['status_code'] = response.status_code
            headers_info['content_length'] = len(response.content)
            
        except Exception as e:
            print(f"[-] 获取头信息失败: {e}")
        
        return headers_info
    
    def detect_version_from_path(self, path):
        """从特定路径检测版本信息"""
        try:
            url = urljoin(self.target_url, path)
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                return response.text
            elif response.status_code == 401:
                # 即使需要认证，有时也会泄露版本信息
                return response.text
                
        except Exception as e:
            pass
        
        return None
    
    def extract_version_info(self, content):
        """从内容中提取版本信息"""
        version_info = {}
        
        for pattern, description in self.version_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                version_info[description] = matches[0]
        
        return version_info
    
    def detect_jboss_version(self):
        """检测JBoss版本"""
        print(f"[*] 检测JBoss版本: {self.target_url}")
        
        all_version_info = {}
        
        # 从服务器头获取信息
        headers_info = self.get_server_headers()
        for header, value in headers_info.items():
            if isinstance(value, str):
                version_info = self.extract_version_info(value)
                all_version_info.update(version_info)
        
        # 从各个路径获取版本信息
        for path in self.version_paths:
            print(f"[*] 检查路径: {path}")
            content = self.detect_version_from_path(path)
            
            if content:
                version_info = self.extract_version_info(content)
                all_version_info.update(version_info)
                
                # 检查特定的版本指示器
                if 'jmx-console' in path.lower():
                    if 'JBoss Management Console' in content:
                        print(f"[+] 发现JMX Console: {path}")
                
                if 'web-console' in path.lower():
                    if 'JBoss Web Console' in content:
                        print(f"[+] 发现Web Console: {path}")
        
        return all_version_info
    
    def detect_java_version(self):
        """检测Java版本信息"""
        print(f"[*] 检测Java环境信息...")
        
        java_info = {}
        
        # 尝试通过JMX获取系统属性
        jmx_paths = [
            '/jmx-console/HtmlAdaptor?action=inspectMBean&name=java.lang:type=Runtime',
            '/status?full=true',
        ]
        
        for path in jmx_paths:
            content = self.detect_version_from_path(path)
            if content:
                # 提取Java系统属性
                java_patterns = [
                    (r'java\.version["\s]*[:=]["\s]*([^<\s"]+)', 'Java Version'),
                    (r'java\.vendor["\s]*[:=]["\s]*([^<\s"]+)', 'Java Vendor'),
                    (r'java\.home["\s]*[:=]["\s]*([^<\s"]+)', 'Java Home'),
                    (r'os\.name["\s]*[:=]["\s]*([^<\s"]+)', 'OS Name'),
                    (r'os\.version["\s]*[:=]["\s]*([^<\s"]+)', 'OS Version'),
                    (r'os\.arch["\s]*[:=]["\s]*([^<\s"]+)', 'OS Architecture'),
                    (r'user\.name["\s]*[:=]["\s]*([^<\s"]+)', 'User Name'),
                    (r'user\.dir["\s]*[:=]["\s]*([^<\s"]+)', 'User Directory'),
                ]
                
                for pattern, description in java_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        java_info[description] = matches[0]
        
        return java_info
    
    def detect_services(self):
        """检测可用的服务和端点"""
        print(f"[*] 检测可用服务...")
        
        services = {}
        
        service_paths = [
            ('/jmx-console/', 'JMX Console'),
            ('/web-console/', 'Web Console'),
            ('/admin-console/', 'Admin Console'),
            ('/console/', 'Management Console'),
            ('/management/', 'Management Interface'),
            ('/invoker/JMXInvokerServlet', 'JMX Invoker'),
            ('/invoker/EJBInvokerServlet', 'EJB Invoker'),
            ('/invoker/readonly', 'ReadOnly Invoker'),
            ('/status', 'Status Page'),
            ('/jbossws/', 'JBoss Web Services'),
        ]
        
        for path, service_name in service_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    services[service_name] = {
                        'path': path,
                        'status': 'accessible',
                        'url': url
                    }
                    print(f"[+] {service_name}: 可访问")
                elif response.status_code == 401:
                    services[service_name] = {
                        'path': path,
                        'status': 'requires_auth',
                        'url': url
                    }
                    print(f"[!] {service_name}: 需要认证")
                elif response.status_code == 403:
                    services[service_name] = {
                        'path': path,
                        'status': 'forbidden',
                        'url': url
                    }
                    print(f"[!] {service_name}: 访问被禁止")
                    
            except Exception as e:
                continue
        
        return services
    
    def get_vulnerability_info(self, version_info):
        """根据版本信息获取相关漏洞"""
        vulnerabilities = []
        
        for version_key, version_value in version_info.items():
            if 'version' in version_key.lower():
                # 提取主版本号
                version_match = re.search(r'(\d+\.\d+)', version_value)
                if version_match:
                    main_version = version_match.group(1)
                    if main_version in self.version_vulnerabilities:
                        vulnerabilities.extend(self.version_vulnerabilities[main_version])
        
        return list(set(vulnerabilities))  # 去重
    
    def generate_report(self):
        """生成完整的检测报告"""
        print("=" * 80)
        print("JBoss 版本检测报告")
        print("=" * 80)
        
        # 检测版本信息
        version_info = self.detect_jboss_version()
        
        if version_info:
            print(f"\n[+] 版本信息:")
            for key, value in version_info.items():
                print(f"  {key}: {value}")
        else:
            print(f"\n[-] 未检测到明确的版本信息")
        
        # 检测Java环境
        java_info = self.detect_java_version()
        
        if java_info:
            print(f"\n[+] Java环境信息:")
            for key, value in java_info.items():
                print(f"  {key}: {value}")
        
        # 检测服务
        services = self.detect_services()
        
        if services:
            print(f"\n[+] 可用服务:")
            for service_name, service_info in services.items():
                print(f"  {service_name}: {service_info['status']} ({service_info['path']})")
        
        # 获取相关漏洞
        vulnerabilities = self.get_vulnerability_info(version_info)
        
        if vulnerabilities:
            print(f"\n[!] 可能存在的漏洞:")
            for cve in vulnerabilities:
                print(f"  {cve}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        if any('accessible' in s.get('status', '') for s in services.values()):
            print("  - 发现可直接访问的管理界面，建议启用访问控制")
        if 'JMX Console' in services and services['JMX Console']['status'] == 'accessible':
            print("  - JMX Console可直接访问，存在高安全风险！")
        if vulnerabilities:
            print("  - 检测到已知漏洞，建议及时更新版本")
        
        return {
            'version_info': version_info,
            'java_info': java_info,
            'services': services,
            'vulnerabilities': vulnerabilities
        }

def main():
    parser = argparse.ArgumentParser(description='JBoss 版本识别和指纹识别工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--services-only', action='store_true', help='仅检测服务')
    parser.add_argument('--version-only', action='store_true', help='仅检测版本')
    
    args = parser.parse_args()
    
    detector = JBossVersionDetector(args.target, args.timeout)
    
    if args.services_only:
        detector.detect_services()
    elif args.version_only:
        version_info = detector.detect_jboss_version()
        for key, value in version_info.items():
            print(f"{key}: {value}")
    else:
        report = detector.generate_report()
        
        # 输出到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"JBoss检测报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\nJava环境:\n")
                for key, value in report['java_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n服务状态:\n")
                for service_name, service_info in report['services'].items():
                    f.write(f"{service_name}: {service_info['status']}\n")
                
                f.write("\n可能的漏洞:\n")
                for cve in report['vulnerabilities']:
                    f.write(f"{cve}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
