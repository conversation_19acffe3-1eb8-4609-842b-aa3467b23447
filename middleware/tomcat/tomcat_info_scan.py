#!/usr/bin/env python3
"""
Tomcat 信息收集和扫描工具
收集tomcat服务器的详细信息和配置
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class TomcatInfoScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 常见的Tomcat路径
        self.tomcat_paths = [
            '/',
            '/manager/html',
            '/manager/text',
            '/manager/status',
            '/manager/jmxproxy',
            '/host-manager/html',
            '/host-manager/text',
            '/examples/',
            '/examples/jsp/',
            '/examples/servlets/',
            '/docs/',
            '/admin/',
            '/status',
            '/server-status',
            '/server-info',
            '/balancer-manager',
            '/jkmanager',
        ]

        # 默认应用
        self.default_apps = [
            '/examples/jsp/',
            '/examples/servlets/',
            '/docs/',
            '/manager/',
            '/host-manager/',
            '/ROOT/',
            '/sample/',
            '/test/',
        ]

        # 敏感文件
        self.sensitive_files = [
            '/WEB-INF/web.xml',
            '/META-INF/context.xml',
            '/META-INF/MANIFEST.MF',
            '/../conf/tomcat-users.xml',
            '/../conf/server.xml',
            '/../conf/web.xml',
            '/../conf/context.xml',
            '/../logs/catalina.out',
            '/../logs/localhost.log',
        ]

        # 版本检测模式
        self.version_patterns = [
            r'Apache Tomcat/([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
            r'Tomcat/([0-9]+\.[0-9]+(?:\.[0-9]+)?)',
            r'Apache-Coyote/([0-9]+\.[0-9]+)',
            r'Server:\s*Apache-Coyote/([0-9]+\.[0-9]+)',
        ]


    def scan_tomcat_info(self):
        """扫描Tomcat基础信息"""
        print(f"[*] 扫描Tomcat基础信息: {self.target_url}")

        info_results = {
            'accessible_paths': [],
            'version_info': {},
            'server_info': {},
            'applications': []
        }

        for path in self.tomcat_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)

                print(f"[*] 测试 {path} -> {response.status_code}")

                if response.status_code in [200, 401, 403]:
                    path_info = {
                        'path': path,
                        'url': url,
                        'status_code': response.status_code,
                        'content_length': len(response.text),
                        'has_tomcat_features': self.check_tomcat_features(response.text)
                    }

                    # 提取版本信息
                    version_info = self.extract_version_info(response)
                    if version_info:
                        path_info['version_info'] = version_info
                        info_results['version_info'].update(version_info)

                    # 检查是否是Tomcat相关页面
                    if self.check_tomcat_features(response.text):
                        print(f"[+] 发现Tomcat相关页面: {path}")
                        info_results['accessible_paths'].append(path_info)

                        # 特殊处理Manager应用
                        if 'manager' in path:
                            self.analyze_manager_app(response, path_info)

                        # 特殊处理示例应用
                        elif 'examples' in path:
                            self.analyze_examples_app(response, path_info)

                    elif response.status_code == 200:
                        print(f"[!] 发现可访问路径: {path}")
                        info_results['accessible_paths'].append(path_info)

            except Exception as e:
                continue

        return info_results

    def check_tomcat_features(self, content):
        """检查内容是否包含Tomcat特征"""
        tomcat_indicators = [
            'tomcat',
            'apache tomcat',
            'catalina',
            'jasper',
            'coyote',
            'servlet',
            'jsp',
            'jsessionid',
            'org.apache.catalina',
            'org.apache.jasper',
        ]

        content_lower = content.lower()

        for indicator in tomcat_indicators:
            if indicator in content_lower:
                return True

        return False

    def extract_version_info(self, response):
        """提取版本信息"""
        version_info = {}

        # 从响应头提取
        server_header = response.headers.get('Server', '')
        if server_header:
            for pattern in self.version_patterns:
                match = re.search(pattern, server_header, re.IGNORECASE)
                if match:
                    version_info['server_header_version'] = match.group(1)
                    break

        # 从响应内容提取
        content = response.text
        for pattern in self.version_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                version_info['content_version'] = match.group(1)
                break

        return version_info

    def analyze_manager_app(self, response, path_info):
        """分析Manager应用"""
        print(f"[*] 分析Manager应用: {path_info['path']}")

        if response.status_code == 401:
            print(f"[!] Manager应用需要认证")
            path_info['requires_auth'] = True
        elif response.status_code == 200:
            print(f"[+] Manager应用可直接访问")
            path_info['requires_auth'] = False

            # 检查是否包含应用列表
            if 'application' in response.text.lower():
                print(f"[+] Manager应用包含应用列表")
                path_info['has_app_list'] = True
        elif response.status_code == 403:
            print(f"[!] Manager应用被禁止访问")
            path_info['forbidden'] = True

    def analyze_examples_app(self, response, path_info):
        """分析示例应用"""
        print(f"[*] 分析示例应用: {path_info['path']}")

        if response.status_code == 200:
            print(f"[+] 示例应用可访问")

            # 检查是否包含JSP示例
            if 'jsp' in response.text.lower():
                print(f"[+] 包含JSP示例")
                path_info['has_jsp_examples'] = True

            # 检查是否包含Servlet示例
            if 'servlet' in response.text.lower():
                print(f"[+] 包含Servlet示例")
                path_info['has_servlet_examples'] = True


    def check_default_apps(self):
        """检测默认应用"""
        print(f"[*] 检测默认应用...")

        default_app_results = []

        for app_path in self.default_apps:
            try:
                url = urljoin(self.target_url, app_path)
                response = self.session.get(url, timeout=self.timeout)

                print(f"[*] 检测 {app_path} -> {response.status_code}")

                if response.status_code == 200:
                    print(f"[+] 发现可访问的默认应用: {app_path}")

                    app_info = {
                        'path': app_path,
                        'url': url,
                        'status_code': response.status_code,
                        'content_length': len(response.text),
                        'description': self.get_app_description(app_path, response.text)
                    }

                    default_app_results.append(app_info)

            except Exception as e:
                continue

        return default_app_results

    def get_app_description(self, app_path, content):
        """获取应用描述"""
        descriptions = {
            '/examples/': 'Tomcat示例应用',
            '/docs/': 'Tomcat文档',
            '/manager/': 'Tomcat Manager应用',
            '/host-manager/': 'Tomcat Host Manager应用',
            '/ROOT/': 'Tomcat根应用',
        }

        for path, desc in descriptions.items():
            if app_path.startswith(path):
                return desc

        return '未知应用'

    def check_sensitive_files(self):
        """检测敏感文件"""
        print(f"[*] 检测敏感文件...")

        sensitive_file_results = []

        for file_path in self.sensitive_files:
            try:
                url = urljoin(self.target_url, file_path)
                response = self.session.get(url, timeout=self.timeout)

                print(f"[*] 检测 {file_path} -> {response.status_code}")

                if response.status_code == 200 and len(response.text) > 0:
                    # 检查是否是有效的文件内容
                    if self.is_valid_sensitive_file(file_path, response.text):
                        print(f"[+] 发现敏感文件: {file_path}")

                        file_info = {
                            'path': file_path,
                            'url': url,
                            'status_code': response.status_code,
                            'content_length': len(response.text),
                            'content_preview': response.text[:500]
                        }

                        sensitive_file_results.append(file_info)

            except Exception as e:
                continue

        return sensitive_file_results

    def is_valid_sensitive_file(self, file_path, content):
        """检查是否是有效的敏感文件"""
        content_lower = content.lower()

        if 'web.xml' in file_path:
            return '<web-app' in content_lower or '<servlet' in content_lower
        elif 'context.xml' in file_path:
            return '<context' in content_lower
        elif 'server.xml' in file_path:
            return '<server' in content_lower or '<connector' in content_lower
        elif 'tomcat-users.xml' in file_path:
            return '<tomcat-users' in content_lower or '<user' in content_lower
        elif '.log' in file_path or 'catalina.out' in file_path:
            return len(content) > 50 and ('info' in content_lower or 'error' in content_lower)
        elif 'manifest.mf' in file_path:
            return 'manifest-version' in content_lower
        else:
            return len(content.strip()) > 20

    def generate_report(self):
        """生成完整的信息收集报告"""
        print("=" * 80)
        print("Tomcat 信息收集报告")
        print("=" * 80)

        # 基础信息扫描
        info_results = self.scan_tomcat_info()

        # 默认应用检测
        default_apps = self.check_default_apps()

        # 敏感文件检测
        sensitive_files = self.check_sensitive_files()

        # 输出报告
        if info_results['version_info']:
            print(f"\n[+] 版本信息:")
            for key, value in info_results['version_info'].items():
                print(f"  {key}: {value}")

        if info_results['accessible_paths']:
            print(f"\n[+] 可访问路径:")
            for path_info in info_results['accessible_paths']:
                print(f"  - {path_info['path']} (状态码: {path_info['status_code']})")

        if default_apps:
            print(f"\n[+] 默认应用:")
            for app_info in default_apps:
                print(f"  - {app_info['path']} - {app_info['description']}")

        if sensitive_files:
            print(f"\n[!] 敏感文件:")
            for file_info in sensitive_files:
                print(f"  - {file_info['path']}")

        # 安全建议
        print(f"\n[*] 安全建议:")
        if default_apps:
            print("  - 移除或限制访问默认应用")
        if sensitive_files:
            print("  - 保护敏感文件不被直接访问")
        if any(path['path'] == '/manager/html' and path.get('requires_auth', True) == False
               for path in info_results['accessible_paths']):
            print("  - 为Manager应用配置认证")

        return {
            'info_results': info_results,
            'default_apps': default_apps,
            'sensitive_files': sensitive_files
        }

def main():
    parser = argparse.ArgumentParser(description='Tomcat 信息收集和扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--info-only', action='store_true', help='仅收集基础信息')
    parser.add_argument('--apps-only', action='store_true', help='仅检测默认应用')
    parser.add_argument('--files-only', action='store_true', help='仅检测敏感文件')

    args = parser.parse_args()

    scanner = TomcatInfoScanner(args.target, args.timeout)

    if args.info_only:
        info_results = scanner.scan_tomcat_info()
        for path_info in info_results['accessible_paths']:
            print(f"{path_info['path']}: {path_info['status_code']}")
    elif args.apps_only:
        default_apps = scanner.check_default_apps()
        for app_info in default_apps:
            print(f"{app_info['path']}: {app_info['description']}")
    elif args.files_only:
        sensitive_files = scanner.check_sensitive_files()
        for file_info in sensitive_files:
            print(f"{file_info['path']}: {file_info['content_length']} bytes")
    else:
        report = scanner.generate_report()

        # 输出到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Tomcat信息收集报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")

                f.write("版本信息:\n")
                for key, value in report['info_results']['version_info'].items():
                    f.write(f"{key}: {value}\n")

                f.write("\n可访问路径:\n")
                for path_info in report['info_results']['accessible_paths']:
                    f.write(f"{path_info['path']}: {path_info['status_code']}\n")

                f.write("\n默认应用:\n")
                for app_info in report['default_apps']:
                    f.write(f"{app_info['path']}: {app_info['description']}\n")

                f.write("\n敏感文件:\n")
                for file_info in report['sensitive_files']:
                    f.write(f"{file_info['path']}\n")

            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()