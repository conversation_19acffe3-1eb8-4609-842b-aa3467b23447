#!/usr/bin/env python3
"""
Tomcat 版本识别和指纹识别工具
通过多种方式识别tomcat版本和配置信息
"""

import sys
import requests
import argparse
import re
from urllib.parse import urljoin
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class TomcatVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # 版本检测路径
        self.version_paths = [
            '/',
            '/index.jsp',
            '/index.html',
            '/manager/html',
            '/manager/text',
            '/host-manager/html',
            '/examples/',
            '/docs/',
            '/admin/',
            '/status',
            '/server-status',
            '/server-info',
            '/404',
            '/nonexistent',
        ]
        
        # 错误页面触发路径
        self.error_paths = [
            '/nonexistent_page_12345',
            '/error_test_tomcat',
            '/404_test',
            '/500_test',
            '/403_test',
        ]
        
        # 已知版本漏洞映射
        self.version_vulnerabilities = {
            '6.0': ['CVE-2020-1938'],
            '7.0': ['CVE-2017-12615', 'CVE-2017-12617', 'CVE-2020-1938'],
            '8.0': ['CVE-2019-0232', 'CVE-2020-1938'],
            '8.5': ['CVE-2019-0232', 'CVE-2020-1938'],
            '9.0': ['CVE-2019-0232', 'CVE-2020-1938', 'CVE-2020-9484'],
        }
        
        # 特征检测
        self.tomcat_indicators = [
            'Apache Tomcat',
            'Tomcat',
            'org.apache.catalina',
            'org.apache.jasper',
            'JSESSIONID',
            'Catalina',
            'jasper',
            'servlet',
        ]
    
    def get_server_headers(self):
        """获取服务器响应头信息"""
        print(f"[*] 获取服务器头信息: {self.target_url}")
        
        headers_info = {}
        
        try:
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            # 提取关键头信息
            important_headers = [
                'Server', 'X-Powered-By', 'X-AspNet-Version', 
                'X-Generator', 'X-Version', 'Via', 'X-Tomcat-Version'
            ]
            
            for header in important_headers:
                if header in response.headers:
                    headers_info[header] = response.headers[header]
                    print(f"[+] {header}: {response.headers[header]}")
            
            # 检查Set-Cookie中的JSESSIONID
            if 'Set-Cookie' in response.headers:
                if 'JSESSIONID' in response.headers['Set-Cookie']:
                    headers_info['JSESSIONID'] = 'Present'
                    print(f"[+] JSESSIONID: Present")
            
            # 检查响应状态和内容
            headers_info['status_code'] = response.status_code
            headers_info['content_length'] = len(response.content)
            
            return headers_info, response
            
        except Exception as e:
            print(f"[-] 获取头信息失败: {e}")
            return {}, None
    
    def detect_version_from_headers(self, headers_info):
        """从响应头检测版本信息"""
        version_info = {}
        
        # 检查Server头
        if 'Server' in headers_info:
            server_header = headers_info['Server']
            
            # 提取Tomcat版本
            tomcat_match = re.search(r'Apache-Coyote/([0-9]+\.[0-9]+)', server_header, re.IGNORECASE)
            if tomcat_match:
                version_info['coyote_version'] = tomcat_match.group(1)
                print(f"[+] 从Server头检测到Coyote版本: {tomcat_match.group(1)}")
            
            tomcat_match = re.search(r'Tomcat/([0-9]+\.[0-9]+(?:\.[0-9]+)?)', server_header, re.IGNORECASE)
            if tomcat_match:
                version_info['tomcat_version'] = tomcat_match.group(1)
                print(f"[+] 从Server头检测到Tomcat版本: {tomcat_match.group(1)}")
            elif 'tomcat' in server_header.lower():
                version_info['tomcat_detected'] = True
                print(f"[+] 检测到Tomcat，但版本被隐藏")
        
        return version_info
    
    def detect_version_from_errors(self):
        """通过错误页面检测版本信息"""
        print(f"[*] 通过错误页面检测版本...")
        
        version_info = {}
        
        for error_path in self.error_paths:
            try:
                url = urljoin(self.target_url, error_path)
                response = self.session.get(url, timeout=self.timeout)
                
                # 检查错误页面内容
                if response.status_code in [404, 403, 500]:
                    content = response.text.lower()
                    
                    # 查找版本信息
                    tomcat_match = re.search(r'apache tomcat/([0-9]+\.[0-9]+(?:\.[0-9]+)?)', content, re.IGNORECASE)
                    if tomcat_match:
                        version_info['tomcat_version'] = tomcat_match.group(1)
                        print(f"[+] 从错误页面检测到版本: {tomcat_match.group(1)}")
                        break
                    elif 'tomcat' in content:
                        version_info['tomcat_detected'] = True
                        print(f"[+] 从错误页面检测到Tomcat")
                        
            except Exception as e:
                continue
        
        return version_info
    
    def detect_version_from_manager(self):
        """通过Manager应用检测版本"""
        print(f"[*] 通过Manager应用检测版本...")
        
        version_info = {}
        
        manager_paths = ['/manager/html', '/manager/text', '/host-manager/html']
        
        for path in manager_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                # 即使返回401，也可能在响应中包含版本信息
                if response.status_code in [200, 401, 403]:
                    content = response.text.lower()
                    
                    # 查找版本信息
                    tomcat_match = re.search(r'apache tomcat/([0-9]+\.[0-9]+(?:\.[0-9]+)?)', content, re.IGNORECASE)
                    if tomcat_match:
                        version_info['tomcat_version'] = tomcat_match.group(1)
                        print(f"[+] 从Manager应用检测到版本: {tomcat_match.group(1)}")
                        break
                    elif any(indicator in content for indicator in self.tomcat_indicators):
                        version_info['tomcat_detected'] = True
                        print(f"[+] 从Manager应用检测到Tomcat")
                        
            except Exception as e:
                continue
        
        return version_info
    
    def detect_version_from_examples(self):
        """通过示例应用检测版本"""
        print(f"[*] 通过示例应用检测版本...")
        
        version_info = {}
        
        examples_paths = ['/examples/', '/examples/jsp/', '/examples/servlets/']
        
        for path in examples_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    
                    # 查找版本信息
                    tomcat_match = re.search(r'apache tomcat/([0-9]+\.[0-9]+(?:\.[0-9]+)?)', content, re.IGNORECASE)
                    if tomcat_match:
                        version_info['tomcat_version'] = tomcat_match.group(1)
                        print(f"[+] 从示例应用检测到版本: {tomcat_match.group(1)}")
                        break
                    elif any(indicator in content for indicator in self.tomcat_indicators):
                        version_info['tomcat_detected'] = True
                        print(f"[+] 从示例应用检测到Tomcat")
                        
            except Exception as e:
                continue
        
        return version_info
    
    def fingerprint_tomcat(self):
        """Tomcat指纹识别"""
        print(f"[*] 进行Tomcat指纹识别...")
        
        fingerprint_info = {}
        
        # 测试特定的Tomcat行为
        fingerprint_tests = [
            # 测试JSP支持
            ('jsp_support', '/test.jsp', 'JSP'),
            # 测试Servlet支持
            ('servlet_support', '/servlet/', 'Servlet'),
            # 测试默认页面
            ('default_page', '/', 'Default'),
            # 测试Manager应用
            ('manager_app', '/manager/', 'Manager'),
        ]
        
        for test_name, test_path, description in fingerprint_tests:
            try:
                url = urljoin(self.target_url, test_path)
                response = self.session.get(url, timeout=self.timeout)
                
                fingerprint_info[test_name] = {
                    'status_code': response.status_code,
                    'content_length': len(response.text),
                    'has_tomcat_indicators': any(indicator in response.text.lower() 
                                               for indicator in self.tomcat_indicators)
                }
                
            except Exception as e:
                fingerprint_info[test_name] = {'error': str(e)}
        
        return fingerprint_info
    
    def get_vulnerability_info(self, version_info):
        """根据版本信息获取相关漏洞"""
        vulnerabilities = []
        
        if 'tomcat_version' in version_info:
            version = version_info['tomcat_version']
            
            # 提取主版本号
            version_parts = version.split('.')
            if len(version_parts) >= 2:
                major_minor = f"{version_parts[0]}.{version_parts[1]}"
                
                # 检查已知漏洞
                for vuln_version, vulns in self.version_vulnerabilities.items():
                    if version.startswith(vuln_version) or major_minor == vuln_version:
                        vulnerabilities.extend(vulns)
        
        return list(set(vulnerabilities))  # 去重
    
    def generate_report(self):
        """生成完整的检测报告"""
        print("=" * 80)
        print("Tomcat 版本检测报告")
        print("=" * 80)
        
        # 获取基础信息
        headers_info, response = self.get_server_headers()
        
        # 版本检测
        version_info = self.detect_version_from_headers(headers_info)
        
        if not version_info.get('tomcat_version'):
            error_version_info = self.detect_version_from_errors()
            version_info.update(error_version_info)
        
        if not version_info.get('tomcat_version'):
            manager_version_info = self.detect_version_from_manager()
            version_info.update(manager_version_info)
        
        if not version_info.get('tomcat_version'):
            examples_version_info = self.detect_version_from_examples()
            version_info.update(examples_version_info)
        
        if version_info:
            print(f"\n[+] 版本信息:")
            for key, value in version_info.items():
                print(f"  {key}: {value}")
        else:
            print(f"\n[-] 未检测到明确的版本信息")
        
        # 指纹识别
        fingerprint_info = self.fingerprint_tomcat()
        if fingerprint_info:
            print(f"\n[+] 指纹信息:")
            for test_name, test_result in fingerprint_info.items():
                print(f"  {test_name}: {test_result}")
        
        # 漏洞信息
        vulnerabilities = self.get_vulnerability_info(version_info)
        if vulnerabilities:
            print(f"\n[!] 可能存在的漏洞:")
            for cve in vulnerabilities:
                print(f"  - {cve}")
        
        # 安全建议
        print(f"\n[*] 安全建议:")
        if 'tomcat_version' in version_info:
            print("  - 检查当前版本是否为最新版本")
        if 'Server' in headers_info:
            print("  - 考虑隐藏Server头中的版本信息")
        if vulnerabilities:
            print("  - 及时修复已知漏洞")
        if fingerprint_info.get('manager_app', {}).get('status_code') in [200, 401]:
            print("  - 限制Manager应用的访问权限")
        
        return {
            'headers_info': headers_info,
            'version_info': version_info,
            'fingerprint_info': fingerprint_info,
            'vulnerabilities': vulnerabilities
        }

def main():
    parser = argparse.ArgumentParser(description='Tomcat 版本识别和指纹识别工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告到文件')
    parser.add_argument('--headers-only', action='store_true', help='仅检测响应头')
    parser.add_argument('--fingerprint-only', action='store_true', help='仅进行指纹识别')
    
    args = parser.parse_args()
    
    detector = TomcatVersionDetector(args.target, args.timeout)
    
    if args.headers_only:
        headers_info, _ = detector.get_server_headers()
        version_info = detector.detect_version_from_headers(headers_info)
        for key, value in version_info.items():
            print(f"{key}: {value}")
    elif args.fingerprint_only:
        fingerprint_info = detector.fingerprint_tomcat()
        for test_name, test_result in fingerprint_info.items():
            print(f"{test_name}: {test_result}")
    else:
        report = detector.generate_report()
        
        # 输出到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(f"Tomcat检测报告 - {args.target}\n")
                f.write("=" * 50 + "\n\n")
                
                f.write("版本信息:\n")
                for key, value in report['version_info'].items():
                    f.write(f"{key}: {value}\n")
                
                f.write("\n指纹信息:\n")
                for test_name, test_result in report['fingerprint_info'].items():
                    f.write(f"{test_name}: {test_result}\n")
                
                f.write("\n可能的漏洞:\n")
                for cve in report['vulnerabilities']:
                    f.write(f"{cve}\n")
            
            print(f"\n[+] 报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
