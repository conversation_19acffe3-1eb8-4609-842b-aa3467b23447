#!/usr/bin/env python3
"""
Tomcat 漏洞扫描工具使用示例
演示如何使用各种扫描脚本
"""

import subprocess
import sys
import time

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("命令执行超时")
    except Exception as e:
        print(f"执行错误: {e}")
    
    time.sleep(2)

def main():
    if len(sys.argv) != 2:
        print("用法: python3 example_usage.py <target_url>")
        print("示例: python3 example_usage.py http://*************:8080")
        sys.exit(1)
    
    target = sys.argv[1]
    
    print("Tomcat 漏洞扫描工具使用示例")
    print(f"目标: {target}")
    
    # 示例1: 综合扫描
    run_command([
        'python3', 'tomcat_comprehensive_scan.py', target
    ], "综合漏洞扫描")
    
    # 示例2: 版本识别
    run_command([
        'python3', 'tomcat_version_detect.py', target
    ], "Tomcat版本识别")
    
    # 示例3: 信息收集
    run_command([
        'python3', 'tomcat_info_scan.py', target
    ], "Tomcat信息收集")
    
    # 示例4: CVE-2020-9484检测
    run_command([
        'python3', 'CVE-2020-9484.py', target, '--check-only'
    ], "CVE-2020-9484 反序列化漏洞检测")
    
    # 示例5: CVE-2017-12615检测
    run_command([
        'python3', 'CVE-2017-12615.py', target, '--check-only'
    ], "CVE-2017-12615 PUT方法文件写入检测")
    
    # 示例6: CVE-2020-1938检测
    run_command([
        'python3', 'CVE-2020-1938.py', target, '--check-only'
    ], "CVE-2020-1938 AJP协议文件读取检测")
    
    # 示例7: CVE-2019-0232检测
    run_command([
        'python3', 'CVE-2019-0232.py', target, '--check-only'
    ], "CVE-2019-0232 CGI Servlet远程代码执行检测")
    
    # 示例8: 文件读取检测
    run_command([
        'python3', 'tomcat_file_read.py', target, '--check-only'
    ], "任意文件读取漏洞检测")
    
    # 示例9: Manager暴力破解
    run_command([
        'python3', 'tomcat_manager_bruteforce.py', target, '--check-only'
    ], "Manager应用检测")
    
    # 示例10: WAR文件上传检测
    run_command([
        'python3', 'tomcat_war_upload.py', target, '--check-only'
    ], "WAR文件上传检测")
    
    print(f"\n{'='*60}")
    print("示例演示完成!")
    print("更多用法请参考 README.md 文件")
    print(f"{'='*60}")

if __name__ == '__main__':
    main()
