#!/usr/bin/env python3
"""
Spring框架综合漏洞扫描工具
集成多个Spring漏洞检测和利用功能的综合扫描器
"""

import sys
import os
import subprocess
import argparse
import json
from datetime import datetime
import warnings
warnings.filterwarnings("ignore")

class SpringComprehensiveScanner:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.scan_results = {
            'target': target_url,
            'scan_time': datetime.now().isoformat(),
            'vulnerabilities': {},
            'actuator_info': {},
            'spel_injection': {},
            'version_info': {},
            'recommendations': []
        }
        
        # 可用的扫描模块
        self.scan_modules = {
            'CVE-2022-22965': {
                'script': 'CVE-2022-22965.py',
                'description': 'Spring4Shell远程代码执行漏洞',
                'severity': 'Critical'
            },
            'CVE-2022-22963': {
                'script': 'CVE-2022-22963.py',
                'description': 'Spring Cloud Function SpEL注入',
                'severity': 'Critical'
            },
            'actuator_scan': {
                'script': 'spring_actuator_scan.py',
                'description': 'Spring Boot Actuator敏感信息泄露',
                'severity': 'High'
            },
            'spel_injection': {
                'script': 'spring_spel_injection.py',
                'description': 'SpEL表达式注入检测',
                'severity': 'High'
            }
        }
    
    def run_script(self, script_name, args):
        """运行指定的扫描脚本"""
        script_path = os.path.join(os.path.dirname(__file__), script_name)
        
        if not os.path.exists(script_path):
            return None, f"脚本不存在: {script_path}"
        
        try:
            cmd = ['python3', script_path] + args
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=self.timeout * 3
            )
            
            return result.returncode, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            return -1, "", "脚本执行超时"
        except Exception as e:
            return -1, "", str(e)
    
    def scan_vulnerability(self, vuln_id, module_info):
        """扫描单个漏洞"""
        print(f"\n[*] 扫描 {vuln_id}: {module_info['description']}")
        print("-" * 60)
        
        # 构造扫描参数
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        
        returncode, stdout, stderr = self.run_script(module_info['script'], args)
        
        vulnerability_result = {
            'vuln_id': vuln_id,
            'description': module_info['description'],
            'severity': module_info['severity'],
            'vulnerable': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 检查输出中是否包含漏洞存在的指示
            if ('存在' in stdout or 'vulnerable' in stdout.lower() or 
                '发现' in stdout or 'found' in stdout.lower() or
                '成功' in stdout or 'success' in stdout.lower() or
                '可访问' in stdout or 'accessible' in stdout.lower()):
                vulnerability_result['vulnerable'] = True
                print(f"[+] {vuln_id} 漏洞存在!")
            else:
                print(f"[-] {vuln_id} 漏洞不存在")
        else:
            print(f"[-] {vuln_id} 扫描失败: {stderr}")
        
        self.scan_results['vulnerabilities'][vuln_id] = vulnerability_result
        return vulnerability_result
    
    def scan_actuator_endpoints(self):
        """扫描Spring Boot Actuator端点"""
        print(f"\n[*] 扫描Spring Boot Actuator端点")
        print("-" * 60)
        
        args = [self.target_url, '--timeout', str(self.timeout), '--json']
        returncode, stdout, stderr = self.run_script('spring_actuator_scan.py', args)
        
        if returncode == 0:
            try:
                actuator_data = json.loads(stdout)
                self.scan_results['actuator_info'] = actuator_data
                
                accessible_count = len(actuator_data.get('accessible_endpoints', []))
                print(f"[+] 发现 {accessible_count} 个可访问的Actuator端点")
                
                # 检查敏感信息
                sensitive_info = actuator_data.get('sensitive_info', {})
                if sensitive_info:
                    print(f"[!] 发现敏感信息泄露:")
                    for category, items in sensitive_info.items():
                        print(f"    {category}: {len(items)}个")
                
            except json.JSONDecodeError:
                print(f"[-] 无法解析Actuator扫描结果")
                self.scan_results['actuator_info'] = {'error': 'JSON解析失败'}
        else:
            print(f"[-] Actuator扫描失败: {stderr}")
            self.scan_results['actuator_info'] = {'error': stderr}
    
    def scan_spel_injection(self):
        """扫描SpEL表达式注入"""
        print(f"\n[*] 扫描SpEL表达式注入")
        print("-" * 60)
        
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('spring_spel_injection.py', args)
        
        spel_result = {
            'scanned': True,
            'vulnerable': False,
            'injection_points': [],
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            if '发现SpEL注入' in stdout:
                spel_result['vulnerable'] = True
                print(f"[+] 发现SpEL表达式注入漏洞!")
                
                # 提取注入点信息
                lines = stdout.split('\n')
                for line in lines:
                    if '发现SpEL注入' in line:
                        spel_result['injection_points'].append(line.strip())
            else:
                print(f"[-] 未发现SpEL表达式注入漏洞")
        else:
            print(f"[-] SpEL注入扫描失败: {stderr}")
        
        self.scan_results['spel_injection'] = spel_result
    
    def detect_spring_version(self):
        """检测Spring版本信息"""
        print(f"\n[*] 检测Spring版本信息")
        print("-" * 60)
        
        try:
            import requests
            session = requests.Session()
            session.verify = False
            
            # 尝试从HTTP头获取版本信息
            response = session.get(self.target_url, timeout=self.timeout)
            
            version_info = {
                'detected': False,
                'version': 'Unknown',
                'server_header': response.headers.get('Server', ''),
                'framework_headers': {}
            }
            
            # 检查Spring相关的HTTP头
            spring_headers = [
                'X-Application-Context', 'X-Spring-Boot-Version',
                'X-Framework-Version', 'X-Powered-By'
            ]
            
            for header in spring_headers:
                if header in response.headers:
                    version_info['framework_headers'][header] = response.headers[header]
                    version_info['detected'] = True
                    print(f"[+] 发现Spring头信息: {header} = {response.headers[header]}")
            
            # 尝试从Actuator端点获取版本信息
            if 'actuator_info' in self.scan_results:
                actuator_data = self.scan_results['actuator_info']
                if isinstance(actuator_data, dict) and 'accessible_endpoints' in actuator_data:
                    for endpoint in actuator_data['accessible_endpoints']:
                        if endpoint['endpoint'] == '/actuator/info':
                            try:
                                info_data = json.loads(endpoint['response'])
                                if 'build' in info_data:
                                    version_info['build_info'] = info_data['build']
                                    version_info['detected'] = True
                                    print(f"[+] 从/actuator/info获取构建信息")
                            except:
                                pass
            
            self.scan_results['version_info'] = version_info
            
            if not version_info['detected']:
                print(f"[-] 未检测到Spring版本信息")
            
        except Exception as e:
            print(f"[-] 版本检测失败: {e}")
            self.scan_results['version_info'] = {'error': str(e)}
    
    def generate_recommendations(self):
        """生成安全建议"""
        recommendations = []
        
        # 基于漏洞扫描结果生成建议
        critical_vulns = []
        high_vulns = []
        
        for vuln_id, vuln_info in self.scan_results['vulnerabilities'].items():
            if vuln_info['vulnerable']:
                if vuln_info['severity'] == 'Critical':
                    critical_vulns.append(vuln_id)
                elif vuln_info['severity'] == 'High':
                    high_vulns.append(vuln_id)
        
        if critical_vulns:
            recommendations.append({
                'priority': 'Critical',
                'issue': f"发现严重漏洞: {', '.join(critical_vulns)}",
                'recommendation': "立即修复这些严重漏洞，它们可能导致远程代码执行"
            })
        
        if high_vulns:
            recommendations.append({
                'priority': 'High',
                'issue': f"发现高危漏洞: {', '.join(high_vulns)}",
                'recommendation': "尽快修复这些高危漏洞"
            })
        
        # 基于Actuator扫描结果生成建议
        if 'actuator_info' in self.scan_results:
            actuator_data = self.scan_results['actuator_info']
            if isinstance(actuator_data, dict):
                accessible_count = len(actuator_data.get('accessible_endpoints', []))
                if accessible_count > 0:
                    recommendations.append({
                        'priority': 'High',
                        'issue': f"发现 {accessible_count} 个可访问的Actuator端点",
                        'recommendation': "限制Actuator端点访问权限，启用Spring Security保护"
                    })
                
                sensitive_info = actuator_data.get('sensitive_info', {})
                if sensitive_info:
                    recommendations.append({
                        'priority': 'High',
                        'issue': "Actuator端点泄露敏感信息",
                        'recommendation': "配置management.endpoints.web.exposure.include仅暴露必要端点"
                    })
        
        # 基于SpEL注入结果生成建议
        if 'spel_injection' in self.scan_results:
            spel_data = self.scan_results['spel_injection']
            if spel_data.get('vulnerable', False):
                recommendations.append({
                    'priority': 'Critical',
                    'issue': "发现SpEL表达式注入漏洞",
                    'recommendation': "严格验证和过滤用户输入，避免直接将用户输入用于SpEL表达式解析"
                })
        
        # 通用安全建议
        recommendations.extend([
            {
                'priority': 'Medium',
                'issue': "Spring框架安全配置",
                'recommendation': "定期更新Spring框架到最新版本"
            },
            {
                'priority': 'Medium',
                'issue': "输入验证",
                'recommendation': "实施严格的输入验证和输出编码"
            },
            {
                'priority': 'Medium',
                'issue': "访问控制",
                'recommendation': "配置适当的访问控制和认证机制"
            }
        ])
        
        self.scan_results['recommendations'] = recommendations
        return recommendations
    
    def run_comprehensive_scan(self, selected_vulns=None):
        """运行综合扫描"""
        print("=" * 80)
        print(f"Spring框架综合漏洞扫描")
        print(f"目标: {self.target_url}")
        print(f"扫描时间: {self.scan_results['scan_time']}")
        print("=" * 80)
        
        # 1. 版本识别
        self.detect_spring_version()
        
        # 2. Actuator端点扫描
        self.scan_actuator_endpoints()
        
        # 3. SpEL注入扫描
        self.scan_spel_injection()
        
        # 4. 漏洞扫描
        vulns_to_scan = selected_vulns or list(self.scan_modules.keys())
        
        for vuln_id in vulns_to_scan:
            if vuln_id in self.scan_modules:
                self.scan_vulnerability(vuln_id, self.scan_modules[vuln_id])
        
        # 5. 生成建议
        recommendations = self.generate_recommendations()
        
        # 6. 输出总结
        self.print_summary()
        
        return self.scan_results
    
    def print_summary(self):
        """打印扫描总结"""
        print("\n" + "=" * 80)
        print("扫描总结")
        print("=" * 80)
        
        # 漏洞总结
        vulnerable_count = sum(1 for v in self.scan_results['vulnerabilities'].values() if v['vulnerable'])
        total_count = len(self.scan_results['vulnerabilities'])
        
        print(f"\n[*] 漏洞扫描结果: {vulnerable_count}/{total_count} 个漏洞存在")
        
        for vuln_id, vuln_info in self.scan_results['vulnerabilities'].items():
            status = "存在" if vuln_info['vulnerable'] else "不存在"
            severity = vuln_info['severity']
            print(f"  {vuln_id} ({severity}): {status}")
        
        # Actuator端点总结
        if 'actuator_info' in self.scan_results:
            actuator_data = self.scan_results['actuator_info']
            if isinstance(actuator_data, dict):
                accessible_count = len(actuator_data.get('accessible_endpoints', []))
                print(f"\n[*] Actuator端点: 发现 {accessible_count} 个可访问端点")
                
                sensitive_count = sum(len(items) for items in actuator_data.get('sensitive_info', {}).values())
                if sensitive_count > 0:
                    print(f"[!] 敏感信息泄露: {sensitive_count} 个敏感信息项")
        
        # SpEL注入总结
        if 'spel_injection' in self.scan_results:
            spel_data = self.scan_results['spel_injection']
            if spel_data.get('vulnerable', False):
                injection_count = len(spel_data.get('injection_points', []))
                print(f"\n[!] SpEL注入: 发现 {injection_count} 个注入点")
        
        # 安全建议
        if self.scan_results['recommendations']:
            print(f"\n[!] 安全建议:")
            for i, rec in enumerate(self.scan_results['recommendations'], 1):
                print(f"  {i}. [{rec['priority']}] {rec['issue']}")
                print(f"     建议: {rec['recommendation']}")
        
        # 风险评估
        critical_count = sum(1 for v in self.scan_results['vulnerabilities'].values() 
                           if v['vulnerable'] and v['severity'] == 'Critical')
        high_count = sum(1 for v in self.scan_results['vulnerabilities'].values() 
                        if v['vulnerable'] and v['severity'] == 'High')
        
        if critical_count > 0:
            print(f"\n[!] 风险评估: 严重 (发现 {critical_count} 个严重漏洞)")
        elif high_count > 0:
            print(f"\n[!] 风险评估: 高 (发现 {high_count} 个高危漏洞)")
        elif vulnerable_count > 0:
            print(f"\n[!] 风险评估: 中等 (发现 {vulnerable_count} 个漏洞)")
        else:
            print(f"\n[+] 风险评估: 低 (未发现已知漏洞)")
    
    def save_report(self, filename):
        """保存扫描报告"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
            print(f"\n[+] 扫描报告已保存到: {filename}")
        except Exception as e:
            print(f"[-] 保存报告失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='Spring框架综合漏洞扫描工具')
    parser.add_argument('target', help='目标URL (例如: http://target.com)')
    parser.add_argument('-v', '--vulns', nargs='+', help='指定要扫描的漏洞 (例如: CVE-2022-22965 actuator_scan)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('-o', '--output', help='输出报告文件 (JSON格式)')
    parser.add_argument('--list-vulns', action='store_true', help='列出所有支持的漏洞')
    
    args = parser.parse_args()
    
    scanner = SpringComprehensiveScanner(args.target, args.timeout)
    
    if args.list_vulns:
        print("支持的漏洞:")
        for vuln_id, info in scanner.scan_modules.items():
            print(f"  {vuln_id}: {info['description']} ({info['severity']})")
        return
    
    # 运行综合扫描
    results = scanner.run_comprehensive_scan(args.vulns)
    
    # 保存报告
    if args.output:
        scanner.save_report(args.output)

if __name__ == '__main__':
    main()