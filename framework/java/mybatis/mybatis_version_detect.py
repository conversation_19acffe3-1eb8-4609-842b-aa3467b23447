#!/usr/bin/env python3
"""
MyBatis 版本检测工具
通过多种方式检测目标应用使用的MyBatis版本
"""

import sys
import requests
import argparse
import re
import json
import warnings
from urllib.parse import urljoin, urlparse
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class MyBatisVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # MyBatis特征路径
        self.mybatis_paths = [
            # 配置文件
            '/WEB-INF/classes/mybatis-config.xml',
            '/WEB-INF/classes/sqlMapConfig.xml',
            '/WEB-INF/classes/configuration.xml',
            '/mybatis-config.xml',
            '/sqlMapConfig.xml',
            
            # JAR文件
            '/WEB-INF/lib/mybatis-3.5.13.jar',
            '/WEB-INF/lib/mybatis-3.5.12.jar',
            '/WEB-INF/lib/mybatis-3.5.11.jar',
            '/WEB-INF/lib/mybatis-3.5.10.jar',
            '/WEB-INF/lib/mybatis-3.4.6.jar',
            '/WEB-INF/lib/mybatis-spring-2.0.7.jar',
            '/WEB-INF/lib/mybatis-spring-boot-starter-2.3.1.jar',
            
            # 错误页面
            '/error',
            '/error.jsp',
            '/500.jsp',
            
            # 管理页面
            '/actuator/info',
            '/actuator/health',
            '/management/info',
            '/info',
            
            # 静态资源
            '/static/js/',
            '/static/css/',
            '/resources/',
            '/assets/'
        ]
        
        # 版本特征模式
        self.version_patterns = [
            # 直接版本号
            r'mybatis[/-]?(\d+\.\d+(?:\.\d+)?)',
            r'org\.apache\.ibatis.*?(\d+\.\d+(?:\.\d+)?)',
            
            # JAR文件名
            r'mybatis-(\d+\.\d+(?:\.\d+)?)\.jar',
            r'mybatis-spring-(\d+\.\d+(?:\.\d+)?)\.jar',
            
            # Maven坐标
            r'<groupId>org\.mybatis</groupId>.*?<version>(\d+\.\d+(?:\.\d+)?)</version>',
            r'org\.mybatis:mybatis:(\d+\.\d+(?:\.\d+)?)',
            
            # 错误信息中的版本
            r'MyBatis\s+(\d+\.\d+(?:\.\d+)?)',
            r'SqlSession.*?(\d+\.\d+(?:\.\d+)?)',
            
            # Spring Boot信息
            r'"mybatis".*?"version".*?"(\d+\.\d+(?:\.\d+)?)"',
            r'mybatis-spring-boot-starter[/-]?(\d+\.\d+(?:\.\d+)?)'
        ]
        
        self.detection_results = {
            'mybatis_detected': False,
            'version': 'Unknown',
            'detection_methods': [],
            'confidence': 'Low',
            'additional_info': {}
        }
    
    def detect_by_http_headers(self):
        """通过HTTP响应头检测"""
        print(f"[*] 通过HTTP响应头检测MyBatis...")
        
        try:
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            # 检查Server头
            server_header = response.headers.get('Server', '')
            if 'mybatis' in server_header.lower():
                print(f"[+] Server头包含MyBatis信息: {server_header}")
                self.detection_results['mybatis_detected'] = True
                self.detection_results['detection_methods'].append('HTTP Headers')
                
                # 尝试提取版本
                for pattern in self.version_patterns:
                    match = re.search(pattern, server_header, re.IGNORECASE)
                    if match:
                        version = match.group(1)
                        self.detection_results['version'] = version
                        print(f"[+] 从Server头检测到版本: {version}")
                        return True
            
            # 检查其他自定义头
            for header_name, header_value in response.headers.items():
                if 'mybatis' in header_name.lower() or 'mybatis' in header_value.lower():
                    print(f"[+] 自定义头包含MyBatis信息: {header_name}: {header_value}")
                    self.detection_results['mybatis_detected'] = True
                    self.detection_results['detection_methods'].append('Custom Headers')
            
            return False
            
        except Exception as e:
            print(f"[-] HTTP头检测失败: {e}")
            return False
    
    def detect_by_error_pages(self):
        """通过错误页面检测"""
        print(f"[*] 通过错误页面检测MyBatis...")
        
        # 构造可能触发错误的请求
        error_triggers = [
            '?id=\'',
            '?id=1\'',
            '?name=test\'',
            '?param=<script>',
            '?data=../../../etc/passwd',
            '/nonexistent.action',
            '/test.do?error=true'
        ]
        
        for trigger in error_triggers:
            try:
                url = self.target_url + trigger
                response = self.session.get(url, timeout=self.timeout)
                
                content = response.text
                
                # 检查错误信息中的MyBatis特征
                mybatis_indicators = [
                    'org.apache.ibatis',
                    'mybatis',
                    'SqlSession',
                    'SqlSessionFactory',
                    'Mapper',
                    'ParameterMap',
                    'ResultMap'
                ]
                
                for indicator in mybatis_indicators:
                    if indicator in content:
                        print(f"[+] 错误页面包含MyBatis特征: {indicator}")
                        self.detection_results['mybatis_detected'] = True
                        self.detection_results['detection_methods'].append('Error Pages')
                        
                        # 尝试提取版本信息
                        for pattern in self.version_patterns:
                            match = re.search(pattern, content, re.IGNORECASE)
                            if match:
                                version = match.group(1)
                                self.detection_results['version'] = version
                                print(f"[+] 从错误页面检测到版本: {version}")
                                return True
                        
                        break
            
            except Exception:
                continue
        
        return False
    
    def detect_by_file_paths(self):
        """通过文件路径检测"""
        print(f"[*] 通过文件路径检测MyBatis...")
        
        found_files = []
        
        for path in self.mybatis_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 发现MyBatis相关文件: {path}")
                    found_files.append(path)
                    self.detection_results['mybatis_detected'] = True
                    
                    if 'File Paths' not in self.detection_results['detection_methods']:
                        self.detection_results['detection_methods'].append('File Paths')
                    
                    content = response.text
                    
                    # 从文件内容中提取版本信息
                    for pattern in self.version_patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            version = match.group(1)
                            self.detection_results['version'] = version
                            print(f"[+] 从文件 {path} 检测到版本: {version}")
                            return True
                    
                    # 从文件名中提取版本信息
                    for pattern in self.version_patterns:
                        match = re.search(pattern, path, re.IGNORECASE)
                        if match:
                            version = match.group(1)
                            self.detection_results['version'] = version
                            print(f"[+] 从文件名 {path} 检测到版本: {version}")
                            return True
            
            except Exception:
                continue
        
        if found_files:
            self.detection_results['additional_info']['found_files'] = found_files
        
        return len(found_files) > 0
    
    def detect_by_actuator_endpoints(self):
        """通过Spring Boot Actuator端点检测"""
        print(f"[*] 通过Actuator端点检测MyBatis...")
        
        actuator_endpoints = [
            '/actuator/info',
            '/actuator/health',
            '/actuator/beans',
            '/actuator/env',
            '/management/info',
            '/management/health',
            '/info',
            '/health'
        ]
        
        for endpoint in actuator_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        # 检查info端点
                        if 'mybatis' in str(data).lower():
                            print(f"[+] Actuator端点包含MyBatis信息: {endpoint}")
                            self.detection_results['mybatis_detected'] = True
                            self.detection_results['detection_methods'].append('Actuator Endpoints')
                            
                            # 尝试提取版本信息
                            content = json.dumps(data)
                            for pattern in self.version_patterns:
                                match = re.search(pattern, content, re.IGNORECASE)
                                if match:
                                    version = match.group(1)
                                    self.detection_results['version'] = version
                                    print(f"[+] 从Actuator端点检测到版本: {version}")
                                    return True
                    
                    except json.JSONDecodeError:
                        # 非JSON响应，检查文本内容
                        content = response.text
                        if 'mybatis' in content.lower():
                            print(f"[+] Actuator端点包含MyBatis信息: {endpoint}")
                            self.detection_results['mybatis_detected'] = True
                            self.detection_results['detection_methods'].append('Actuator Endpoints')
            
            except Exception:
                continue
        
        return False
    
    def detect_by_page_content(self):
        """通过页面内容检测"""
        print(f"[*] 通过页面内容检测MyBatis...")
        
        # 常见页面路径
        page_paths = [
            '/',
            '/index',
            '/index.jsp',
            '/index.html',
            '/home',
            '/main',
            '/login',
            '/admin'
        ]
        
        for path in page_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 检查页面源码中的MyBatis特征
                    mybatis_indicators = [
                        'mybatis',
                        'SqlSession',
                        'org.apache.ibatis',
                        'mapper',
                        'sqlSessionFactory'
                    ]
                    
                    for indicator in mybatis_indicators:
                        if indicator in content.lower():
                            print(f"[+] 页面内容包含MyBatis特征: {indicator}")
                            self.detection_results['mybatis_detected'] = True
                            self.detection_results['detection_methods'].append('Page Content')
                            
                            # 尝试提取版本信息
                            for pattern in self.version_patterns:
                                match = re.search(pattern, content, re.IGNORECASE)
                                if match:
                                    version = match.group(1)
                                    self.detection_results['version'] = version
                                    print(f"[+] 从页面内容检测到版本: {version}")
                                    return True
                            
                            break
            
            except Exception:
                continue
        
        return False
    
    def detect_by_javascript_files(self):
        """通过JavaScript文件检测"""
        print(f"[*] 通过JavaScript文件检测MyBatis...")
        
        # 常见JS文件路径
        js_paths = [
            '/static/js/app.js',
            '/static/js/main.js',
            '/static/js/config.js',
            '/js/app.js',
            '/js/main.js',
            '/js/config.js',
            '/assets/js/app.js',
            '/resources/js/app.js'
        ]
        
        for js_path in js_paths:
            try:
                url = urljoin(self.target_url, js_path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 检查JS文件中的MyBatis配置或引用
                    if 'mybatis' in content.lower():
                        print(f"[+] JavaScript文件包含MyBatis信息: {js_path}")
                        self.detection_results['mybatis_detected'] = True
                        self.detection_results['detection_methods'].append('JavaScript Files')
                        
                        # 尝试提取版本信息
                        for pattern in self.version_patterns:
                            match = re.search(pattern, content, re.IGNORECASE)
                            if match:
                                version = match.group(1)
                                self.detection_results['version'] = version
                                print(f"[+] 从JavaScript文件检测到版本: {version}")
                                return True
            
            except Exception:
                continue
        
        return False
    
    def analyze_version_compatibility(self, version):
        """分析版本兼容性和安全性"""
        if version == 'Unknown':
            return
        
        try:
            version_parts = [int(x) for x in version.split('.')]
            major, minor = version_parts[0], version_parts[1]
            patch = version_parts[2] if len(version_parts) > 2 else 0
            
            # 版本分析
            version_info = {
                'is_latest': False,
                'is_supported': True,
                'security_issues': [],
                'recommendations': []
            }
            
            # 检查是否为最新版本（假设最新版本为3.5.13）
            if major == 3 and minor == 5 and patch >= 13:
                version_info['is_latest'] = True
            
            # 检查已知安全问题
            if major < 3 or (major == 3 and minor < 4):
                version_info['security_issues'].append('版本过旧，存在多个已知安全漏洞')
                version_info['recommendations'].append('立即升级到最新版本')
            
            if major == 3 and minor == 4 and patch < 6:
                version_info['security_issues'].append('存在XXE漏洞风险')
                version_info['recommendations'].append('升级到3.4.6或更高版本')
            
            if major == 3 and minor == 5 and patch < 10:
                version_info['security_issues'].append('存在反序列化漏洞风险')
                version_info['recommendations'].append('升级到3.5.10或更高版本')
            
            self.detection_results['additional_info']['version_analysis'] = version_info
            
        except Exception as e:
            print(f"[-] 版本分析失败: {e}")
    
    def determine_confidence_level(self):
        """确定检测置信度"""
        method_count = len(self.detection_results['detection_methods'])
        has_version = self.detection_results['version'] != 'Unknown'
        
        if method_count >= 3 and has_version:
            self.detection_results['confidence'] = 'High'
        elif method_count >= 2 or has_version:
            self.detection_results['confidence'] = 'Medium'
        elif method_count >= 1:
            self.detection_results['confidence'] = 'Low'
        else:
            self.detection_results['confidence'] = 'None'
    
    def run_detection(self):
        """运行完整的检测流程"""
        print(f"[*] 开始MyBatis版本检测")
        print(f"[*] 目标: {self.target_url}")
        print("-" * 50)
        
        # 执行各种检测方法
        detection_methods = [
            self.detect_by_http_headers,
            self.detect_by_error_pages,
            self.detect_by_file_paths,
            self.detect_by_actuator_endpoints,
            self.detect_by_page_content,
            self.detect_by_javascript_files
        ]
        
        for method in detection_methods:
            try:
                if method():
                    # 如果已经检测到版本，可以提前结束
                    if self.detection_results['version'] != 'Unknown':
                        break
            except Exception as e:
                print(f"[-] 检测方法执行失败: {e}")
        
        # 分析版本兼容性
        self.analyze_version_compatibility(self.detection_results['version'])
        
        # 确定置信度
        self.determine_confidence_level()
        
        return self.detection_results
    
    def generate_report(self):
        """生成检测报告"""
        print(f"\n" + "="*50)
        print(f"MyBatis版本检测报告")
        print(f"="*50)
        print(f"目标: {self.target_url}")
        
        if self.detection_results['mybatis_detected']:
            print(f"[+] MyBatis检测结果: 已检测到")
            print(f"[+] 检测到的版本: {self.detection_results['version']}")
            print(f"[+] 检测置信度: {self.detection_results['confidence']}")
            print(f"[+] 检测方法: {', '.join(self.detection_results['detection_methods'])}")
            
            # 版本分析信息
            version_analysis = self.detection_results.get('additional_info', {}).get('version_analysis')
            if version_analysis:
                print(f"\n版本分析:")
                print(f"  是否最新版本: {'是' if version_analysis['is_latest'] else '否'}")
                print(f"  是否受支持: {'是' if version_analysis['is_supported'] else '否'}")
                
                if version_analysis['security_issues']:
                    print(f"  安全问题:")
                    for issue in version_analysis['security_issues']:
                        print(f"    - {issue}")
                
                if version_analysis['recommendations']:
                    print(f"  建议:")
                    for rec in version_analysis['recommendations']:
                        print(f"    - {rec}")
            
            # 发现的文件
            found_files = self.detection_results.get('additional_info', {}).get('found_files')
            if found_files:
                print(f"\n发现的相关文件:")
                for file_path in found_files:
                    print(f"  - {file_path}")
        
        else:
            print(f"[-] MyBatis检测结果: 未检测到")
            print(f"[*] 这可能意味着:")
            print(f"    - 目标应用未使用MyBatis")
            print(f"    - MyBatis配置被很好地隐藏")
            print(f"    - 需要更深入的检测方法")
        
        print(f"="*50)

def main():
    parser = argparse.ArgumentParser(description='MyBatis版本检测工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--output', help='输出结果到JSON文件')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 创建检测器实例
    detector = MyBatisVersionDetector(args.target, args.timeout)
    
    try:
        # 运行检测
        results = detector.run_detection()
        
        # 生成报告
        detector.generate_report()
        
        # 保存结果到文件
        if args.output:
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"\n[+] 检测结果已保存到: {args.output}")
            except Exception as e:
                print(f"[-] 保存结果失败: {e}")
        
        # 返回适当的退出码
        if results['mybatis_detected']:
            sys.exit(0)
        else:
            sys.exit(1)
    
    except KeyboardInterrupt:
        print(f"\n[*] 检测被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"[-] 检测过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
