# MyBatis常见路径字典
# 配置文件路径
/WEB-INF/classes/mybatis-config.xml
/WEB-INF/classes/sqlMapConfig.xml
/WEB-INF/classes/configuration.xml
/mybatis-config.xml
/sqlMapConfig.xml
/configuration.xml
/config/mybatis-config.xml
/config/sqlMapConfig.xml

# Mapper文件路径
/WEB-INF/classes/mapper/
/WEB-INF/classes/mappers/
/WEB-INF/classes/com/example/mapper/
/mapper/
/mappers/
/sql/
/dao/

# JAR文件路径
/WEB-INF/lib/mybatis-3.5.13.jar
/WEB-INF/lib/mybatis-3.5.12.jar
/WEB-INF/lib/mybatis-3.5.11.jar
/WEB-INF/lib/mybatis-3.5.10.jar
/WEB-INF/lib/mybatis-3.4.6.jar
/WEB-INF/lib/mybatis-spring-2.0.7.jar
/WEB-INF/lib/mybatis-spring-boot-starter-2.3.1.jar

# 应用端点
/user
/users
/admin
/login
/search
/query
/list
/api/user
/api/users
/api/search
/api/query
/api/list

# 管理端点
/actuator/info
/actuator/health
/actuator/beans
/actuator/env
/management/info
/management/health
/info
/health
/status

# 错误页面
/error
/error.jsp
/500.jsp
/404.jsp
/exception.jsp

# 静态资源
/static/
/resources/
/assets/
/js/
/css/
/images/

# 备份文件
/WEB-INF/classes/mybatis-config.xml.bak
/WEB-INF/classes/mybatis-config.xml.old
/mybatis-config.xml.bak
/mybatis-config.xml.old
/config.xml.bak
/sqlMapConfig.xml.bak

# 日志文件
/logs/mybatis.log
/logs/sql.log
/logs/application.log
/WEB-INF/classes/log4j.properties
/WEB-INF/classes/logback.xml

# 其他配置文件
/WEB-INF/classes/application.properties
/WEB-INF/classes/application.yml
/WEB-INF/classes/database.properties
/WEB-INF/classes/jdbc.properties
/WEB-INF/web.xml
/META-INF/MANIFEST.MF