#!/usr/bin/env python3
"""
MyBatis 综合漏洞扫描工具
集成多个MyBatis漏洞检测和利用功能的综合扫描器
"""

import sys
import os
import subprocess
import argparse
import json
import threading
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings("ignore")

class MyBatisComprehensiveScanner:
    def __init__(self, target_url, timeout=10, threads=5):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.threads = threads
        self.scan_results = {
            'target': target_url,
            'scan_time': datetime.now().isoformat(),
            'mybatis_version': 'Unknown',
            'vulnerabilities': {},
            'information_disclosure': {},
            'sql_injection': {},
            'xxe_vulnerabilities': {},
            'config_leaks': {},
            'recommendations': []
        }
        
        # 可用的扫描模块
        self.scan_modules = {
            # 严重漏洞
            'CVE-2020-26945': {
                'script': 'CVE-2020-26945.py',
                'description': 'MyBatis XXE漏洞',
                'severity': 'Critical',
                'cve': 'CVE-2020-26945'
            },
            'CVE-2023-25330': {
                'script': 'CVE-2023-25330.py',
                'description': 'MyBatis反序列化RCE',
                'severity': 'Critical',
                'cve': 'CVE-2023-25330'
            },
            'CVE-2022-25845': {
                'script': 'CVE-2022-25845.py',
                'description': 'MyBatis SQL注入',
                'severity': 'High',
                'cve': 'CVE-2022-25845'
            },
            
            # 高危漏洞
            'SQL_INJECTION': {
                'script': 'mybatis_sql_injection.py',
                'description': '动态SQL注入',
                'severity': 'High',
                'cve': None
            },
            'XXE_EXPLOIT': {
                'script': 'mybatis_xxe_exploit.py',
                'description': 'XML外部实体注入',
                'severity': 'High',
                'cve': None
            },
            'CONFIG_LEAK': {
                'script': 'mybatis_config_leak.py',
                'description': '配置文件泄露',
                'severity': 'Medium',
                'cve': None
            },
            'CACHE_POISON': {
                'script': 'mybatis_cache_poison.py',
                'description': '缓存投毒攻击',
                'severity': 'Medium',
                'cve': None
            },
            'DESERIALIZATION': {
                'script': 'mybatis_deserialization.py',
                'description': '反序列化漏洞',
                'severity': 'High',
                'cve': None
            }
        }
    
    def run_script(self, script_name, args):
        """运行指定的扫描脚本"""
        script_path = os.path.join(os.path.dirname(__file__), script_name)
        
        if not os.path.exists(script_path):
            return None, f"脚本不存在: {script_path}"
        
        try:
            cmd = ['python3', script_path] + args
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=self.timeout * 3
            )
            
            return result.returncode, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            return -1, "", "脚本执行超时"
        except Exception as e:
            return -1, "", str(e)
    
    def scan_vulnerability(self, vuln_id, module_info):
        """扫描单个漏洞"""
        print(f"[*] 扫描 {vuln_id}: {module_info['description']}")
        
        # 构造扫描参数
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        
        returncode, stdout, stderr = self.run_script(module_info['script'], args)
        
        vulnerability_result = {
            'vuln_id': vuln_id,
            'description': module_info['description'],
            'severity': module_info['severity'],
            'cve': module_info.get('cve'),
            'vulnerable': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 检查输出中是否包含漏洞存在的指示
            if ('存在' in stdout or 'vulnerable' in stdout.lower() or 
                '发现' in stdout or 'found' in stdout.lower() or
                '成功' in stdout or 'success' in stdout.lower() or
                '可利用' in stdout or 'exploitable' in stdout.lower()):
                vulnerability_result['vulnerable'] = True
                print(f"[+] {vuln_id} 漏洞存在!")
            else:
                print(f"[-] {vuln_id} 漏洞不存在")
        else:
            print(f"[-] {vuln_id} 扫描失败: {stderr}")
        
        return vulnerability_result
    
    def detect_mybatis_version(self):
        """检测MyBatis版本"""
        print(f"[*] 检测MyBatis版本...")
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('mybatis_version_detect.py', args)
        
        if returncode == 0:
            # 从输出中提取版本信息
            lines = stdout.split('\n')
            for line in lines:
                if 'MyBatis版本' in line or 'version' in line.lower():
                    # 提取版本号
                    import re
                    version_match = re.search(r'(\d+\.\d+\.\d+)', line)
                    if version_match:
                        version = version_match.group(1)
                        self.scan_results['mybatis_version'] = version
                        print(f"[+] 检测到MyBatis版本: {version}")
                        return version
        
        print(f"[-] 无法检测MyBatis版本")
        return 'Unknown'
    
    def scan_information_disclosure(self):
        """扫描信息泄露"""
        print(f"[*] 扫描信息泄露...")
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('mybatis_info_disclosure.py', args)
        
        info_disclosure = {
            'scanned': True,
            'version_exposed': False,
            'config_files': [],
            'mapper_files': [],
            'database_info': [],
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            if '版本信息' in stdout or 'version' in stdout.lower():
                info_disclosure['version_exposed'] = True
            
            if '配置文件' in stdout or 'config' in stdout.lower():
                # 提取配置文件路径
                lines = stdout.split('\n')
                for line in lines:
                    if '.xml' in line or '.properties' in line or '.yml' in line:
                        info_disclosure['config_files'].append(line.strip())
            
            if 'mapper' in stdout.lower():
                # 提取Mapper文件信息
                lines = stdout.split('\n')
                for line in lines:
                    if 'mapper' in line.lower() and '.xml' in line:
                        info_disclosure['mapper_files'].append(line.strip())
        
        self.scan_results['information_disclosure'] = info_disclosure
    
    def scan_sql_injection(self):
        """扫描SQL注入漏洞"""
        print(f"[*] 扫描SQL注入漏洞...")
        
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('mybatis_sql_injection.py', args)
        
        sql_injection_result = {
            'scanned': True,
            'injection_points': [],
            'vulnerable_params': [],
            'database_type': 'Unknown',
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 提取注入点信息
            lines = stdout.split('\n')
            for line in lines:
                if '注入点' in line or 'injection' in line.lower():
                    sql_injection_result['injection_points'].append(line.strip())
                
                if '参数' in line and ('可注入' in line or 'vulnerable' in line.lower()):
                    sql_injection_result['vulnerable_params'].append(line.strip())
                
                if '数据库' in line or 'database' in line.lower():
                    if 'mysql' in line.lower():
                        sql_injection_result['database_type'] = 'MySQL'
                    elif 'oracle' in line.lower():
                        sql_injection_result['database_type'] = 'Oracle'
                    elif 'postgresql' in line.lower():
                        sql_injection_result['database_type'] = 'PostgreSQL'
        
        self.scan_results['sql_injection'] = sql_injection_result
    
    def scan_xxe_vulnerabilities(self):
        """扫描XXE漏洞"""
        print(f"[*] 扫描XXE漏洞...")
        
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('mybatis_xxe_exploit.py', args)
        
        xxe_result = {
            'scanned': True,
            'xxe_endpoints': [],
            'file_read_possible': False,
            'ssrf_possible': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 提取XXE相关信息
            lines = stdout.split('\n')
            for line in lines:
                if 'XXE' in line and ('端点' in line or 'endpoint' in line.lower()):
                    xxe_result['xxe_endpoints'].append(line.strip())
                
                if '文件读取' in line or 'file read' in line.lower():
                    xxe_result['file_read_possible'] = True
                
                if 'SSRF' in line:
                    xxe_result['ssrf_possible'] = True
        
        self.scan_results['xxe_vulnerabilities'] = xxe_result
    
    def scan_config_leaks(self):
        """扫描配置文件泄露"""
        print(f"[*] 扫描配置文件泄露...")
        
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('mybatis_config_leak.py', args)
        
        config_leak_result = {
            'scanned': True,
            'leaked_configs': [],
            'database_credentials': False,
            'sensitive_info': [],
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 提取配置泄露信息
            lines = stdout.split('\n')
            for line in lines:
                if '配置文件' in line and ('泄露' in line or 'leaked' in line.lower()):
                    config_leak_result['leaked_configs'].append(line.strip())
                
                if '数据库' in line and ('密码' in line or 'password' in line.lower()):
                    config_leak_result['database_credentials'] = True
                
                if '敏感信息' in line or 'sensitive' in line.lower():
                    config_leak_result['sensitive_info'].append(line.strip())
        
        self.scan_results['config_leaks'] = config_leak_result
    
    def run_comprehensive_scan(self, selected_vulns=None):
        """运行综合扫描"""
        print(f"[*] 开始MyBatis综合漏洞扫描")
        print(f"[*] 目标: {self.target_url}")
        print(f"[*] 超时: {self.timeout}秒")
        print(f"[*] 线程: {self.threads}")
        print("-" * 60)
        
        # 检测MyBatis版本
        self.detect_mybatis_version()
        
        # 信息收集
        self.scan_information_disclosure()
        self.scan_sql_injection()
        self.scan_xxe_vulnerabilities()
        self.scan_config_leaks()
        
        # 选择要扫描的漏洞
        if selected_vulns:
            vulns_to_scan = {k: v for k, v in self.scan_modules.items() 
                           if k in selected_vulns}
        else:
            vulns_to_scan = self.scan_modules
        
        print(f"[*] 准备扫描 {len(vulns_to_scan)} 个漏洞...")
        
        # 多线程扫描漏洞
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            future_to_vuln = {
                executor.submit(self.scan_vulnerability, vuln_id, module_info): vuln_id
                for vuln_id, module_info in vulns_to_scan.items()
            }
            
            for future in as_completed(future_to_vuln):
                vuln_id = future_to_vuln[future]
                try:
                    result = future.result()
                    self.scan_results['vulnerabilities'][vuln_id] = result
                except Exception as e:
                    print(f"[-] {vuln_id} 扫描异常: {e}")
                    self.scan_results['vulnerabilities'][vuln_id] = {
                        'vuln_id': vuln_id,
                        'vulnerable': False,
                        'error': str(e)
                    }
        
        # 生成安全建议
        self.generate_recommendations()
        
        print(f"\n[*] 扫描完成!")
        return self.scan_results
    
    def generate_recommendations(self):
        """生成安全建议"""
        recommendations = []
        
        # 检查是否有严重漏洞
        critical_vulns = [v for v in self.scan_results['vulnerabilities'].values() 
                         if v.get('vulnerable') and v.get('severity') == 'Critical']
        
        if critical_vulns:
            recommendations.append("立即升级MyBatis到最新安全版本")
            recommendations.append("部署Web应用防火墙(WAF)阻止恶意请求")
        
        # 检查SQL注入
        if self.scan_results.get('sql_injection', {}).get('injection_points'):
            recommendations.append("修复动态SQL注入漏洞，使用#{}而不是${}")
            recommendations.append("对所有用户输入进行严格验证和过滤")
        
        # 检查XXE漏洞
        if self.scan_results.get('xxe_vulnerabilities', {}).get('xxe_endpoints'):
            recommendations.append("禁用XML外部实体解析")
            recommendations.append("升级XML解析器到安全版本")
        
        # 检查配置文件泄露
        if self.scan_results.get('config_leaks', {}).get('leaked_configs'):
            recommendations.append("保护配置文件不被直接访问")
            recommendations.append("移除敏感信息或使用环境变量")
        
        # 检查信息泄露
        if self.scan_results.get('information_disclosure', {}).get('version_exposed'):
            recommendations.append("隐藏MyBatis版本信息")
        
        # 通用建议
        recommendations.extend([
            "定期更新MyBatis及相关依赖",
            "实施最小权限原则配置数据库用户",
            "启用SQL执行日志监控",
            "定期进行安全扫描和代码审计",
            "使用参数化查询防止SQL注入",
            "配置安全的XML解析器设置"
        ])
        
        self.scan_results['recommendations'] = recommendations
    
    def generate_report(self, output_file=None):
        """生成扫描报告"""
        print(f"\n" + "="*60)
        print(f"MyBatis 综合漏洞扫描报告")
        print(f"="*60)
        print(f"目标: {self.target_url}")
        print(f"扫描时间: {self.scan_results['scan_time']}")
        print(f"MyBatis版本: {self.scan_results['mybatis_version']}")
        
        # 漏洞统计
        total_vulns = len(self.scan_results['vulnerabilities'])
        vulnerable_count = len([v for v in self.scan_results['vulnerabilities'].values() 
                              if v.get('vulnerable')])
        
        print(f"\n漏洞扫描统计:")
        print(f"  总扫描漏洞数: {total_vulns}")
        print(f"  发现漏洞数: {vulnerable_count}")
        
        # 按严重程度分类
        severity_stats = {}
        for vuln in self.scan_results['vulnerabilities'].values():
            if vuln.get('vulnerable'):
                severity = vuln.get('severity', 'Unknown')
                severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        if severity_stats:
            print(f"\n按严重程度分类:")
            for severity, count in severity_stats.items():
                print(f"  {severity}: {count}个")
        
        # 详细漏洞信息
        if vulnerable_count > 0:
            print(f"\n发现的漏洞详情:")
            for vuln_id, vuln_info in self.scan_results['vulnerabilities'].items():
                if vuln_info.get('vulnerable'):
                    cve = f" ({vuln_info['cve']})" if vuln_info.get('cve') else ""
                    print(f"  [!] {vuln_id}{cve}: {vuln_info['description']}")
                    print(f"      严重程度: {vuln_info['severity']}")
        
        # SQL注入情况
        sql_injection = self.scan_results.get('sql_injection', {})
        if sql_injection.get('injection_points'):
            print(f"\nSQL注入情况:")
            print(f"  发现注入点: {len(sql_injection['injection_points'])}个")
            print(f"  数据库类型: {sql_injection.get('database_type', 'Unknown')}")
            if sql_injection.get('vulnerable_params'):
                print(f"  可注入参数: {len(sql_injection['vulnerable_params'])}个")
        
        # XXE漏洞情况
        xxe_vulns = self.scan_results.get('xxe_vulnerabilities', {})
        if xxe_vulns.get('xxe_endpoints'):
            print(f"\nXXE漏洞情况:")
            print(f"  XXE端点: {len(xxe_vulns['xxe_endpoints'])}个")
            if xxe_vulns.get('file_read_possible'):
                print(f"  [!] 可能支持文件读取")
            if xxe_vulns.get('ssrf_possible'):
                print(f"  [!] 可能支持SSRF攻击")
        
        # 配置文件泄露情况
        config_leaks = self.scan_results.get('config_leaks', {})
        if config_leaks.get('leaked_configs'):
            print(f"\n配置文件泄露情况:")
            print(f"  泄露配置文件: {len(config_leaks['leaked_configs'])}个")
            if config_leaks.get('database_credentials'):
                print(f"  [!] 数据库凭据可能泄露")
        
        # 信息泄露情况
        info_disclosure = self.scan_results.get('information_disclosure', {})
        if (info_disclosure.get('version_exposed') or 
            info_disclosure.get('config_files') or 
            info_disclosure.get('mapper_files')):
            print(f"\n信息泄露情况:")
            if info_disclosure.get('version_exposed'):
                print(f"  [!] 版本信息泄露")
            if info_disclosure.get('config_files'):
                print(f"  配置文件泄露: {len(info_disclosure['config_files'])}个")
            if info_disclosure.get('mapper_files'):
                print(f"  Mapper文件泄露: {len(info_disclosure['mapper_files'])}个")
        
        # 安全建议
        if self.scan_results.get('recommendations'):
            print(f"\n安全建议:")
            for i, rec in enumerate(self.scan_results['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print(f"="*60)
        
        # 保存到文件
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
                print(f"\n[+] 扫描结果已保存到: {output_file}")
            except Exception as e:
                print(f"[-] 保存结果失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='MyBatis综合漏洞扫描工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--threads', type=int, default=5, help='并发线程数')
    parser.add_argument('--vulns', help='指定要扫描的漏洞类型，用逗号分隔')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不利用')
    parser.add_argument('--output', help='输出结果到JSON文件')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 创建扫描器实例
    scanner = MyBatisComprehensiveScanner(
        target_url=args.target,
        timeout=args.timeout,
        threads=args.threads
    )
    
    # 解析要扫描的漏洞类型
    selected_vulns = None
    if args.vulns:
        selected_vulns = [v.strip().upper() for v in args.vulns.split(',')]
        # 映射简化名称到完整名称
        vuln_mapping = {
            'SQL': 'SQL_INJECTION',
            'XXE': 'XXE_EXPLOIT',
            'CONFIG': 'CONFIG_LEAK',
            'CACHE': 'CACHE_POISON',
            'DESER': 'DESERIALIZATION'
        }
        
        mapped_vulns = []
        for vuln in selected_vulns:
            if vuln in vuln_mapping:
                mapped_vulns.append(vuln_mapping[vuln])
            elif vuln in scanner.scan_modules:
                mapped_vulns.append(vuln)
            else:
                print(f"[-] 未知的漏洞类型: {vuln}")
        
        selected_vulns = mapped_vulns if mapped_vulns else None
    
    try:
        # 运行综合扫描
        results = scanner.run_comprehensive_scan(selected_vulns)
        
        # 生成报告
        scanner.generate_report(args.output)
        
        # 返回适当的退出码
        vulnerable_count = len([v for v in results['vulnerabilities'].values() 
                              if v.get('vulnerable')])
        
        if vulnerable_count > 0:
            print(f"\n[!] 发现 {vulnerable_count} 个漏洞，建议立即修复!")
            sys.exit(1)
        else:
            print(f"\n[+] 未发现明显漏洞，但建议定期进行安全检查")
            sys.exit(0)
    
    except KeyboardInterrupt:
        print(f"\n[*] 扫描被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"[-] 扫描过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
