#!/usr/bin/env python3
"""
Apache Struts 综合漏洞扫描工具
集成多个Struts漏洞检测和利用功能的综合扫描器
"""

import sys
import os
import subprocess
import argparse
import json
import threading
import time
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import warnings
warnings.filterwarnings("ignore")

class StrutsComprehensiveScanner:
    def __init__(self, target_url, timeout=10, threads=5):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.threads = threads
        self.scan_results = {
            'target': target_url,
            'scan_time': datetime.now().isoformat(),
            'struts_version': 'Unknown',
            'vulnerabilities': {},
            'file_uploads': {},
            'information_disclosure': {},
            'recommendations': []
        }
        
        # 可用的扫描模块
        self.scan_modules = {
            # 严重漏洞
            'S2-045': {
                'script': 'CVE-2017-5638.py',
                'description': 'Jakarta文件上传远程代码执行',
                'severity': 'Critical',
                'cve': 'CVE-2017-5638'
            },
            'S2-048': {
                'script': 'CVE-2017-9791.py', 
                'description': 'Struts2 Showcase远程代码执行',
                'severity': 'Critical',
                'cve': 'CVE-2017-9791'
            },
            'S2-057': {
                'script': 'CVE-2018-11776.py',
                'description': '命名空间远程代码执行',
                'severity': 'Critical',
                'cve': 'CVE-2018-11776'
            },
            'S2-059': {
                'script': 'CVE-2019-0230.py',
                'description': 'OGNL表达式注入RCE',
                'severity': 'Critical',
                'cve': 'CVE-2019-0230'
            },
            'S2-061': {
                'script': 'CVE-2021-31805.py',
                'description': '文件上传远程代码执行',
                'severity': 'Critical',
                'cve': 'CVE-2021-31805'
            },
            'S2-066': {
                'script': 'CVE-2023-50164.py',
                'description': '文件上传路径遍历RCE',
                'severity': 'Critical',
                'cve': 'CVE-2023-50164'
            },
            
            # 高危漏洞
            'S2-001': {
                'script': 'S2-001.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-003': {
                'script': 'S2-003.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-005': {
                'script': 'S2-005.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-007': {
                'script': 'S2-007.py',
                'description': '用户输入验证绕过',
                'severity': 'High',
                'cve': None
            },
            'S2-008': {
                'script': 'S2-008.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-009': {
                'script': 'S2-009.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-013': {
                'script': 'S2-013.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-015': {
                'script': 'S2-015.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-016': {
                'script': 'S2-016.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-019': {
                'script': 'S2-019.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            },
            'S2-032': {
                'script': 'S2-032.py',
                'description': 'OGNL表达式注入',
                'severity': 'High',
                'cve': None
            }
        }
    
    def run_script(self, script_name, args):
        """运行指定的扫描脚本"""
        script_path = os.path.join(os.path.dirname(__file__), script_name)
        
        if not os.path.exists(script_path):
            return None, f"脚本不存在: {script_path}"
        
        try:
            cmd = ['python3', script_path] + args
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=self.timeout * 3
            )
            
            return result.returncode, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            return -1, "", "脚本执行超时"
        except Exception as e:
            return -1, "", str(e)
    
    def scan_vulnerability(self, vuln_id, module_info):
        """扫描单个漏洞"""
        print(f"[*] 扫描 {vuln_id}: {module_info['description']}")
        
        # 构造扫描参数
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        
        returncode, stdout, stderr = self.run_script(module_info['script'], args)
        
        vulnerability_result = {
            'vuln_id': vuln_id,
            'description': module_info['description'],
            'severity': module_info['severity'],
            'cve': module_info.get('cve'),
            'vulnerable': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 检查输出中是否包含漏洞存在的指示
            if ('存在' in stdout or 'vulnerable' in stdout.lower() or 
                '发现' in stdout or 'found' in stdout.lower() or
                '成功' in stdout or 'success' in stdout.lower() or
                '可利用' in stdout or 'exploitable' in stdout.lower()):
                vulnerability_result['vulnerable'] = True
                print(f"[+] {vuln_id} 漏洞存在!")
            else:
                print(f"[-] {vuln_id} 漏洞不存在")
        else:
            print(f"[-] {vuln_id} 扫描失败: {stderr}")
        
        return vulnerability_result
    
    def detect_struts_version(self):
        """检测Struts版本"""
        print(f"[*] 检测Struts版本...")
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('struts_version_detect.py', args)
        
        if returncode == 0:
            # 从输出中提取版本信息
            lines = stdout.split('\n')
            for line in lines:
                if 'Struts版本' in line or 'version' in line.lower():
                    # 提取版本号
                    import re
                    version_match = re.search(r'(\d+\.\d+\.\d+)', line)
                    if version_match:
                        version = version_match.group(1)
                        self.scan_results['struts_version'] = version
                        print(f"[+] 检测到Struts版本: {version}")
                        return version
        
        print(f"[-] 无法检测Struts版本")
        return 'Unknown'
    
    def scan_information_disclosure(self):
        """扫描信息泄露"""
        print(f"[*] 扫描信息泄露...")
        
        args = [self.target_url, '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('struts_info_disclosure.py', args)
        
        info_disclosure = {
            'scanned': True,
            'version_exposed': False,
            'config_files': [],
            'dev_mode_enabled': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            if '版本信息' in stdout or 'version' in stdout.lower():
                info_disclosure['version_exposed'] = True
            
            if '配置文件' in stdout or 'config' in stdout.lower():
                # 提取配置文件路径
                lines = stdout.split('\n')
                for line in lines:
                    if '.xml' in line or '.properties' in line:
                        info_disclosure['config_files'].append(line.strip())
            
            if '开发模式' in stdout or 'dev mode' in stdout.lower():
                info_disclosure['dev_mode_enabled'] = True
        
        self.scan_results['information_disclosure'] = info_disclosure
    
    def scan_file_uploads(self):
        """扫描文件上传漏洞"""
        print(f"[*] 扫描文件上传漏洞...")
        
        args = [self.target_url, '--check', '--timeout', str(self.timeout)]
        returncode, stdout, stderr = self.run_script('struts_file_upload.py', args)
        
        file_upload_result = {
            'scanned': True,
            'upload_endpoints': [],
            'vulnerable_uploads': [],
            'webshell_uploaded': False,
            'output': stdout,
            'error': stderr
        }
        
        if returncode == 0:
            # 提取上传端点信息
            lines = stdout.split('\n')
            for line in lines:
                if '上传端点' in line or 'upload' in line.lower():
                    file_upload_result['upload_endpoints'].append(line.strip())
                
                if '可利用' in line or 'exploitable' in line.lower():
                    file_upload_result['vulnerable_uploads'].append(line.strip())
        
        self.scan_results['file_uploads'] = file_upload_result
    
    def run_comprehensive_scan(self, selected_vulns=None):
        """运行综合扫描"""
        print(f"[*] 开始Struts综合漏洞扫描")
        print(f"[*] 目标: {self.target_url}")
        print(f"[*] 超时: {self.timeout}秒")
        print(f"[*] 线程: {self.threads}")
        print("-" * 60)
        
        # 检测Struts版本
        self.detect_struts_version()
        
        # 信息收集
        self.scan_information_disclosure()
        self.scan_file_uploads()
        
        # 选择要扫描的漏洞
        if selected_vulns:
            vulns_to_scan = {k: v for k, v in self.scan_modules.items() 
                           if k in selected_vulns}
        else:
            vulns_to_scan = self.scan_modules
        
        print(f"[*] 准备扫描 {len(vulns_to_scan)} 个漏洞...")
        
        # 多线程扫描漏洞
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            future_to_vuln = {
                executor.submit(self.scan_vulnerability, vuln_id, module_info): vuln_id
                for vuln_id, module_info in vulns_to_scan.items()
            }
            
            for future in as_completed(future_to_vuln):
                vuln_id = future_to_vuln[future]
                try:
                    result = future.result()
                    self.scan_results['vulnerabilities'][vuln_id] = result
                except Exception as e:
                    print(f"[-] {vuln_id} 扫描异常: {e}")
                    self.scan_results['vulnerabilities'][vuln_id] = {
                        'vuln_id': vuln_id,
                        'vulnerable': False,
                        'error': str(e)
                    }
        
        # 生成安全建议
        self.generate_recommendations()
        
        print(f"\n[*] 扫描完成!")
        return self.scan_results
    
    def generate_recommendations(self):
        """生成安全建议"""
        recommendations = []
        
        # 检查是否有严重漏洞
        critical_vulns = [v for v in self.scan_results['vulnerabilities'].values() 
                         if v.get('vulnerable') and v.get('severity') == 'Critical']
        
        if critical_vulns:
            recommendations.append("立即升级Struts到最新安全版本")
            recommendations.append("部署Web应用防火墙(WAF)阻止恶意请求")
        
        # 检查版本信息
        if self.scan_results.get('information_disclosure', {}).get('version_exposed'):
            recommendations.append("隐藏Struts版本信息")
        
        # 检查开发模式
        if self.scan_results.get('information_disclosure', {}).get('dev_mode_enabled'):
            recommendations.append("禁用Struts开发模式")
        
        # 检查文件上传
        if self.scan_results.get('file_uploads', {}).get('vulnerable_uploads'):
            recommendations.append("加强文件上传验证和限制")
        
        # 通用建议
        recommendations.extend([
            "禁用动态方法调用(DynamicMethodInvocation)",
            "限制OGNL表达式执行权限",
            "定期进行安全扫描和渗透测试",
            "监控应用日志中的异常请求",
            "实施输入验证和输出编码"
        ])
        
        self.scan_results['recommendations'] = recommendations
    
    def generate_report(self, output_file=None):
        """生成扫描报告"""
        print(f"\n" + "="*60)
        print(f"Apache Struts 综合漏洞扫描报告")
        print(f"="*60)
        print(f"目标: {self.target_url}")
        print(f"扫描时间: {self.scan_results['scan_time']}")
        print(f"Struts版本: {self.scan_results['struts_version']}")
        
        # 漏洞统计
        total_vulns = len(self.scan_results['vulnerabilities'])
        vulnerable_count = len([v for v in self.scan_results['vulnerabilities'].values() 
                              if v.get('vulnerable')])
        
        print(f"\n漏洞扫描统计:")
        print(f"  总扫描漏洞数: {total_vulns}")
        print(f"  发现漏洞数: {vulnerable_count}")
        
        # 按严重程度分类
        severity_stats = {}
        for vuln in self.scan_results['vulnerabilities'].values():
            if vuln.get('vulnerable'):
                severity = vuln.get('severity', 'Unknown')
                severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        if severity_stats:
            print(f"\n按严重程度分类:")
            for severity, count in severity_stats.items():
                print(f"  {severity}: {count}个")
        
        # 详细漏洞信息
        if vulnerable_count > 0:
            print(f"\n发现的漏洞详情:")
            for vuln_id, vuln_info in self.scan_results['vulnerabilities'].items():
                if vuln_info.get('vulnerable'):
                    cve = f" ({vuln_info['cve']})" if vuln_info.get('cve') else ""
                    print(f"  [!] {vuln_id}{cve}: {vuln_info['description']}")
                    print(f"      严重程度: {vuln_info['severity']}")
        
        # 信息泄露情况
        info_disclosure = self.scan_results.get('information_disclosure', {})
        if info_disclosure.get('version_exposed') or info_disclosure.get('config_files'):
            print(f"\n信息泄露情况:")
            if info_disclosure.get('version_exposed'):
                print(f"  [!] 版本信息泄露")
            if info_disclosure.get('dev_mode_enabled'):
                print(f"  [!] 开发模式启用")
            if info_disclosure.get('config_files'):
                print(f"  [!] 配置文件泄露: {len(info_disclosure['config_files'])}个")
        
        # 文件上传情况
        file_uploads = self.scan_results.get('file_uploads', {})
        if file_uploads.get('upload_endpoints'):
            print(f"\n文件上传情况:")
            print(f"  发现上传端点: {len(file_uploads['upload_endpoints'])}个")
            if file_uploads.get('vulnerable_uploads'):
                print(f"  可利用上传点: {len(file_uploads['vulnerable_uploads'])}个")
        
        # 安全建议
        if self.scan_results.get('recommendations'):
            print(f"\n安全建议:")
            for i, rec in enumerate(self.scan_results['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print(f"="*60)
        
        # 保存到文件
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
                print(f"[+] 扫描报告已保存到: {output_file}")
            except Exception as e:
                print(f"[-] 保存报告失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='Apache Struts 综合漏洞扫描工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--vulns', help='指定要扫描的漏洞(逗号分隔)', default=None)
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--threads', type=int, default=5, help='扫描线程数')
    parser.add_argument('--output', '-o', help='输出报告文件路径')
    parser.add_argument('--check-only', action='store_true', help='仅检测漏洞，不利用')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--silent', action='store_true', help='静默模式')
    
    args = parser.parse_args()
    
    # 解析要扫描的漏洞
    selected_vulns = None
    if args.vulns:
        selected_vulns = [v.strip() for v in args.vulns.split(',')]
    
    # 创建扫描器实例
    scanner = StrutsComprehensiveScanner(
        args.target, 
        timeout=args.timeout,
        threads=args.threads
    )
    
    try:
        # 运行综合扫描
        results = scanner.run_comprehensive_scan(selected_vulns)
        
        # 生成报告
        scanner.generate_report(args.output)
        
        # 根据扫描结果设置退出码
        vulnerable_count = len([v for v in results['vulnerabilities'].values() 
                              if v.get('vulnerable')])
        
        if vulnerable_count > 0:
            print(f"\n[!] 发现 {vulnerable_count} 个漏洞!")
            sys.exit(1)
        else:
            print(f"\n[+] 未发现已知漏洞")
            sys.exit(0)
        
    except KeyboardInterrupt:
        print(f"\n[!] 扫描被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 扫描过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
