#!/usr/bin/env python3
"""
Apache Struts 信息泄露检测工具
检测Struts应用中的各种信息泄露漏洞
"""

import sys
import requests
import argparse
import json
import re
import warnings
from urllib.parse import urljoin, urlparse
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class StrutsInfoDisclosure:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 敏感路径列表
        self.sensitive_paths = [
            # Struts配置文件
            '/WEB-INF/classes/struts.xml',
            '/WEB-INF/classes/struts.properties',
            '/WEB-INF/classes/struts-config.xml',
            '/WEB-INF/struts-config.xml',
            '/struts.xml',
            '/struts.properties',
            '/struts-config.xml',
            
            # 开发模式相关
            '/struts/webconsole.html',
            '/struts/devmode.jsp',
            '/struts-tags',
            
            # 日志文件
            '/WEB-INF/classes/log4j.properties',
            '/WEB-INF/classes/logback.xml',
            '/logs/struts.log',
            '/logs/application.log',
            
            # 错误页面
            '/error.jsp',
            '/error.action',
            '/exception.jsp',
            
            # 示例和测试页面
            '/struts2-showcase/',
            '/struts2-blank/',
            '/example/',
            '/examples/',
            '/test/',
            '/demo/',
            
            # 静态资源
            '/struts/struts-tags.tld',
            '/struts/struts-bean.tld',
            '/struts/struts-html.tld',
            '/struts/struts-logic.tld',
            '/js/struts.js',
            '/css/struts.css',
            
            # 备份文件
            '/WEB-INF/classes/struts.xml.bak',
            '/WEB-INF/classes/struts.xml.old',
            '/struts.xml.bak',
            '/struts.xml.old',
            
            # 其他敏感文件
            '/WEB-INF/web.xml',
            '/META-INF/MANIFEST.MF',
            '/WEB-INF/lib/',
            '/WEB-INF/classes/'
        ]
        
        # 敏感信息模式
        self.sensitive_patterns = {
            'database': [
                r'jdbc:[\w]+://([^/\s"\']+)',
                r'database["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'datasource["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'password': [
                r'password["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'passwd["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'pwd["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'api_key': [
                r'api[_-]?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'apikey["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'access[_-]?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'token': [
                r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'jwt["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'bearer["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'secret': [
                r'secret["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'private[_-]?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ],
            'email': [
                r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            ],
            'ip_address': [
                r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b',
            ],
            'path': [
                r'["\']([/\\][^"\']*)["\']',
                r'path["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ]
        }
        
        self.scan_results = {
            'accessible_paths': [],
            'sensitive_info': {},
            'version_info': {},
            'dev_mode_info': {},
            'error_pages': [],
            'recommendations': []
        }
    
    def scan_sensitive_paths(self):
        """扫描敏感路径"""
        print(f"[*] 扫描敏感路径...")
        
        accessible_count = 0
        
        for path in self.sensitive_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    accessible_count += 1
                    print(f"[+] 发现可访问路径: {path}")
                    
                    path_info = {
                        'path': path,
                        'url': url,
                        'status_code': response.status_code,
                        'content_type': response.headers.get('Content-Type', ''),
                        'content_length': len(response.text),
                        'response': response.text
                    }
                    
                    self.scan_results['accessible_paths'].append(path_info)
                    
                    # 分析路径内容
                    self.analyze_path_content(path, response)
                
                elif response.status_code in [401, 403]:
                    print(f"[!] 路径需要认证: {path} (状态码: {response.status_code})")
                
            except Exception as e:
                continue
        
        print(f"\n[*] 扫描完成，发现 {accessible_count} 个可访问路径")
        return accessible_count > 0
    
    def analyze_path_content(self, path, response):
        """分析路径内容，提取敏感信息"""
        content = response.text
        
        try:
            # 尝试解析XML
            if 'xml' in response.headers.get('Content-Type', '').lower() or path.endswith('.xml'):
                self.extract_sensitive_from_xml(path, content)
            else:
                self.extract_sensitive_from_text(path, content)
        except:
            self.extract_sensitive_from_text(path, content)
        
        # 检查版本信息
        self.extract_version_info(path, content)
        
        # 检查开发模式信息
        self.check_dev_mode(path, content)
        
        # 检查错误信息
        self.check_error_information(path, response)
    
    def extract_sensitive_from_xml(self, path, content):
        """从XML内容中提取敏感信息"""
        try:
            import xml.etree.ElementTree as ET
            root = ET.fromstring(content)
            
            # 遍历所有元素
            for elem in root.iter():
                if elem.text:
                    self.extract_sensitive_from_text(path, elem.text)
                
                # 检查属性
                for attr_name, attr_value in elem.attrib.items():
                    if attr_value:
                        self.extract_sensitive_from_text(path, f"{attr_name}={attr_value}")
        
        except Exception:
            # XML解析失败，按文本处理
            self.extract_sensitive_from_text(path, content)
    
    def extract_sensitive_from_text(self, path, content):
        """从文本内容中提取敏感信息"""
        for category, pattern_list in self.sensitive_patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    if category not in self.scan_results['sensitive_info']:
                        self.scan_results['sensitive_info'][category] = []
                    
                    for match in matches:
                        # 过滤明显的假阳性
                        if self.is_valid_sensitive_data(match, category):
                            self.scan_results['sensitive_info'][category].append({
                                'source': path,
                                'pattern': pattern,
                                'value': match
                            })
    
    def is_valid_sensitive_data(self, data, category):
        """验证敏感数据的有效性，过滤假阳性"""
        if not data or len(data.strip()) < 3:
            return False
        
        # 常见的假阳性过滤
        false_positives = [
            'password', 'token', 'secret', 'key', 'example', 'test', 'demo',
            'placeholder', 'your_password', 'your_token', 'change_me',
            '123456', 'admin', 'root', 'user', 'guest', 'default',
            'localhost', '127.0.0.1', '0.0.0.0', 'example.com'
        ]
        
        data_lower = data.lower()
        
        for fp in false_positives:
            if fp in data_lower:
                return False
        
        return True
    
    def extract_version_info(self, path, content):
        """提取版本信息"""
        version_patterns = [
            r'struts[/-]?(\d+\.\d+(?:\.\d+)?)',
            r'apache\s+struts\s+(\d+\.\d+(?:\.\d+)?)',
            r'version["\']?\s*[:=]\s*["\']?(\d+\.\d+(?:\.\d+)?)["\']?',
            r'Implementation-Version:\s*(\d+\.\d+(?:\.\d+)?)',
            r'Bundle-Version:\s*(\d+\.\d+(?:\.\d+)?)'
        ]
        
        for pattern in version_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                version = matches[0]
                if self.is_valid_struts_version(version):
                    self.scan_results['version_info'][path] = version
                    print(f"[+] 在 {path} 中发现Struts版本: {version}")
    
    def is_valid_struts_version(self, version):
        """验证Struts版本号的有效性"""
        try:
            parts = version.split('.')
            if len(parts) < 2:
                return False
            
            major = int(parts[0])
            minor = int(parts[1])
            
            # Struts版本范围检查
            if major == 2 and 0 <= minor <= 6:
                return True
            elif major == 6 and 0 <= minor <= 5:
                return True
            elif major == 1 and 0 <= minor <= 4:
                return True
            
            return False
        except:
            return False
    
    def check_dev_mode(self, path, content):
        """检查开发模式信息"""
        dev_indicators = [
            'struts.devmode',
            'devMode',
            'development mode',
            'debug mode',
            'struts.configuration.xml.reload',
            'struts.freemarker.templatesCache'
        ]
        
        content_lower = content.lower()
        
        for indicator in dev_indicators:
            if indicator.lower() in content_lower:
                # 检查是否启用
                if 'true' in content_lower:
                    self.scan_results['dev_mode_info'][path] = {
                        'indicator': indicator,
                        'enabled': True,
                        'context': self.get_context(content, indicator)
                    }
                    print(f"[!] 在 {path} 中发现开发模式启用: {indicator}")
                else:
                    self.scan_results['dev_mode_info'][path] = {
                        'indicator': indicator,
                        'enabled': False,
                        'context': self.get_context(content, indicator)
                    }
    
    def get_context(self, content, target, context_size=100):
        """获取目标字符串的上下文"""
        try:
            index = content.lower().find(target.lower())
            if index == -1:
                return ""
            
            start = max(0, index - context_size)
            end = min(len(content), index + len(target) + context_size)
            
            context = content[start:end]
            return context.strip()
        
        except Exception:
            return ""
    
    def check_error_information(self, path, response):
        """检查错误信息泄露"""
        content = response.text.lower()
        
        error_indicators = [
            'exception', 'error', 'stacktrace', 'java.lang',
            'struts', 'ognl', 'freemarker', 'xwork',
            'com.opensymphony', 'org.apache.struts'
        ]
        
        found_errors = []
        for indicator in error_indicators:
            if indicator in content:
                found_errors.append(indicator)
        
        if found_errors:
            self.scan_results['error_pages'].append({
                'path': path,
                'indicators': found_errors,
                'content_preview': response.text[:500]
            })
    
    def check_showcase_vulnerabilities(self):
        """检查Struts2 Showcase相关漏洞"""
        print(f"[*] 检查Struts2 Showcase漏洞...")
        
        showcase_paths = [
            '/struts2-showcase/',
            '/showcase/',
            '/struts2-blank/',
            '/blank/'
        ]
        
        for path in showcase_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    print(f"[+] 发现Struts2 Showcase: {path}")
                    
                    # 检查是否存在已知的Showcase漏洞
                    showcase_vulns = [
                        'integration/editGangster.action',
                        'showcase.action',
                        'fileupload/upload.action'
                    ]
                    
                    for vuln_path in showcase_vulns:
                        vuln_url = urljoin(url, vuln_path)
                        try:
                            vuln_response = self.session.get(vuln_url, timeout=self.timeout)
                            if vuln_response.status_code == 200:
                                print(f"[!] 发现潜在漏洞端点: {vuln_path}")
                        except:
                            continue
            
            except Exception:
                continue
    
    def generate_recommendations(self):
        """生成安全建议"""
        recommendations = []
        
        # 检查敏感路径访问
        if self.scan_results['accessible_paths']:
            recommendations.append("限制敏感文件和目录的访问权限")
            recommendations.append("配置Web服务器阻止对WEB-INF目录的直接访问")
        
        # 检查版本信息泄露
        if self.scan_results['version_info']:
            recommendations.append("隐藏Struts版本信息，避免版本信息泄露")
        
        # 检查开发模式
        dev_enabled = any(info.get('enabled', False) for info in self.scan_results['dev_mode_info'].values())
        if dev_enabled:
            recommendations.append("在生产环境中禁用Struts开发模式")
        
        # 检查敏感信息
        if self.scan_results['sensitive_info']:
            recommendations.append("移除配置文件中的敏感信息")
            recommendations.append("使用环境变量或加密配置管理敏感数据")
        
        # 检查错误页面
        if self.scan_results['error_pages']:
            recommendations.append("配置自定义错误页面，避免技术细节泄露")
        
        # 通用建议
        recommendations.extend([
            "定期更新Struts到最新安全版本",
            "实施Web应用防火墙(WAF)保护",
            "配置安全的HTTP响应头",
            "定期进行安全扫描和渗透测试",
            "监控应用日志中的异常访问"
        ])
        
        self.scan_results['recommendations'] = recommendations
    
    def generate_report(self):
        """生成扫描报告"""
        print(f"\n" + "="*60)
        print(f"Struts信息泄露扫描报告")
        print(f"="*60)
        print(f"目标: {self.target_url}")
        
        # 可访问路径统计
        accessible_count = len(self.scan_results['accessible_paths'])
        print(f"\n可访问敏感路径: {accessible_count}个")
        
        if accessible_count > 0:
            print(f"发现的敏感路径:")
            for path_info in self.scan_results['accessible_paths'][:10]:  # 只显示前10个
                print(f"  - {path_info['path']}")
        
        # 版本信息
        if self.scan_results['version_info']:
            print(f"\n版本信息泄露:")
            for path, version in self.scan_results['version_info'].items():
                print(f"  {path}: Struts {version}")
        
        # 开发模式信息
        dev_enabled_count = sum(1 for info in self.scan_results['dev_mode_info'].values() 
                               if info.get('enabled', False))
        if dev_enabled_count > 0:
            print(f"\n开发模式启用: {dev_enabled_count}个位置")
            for path, info in self.scan_results['dev_mode_info'].items():
                if info.get('enabled', False):
                    print(f"  {path}: {info['indicator']}")
        
        # 敏感信息统计
        if self.scan_results['sensitive_info']:
            print(f"\n敏感信息泄露:")
            for category, items in self.scan_results['sensitive_info'].items():
                print(f"  {category}: {len(items)}个")
        
        # 错误页面
        if self.scan_results['error_pages']:
            print(f"\n错误信息泄露: {len(self.scan_results['error_pages'])}个页面")
        
        # 安全建议
        if self.scan_results['recommendations']:
            print(f"\n安全建议:")
            for i, rec in enumerate(self.scan_results['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print(f"="*60)

def main():
    parser = argparse.ArgumentParser(description='Apache Struts信息泄露检测工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--output', help='输出结果到JSON文件')
    parser.add_argument('--paths-only', action='store_true', help='仅扫描路径，不分析内容')
    parser.add_argument('--credentials-only', action='store_true', help='仅扫描凭据信息')
    parser.add_argument('--config-only', action='store_true', help='仅扫描配置文件')
    
    args = parser.parse_args()
    
    # 创建扫描器实例
    scanner = StrutsInfoDisclosure(args.target, args.timeout)
    
    try:
        # 运行扫描
        scanner.scan_sensitive_paths()
        
        # 检查Showcase漏洞
        scanner.check_showcase_vulnerabilities()
        
        # 生成安全建议
        scanner.generate_recommendations()
        
        # 生成报告
        scanner.generate_report()
        
        # 保存结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(scanner.scan_results, f, ensure_ascii=False, indent=2)
            print(f"\n[+] 扫描结果已保存到: {args.output}")
        
        # 根据扫描结果设置退出码
        issues_found = (
            len(scanner.scan_results['accessible_paths']) +
            len(scanner.scan_results['sensitive_info']) +
            len(scanner.scan_results['error_pages'])
        )
        
        if issues_found > 0:
            print(f"\n[!] 发现 {issues_found} 个信息泄露问题!")
            sys.exit(1)
        else:
            print(f"\n[+] 未发现明显的信息泄露问题")
            sys.exit(0)
        
    except KeyboardInterrupt:
        print(f"\n[!] 扫描被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 扫描过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
