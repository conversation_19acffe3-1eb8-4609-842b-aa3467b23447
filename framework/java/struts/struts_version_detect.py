#!/usr/bin/env python3
"""
Apache Struts 版本检测工具
通过多种方式识别Struts框架版本和相关组件信息
"""

import sys
import requests
import argparse
import re
import json
import warnings
from urllib.parse import urljoin, urlparse
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class StrutsVersionDetector:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.version_info = {
            'struts_version': 'Unknown',
            'java_version': 'Unknown',
            'server_info': 'Unknown',
            'framework_info': {},
            'detection_methods': [],
            'vulnerabilities': []
        }
        
        # Struts特征路径
        self.struts_paths = [
            '/struts/',
            '/struts2/',
            '/WEB-INF/lib/',
            '/WEB-INF/classes/',
            '/struts-tags',
            '/struts.xml',
            '/struts.properties',
            '/struts-config.xml'
        ]
        
        # 常见的Struts Action扩展名
        self.action_extensions = [
            '.action',
            '.do',
            '.jsp'
        ]
    
    def detect_from_headers(self):
        """从HTTP头检测版本信息"""
        print(f"[*] 从HTTP头检测版本信息...")
        
        try:
            response = self.session.get(self.target_url, timeout=self.timeout)
            
            # 检查服务器头信息
            server_header = response.headers.get('Server', '')
            if server_header:
                self.version_info['server_info'] = server_header
                print(f"[+] 服务器信息: {server_header}")
                self.version_info['detection_methods'].append('HTTP头 (Server)')
            
            # 检查其他可能包含版本信息的头
            version_headers = [
                'X-Powered-By', 'X-Framework', 'X-Struts-Version',
                'X-Application-Context', 'X-Version'
            ]
            
            for header in version_headers:
                if header in response.headers:
                    value = response.headers[header]
                    print(f"[+] 发现版本头 {header}: {value}")
                    self.version_info['framework_info'][header] = value
                    self.version_info['detection_methods'].append(f'HTTP头 ({header})')
                    
                    # 尝试从头信息中提取Struts版本
                    version_match = re.search(r'struts[/-]?(\d+\.\d+(?:\.\d+)?)', value, re.IGNORECASE)
                    if version_match:
                        self.version_info['struts_version'] = version_match.group(1)
                        print(f"[+] 从HTTP头检测到Struts版本: {version_match.group(1)}")
            
            return len(self.version_info['detection_methods']) > 0
            
        except Exception as e:
            print(f"[-] HTTP头检测失败: {e}")
            return False
    
    def detect_from_error_pages(self):
        """从错误页面检测版本信息"""
        print(f"[*] 从错误页面检测版本信息...")
        
        # 构造可能触发错误的请求
        error_urls = [
            '/nonexistent.action',
            '/test.do',
            '/error.action',
            '/admin.action',
            '/login.action?error=true'
        ]
        
        for error_url in error_urls:
            try:
                url = urljoin(self.target_url, error_url)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code in [404, 500, 400]:
                    content = response.text
                    
                    # 查找Struts版本信息
                    version_patterns = [
                        r'struts[/-]?(\d+\.\d+(?:\.\d+)?)',
                        r'apache\s+struts\s+(\d+\.\d+(?:\.\d+)?)',
                        r'struts2[/-]?(\d+\.\d+(?:\.\d+)?)',
                        r'org\.apache\.struts2.*?(\d+\.\d+(?:\.\d+)?)',
                        r'com\.opensymphony\.xwork2.*?(\d+\.\d+(?:\.\d+)?)'
                    ]
                    
                    for pattern in version_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            version = matches[0]
                            if self.is_valid_struts_version(version):
                                self.version_info['struts_version'] = version
                                print(f"[+] 从错误页面检测到Struts版本: {version}")
                                self.version_info['detection_methods'].append('错误页面')
                                return True
                    
                    # 查找Java版本信息
                    java_patterns = [
                        r'java\s+version\s+"([^"]+)"',
                        r'java[/-]?(\d+\.\d+(?:\.\d+)?)',
                        r'jdk[/-]?(\d+\.\d+(?:\.\d+)?)'
                    ]
                    
                    for pattern in java_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            java_version = matches[0]
                            self.version_info['java_version'] = java_version
                            print(f"[+] 检测到Java版本: {java_version}")
                            break
            
            except Exception:
                continue
        
        return False
    
    def detect_from_static_files(self):
        """从静态文件检测版本信息"""
        print(f"[*] 从静态文件检测版本信息...")
        
        # 常见的包含版本信息的文件
        static_files = [
            '/struts/struts-tags.tld',
            '/struts/struts-bean.tld',
            '/struts/struts-html.tld',
            '/struts/struts-logic.tld',
            '/META-INF/MANIFEST.MF',
            '/WEB-INF/web.xml',
            '/js/struts.js',
            '/css/struts.css'
        ]
        
        for file_path in static_files:
            try:
                url = urljoin(self.target_url, file_path)
                response = self.session.get(url, timeout=self.timeout)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # 查找版本信息
                    version_patterns = [
                        r'struts[/-]?(\d+\.\d+(?:\.\d+)?)',
                        r'version["\']?\s*[:=]\s*["\']?(\d+\.\d+(?:\.\d+)?)["\']?',
                        r'Implementation-Version:\s*(\d+\.\d+(?:\.\d+)?)',
                        r'Bundle-Version:\s*(\d+\.\d+(?:\.\d+)?)'
                    ]
                    
                    for pattern in version_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            version = matches[0]
                            if self.is_valid_struts_version(version):
                                self.version_info['struts_version'] = version
                                print(f"[+] 从静态文件 {file_path} 检测到Struts版本: {version}")
                                self.version_info['detection_methods'].append(f'静态文件 ({file_path})')
                                return True
            
            except Exception:
                continue
        
        return False
    
    def detect_from_jar_files(self):
        """从JAR文件路径检测版本信息"""
        print(f"[*] 从JAR文件路径检测版本信息...")
        
        # 常见的Struts JAR文件路径
        jar_paths = [
            '/WEB-INF/lib/struts2-core-',
            '/WEB-INF/lib/struts-core-',
            '/WEB-INF/lib/xwork-core-',
            '/WEB-INF/lib/ognl-',
            '/WEB-INF/lib/freemarker-'
        ]
        
        # 常见的版本号范围
        version_ranges = [
            '2.0.', '2.1.', '2.2.', '2.3.', '2.4.', '2.5.', '2.6.',
            '6.0.', '6.1.', '6.2.', '6.3.'
        ]
        
        for jar_path in jar_paths:
            for version_prefix in version_ranges:
                for patch in range(0, 50):  # 尝试常见的补丁版本
                    version = f"{version_prefix}{patch}"
                    jar_url = f"{jar_path}{version}.jar"
                    
                    try:
                        url = urljoin(self.target_url, jar_url)
                        response = self.session.head(url, timeout=self.timeout)
                        
                        if response.status_code == 200:
                            self.version_info['struts_version'] = version
                            print(f"[+] 从JAR文件路径检测到Struts版本: {version}")
                            self.version_info['detection_methods'].append(f'JAR文件 ({jar_url})')
                            return True
                    
                    except Exception:
                        continue
        
        return False
    
    def detect_from_action_responses(self):
        """从Action响应检测版本信息"""
        print(f"[*] 从Action响应检测版本信息...")
        
        # 常见的Action路径
        action_paths = [
            '/login',
            '/index',
            '/home',
            '/main',
            '/admin',
            '/user',
            '/test',
            '/example'
        ]
        
        for action_path in action_paths:
            for extension in self.action_extensions:
                try:
                    url = urljoin(self.target_url, action_path + extension)
                    response = self.session.get(url, timeout=self.timeout)
                    
                    if response.status_code in [200, 302, 500]:
                        content = response.text
                        
                        # 查找Struts特征
                        struts_indicators = [
                            r'struts[/-]?(\d+\.\d+(?:\.\d+)?)',
                            r'com\.opensymphony\.xwork2',
                            r'org\.apache\.struts2',
                            r'freemarker\.template',
                            r'ognl\.Ognl'
                        ]
                        
                        for indicator in struts_indicators:
                            matches = re.findall(indicator, content, re.IGNORECASE)
                            if matches and indicator.startswith('struts'):
                                version = matches[0]
                                if self.is_valid_struts_version(version):
                                    self.version_info['struts_version'] = version
                                    print(f"[+] 从Action响应检测到Struts版本: {version}")
                                    self.version_info['detection_methods'].append(f'Action响应 ({url})')
                                    return True
                            elif matches:
                                print(f"[+] 发现Struts框架特征: {indicator}")
                                self.version_info['framework_info'][indicator] = True
                
                except Exception:
                    continue
        
        return False
    
    def is_valid_struts_version(self, version):
        """验证Struts版本号的有效性"""
        try:
            parts = version.split('.')
            if len(parts) < 2:
                return False
            
            major = int(parts[0])
            minor = int(parts[1])
            
            # Struts版本范围检查
            if major == 2 and 0 <= minor <= 6:
                return True
            elif major == 6 and 0 <= minor <= 5:
                return True
            elif major == 1 and 0 <= minor <= 4:
                return True
            
            return False
        except:
            return False
    
    def map_version_to_vulnerabilities(self):
        """根据版本映射已知漏洞"""
        version = self.version_info['struts_version']
        
        if version == 'Unknown':
            return
        
        print(f"[*] 映射版本 {version} 的已知漏洞...")
        
        # 漏洞版本映射表
        vulnerability_map = {
            'S2-001': {'min': '2.0.0', 'max': '2.0.8'},
            'S2-003': {'min': '2.0.0', 'max': '2.0.11.2'},
            'S2-005': {'min': '2.0.0', 'max': '2.1.8.1'},
            'S2-007': {'min': '2.0.0', 'max': '2.2.3'},
            'S2-008': {'min': '2.1.0', 'max': '2.3.1'},
            'S2-009': {'min': '2.1.0', 'max': '2.3.1.1'},
            'S2-013': {'min': '2.0.0', 'max': '2.3.14.1'},
            'S2-015': {'min': '2.0.0', 'max': '2.3.14.2'},
            'S2-016': {'min': '2.0.0', 'max': '2.3.15'},
            'S2-019': {'min': '2.0.0', 'max': '2.3.15.1'},
            'S2-032': {'min': '2.3.20', 'max': '2.3.28'},
            'S2-045': {'min': '2.3.5', 'max': '2.3.31'},
            'S2-048': {'min': '2.3.0', 'max': '2.5.10'},
            'S2-057': {'min': '2.3.0', 'max': '2.3.34'},
            'S2-059': {'min': '2.0.0', 'max': '2.5.20'},
            'S2-061': {'min': '2.0.0', 'max': '2.5.29'},
            'S2-066': {'min': '2.5.0', 'max': '2.5.32'}
        }
        
        vulnerable_to = []
        
        for vuln_id, version_range in vulnerability_map.items():
            if self.is_version_in_range(version, version_range['min'], version_range['max']):
                vulnerable_to.append(vuln_id)
        
        if vulnerable_to:
            self.version_info['vulnerabilities'] = vulnerable_to
            print(f"[!] 版本 {version} 可能受以下漏洞影响:")
            for vuln in vulnerable_to:
                print(f"    - {vuln}")
        else:
            print(f"[+] 版本 {version} 未发现已知漏洞")
    
    def is_version_in_range(self, version, min_version, max_version):
        """检查版本是否在指定范围内"""
        try:
            def version_to_tuple(v):
                return tuple(map(int, v.split('.')))
            
            version_tuple = version_to_tuple(version)
            min_tuple = version_to_tuple(min_version)
            max_tuple = version_to_tuple(max_version)
            
            return min_tuple <= version_tuple <= max_tuple
        except:
            return False
    
    def run_detection(self):
        """运行版本检测"""
        print(f"[*] 开始Struts版本检测...")
        print(f"[*] 目标: {self.target_url}")
        print("-" * 50)
        
        # 按优先级运行检测方法
        detection_methods = [
            self.detect_from_headers,
            self.detect_from_error_pages,
            self.detect_from_static_files,
            self.detect_from_jar_files,
            self.detect_from_action_responses
        ]
        
        for method in detection_methods:
            try:
                if method():
                    break
            except Exception as e:
                print(f"[-] 检测方法失败: {e}")
                continue
        
        # 映射漏洞
        self.map_version_to_vulnerabilities()
        
        return self.version_info
    
    def generate_report(self):
        """生成检测报告"""
        print(f"\n" + "="*50)
        print(f"Struts版本检测报告")
        print(f"="*50)
        print(f"目标: {self.target_url}")
        print(f"Struts版本: {self.version_info['struts_version']}")
        print(f"Java版本: {self.version_info['java_version']}")
        print(f"服务器信息: {self.version_info['server_info']}")
        
        if self.version_info['detection_methods']:
            print(f"\n检测方法:")
            for method in self.version_info['detection_methods']:
                print(f"  - {method}")
        
        if self.version_info['framework_info']:
            print(f"\n框架信息:")
            for key, value in self.version_info['framework_info'].items():
                print(f"  {key}: {value}")
        
        if self.version_info['vulnerabilities']:
            print(f"\n可能受影响的漏洞:")
            for vuln in self.version_info['vulnerabilities']:
                print(f"  - {vuln}")
        
        print(f"="*50)

def main():
    parser = argparse.ArgumentParser(description='Apache Struts版本检测工具')
    parser.add_argument('target', help='目标URL')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--output', help='输出结果到JSON文件')
    parser.add_argument('--json', action='store_true', help='以JSON格式输出')
    
    args = parser.parse_args()
    
    # 创建检测器实例
    detector = StrutsVersionDetector(args.target, args.timeout)
    
    try:
        # 运行检测
        result = detector.run_detection()
        
        # 生成报告
        if args.json:
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            detector.generate_report()
        
        # 保存结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n[+] 检测结果已保存到: {args.output}")
        
        # 根据检测结果设置退出码
        if result['struts_version'] != 'Unknown':
            sys.exit(0)
        else:
            sys.exit(1)
        
    except KeyboardInterrupt:
        print(f"\n[!] 检测被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[!] 检测过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()