# Struts常见路径字典
# Action端点
/login.action
/index.action
/user.action
/admin.action
/test.action
/example.action
/welcome.action
/home.action
/main.action
/search.action
/upload.action
/fileUpload.action
/uploadFile.action
/doUpload.action
/download.action
/logout.action
/register.action
/profile.action
/settings.action
/config.action

# DO端点
/login.do
/index.do
/user.do
/admin.do
/test.do
/example.do
/welcome.do
/home.do
/main.do
/search.do
/upload.do
/fileUpload.do
/uploadFile.do
/doUpload.do
/download.do
/logout.do
/register.do
/profile.do
/settings.do
/config.do

# 配置文件
/WEB-INF/classes/struts.xml
/WEB-INF/classes/struts.properties
/WEB-INF/classes/struts-config.xml
/WEB-INF/struts-config.xml
/struts.xml
/struts.properties
/struts-config.xml
/WEB-INF/web.xml
/META-INF/MANIFEST.MF

# 示例和测试
/struts2-showcase/
/struts2-blank/
/example/
/examples/
/test/
/demo/
/sample/
/tutorial/

# 静态资源
/struts/struts-tags.tld
/struts/struts-bean.tld
/struts/struts-html.tld
/struts/struts-logic.tld
/js/struts.js
/css/struts.css
/images/struts/
/static/struts/

# 错误页面
/error.jsp
/error.action
/exception.jsp
/404.jsp
/500.jsp

# 开发模式
/struts/webconsole.html
/struts/devmode.jsp
/struts-tags
/console/
/debug/

# 日志文件
/logs/struts.log
/logs/application.log
/logs/error.log
/WEB-INF/classes/log4j.properties
/WEB-INF/classes/logback.xml

# 备份文件
/WEB-INF/classes/struts.xml.bak
/WEB-INF/classes/struts.xml.old
/struts.xml.bak
/struts.xml.old
/struts.properties.bak
/struts-config.xml.bak

# JAR文件路径
/WEB-INF/lib/struts2-core-
/WEB-INF/lib/struts-core-
/WEB-INF/lib/xwork-core-
/WEB-INF/lib/ognl-
/WEB-INF/lib/freemarker-
/WEB-INF/lib/
/WEB-INF/classes/