# Struts常见参数名称字典
username
password
email
name
id
user
login
search
query
input
data
value
param
field
text
content
message
title
description
comment
note
remark
info
detail
summary
abstract
keyword
tag
category
type
kind
sort
order
direction
limit
offset
page
size
count
total
max
min
start
end
from
to
date
time
timestamp
year
month
day
hour
minute
second
format
encoding
charset
language
locale
timezone
currency
unit
measure
weight
height
width
length
depth
area
volume
price
cost
amount
quantity
number
code
key
token
session
cookie
header
agent
referer
host
port
protocol
scheme
domain
subdomain
path
file
filename
extension
mime
size
hash
checksum
signature
certificate
public
private
secret
salt
iv
nonce
challenge
response
request
reply
answer
question
option
choice
selection
preference
setting
config
configuration
parameter
argument
variable
constant
property
attribute
feature
flag
switch
toggle
enable
disable
active
inactive
visible
hidden
public
private
readonly
editable
required
optional
valid
invalid
error
warning
info
debug
trace
log
audit
monitor
track
trace
profile
benchmark
performance
memory
cpu
disk
network
bandwidth
latency
throughput
capacity
usage
load
stress
test
mock
fake
dummy
sample
example
demo
trial
preview
draft
temp
temporary
cache
buffer
queue
stack
heap
pool
batch
bulk
single
multiple
all
none
any
some
few
many
most
least
first
last
next
previous
current
latest
oldest
newest
recent
old
new
fresh
stale
expired
valid
invalid
active
inactive
enabled
disabled
on
off
yes
no
true
false
ok
cancel
submit
reset
clear
save
load
create
read
update
delete
insert
select
modify
change
edit
add
remove
copy
move
cut
paste
undo
redo
refresh
reload
restart
shutdown
reboot
pause
resume
play
stop
start
end
begin
finish
complete
abort
cancel
skip
retry
repeat
loop
break
continue
return
exit
quit
close
open
show
hide
expand
collapse
maximize
minimize
restore
resize
drag
drop
click
double
right
left
up
down
forward
backward
home
back
go
navigate
browse
search
find
locate
filter
sort
group
ungroup